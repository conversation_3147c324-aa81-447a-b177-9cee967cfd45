package com.wbgame.mapper.master;

import java.util.List;
import java.util.Map;

import com.wbgame.pojo.nnjy.HotQuestionsInfo;
import com.wbgame.pojo.nnjy.NnjyLevelTotalVo;
import com.wbgame.pojo.nnjy.NnjyLevelUseToolVo;
import com.wbgame.pojo.nnjy.QuestionsInfo;

public interface NnjyCustomerMapper {
	
	List<QuestionsInfo> selectByQType(Integer questionType);
    List<QuestionsInfo> selectByQuestionId(QuestionsInfo questionsInfo); 
    List<QuestionsInfo> selectByKeywords (String keywords);
    int updateQuestions(QuestionsInfo questionsInfo);
    int deleteByQuestionId(Integer id);
    int deleteByQuestionType(Integer questionType);
    int insertQuestion(QuestionsInfo questionsInfo);
    List<HotQuestionsInfo> selectByHotQuestions();
    int updateHit(Integer id);
    int updateHotQuestions(HotQuestionsInfo hotQuestionsInfo);
    int deleteHotQuestions(Integer id);
    int insertHotQuestions(HotQuestionsInfo hotQuestionsInfo);
	
}