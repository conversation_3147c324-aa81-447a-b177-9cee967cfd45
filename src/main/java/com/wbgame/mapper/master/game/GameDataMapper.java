package com.wbgame.mapper.master.game;


import com.wbgame.pojo.game.config.query.AICustomConfigRequestParam;
import com.wbgame.pojo.game.config.query.AliAccountConfigRequestParam;
import com.wbgame.pojo.game.config.query.AliAccountConfigSaveRequestParam;
import com.wbgame.pojo.game.config.response.AICustomConfigResponse;
import com.wbgame.pojo.game.config.response.AliAccountConfigResponse;
import com.wbgame.pojo.game.report.query.AICustomHandleParam;
import com.wbgame.pojo.game.report.query.AiCustomRequestParam;
import com.wbgame.pojo.game.report.response.AICustomResponse;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname GameMapper
 * @Description TODO
 * @Date 2025/03/10 16:56
 */
@Mapper
public interface GameDataMapper {

   List<AICustomResponse> selectAICustomList(AiCustomRequestParam param);

   List<AICustomResponse> selectAICustomListNew(AiCustomRequestParam param);

   List<AICustomConfigResponse> selectAICustomConfigList(AICustomConfigRequestParam param);

   Long insertAICustomConfig(AICustomConfigRequestParam param);

   Long updateAICustomConfig(AICustomConfigRequestParam param);

   void deleteAICustomConfig(Long id);

   int insertAICustomListBatch(List<AICustomResponse> list);

   Long updateAICustomState(AICustomHandleParam param);

   List<AliAccountConfigResponse> selectAliAccountConfigListPage(AliAccountConfigRequestParam param);

   AliAccountConfigResponse getAliAccountConfig(String account);

   Long saveAliAccountConfig(AliAccountConfigSaveRequestParam param);

   Long updateAliAccountConfig(AliAccountConfigSaveRequestParam param);

   void deleteAliAccountConfig(Long id);



}
