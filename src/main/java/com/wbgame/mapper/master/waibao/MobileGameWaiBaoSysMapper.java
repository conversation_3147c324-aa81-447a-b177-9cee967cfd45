package com.wbgame.mapper.master.waibao;

import com.wbgame.pojo.SysTempVo;
import com.wbgame.pojo.WaibaoUserVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * 系统管理
 *
 * <AUTHOR>
 * @date 2020/10/16
 */
public interface MobileGameWaiBaoSysMapper {

    List<WaibaoUserVo> selectOrgMenu(String[] ids);

    List<WaibaoUserVo> selectApp(Map<String, Object> map);

    List<WaibaoUserVo> selectRole(Map<String, Object> map);

    List<WaibaoUserVo> selectMenu(Map<String, Object> map);

    List<WaibaoUserVo> selectOrg(Map<String, Object> map);

    WaibaoUserVo selectUser(Map<String, Object> map);

    List<WaibaoUserVo> selectTest(Map<String, Object> map);

    WaibaoUserVo selectWbSysUser(Map<String, Object> map);

    List<WaibaoUserVo> selectUserList(Map<String, Object> map);

    int deleteUserList(Map<String, Object> map);

    int upDataUserList(Map<String, Object> map);

    int insertUserList(Map<String, Object> map);

    int insertApp(Map<String, Object> map);

    int updataApp(Map<String, Object> map);

    int deleteApp(Map<String, Object> map);

    int deleteWbRole(Map<String, Object> map);

    int insertWbRole(Map<String, Object> map);

    int updateWbRole(Map<String, Object> map);

    int deleteWbMenu(Map<String, Object> map);

    int insertWbMenu(Map<String, Object> map);

    int updateWbMenu(Map<String, Object> map);

    int insertWbOrg(Map<String, Object> map);

    int upDataWbOrg(Map<String, Object> map);

    int deleteWbOrg(Map<String, Object> map);

    int insertWbSysUser(WaibaoUserVo cu);

    int updateWbSysUser(WaibaoUserVo cu);

    int deleteWbSysUser(WaibaoUserVo cu);

    List<WaibaoUserVo> selectWbSysUserInfoList(WaibaoUserVo cu);

    @Update("update dn_fin.min_sys_user set password = #{password} where user_id = #{user_id}")
    int updateWbSysUserForPassword(WaibaoUserVo cu);

    int insertWbSysRole(WaibaoUserVo cu);

    int updateWbSysRole(WaibaoUserVo cu);

    int deleteWbSysRole(WaibaoUserVo cu);

    List<WaibaoUserVo> selectWbSysRoleInfoList(WaibaoUserVo cu);

    @MapKey("login_name")
    @Select("select * from waibao_sys_user")
    Map<String, WaibaoUserVo> selectWbSysUserAllMap();

    @MapKey("role_id")
    @Select("select CAST(role_id as char(20)) as role_id,role_name,app_group from waibao_sys_role")
    Map<String, WaibaoUserVo> selectWbSysRoleAllMap();

    @Select(" ${sql} ")
    public List<Map<String, Object>> queryListMap(@Param("sql") String sql);

    // 用户登陆验证
    SysTempVo userIf(Map<String, Object> map);
    SysTempVo orgIf(Map<String, Object> map);
}