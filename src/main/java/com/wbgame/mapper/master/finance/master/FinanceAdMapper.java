package com.wbgame.mapper.master.finance.master;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;


/**
 * 通用执行语句和查询语句模板
 *
 * <AUTHOR>
 * @date 2020/10/16
 */
public interface FinanceAdMapper {

    @Update(" ${sql} ")
    public int execSql(@Param("sql") String sql); // 直接执行DML sql语句

    @Select(" ${sql} ")
    public List<String> queryListString(@Param("sql") String sql);

    @Select(" ${sql} ")
    public List<Map<String, Object>> queryListMap(@Param("sql") String sql);

    @MapKey("mapkey")
    @Select(" ${sql} ")
    public Map<String, Map<String, Object>> queryListMapOfKey(@Param("sql") String sql);

    @Update(" ${sql} ")
    int execSqlHandle(@Param("sql") String sql, @Param("obj") Object obj);

    @Select(" ${sql} ")
    public List<Map<String, String>> queryListMapOne(@Param("sql")String sql);

    @Select(" ${sql} ")
    public List<Map<String, Object>> queryListMapTwo(@Param("sql") String sql, @Param("obj") Object obj);

    public int batchExecSql(Map<String, Object> paramMap);

    /* 支付宝商户投诉率-查询 */
    public List<Map<String, Object>> selectAlipayComplaintList(Map<String, String> params);
    public Map<String, Object> selectAlipayComplaintSum(Map<String, String> params);

    public int insertAlipayComplaintInfo(JSONObject jsonObject);
    public int updateAlipayComplaintInfoAsOrderCount(List<Map<String, String>> list);

}