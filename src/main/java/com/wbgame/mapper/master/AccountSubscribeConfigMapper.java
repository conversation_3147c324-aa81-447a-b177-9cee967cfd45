package com.wbgame.mapper.master;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.redpack.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 账户订阅页mapper
 * @Author: zl
 * @Date: 2025/03/28 10:40
 */
public interface AccountSubscribeConfigMapper {

    int deleteByIdList(@Param("idList") List<Integer> idList);

    int updateById(@Param("entity") AccountSubscribePageConfig record);

    int batchUpdate(AccountSubscribePageConfig record);

    List<AccountSubscribePageConfig> selectByCondition(@Param("query") AccountSubscribeConfigQuery wxGravityConfigDTO);

    List<AccountSubscribePageConfig> selectByIdList(@Param("idList") List<Integer> idList);

    int insertConfig(@Param("entity") AccountSubscribePageConfig config);

    List<SubscribeConfig> selectSubscribePages(AppSubscribeConfigQuery dto);

    int count(@Param("entity") AccountSubscribePageConfig config);

    String selectLink(@Param("appid") Integer appId);
}
