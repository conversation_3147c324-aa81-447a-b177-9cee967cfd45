package com.wbgame.mapper.master;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.custom.CompetitorDataVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @description: 竞品数据分析表Mapper接口
 **/
public interface CompetitorDataMapper {
    
    /**
     * 查询竞品数据分析列表
     * @param paramMap 查询参数
     * @return 竞品数据列表
     */
    List<CompetitorDataVo> selectCompetitorDataList(Map<String, String> paramMap);
    
    /**
     * 批量插入竞品数据
     * @param list 竞品数据列表
     * @return 影响行数
     */
    int insertCompetitorDataBatch(List<JSONObject> list);

    /**
     * 根据MD5值查询竞品数据
     * @param md5 MD5值
     * @return
     */
    @Select("SELECT COUNT(*) FROM dn_competitor_data WHERE md5 = #{md5}")
    public int countByMd5(@Param("md5")String md5);
    /**
     * 根据MD5值删除竞品数据
     * @param md5 MD5值
     * @return
     */
    @Delete("DELETE FROM dn_competitor_data WHERE md5 = #{md5}")
    public int deleteCompetitorDataByMd5(@Param("md5")String md5);

}