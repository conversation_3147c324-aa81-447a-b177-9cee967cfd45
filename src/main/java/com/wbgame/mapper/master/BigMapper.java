package com.wbgame.mapper.master;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;


public interface BigMapper {
	
	@Update(" ${sql} ")
	public int execSql(@Param("sql")String sql); // 直接执行DML sql语句
	@Select(" ${sql} ")
	public List<String> queryListString(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, Object>> queryListMap(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, String>> queryListMapOne(@Param("sql")String sql);
	@MapKey("mapkey")
	@Select(" ${sql} ")
	public Map<String, Map<String, Object>> queryListMapOfKey(@Param("sql")String sql);
	@Update(" ${sql} ")
	public int execSqlHandle(@Param("sql")String sql, @Param("obj")Object obj);
	@Select(" ${sql} ")
	public List<Map<String, String>> queryListMapTwo(@Param("sql")String sql, @Param("obj")Object obj);
	public int batchExecSql(Map<String, Object> paramMap);
	public int batchExecSqlTwo(Map<String, Object> paramMap);
	
	
	/** 同步变现-汇总数据校准 */
	public int insertExtendAdtypeRevise(@Param("tdate")String tdate);
	public int insertExtendAdtypeReviseTwo(@Param("tdate")String tdate);
	/** 同步变现-汇总数据-用户群 */
	public int insertExtendAdtypeGroup(@Param("tdate")String tdate);
	/** 同步变现-项目ID收入预估 */
	public int insertExtendPrjidIncome(@Param("tdate")String tdate);
	/** 同步变现-二级子渠道展示收入 */
	public int insertExtendSubchaTotal(@Param("tdate")String tdate);
	public List<Map<String, Object>> selectSubchaTotalOfBanner(@Param("tdate")String tdate);
	
	// 项目ID+子渠道+二级子渠道分组的新增 活跃
	int insertPushSubChaAddUser(Map<String, Object> paramMap);
	int insertPushSubChaActUser(Map<String, Object> paramMap);
	
	
	/** 变现-数据gap统计 */
	public List<Map<String,Object>> selectDnShowGapTotal(Map<String, Object> paramMap);
	public Map<String,Object> selectDnShowGapTotalSum(Map<String, Object> paramMap);
	/** 变现收入校准*/
	public List<Map<String,Object>> selectExtendIncomeRevise(Map<String, Object> paramMap);
	public Map<String,Object> selectExtendIncomeReviseSum(Map<String, Object> paramMap);
	/** 汇总数据校准 */
	public List<Map<String,Object>> selectAdtypeTotalRevise(Map<String, Object> paramMap);
	public Map<String,Object> selectAdtypeTotalReviseSum(Map<String, Object> paramMap);
	/** 项目ID收入预估 */
	public List<Map<String,Object>> selectPrjidTotalIncome(Map<String, Object> paramMap);
	public Map<String,Object> selectPrjidTotalIncomeSum(Map<String, Object> paramMap);
	
	/** 二级子渠道展示收入 */
	public List<Map<String,Object>> selectSubchaTotal(Map<String, Object> paramMap);
	public Map<String,Object> selectSubchaTotalSum(Map<String, Object> paramMap);
	
	/** 变现-广告位数据 */
	public List<Map<String,Object>> selectAdposData(Map<String, Object> paramMap);
	public Map<String,Object> selectAdposDataSum(Map<String, Object> paramMap);
	
	/** 聚合综合查询汇总 */
	public List<Map<String,Object>> selectDnGroupCom(Map<String, Object> paramMap);
	public Map<String,Object> selectDnGroupComSum(Map<String, Object> paramMap);
	
	/** 汇总数据-用户群 */
	public List<Map<String,Object>> selectAdtypeTotalGroup(Map<String, Object> paramMap);
	public Map<String,Object> selectAdtypeTotalGroupSum(Map<String, Object> paramMap);
	
}
