package com.wbgame.mapper.master;

import com.wbgame.pojo.CdnResource;import java.util.List;import java.util.Map;

public interface CdnResourceMapper {
    int deleteByPrimaryKey(String id);

    int insert(CdnResource record);

    int insertSelective(CdnResource record);

    CdnResource selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CdnResource record);

    int updateByPrimaryKey(CdnResource record);

    List<CdnResource> selectByAll(Map param);
}