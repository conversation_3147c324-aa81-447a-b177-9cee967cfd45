package com.wbgame.mapper.redpack;

import com.wbgame.pojo.redpack.TtCodeConfig;
import com.wbgame.pojo.redpack.TtCodeConfigDTO;
import com.wbgame.pojo.redpack.TtCodeConfigVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TtCodeConfigMapper {


    int deleteByAppid(List<Integer> appidList);

    int insertTtCodeConfig(TtCodeConfig record);


    int updateByTtCodeConfig(TtCodeConfig record);

    List<TtCodeConfigVO> selectByCondition(TtCodeConfigDTO ttCodeConfigDTO);

    @Select("select count(*) from tt_code_config where appid = #{appid} ")
    int countAppid(Integer appid);

}