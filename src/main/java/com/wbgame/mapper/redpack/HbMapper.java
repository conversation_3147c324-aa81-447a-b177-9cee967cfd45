package com.wbgame.mapper.redpack;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.HbUserWithdrawExamine;
import com.wbgame.pojo.mobile.ApiPayConfigVo;
import com.wbgame.pojo.mobile.DouyinConfigVo;
import com.wbgame.pojo.mobile.IosConfigVo;
import com.wbgame.pojo.operate.*;
import com.wbgame.pojo.param.ExchangeCodeParam;
import com.wbgame.pojo.redpack.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @description: 红包
 * @author: huangmb
 * @date: 2021/08/11
 **/
public interface HbMapper {

    HbWithdrawQuery countHbWithdrawQuery(Map<String, Object> param) ;

    List<HbWithdrawQuery> selectHbWithdrawQuery(Map<String, Object> param) ;

    List<Map<String, Object>> selectHbExamines(HbExamineParam param);

    HbUserWithdrawExamine selectByPrimaryKey(int id);

    List<HbRedpackLog> selectUserRedpackLog(Map<String, Object> param);

    List<HbEcpmLog> selectUserEcpmLog(Map<String, Object> param);

    List<String> selectMonthTables(String s);

    //红包提现参数配置
    int deleteRedpackWxconfig(HbWithdrawParamHandle record);
    int insertRedpackWxconfig(HbWithdrawParamHandle record);
    int updateRedpackWxconfig(HbWithdrawParamHandle record);

    List<HbRedpackWithdrawDetailVo> selectNewRedpackdraw(PidReportVo param);

    List<Map<String, Object>> selectRedpackTools();

    List<Map<String, Object>> selectMonthChannelRedpackShares(Map param);

    List<ApiPayConfigVo> selectVivoPayConfig(ApiPayConfigVo param);

    void insertVivoPayConfig(ApiPayConfigVo param);

    void updateVivoPayConfig(ApiPayConfigVo param);

    void deleteVivoPayConfig(ApiPayConfigVo param);

    List<ApiPayConfigVo> selectHuaweiPayConfig(ApiPayConfigVo param);

    void insertHuaweiPayConfig(ApiPayConfigVo param);

    void updateHuaweiPayConfig(ApiPayConfigVo param);

    void deleteHuaweiPayConfig(ApiPayConfigVo param);

    List<ApiPayConfigVo> selectMeizuPayConfig(ApiPayConfigVo param);

    void insertMeizuPayConfig(ApiPayConfigVo param);

    void updateMeizuPayConfig(ApiPayConfigVo param);

    void deleteMeizuPayConfig(ApiPayConfigVo param);

    List<ApiPayConfigVo> selectFtnnPayConfig(ApiPayConfigVo param);

    void insertFtnnPayConfig(ApiPayConfigVo param);

    void updateFtnnPayConfig(ApiPayConfigVo param);

    void deleteFtnnPayConfig(ApiPayConfigVo param);

    List<DouyinConfigVo> selectDouyinConfig(DouyinConfigVo param);

    void insertDouyinPayConfig(DouyinConfigVo param);

    void updateDouyinPayConfig(DouyinConfigVo param);

    void deleteDouyinPayConfig(DouyinConfigVo param);

    List<IosConfigVo> selectIosConfig(IosConfigVo param);

    void insertIosPayConfig(IosConfigVo param);

    void updateIosPayConfig(IosConfigVo param);

    void deleteIosPayConfig(IosConfigVo param);

    Map<String, Object> selectMdsConfig(String appid);

    List<MdsConfigVo> selectMdsConfigs(CommonReportVo param);

    void addMdsConfig(MdsConfigVo param);

    void updateMdsConfig(MdsConfigVo param);

    void deleteMdsConfig(MdsConfigVo param);

    HbRedpackWithdrawDetailVo countNewRedpackdraw(PidReportVo param);

    List<ExchangeCodeVo> selectExchangeCodes(ExchangeCodeParam param);

    void addExchangeCodes(ExchangeCodeVo param);

    void addbatchExchangeCodes(List<ExchangeCodeVo> list);

    List<JSONObject> selectWxAmountWithdraws();

    JSONObject selectMchidAmount(JSONObject j);

    List<HbWithdrawParamVo> selectRedpackWxconfig(HbWithdrawParamVo hbWithdrawParamVo);

    List<VivoMiniConfigVo> selectVivoMiniConfigs(VivoMiniConfigVo param);

    void addVivoMiniConfig(List<VivoMiniConfigVo> param);

    void updateVivoMiniConfig(VivoMiniConfigVo param);

    void deleteVivoMiniConfig(String appid);

    List<PayWarnConfigVo> selectPayWarnConfig(CommonReportVo param);

    void addPayWarnConfig(PayWarnConfigVo param);

    void deletePayWarnConfig(PayWarnConfigVo param);

    com.wbgame.pojo.WbPayConfig selectSubPayConfig(@Param("appid") String appid, @Param("param2")String param2);

    com.wbgame.pojo.WbPayConfig selectOnePayConfig(@Param("appid")String appid, @Param("param2")String param2);

    String selectcpRefundUrl(@Param("appid")String appid);

    List<ExchangeCodeVo> selectCloseExchange(List<String> codes);
}
