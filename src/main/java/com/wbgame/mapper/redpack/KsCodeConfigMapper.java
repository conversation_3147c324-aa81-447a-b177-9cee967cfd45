package com.wbgame.mapper.redpack;

import com.wbgame.pojo.redpack.*;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description:
 * @Author: xiaoxh
 * @Date: 2024/1/19
 */
public interface KsCodeConfigMapper {
    int deleteByAppid(List<Integer> appidList);

    int insertKsCodeConfig(KsCodeConfig record);


    int updateByKsCodeConfig(KsCodeConfig record);

    List<KsCodeConfigVO> selectByCondition(KsCodeConfigDTO ksCodeConfigDTO);

    @Select("select count(*) from ks_micgame_config where appid = #{appid} ")
    int countAppid(String appid);
}
