package com.wbgame.mapper.haiwaiad;

import com.wbgame.pojo.CustomerServiceDTO;
import com.wbgame.pojo.game.userinfo.FeedbackVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 玩家反馈信息数据mapper
 * @Date 2024/8/22 11:14
 */
public interface HwUserFeedbackInfoMapper {

    List<FeedbackVo> selectFeedbackList(CustomerServiceDTO dto);

    @Delete("delete from user_feedback_info where id in (${id})")
    int delFeedback(@Param("id") String id);


    /**
     * 更新玩家反馈信息状态
     * @param id 主键id集合，格式： 1,2,3
     * @param modify_user 更新人
     * @return 影响行数
     */
    int updateFeedback(CustomerServiceDTO dto);

}
