package com.wbgame.mapper.haiwaiad;

import com.wbgame.pojo.operate.GoogleKeyConfig;
import com.wbgame.pojo.operate.GoogleKeyConfigDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: google 支付密钥 配置
 * @author: xiaoxh
 * @date: 2024/08/29
 **/
public interface GoogleKeyConfigMapper {

    int deleteGoogleKey(@Param("appidList") List<Integer> appidList);

    int existGoogleKey(@Param("appid") Integer appid);

    int insertGoogleKey(GoogleKeyConfigDTO dto);

    int updateGoogleKey(GoogleKeyConfigDTO dto);

    List<GoogleKeyConfig> selectByCondition(GoogleKeyConfigDTO dto);
}
