package com.wbgame.mapper.adb;


import com.wbgame.pojo.AdsOverseasToolBasicDailyDTO;
import com.wbgame.pojo.AdsOverseasToolBasicDailyVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface AdsOverseasToolBasicDailyMapper {

    @Select("select appid, sum(reg_user) total from ads_overseas_tool_basic_daily group by appid")
    List<AdsOverseasToolBasicDailyVO> selectGroupByAppid();


    List<AdsOverseasToolBasicDailyVO> selectByCondition(AdsOverseasToolBasicDailyDTO adsOverseasToolBasicDailyDTO);


    /**
     * 获取地区
     */
    @Select("SELECT country_code countryCode, country_name countryName FROM dim_country")
    List<Map<String, String>> selectCountry();

    /**
     * 获取机型
     */
    @Select("SELECT brand_model FROM dim_app_brand_model_daily group by brand_model order by brand_model desc")
    List<String> selectModel();

    /**
     * 品牌
     */
    @Select("select distinct brand from dim_app_brand_model_daily")
    List<String> selectBrand();

    /**
     * 版本
     */
    @Select("select distinct ver from ads_dim_app_info_daily")
    List<String> selectVer();
}