package com.wbgame.mapper.adb;


import com.wbgame.pojo.game.report.ActiveActivityReportVo;
import com.wbgame.pojo.game.report.ActiveCurrencyReportVo;
import com.wbgame.pojo.game.report.MicGameModelRetentionReportVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname RetentionMapper
 * @Description TODO
 * @Date 2023/05/22 15:20
 */
public interface RetentionMapper {

    List<MicGameModelRetentionReportVo> getMicGameModelRetentionList(MicGameModelRetentionReportVo vo);

    MicGameModelRetentionReportVo getMicGameModelRetentionSum(MicGameModelRetentionReportVo vo);

    List<ActiveActivityReportVo> getActiveActivityList(ActiveActivityReportVo vo);

    ActiveActivityReportVo getActiveActivitySum(ActiveActivityReportVo vo);

    List<ActiveCurrencyReportVo> getActiveCurrencyList(ActiveCurrencyReportVo vo);

}
