package com.wbgame.mapper.tfxt;

import com.wbgame.pojo.jettison.param.ClickUrlConfigParam;
import com.wbgame.pojo.jettison.vo.ClickUrlConfigVo;
import com.wbgame.pojo.jettison.vo.GameNameVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/17 17:23
 */
public interface ClickUrlConfigMapper {
    List<ClickUrlConfigVo> selectClickUrlConfig(ClickUrlConfigParam param);

    void add(ClickUrlConfigVo param);

    void update(ClickUrlConfigVo param);

    void delete(@Param("ids") List<Integer> ids);

    void batchAdd(List<ClickUrlConfigVo> list);

    ClickUrlConfigVo getClickUrlConfig(ClickUrlConfigParam param);

    List<GameNameVo> getGameNames(GameNameVo param);
}
