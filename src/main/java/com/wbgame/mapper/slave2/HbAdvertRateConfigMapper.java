package com.wbgame.mapper.slave2;
import org.apache.ibatis.annotations.Param;
import java.util.Date;

import com.wbgame.pojo.redpack.HbAdvertRateConfig;

import java.util.List;

public interface HbAdvertRateConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(HbAdvertRateConfig record);

    int insertSelective(HbAdvertRateConfig record);

    HbAdvertRateConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(HbAdvertRateConfig record);

    int updateByPrimaryKey(HbAdvertRateConfig record);

    List<HbAdvertRateConfig> selectByAll(HbAdvertRateConfig hbAdvertRateConfig);


}