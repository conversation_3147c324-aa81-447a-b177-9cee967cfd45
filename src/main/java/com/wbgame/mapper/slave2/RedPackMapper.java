package com.wbgame.mapper.slave2;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.wbgame.pojo.AdTotalHourVo;
import com.wbgame.pojo.custom.AdMsgDetailVo;
import com.wbgame.pojo.custom.AdMsgLoadVo;
import com.wbgame.pojo.custom.AdMsgStatsTotalVo;
import com.wbgame.pojo.custom.AdMsgTotalVo;
import com.wbgame.pojo.custom.WarmHomeMailVo;
import com.wbgame.pojo.push.ApkProductStatsVo;
import com.wbgame.pojo.realAuth.RedPackInfoDto;

public interface RedPackMapper {
	// 抽取数据
	List<RedPackInfoDto> selectRedPackInfo(Map<String, Object> paramMap);
	List<RedPackInfoDto> selectTwoRate(Map<String, Object> paramMap);
	List<RedPackInfoDto> selectDau(Map<String, Object> paramMap);
	//查询达成用户
	List<RedPackInfoDto> selectOverUser(Map<String, Object> paramMap);
	//查询开启用户
	List<RedPackInfoDto> selectOpenUser(Map<String, Object> paramMap);
	//微信登录成功人数
	List<RedPackInfoDto> selectWxSuccess(Map<String, Object> paramMap);
	//微信登录失败人数
	List<RedPackInfoDto> selectWxFail(Map<String, Object> paramMap);
	//提现请求人数
	List<RedPackInfoDto> selectWithDrawRequest(Map<String, Object> paramMap);
	//假提现开启人数
	List<RedPackInfoDto> selectCashUser(Map<String, Object> paramMap);
	//真提现开启人数
	List<RedPackInfoDto> selectWithdrawUser(Map<String, Object> paramMap);
	//真提现获取开启人数
	List<RedPackInfoDto> selectWithdrawRet(Map<String, Object> paramMap);

    List<Map<String, Object>> selectHomeDiamondLog(Map param);

	void batchinsertUserRecord(List<Map<String, Object>> list);

	//新版红包提现查询
    List<Map<String, Object>> selectNewRedpackdraw(Map<String, Object> param);

    List<Map<String, Object>> selectUserWithdrawTotal(Map<String, Object> param);

    List<Map<String, Object>> selectRedPackWithdrawLog(String ds);

	JSONObject selectOldMchidAmount(JSONObject j);
}