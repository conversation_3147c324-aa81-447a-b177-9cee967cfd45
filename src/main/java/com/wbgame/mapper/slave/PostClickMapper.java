package com.wbgame.mapper.slave;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Select;

import com.wbgame.pojo.PostClickTotalVo;

public interface PostClickMapper {
	
	@Select("select * from post_user_list where iscommit = 0 limit 500")
	List<PostClickTotalVo> selectPostUserListForCommit();
	
	// 角色授权
	int insertPostClickTotal(List<PostClickTotalVo> list); 
	int insertPostClickToUserList(Map<String, Object> map); 
	int updatePostUserListForCommit(List<PostClickTotalVo> list); 
	
	
}