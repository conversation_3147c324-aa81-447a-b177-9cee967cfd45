package com.wbgame.mapper.adv2;

import com.wbgame.pojo.AppInfo;
import com.wbgame.pojo.ChannelInfo;
import com.wbgame.pojo.NpPostVo;
import com.wbgame.pojo.adv2.OppoReportVO;
import com.wbgame.pojo.product.ProductAssessVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

public interface OppoH5Mapper {

	// 通用执行语句和查询语句
	@Update(" ${sql} ")
	public int execSql(@Param("sql")String sql); // 直接执行DML sql语句
	@Select(" ${sql} ")
	public List<String> queryListString(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, Object>> queryListMap(@Param("sql")String sql);
	@MapKey("mapkey")
	@Select(" ${sql} ")
	public Map<String, Map<String, Object>> queryListMapOfKey(@Param("sql")String sql);
	@Update(" ${sql} ")
	int execSqlHandle(@Param("sql")String sql, @Param("obj")Object obj);
	@Select(" ${sql} ")
	public List<NpPostVo> queryNpPost(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, String>> queryListMapOne(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, Object>> queryListMapTwo(@Param("sql")String sql, @Param("obj")Object obj);
	public int batchExecSql(Map<String, Object> paramMap);
	public int batchExecSqlTwo(Map<String, Object> paramMap);
	
	
	/** oppo报表数据 */
	public List<Map<String,Object>> selectOppoReportList(Map<String, String> paramMap);
	public List<Map<String,Object>> selectOppoReportListTwo(Map<String, String> paramMap);
	public Map<String,Object> selectOppoReportListThree(Map<String, String> paramMap);
	public Map<String,Object> selectOppoReportListSum(Map<String, String> paramMap);
	
	@Select("select xx.*,TRUNCATE(xx.revenue-xx.rebateSpend,2) income from oppo_report_index xx order by tdate desc ")
	public List<Map<String,Object>> selectOppoReportIndex(Map<String, String> paramMap);
	
	
	/** oppo报表系统2数据 */
	public Map<String,Object> selectOppoReportTab1(Map<String, String> paramMap);
	
	public List<Map<String,Object>> selectOppoReportTab2(Map<String, String> paramMap);
	public Map<String,Object> selectOppoReportTab2Sum(Map<String, String> paramMap);
	
	public List<Map<String,Object>> selectOppoReportTab3(Map<String, String> paramMap);
	public List<Map<String,Object>> selectOppoReportTab3Group(Map<String, String> paramMap);
	public Map<String,Object> selectOppoReportTab3Sum(Map<String, String> paramMap);
	
	public List<Map<String,Object>> selectOppoReportTab4(Map<String, String> paramMap);
	
	public List<Map<String,Object>> selectOppoReportTab5(Map<String, String> paramMap);
	
}