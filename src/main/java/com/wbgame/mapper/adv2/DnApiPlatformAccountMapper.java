package com.wbgame.mapper.adv2;

import com.wbgame.pojo.DnApiPlatformAccount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description oppo,vivo广告位创建账号配置mapper
 * @Date 2025/5/7 11:42
 */
public interface DnApiPlatformAccountMapper {


    List<DnApiPlatformAccount> queryList(DnApiPlatformAccount dto);


    int insert(DnApiPlatformAccount dto);

    int deleteById(@Param("id") String id);

    int updateById(DnApiPlatformAccount dto);
}
