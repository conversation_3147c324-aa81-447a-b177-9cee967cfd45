package com.wbgame.mapper.clean.master;


import com.wbgame.pojo.clean.IdiomRedpackChangeDTO;
import com.wbgame.pojo.clean.IdiomRedpackChangeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IdiomRedpackChangeMapper {


    List<IdiomRedpackChangeVO> selectIdiomRedpackChange(@Param("environment") String environment,
                                                        @Param("dto") IdiomRedpackChangeDTO dto);


    IdiomRedpackChangeVO countIdiomRedpackChange(@Param("environment") String environment,
                                                       @Param("dto") IdiomRedpackChangeDTO dto);

}