package com.wbgame.mapper.clean.master;


import com.wbgame.pojo.clean.toonstory.OperatingArea;
import com.wbgame.pojo.clean.toonstory.OperatingAreaDTO;
import com.wbgame.pojo.clean.toonstory.OperatingAreaVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface OperatingAreaMapper {

    int insertOperatingArea(OperatingArea record);

    List<OperatingAreaVO> selectOperatingAreaMapper(OperatingAreaDTO dto);

    int updateOperatingArea(OperatingArea record);

    /**
     * 获取运营地区名是否存在
     */
    @Select("select id from gj_b.toon_area where area_name = #{areaName} limit 1")
    Long selectOperationAreaNameLinkAppId(@Param("areaName") String areaName, @Param("appId") String appId);

    /**
     * 获取运营地区下拉列表
     */
    @Select("select id, area_name areaName from gj_b.toon_area")
    List<OperatingAreaVO> getAreaName();

    /**
     * 根据ID获取数据
     */
    @Select("select 1 from gj_b.toon_area where id = #{id}")
    Integer getAreaById(Long id);
}