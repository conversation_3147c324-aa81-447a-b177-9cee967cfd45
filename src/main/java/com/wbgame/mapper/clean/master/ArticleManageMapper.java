package com.wbgame.mapper.clean.master;


import com.wbgame.pojo.clean.ArticleManage;
import com.wbgame.pojo.clean.ArticleManageDTO;
import com.wbgame.pojo.clean.ArticleManageVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ArticleManageMapper {

    int updateByIsDeleted(ArticleManageDTO dto);

    int insertArticleManage(ArticleManage record);

    List<ArticleManageVO> selectArticleManage(ArticleManageDTO articleManage);

    int updateArticleManage(ArticleManage record);

    /**
     * 获取需要删除的数据 1209600000ms == 14天
     */
    @Select("select id from gj_b.article_manage where is_deleted = 1 and modify_time <= #{currentTimeMill} - 1209600000")
    List<Long> getDeleteData(Long currentTimeMill);

    int deleteArticleManageById(List<Long> idList);

}