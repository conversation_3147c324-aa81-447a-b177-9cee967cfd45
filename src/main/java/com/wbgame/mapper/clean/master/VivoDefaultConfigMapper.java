package com.wbgame.mapper.clean.master;


import com.wbgame.pojo.clean.VivoDefaultConfig;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface VivoDefaultConfigMapper {

    int deleteVivoConfig(List<String> prjidList);

    int insertVivoConfig(VivoDefaultConfig record);

    List<VivoDefaultConfig> selectVivoConfig(VivoDefaultConfig example);

    @Select("select prjid from vivo_default_config where prjid = #{prjid} limit 1 ")
    String selectVivoConfigByprjid(String prjid);

    int updateVivoConfig(VivoDefaultConfig record);

    int batchUpdateVivoConfig(VivoDefaultConfig record);


}