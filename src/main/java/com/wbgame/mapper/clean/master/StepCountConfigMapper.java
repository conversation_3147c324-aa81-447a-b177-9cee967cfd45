package com.wbgame.mapper.clean.master;

import com.wbgame.pojo.clean.stepcount.StepCountConfig;
import com.wbgame.pojo.clean.stepcount.StepCountConfigVO;

import java.util.List;

public interface StepCountConfigMapper {

    int deleteStepCountConfig(List<Long> idList);

    int insertStepCountConfig(StepCountConfig record);

    List<StepCountConfigVO> selectStepCountConfig(StepCountConfig dto);

    int updateStepCountConfig(StepCountConfig record);

    Integer selectStepCountExits(StepCountConfig stepCountConfig);

}