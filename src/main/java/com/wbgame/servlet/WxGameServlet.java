package com.wbgame.servlet;


import com.wbgame.service.impl.WxGameServiceImpl;
import com.wbgame.utils.GzsjSignUtil;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 2333微信公众号入口
 * <AUTHOR>
 *
 */
@WebServlet(name="WxGameServlet",urlPatterns="/WxGameServlet")
public class WxGameServlet extends HttpServlet{

    private static final long serialVersionUID = 771449745996544216L;
    @Autowired
    private WxGameServiceImpl wxGameService;

    /**
     * 微信认证入口
     */
    public void doGet(HttpServletRequest request,HttpServletResponse response)
            throws ServletException,IOException{
        // 微信加密签名
        String signature = request.getParameter("signature");
        // 时间戳
        String timestamp = request.getParameter("timestamp");
        // 随机数
        String nonce = request.getParameter("nonce");
        // 随机字符串
        String echostr = request.getParameter("echostr");
        PrintWriter out = response.getWriter();
        // 通过检验signature对请求进行校验，若校验成功则原样返回echostr，表示接入成功，否则接入失败
        if (GzsjSignUtil.checkSignature(signature, timestamp, nonce)) {
            out.print(echostr);
        }
        out.close();
    }

    /**
     * 微信数据处理
     */
    public void doPost(HttpServletRequest request,HttpServletResponse response)
            throws ServletException,IOException{
        request.setCharacterEncoding("utf-8");
        response.setCharacterEncoding("utf-8");
        // 调用核心业务类接收消息、处理消息
        String respMessage = wxGameService.processRequest(request);

        // 响应消息
        PrintWriter out = response.getWriter();
        out.print(respMessage);
        out.close();
    }

}
