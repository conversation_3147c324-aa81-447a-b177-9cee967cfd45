package com.wbgame.servlet.qihoo;

import static org.hamcrest.CoreMatchers.is;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.utils.BlankUtils;

@WebServlet(name="qihooApi",urlPatterns="/qihooApi")
public class QihooApi extends HttpServlet {

	private static Logger log = LoggerFactory.getLogger(QihooApi.class);
	
	private static final long serialVersionUID = 1L;

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	
	/**
	 * Constructor of the object.
	 */
	public QihooApi() {
		super();
	}

	/**
	 * Destruction of the servlet. <br>
	 */
	public void destroy() {
		super.destroy(); // Just puts "destroy" string in log
		// Put your code here
	}

	/**
	 * The doGet method of the servlet. <br>
	 *
	 * This method is called when a form has its tag value method equals to get.
	 * 
	 * @param request the request send by the client to the server
	 * @param response the response send by the server to the client
	 * @throws ServletException if an error occurred
	 * @throws IOException if an error occurred
	 */
	public void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {

		doPost(request, response);
	}

	/**
	 * The doPost method of the servlet. <br>
	 *
	 * This method is called when a form has its tag value method equals to post.
	 * 
	 * @param request the request send by the client to the server
	 * @param response the response send by the server to the client
	 * @throws ServletException if an error occurred
	 * @throws IOException if an error occurred
	 */
	public void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		response.setHeader("Access-Control-Allow-Origin", "*");
		
		//wap页参数
		String callback =  BlankUtils.checkNull(request, "jsoncallback");//跨域访问回调方法名
		String vgtype = BlankUtils.checkNull(request, "vgtype");//自定义请求类型getTab:请求当前可用频道
		String lsn = BlankUtils.checkNull(request, "lsn");//设备id
		String appVer = BlankUtils.checkNull(request, "appVer");//客户端应用版本号
		String ctype = request.getParameter("ctype");
		String isfirst = request.getParameter("isfirst");
		
		//平台参数
		String getTokenUrl = QihooApiParam.GETTOKENURL;
		String secret = QihooApiParam.SECRET;
		String ap = QihooApiParam.AP;
		String sign = QihooApiParam.SIGN;
		String pn = QihooApiParam.PN;
		
		
		Long ts = System.currentTimeMillis()/1000;
		String rn = StringUtils.getRandStringPlus(16);
		String chc = "";
		
		MD5 m = new MD5();
		String u = "6b09f44a58db79719102ceb4a5528a87";//临时测试 uid
		
		if(lsn!=null){
			u = m.getMD5ofStr(lsn).toLowerCase();
		}
		
		String version = "1.0.1";//临时测试
		
		if(appVer!=null){
			version = appVer;
		}
		
		String accsToken = null;
		String resDt = null;
		
		String c = "youlike";
		if(!BlankUtils.checkBlank(ctype))
			c = ctype;
			
		String n = "10";
		String f = "json";
		String device = "0";
		String sv = "1";
		String usid = request.getSession().getId();
		request.getSession().setMaxInactiveInterval(360);
		
		String str=ts+rn+ap+sign+pn+secret;
		MessageDigest messageDigest;
		try {
			messageDigest = MessageDigest.getInstance("SHA1");
			messageDigest.update(str.getBytes());
			chc = getFormattedText(messageDigest.digest());
			
			// 重新获取access_token
			accsToken = (String)redisTemplate.opsForValue().get("qihoo_new_access_token");
			if(accsToken == null){
				String getUrl = "/v2/access_token?ts="+ts+"&rn="+rn+"&ap="+ap+"&sign="+sign+"&pn="+pn+"&chc="+chc+"&u="+u+"&version="+version;
				/*HttpMethod method = new GetMethod(getUrl);   
				String resDt = exeClienUrl(method, getTokenUrl);*/
				resDt = httpGet(getTokenUrl+getUrl);
				
				if(!StringUtils.isEmpty(resDt)){ 
					JSONObject resMap = JSONObject.parseObject(resDt);
					if(resMap!=null && resMap.getIntValue("errno")==0){
						JSONObject dataObj = resMap.getJSONObject("data");
						if(dataObj!=null){
							accsToken = dataObj.getString("access_token");
							redisTemplate.opsForValue().set("qihoo_new_access_token", accsToken, 12, TimeUnit.HOURS);
						}
					}
				}
			}
			
			String result = "no data";
			if("getTab".equals(vgtype)){
				String getTabUrl = "/v2/tabs?u="+u+"&sign="+sign+"&access_token="+accsToken+"&version="+version+"&device="+device;
				
				log.debug("QihooApi getTab url=>"+getTokenUrl+getTabUrl);
				/*HttpMethod tabMethod = new GetMethod(getTabUrl);   
				listRes = exeClienUrl(tabMethod, getTokenUrl);*/
				String listRes = httpGet(getTokenUrl+getTabUrl);
				result = callback + "("+listRes+")";
				
				log.debug("QihooApi getTab res=>"+result);
			}else{
				String getListUrl = "/v2/list?u="+u+"&n="+n+"&sign="+sign+"&access_token="+accsToken+"&c="
				+c+"&f="+f+"&version="+version+"&device="+device+"&sv="+sv+"&usid="+usid+"&callback="+callback;
				
				log.debug("QihooApi get list url=>"+getTokenUrl+getListUrl);
				/*HttpMethod listMethod = new GetMethod(getListUrl);   
				listRes = exeClienUrl(listMethod, getTokenUrl);*/
				
				String listRes = (String)redisTemplate.opsForValue().get("QihooApiListCache_"+c);
				if(BlankUtils.checkBlank(listRes) || BlankUtils.checkBlank(isfirst)){
					// 3分钟失效
					listRes = httpGet(getTokenUrl+getListUrl);
					if(!StringUtils.isEmpty(listRes) 
							&& "0".equals(JSONObject.parseObject(listRes).getString("errno"))){
						
						redisTemplate.opsForValue().set("QihooApiListCache_"+c,listRes,3,TimeUnit.MINUTES);
					}
				}
				
				result = callback + "("+listRes+")";
				log.debug("QihooApi get list res=>"+listRes);
			}
			
			
			OutputStream os = null;
			try {
				byte[] responseBytes = result.getBytes("UTF-8");
				response.setContentLength(responseBytes.length);
				os = response.getOutputStream();
				os.write(responseBytes);
				os.flush(); 
				os.close();  
			} catch (Exception e) {
				e.printStackTrace();
			}finally{
			
				if(os!=null)
				{
					os.flush(); 
					os.close();  
				}
			}
			
		} catch (NoSuchAlgorithmException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
	}

	public String httpGet(String url) {
    	String responseContent = null;
    	RequestConfig defaultRequestConfig = RequestConfig.custom()
    		    .setSocketTimeout(10000)
    		    .setConnectTimeout(10000)
    		    .setConnectionRequestTimeout(10000)
    		    .setStaleConnectionCheckEnabled(true)
    		    .build();
		CloseableHttpClient httpclient = HttpClients.custom()
		    .setDefaultRequestConfig(defaultRequestConfig).build();

        try {
            HttpGet httpGet = new HttpGet("http://"+url);
            httpGet.addHeader("Connection", "close");
            CloseableHttpResponse response = httpclient.execute(httpGet);  
            try {  
                HttpEntity entity = response.getEntity();
                responseContent = EntityUtils.toString(entity, "UTF-8");
            } finally {  
                response.close();  
            }  
        } catch (Exception e) {
            e.printStackTrace();
        } finally {  
            try {  
                httpclient.close();  
            } catch (IOException e) {  
                e.printStackTrace();  
            }  
        }  
        return responseContent;  
    }  
	
	/*private String exeClienUrl(HttpMethod method,String url){
		StringBuffer sb = new StringBuffer();
		long t1 = System.currentTimeMillis();
		//post.setRequestHeader("Content-Type","text/html");
		HttpClient client = new HttpClient();
		client.getHttpConnectionManager().getParams().setConnectionTimeout(10000); 
		client.getHttpConnectionManager().getParams().setSoTimeout(10000);
		client.getHostConfiguration().setHost(url, 80,
				"http");
		method.setRequestHeader("Connection", "close");
		InputStream inputStream = null;
		BufferedReader bre = null;
		try {
			client.executeMethod(method);		
			
			inputStream = method.getResponseBodyAsStream();  
			bre = new BufferedReader(new InputStreamReader(inputStream,"utf-8"));  
			String str= "";  
			while((str = bre.readLine()) != null){  
				sb .append(str);  
			} 
		} catch (HttpException e) {
			// TODO Auto-generated catch block
			//e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			//e.printStackTrace();
		}finally{
			if(inputStream!=null){
				try {
					inputStream.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					//e.printStackTrace();
				}
			}
			if(bre!=null){
				try {
					bre.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					//e.printStackTrace();
				}
			}
			method.releaseConnection();
			client.getHttpConnectionManager().closeIdleConnections(0);
			long t2 = System.currentTimeMillis();
			
			Calendar c = Calendar.getInstance();  
		    c.setTimeInMillis(t2 - t1);  
		  
		    log.debug("耗时: " + c.get(Calendar.MINUTE) + "分 "  
		                + c.get(Calendar.SECOND) + "秒 " + c.get(Calendar.MILLISECOND)  
		                + " 毫秒"); 
		}
		//log.debug("api type=>"+url);
		//log.debug("api get res = >"+sb.toString());
		
		return sb.toString();
	}*/
	
	
	/**
	 * chc计算公式
	 * @param bytes
	 * @return
	 */
	private String getFormattedText(byte[] bytes) {
		  char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
		  int len = bytes.length;
		  StringBuilder buf = new StringBuilder(len * 2);
		  // 把密文转换成十六进制的字符串形式
		  for (int j = 0; j < len; j++) {
		    buf.append(HEX_DIGITS[(bytes[j] >> 4) & 0x0f]);
		    buf.append(HEX_DIGITS[bytes[j] & 0x0f]);
		  }
		  return buf.toString();
		}
	/**
	 * Initialization of the servlet. <br>
	 *
	 * @throws ServletException if an error occurs
	 */
	public void init() throws ServletException {
		// Put your code here
	}

}
