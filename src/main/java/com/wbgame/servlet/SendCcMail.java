package com.wbgame.servlet;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.MailTool;

@WebServlet(name="sendCcMail",urlPatterns="/sendCcMail")
public class SendCcMail extends HttpServlet {

	private static final long serialVersionUID = 1L;
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		
		resp.setCharacterEncoding("UTF-8");
		resp.setHeader("Access-Control-Allow-Origin", "*");
		PrintWriter out = resp.getWriter();
		JSONObject result = new JSONObject();
		
		boolean flag = true;
		try {
			if(flag) {
				// 已停用
				result.put("ret", 0);
				result.put("msg", "fail");
				out.print(result.toJSONString());
				out.close();
				return;
			}
			
			String replace = req.getParameter("value").replace(" ", "+");
			String value = new String(Base64.decodeBase64(replace),"UTF-8");
			JSONArray array = JSONArray.parseArray(value);
			if(array != null && array.size() > 0){
				ListOperations<String, Object> opsForList = redisTemplate.opsForList();
				
				for (int i = 0; i < array.size(); i++) {
					JSONObject obj = array.getJSONObject(i);
					if(BlankUtils.checkBlank(obj.getString("to"))
						|| BlankUtils.checkBlank(obj.getString("cc"))
						|| BlankUtils.checkBlank(obj.getString("title"))){
						
						continue;
					}
					opsForList.leftPush("send_ccmail_info_list", obj);
				}
			}
			
			result.put("ret", 1);
			result.put("msg", "success");
		} catch (IOException e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "fail");
		}
		
		out.print(result.toJSONString());
		out.close();
	}
	
	@Override
	public void init() throws ServletException {
		super.init();
	}
	@Override
	public void destroy() {
		super.destroy();
	}
}
