package com.wbgame.servlet.clean;


import com.wbgame.service.AdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;


/**
 * 海外界面刷缓存
 * 
 */ 
@WebServlet(name="/haiwai/clean/refreshCache",urlPatterns="/haiwai/clean/refreshCache")
public class HaiWaiReCacheServlert extends HttpServlet{

	@Autowired
	private AdService adService;

	private static final long serialVersionUID = 1L;
	private static Log logger = LogFactory.getLog(HaiWaiReCacheServlert.class);
  
	protected void doGet(HttpServletRequest request,  
			HttpServletResponse response) throws ServletException, IOException {

		response.setCharacterEncoding("UTF-8");
		response.setHeader("Access-Control-Allow-Origin", "*");
		PrintWriter out = response.getWriter();
		try {
			String mapid= BlankUtils.checkNull(request, "mapid");//id
			String baseParam = "?mapid="+mapid;
			/** 拉取刷新列表，遍历刷新服务地址 */
			String sql = "select url from dn_recache_config where mark = 'HaiwaiClean' ";
			List<String> urlList = adService.queryListString(sql);
			for (String url : urlList) {
				String resp = HttpClientUtils.getInstance().httpGet(url + baseParam);
				if(resp == null || !"ok".equals(resp.trim())){

					out.print("fail");
					out.close();
					return;
				}
			}
			out.print("ok");
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
			logger.warn("HaiwaiClean刷新缓存失败!!!");
			out.print("fail");
			out.close();
		}
	}
	


	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)throws ServletException, IOException {
		doGet(req,resp);
	}

	
}
