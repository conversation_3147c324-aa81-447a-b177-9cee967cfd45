package com.wbgame.servlet.clean;


import com.wbgame.service.AdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;


/**
 * 界面刷缓存
 * 
 */ 
@WebServlet(name="/clean/refreshCache",urlPatterns="/clean/refreshCache")
public class ReCacheServlert extends HttpServlet{
	private static final long serialVersionUID = 1L;
	private static Log logger = LogFactory.getLog(ReCacheServlert.class);

	@Autowired
	private AdService adService;
  
	protected void doGet(HttpServletRequest request,  
			HttpServletResponse response) throws ServletException, IOException {
		response.setHeader("Access-Control-Allow-Origin", "*");
		OutputStream os = null; 
		String mapid= BlankUtils.checkNull(request, "mapid");//id

		StringBuffer sbMsg = new StringBuffer();
		StringBuffer content = new StringBuffer();

		String sql = "select url from dn_recache_config where mark = 'Clean' ";
		List<String> urlList = adService.queryListString(sql);
		String result = "fail";
		try {
			for (String url:urlList){
				String resp = HttpClientUtils.getInstance().httpGet(url+"/refreshCache?mapid=" + mapid);
				if (resp == null || !"ok".equals(resp)){
					content.append(url+";");
				}
			}
			if (content.length()>0){
				sbMsg.append("refresh failed server:");
				sbMsg.append(content);
			}

			if (sbMsg.length()==0){
				result = "ok";
			}else {
				result = sbMsg.toString();
			}
		}catch (Exception e){
			logger.error("clean_refreshCache error1:",e);
		}

		try {
			byte[] responseBytes = result.getBytes("UTF-8");
			response.setContentLength(responseBytes.length);
			os = response.getOutputStream();
			os.write(responseBytes);
			os.flush();
			os.close();
		} catch (Exception e) {
			logger.info("clean_refreshCache error2:",e);
		} finally {
			try {
				os.close();
			} catch (Exception e) {
			}
		}
	}
	


	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)throws ServletException, IOException {
		doGet(req,resp);
	}

	
}
