package com.wbgame.servlet;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.service.WorldService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;

@WebServlet(name="wxSendMSg",urlPatterns="/wxSendMSg")
public class WxSendMSg extends HttpServlet {

	private static final long serialVersionUID = 1L;
	// 微信token验证
	private static final String token = "wbkj_wx_token";
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private WorldService worldService;
	
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		// 验证消息的确来自微信服务器，然后将消息发送给服务器传递的openid
		String signature = req.getParameter("signature");
		String timestamp = req.getParameter("timestamp");
		String nonce = req.getParameter("nonce");
		String echostr = req.getParameter("echostr");
		List<String> list = new ArrayList<String>();
		list.add(token);
		list.add(timestamp);
		list.add(nonce);
		Collections.sort(list);
		
		String shaHex = DigestUtils.shaHex(list.get(0)+list.get(1)+list.get(2));
		if(shaHex.equals(signature)){
			System.out.println("Check Success! "+echostr);
			try {
				PrintWriter out = resp.getWriter();
				out.print(echostr);
				out.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			
		}else{
			System.out.println("微信接入有误...");
			return;
		}
	}
	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		
		// 读取请求内容  
		try {
	        BufferedReader br = new BufferedReader(new InputStreamReader(req.getInputStream(),"UTF-8"));  
	        String line = null;  
	        StringBuilder sb = new StringBuilder();  
	        while ((line = br.readLine()) != null) {  
	            sb.append(line);  
	        }  
	        //将json字符串转换为json对象  
	        JSONObject msgObj = JSONObject.parseObject(sb.toString());
	        br.close();
	        
	        // 进入到客服消息的用户的openid
	        String touser = msgObj.getString("FromUserName");
	        
	        // 响应消息给用户，取到该用户的session_form和消息，获取对应的回复内容
 			Map<String, Object> msgMap = worldService.selectWxCustomMessageMap();
 			String sessionform = (String)redisTemplate.opsForValue().get(touser);
 			
 			// 通过应用的名称 获取到不同应用的token
 			String pm = CommonUtil.getTokenParam(msgObj.getString("ToUserName"));
	        if(BlankUtils.checkBlank(pm)){
	        	return;
	        }
	        String[] split = pm.split("#_&");
			String access_token = BlankUtils.getWxAccess_Token(split[0], split[1]);
			
	        if(touser != null && !touser.isEmpty()){
	        	resp.setCharacterEncoding("UTF-8");
	        	PrintWriter out = resp.getWriter();
				out.print("success");
				out.close();
				if(msgObj.getString("MsgType").equals("event")){ // 刚进入客服消息的初始化
					// 存入该用户进入的应用
					redisTemplate.opsForValue().set(touser, msgObj.getString("SessionFrom"));
					
					String to_msg = (String)msgMap.get(sessionform+"#_&init");
					JSONObject obj = JSONObject.parseObject(to_msg);
					if(obj != null){
						obj.put("touser", touser);
						String result = sendMsg(access_token, obj); // 发送
					}
					return;
				}
	        }
			
	        // 接收到客户端发送的消息
//			System.out.println("sf == "+sessionform+"#_&"+msgObj.getString("Content"));
			String to_msg = (String)msgMap.get(sessionform+"#_&"+msgObj.getString("Content"));
			if(BlankUtils.checkBlank(to_msg)){ // 默认回复内容
				to_msg = (String)msgMap.get(sessionform+"#_&zero");
			}
			JSONObject obj = JSONObject.parseObject(to_msg);
			obj.put("touser", touser);
			sendMsg(access_token, obj); // 发送消息
			
//			System.out.println("内容为 == "+obj.toJSONString());
			
			/*JSONObject content = new JSONObject();
			if(msgObj.getString("Content").equals("1")){
				content.put("content", "内容 : <a href='http://www.qq.com' data-miniprogram-appid='wx2d89c2d114f29bcb' data-miniprogram-path='pages/home/<USER>'>点击跳小程序</a>");
				map.put("msgtype", "text");
				map.put("text", content);
			}else if(msgObj.getString("Content").equals("2")){
				content.put("content", "内容 : <a href='http://www.qq.com' data-miniprogram-appid='wx87370ad3e18779bb' data-miniprogram-path=''>点击跳小游戏</a>");
				map.put("msgtype", "text");
				map.put("text", content);
			}else if(msgObj.getString("Content").equals("3")){
				content.put("title", "卡片小程序跳转");
				content.put("pagepath", "pages/MainPage/Main");
				content.put("thumb_media_id", "4lpp91hcCys_auRPj418VRl9gd9u57ZtG5BtEPW9M45jjQaF8So634QcB6FAJfNt");
				map.put("msgtype", "miniprogrampage");
				map.put("miniprogrampage", content);
			}else if(msgObj.getString("Content").equals("4")){ // 图片
				content.put("media_id", "2_wTPRQyYWN4ZrTtSOP0xOx46oh_7VpRaVNdNyluf6CSXJEpdKyHrVX0haACEzmX");
				map.put("msgtype", "image");
				map.put("image", content);
			}else if(msgObj.getString("Content").equals("5")){ // 图文
				content.put("title", "图文链接");
				content.put("description", "百度一下 你就知道");
				content.put("url", "https://www.baidu.com");
				content.put("thumb_url", "https://gss0.bdstatic.com/94o3dSag_xI4khGkpoWK1HF6hhy/baike/c0%3Dbaike80%2C5%2C5%2C80%2C26/sign=d757200dc4fcc3cea0cdc161f32cbded/279759ee3d6d55fb22bcda0961224f4a20a4dda3.jpg");
				map.put("msgtype", "link");
				map.put("link", content);
			}*/
			
		} catch (Exception e){
	    	e.printStackTrace();
	    }
        
	}
	public String sendMsg(String access_token, JSONObject obj){
		CloseableHttpClient client = HttpClients.createDefault();
		try {
			String url = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token="+access_token;
			HttpPost httpPost = new HttpPost(url); // 创建httpPost     
	        httpPost.setHeader("Accept", "application/json");   
	        httpPost.setHeader("Content-Type", "application/json");  
	        StringEntity entity = new StringEntity(obj.toJSONString(), "UTF-8");  
	        httpPost.setEntity(entity);
	        
	        CloseableHttpResponse response = client.execute(httpPost);
			String result = EntityUtils.toString(response.getEntity());
			return result;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	@Override
	public void init() throws ServletException {
		super.init();
	}
	@Override
	public void destroy() {
		super.destroy();
	}
}
