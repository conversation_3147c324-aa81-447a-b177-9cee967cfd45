package com.wbgame.servlet.zhpay;


import java.io.IOException;

import java.io.OutputStream;
import java.io.PrintWriter;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wbgame.common.RefeshCacheConstant;
import com.wbgame.service.AdService;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;


/**
 * 新版Xyxtj2大数据采集缓存
 * <AUTHOR>
 * 
 */ 
@WebServlet(name="recacheNewXyxtj2",urlPatterns="/recacheNewXyxtj2")
public class ReCacheNewXyxtj2Servlert extends HttpServlet{
	
	private static final long serialVersionUID = 1L;
	private static Log logger = LogFactory.getLog(ReCacheNewXyxtj2Servlert.class);
	
	@Autowired
	private AdService adService;
  
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		
		response.setCharacterEncoding("UTF-8");
		response.setHeader("Access-Control-Allow-Origin", "*");
		PrintWriter out = response.getWriter();
		
		String mapid = BlankUtils.checkNull(request, "mapid");
		String appid = request.getParameter("appid");
		try {
			
			String baseParam = "?mapid="+mapid;
			if(!BlankUtils.checkBlank(appid))
				baseParam += "&appid="+appid;
					
			/** 拉取刷新列表，遍历刷新服务地址 */
			String sql = "select url from dn_recache_config where mark = 'NewXyxtj2'";
			List<String> urlList = adService.queryListString(sql);
			for (String url : urlList) {
				String resp = HttpClientUtils.getInstance().httpGet(url + baseParam);
				if(resp == null || !"ok".equals(resp.trim())){
					
					out.print("fail");
					out.close();
					return;
				}
			}
			
			out.print("ok");
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
			logger.warn("NewXyxtj2刷新缓存失败!!!");
			
			out.print("fail");
			out.close();
		}
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)throws ServletException, IOException {
		doGet(req,resp);
	}
	
	@Override
	public void init() throws ServletException {
		super.init();
	}
	@Override
	public void destroy() {
		super.destroy();
	}
}
