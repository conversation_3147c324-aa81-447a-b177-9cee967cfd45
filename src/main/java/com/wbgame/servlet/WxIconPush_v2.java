package com.wbgame.servlet;


import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wbgame.pojo.WxIconPushVo;
import com.wbgame.service.WorldService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;

@WebServlet(name="wxIconPush/v2",urlPatterns="/wxIconPush/v2")
public class WxIconPush_v2 extends HttpServlet {

	private static final long serialVersionUID = 1L;
	
	@Autowired
	private WorldService worldService;
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	
	@Override
	protected void service(HttpServletRequest req, HttpServletResponse resp) throws IOException {
		resp.setCharacterEncoding("UTF-8");
		resp.setHeader("Access-Control-Allow-Origin", "*");
		PrintWriter out = resp.getWriter();
		boolean flag = true;
		
		String wbappid = req.getParameter("appid");
		String platform = req.getParameter("platform");
		String channel = req.getParameter("channel");
		if(!BlankUtils.isNumeric(wbappid)){
			out.print("{\"res\":0,\"msg\":\"parameter type error!\"}");
			out.close();
			return ;
		}
		if(BlankUtils.checkBlank(channel)){ // 默认为wechat渠道
			channel = "wechat";
		}
		
		try {
			JSONArray iconList = new JSONArray();
			JSONArray bannerList = new JSONArray();
			JSONArray plaqueList = new JSONArray();
			JSONArray splashList = new JSONArray();
			JSONArray topList = new JSONArray();
			JSONArray bottomList = new JSONArray();
			JSONArray moregameList = new JSONArray();
			JSONArray grouppageList = new JSONArray();
			JSONArray moregroupList = new JSONArray();
			JSONArray plaqueTimesList = new JSONArray();
			JSONArray fungameList = new JSONArray();
			JSONArray hotgameList = new JSONArray();
			
			// 在排序时需要把没有设置level(没有顺序的) 部分数据打乱
			JSONArray array0 = new JSONArray();
			JSONArray array1 = new JSONArray();
			JSONArray array2 = new JSONArray();
			JSONArray array3 = new JSONArray();
			JSONArray array4 = new JSONArray();
			JSONArray array5 = new JSONArray();
			JSONArray array6 = new JSONArray();
			JSONArray array9 = new JSONArray();
			JSONArray array10 = new JSONArray();
			JSONArray array11 = new JSONArray();
			JSONArray array12 = new JSONArray();
			JSONArray array13 = new JSONArray();
			
			
			String reqip = req.getHeader("X-Forwarded-For");
			if(reqip != null && reqip.length() > 0){
				reqip = reqip.split(",")[0];
			}else {
				reqip = req.getRemoteAddr().trim();
			}
			
			String citycode = "-1";
			String cityname = "未识别";
			String[] find = CommonUtil.cityUtil.find(reqip);
			if(BlankUtils.checkBlank(find[2])){
//				// 使用阿里sdk获取ip信息
//				String[] geoIp = worldService.getGeoIpInfo(reqip);
//
//				if(geoIp != null && geoIp.length == 2){
//					cityname = geoIp[0];
//					if(geoIp[1].contains("深圳") || geoIp[1].contains("广州")
//						|| geoIp[1].contains("杭州") || geoIp[1].contains("成都"))
//						cityname = geoIp[1];
//				}
			}else{
				// 默认按省级城市生效，如果城市为深圳和广州，则加入判断
				cityname = find[1];
				if(find[2].contains("深圳") || find[2].contains("广州")
					|| find[2].contains("杭州") || find[2].contains("成都"))
					cityname = find[2];
			}
			
			List<String> proList = worldService.getProvinceList();
			for (String st : proList) {
				String[] split = st.split(",");
				// 通过名称获取到对应省份code
				if(cityname.contains(split[0])) {
					citycode = split[1];
				}
			}
			
			// 城市码过滤
			Map<String, Map<String, Object>> filterMap = worldService.selectWxPushFilter();
			Map<String, Object> cityMap = filterMap.get(wbappid+channel+""+"1");
			if(cityMap != null) {
				// 匹配过滤城市码是否包含
				List<String> codeList = Arrays.asList((cityMap.get("citycode")+"").split(","));
				if(codeList.contains(citycode)) {
					out.print("{\"res\":0,\"msg\":\"在屏蔽地区不可展示!\"}");
					out.close();
					return ;
				}
			}
			
			if(flag){
				List<WxIconPushVo> pList = worldService.selectWxIconPushByAppid(Integer.valueOf(wbappid));
				for (WxIconPushVo ic : pList) {
					if(!channel.equals(ic.getChannel())){ // 只返回指定渠道的数据
						continue;
					}
					
					if(!BlankUtils.checkBlank(platform)){ // 区分平台数据，如为ios则返回ios数据和公共数据
						if("ios".equals(platform) && "android".equals(ic.getParam()))
							continue;
						else if("android".equals(platform) && "ios".equals(ic.getParam()))
							continue;
					}
					JSONObject obj = new JSONObject();
					obj.put("id", ic.getId());
					obj.put("icon", ic.getIcon());
					obj.put("pushAppID", ic.getPush_appid());
					obj.put("gameName", ic.getGame_name());
					obj.put("extraData", ic.getExtraData()==null?"":ic.getExtraData());
					
					if(!"tt".equals(channel)) { // 头条渠道不需要这些参数
						obj.put("plist", ic.getPlist());
						obj.put("pushImage", ic.getPush_image());
						obj.put("open", ic.getOpen());
						obj.put("openPath", ic.getOpen_path());
						obj.put("gameIntroduce", ic.getGame_introduce());
						obj.put("gameStar", ic.getGame_star());
						obj.put("extra", ic.getExtra());
						obj.put("param", ic.getParam());
					}
					
					// icon中当param不为null时，必须不等于jumppath才下发
					if(ic.getImgtype() == 0 && 
							(BlankUtils.checkBlank(ic.getExtra()) || !ic.getExtra().contains("jumppath"))){
						
						if(ic.getLevel() == 0)
							array0.add(obj);
						else
							iconList.add(obj);
						
					}else if(ic.getImgtype() == 1){
						if(ic.getLevel() == 0)
							array1.add(obj);
						else
							bannerList.add(obj);
						
					}else if(ic.getImgtype() == 2){
						if(ic.getLevel() == 0)
							array2.add(obj);
						else
							plaqueList.add(obj);
						
					}else if(ic.getImgtype() == 3){
						if(ic.getLevel() == 0)
							array3.add(obj);
						else
							splashList.add(obj);
						
					}else if(ic.getImgtype() == 4){
						if(ic.getLevel() == 0)
							array4.add(obj);
						else
							topList.add(obj);
						
					}else if(ic.getImgtype() == 5){
						if(ic.getLevel() == 0)
							array5.add(obj);
						else
							bottomList.add(obj);
						
					}else if(ic.getImgtype() == 6){
						if(ic.getLevel() == 0)
							array6.add(obj);
						else
							moregameList.add(obj);
						
					}else if(ic.getImgtype() == 9){
						if(ic.getLevel() == 0)
							array9.add(obj);
						else
							grouppageList.add(obj);
						
					}else if(ic.getImgtype() == 10){
						if(ic.getLevel() == 0)
							array10.add(obj);
						else
							moregroupList.add(obj);
						
					}else if(ic.getImgtype() == 11){
						if(ic.getLevel() == 0)
							array11.add(obj);
						else
							plaqueTimesList.add(obj);
						
					}else if(ic.getImgtype() == 12){
						if(ic.getLevel() == 0)
							array12.add(obj);
						else
							fungameList.add(obj);
						
					}else if(ic.getImgtype() == 13){
						if(ic.getLevel() == 0)
							array13.add(obj);
						else
							hotgameList.add(obj);
						
					}
				}
			}
			
//			redisTemplate.opsForValue()
//				.increment("wxIconPushCount"+DateTime.now().toString("yyyyMMdd"), 1);
			
			// 打乱顺序，写入有序数据中
			Collections.shuffle(array0);Collections.shuffle(array1);
			Collections.shuffle(array2);Collections.shuffle(array3);
			Collections.shuffle(array4);Collections.shuffle(array5);
			Collections.shuffle(array6);Collections.shuffle(array9);
			Collections.shuffle(array10);Collections.shuffle(array11);
			Collections.shuffle(array12);Collections.shuffle(array13);
			iconList.addAll(array0);
			bannerList.addAll(array1);
			plaqueList.addAll(array2);
			splashList.addAll(array3);
			topList.addAll(array4);
			bottomList.addAll(array5);
			moregameList.addAll(array6);
			grouppageList.addAll(array9);
			moregroupList.addAll(array10);
			plaqueTimesList.addAll(array11);
			fungameList.addAll(array12);
			hotgameList.addAll(array13);
			
			JSONObject result = new JSONObject();
			result.put("iconList", iconList);
			result.put("bannerList", bannerList);
			result.put("plaqueList", plaqueList);
			result.put("splashList", splashList);
			result.put("topList", topList);
			result.put("bottomList", bottomList);
			result.put("moregameList", moregameList);
			result.put("grouppageList", grouppageList);
			result.put("moregroupList", moregroupList);
			result.put("plaqueTimesList", plaqueTimesList);
			result.put("fungameList", fungameList);
			result.put("hotgameList", hotgameList);
			
			// 时间段生效配置
			boolean bl = false;
			try {
				Map<String, Object> timeFilter = filterMap.get(wbappid+channel+""+"3");
				if(timeFilter != null && !BlankUtils.checkBlank(timeFilter.get("filterlist")+"")){
					
					String[] hours = (timeFilter.get("filterlist")+"").split("-");
					DateTime start = DateTime.now().withHourOfDay(Integer.valueOf(hours[0])).withMinuteOfHour(0);
					DateTime end = DateTime.now().withHourOfDay(Integer.valueOf(hours[1])).withMinuteOfHour(59);
					DateTime dateTime = DateTime.now();
					
					// 在指定的时间区间中
					if(dateTime.isAfter(start) && dateTime.isBefore(end)){
						
						bl = true;
						// 匹配屏蔽生效的城市
						if(!BlankUtils.checkBlank(timeFilter.get("citycode")+"")){
							// 当获取到对应省份code时，匹配过滤城市码是否包含
							List<String> codeList = Arrays.asList((timeFilter.get("citycode")+"").split(","));
							if(codeList.contains(citycode)){
								bl = false;
							}
						}
					}
					
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			
			JSONObject config = new JSONObject();
			// 添加该项目的状态  development(开发) auditing(审核中) running(上线运作中) 
			// 项目版本号
			// 控制插屏展示概率
			Map<String, Object> sMap = CommonUtil.cMap.get(wbappid);
			if(sMap != null){
				result.put("projectStatus", sMap.get("project_status"));
				result.put("projectVer", sMap.get("project_ver"));
				result.put("plaqueRate", sMap.get("plaque_rate"));
				
				// 广告开关、游戏配置、红包开关
				if(sMap.get("ad_flag") != null){ // 广告开关的值作用相反，true为关闭
					config.put("vigame_ad_flag", sMap.get("ad_flag"));
					config.put("config", sMap.get("game_config"));
				}
				if(sMap.get("cash_flag") != null){ // 红包配置开关，0表示不开 1表示开启
					config.put("cash", sMap.get("cash_flag")+"");
				}
				
				// 误点配置参数
				if(bl)
					config.put("late_config", sMap.get("late_config"));
			}
			
			// 广告屏蔽开关配置，用于控制广告位展示
			Map<String, Map<String, Object>> pushMap = worldService.getPushAdconfigConfig();
			Map<String, Object> adparam = pushMap.get(wbappid+channel);
			if(adparam != null){
				// 红包配置开关，不受屏蔽规则影响，优先使用此处的
				if(adparam.get("cash_flag") != null)
					config.put("cash", adparam.get("cash_flag")+"");
				
				boolean bk = false;
				// 验证时段屏蔽，参数格式为 9-12,14-18
				String[] split = (adparam.get("filterlist")+"").split(",");
				for (String sd : split) {
					
					String[] hours = sd.split("-");
					DateTime start = DateTime.now().withHourOfDay(Integer.valueOf(hours[0])).withMinuteOfHour(0);
					DateTime end = DateTime.now().withHourOfDay(Integer.valueOf(hours[1])).withMinuteOfHour(59);
					DateTime dateTime = DateTime.now();
					
					// 在指定的时间区间中
					if(dateTime.isAfter(start) && dateTime.isBefore(end)){
						bk = true;
						break;
					}
				}
				
				// 验证城市屏蔽
				List<String> asList = Arrays.asList((adparam.get("citycode")+"").split(","));
				if(asList.contains(citycode))
					bk = false;
				
				if(bk){
					config.put("level_config", adparam.get("ADParams"));
					
					if(BlankUtils.isJSONArray(adparam.get("ad_config")+""))
						config.put("ad_config", JSONArray.parseArray(adparam.get("ad_config")+""));
					else
						config.put("ad_config", adparam.get("ad_config"));
				}
			}
			
			result.put("gameConfig", config);
			
			// 根据应用和渠道过滤 对应的列表，单平台无配置时尝试全平台all
			Map<String, Object> filter = filterMap.get(wbappid+channel+platform+"2");
			if(filter == null) {filter = filterMap.get(wbappid+channel+"all"+"2");}
			if(filter != null) {
				String[] split = (filter.get("filterlist")+"").split(",");
				for (String str : split) {
					result.put(str, new JSONArray());
				}
			}
				
			out.print(JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty));
			out.close();
		} catch (Exception e) {
			System.out.println("微信互推图标获取异常 : "+e.getMessage());
			e.printStackTrace();
			out.print("{\"res\":0,\"msg\":\"错误信息 : "+e.getMessage()+"\"}");
			out.close();
		}
		
	}
	
	
	@Override
	public void init() throws ServletException {
		super.init();
	}
	@Override
	public void destroy() {
		super.destroy();
	}
}
