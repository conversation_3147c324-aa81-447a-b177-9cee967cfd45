package com.wbgame.task;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.constants.SyncDataConstants;
import com.wbgame.mapper.master.ExchangeMapper;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.FeishuUtils;
import com.wbgame.utils.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description 汇率查询定时任务
 * <AUTHOR>
 * @Date 2024/7/29 10:21
 */
@Component
@Slf4j
@RequestMapping("/exchangeTask")
public class ExchangeTask {

    private static final String getCurrenyurl = "https://api.jisuapi.com/exchange/single?appkey=b2f931be191491cd&currency=CNY";

    @Resource
    private ExchangeMapper exchangeMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 人民币汇率任务
     */
    @Scheduled(cron="0 0 1,8,21 1 * ?")
    public void auto_sync(){
        log.info("人民币汇率自动开始更新...");
        handleExchange();
        log.info("人民币汇率自动结束更新...");
    }

    @RequestMapping("/manual_sync")
    @ResponseBody
    public String manual_sync(String token){
        if (!"dnwx241343fnsd134".equals(token)) {
            return "fail";
        }
        Integer o = (Integer)redisTemplate.opsForValue().get("sync_exchange_lock_" + DateTime.now().toString("yyyy-MM-dd"));
        if (o != null && o >= 3) {
            return "limit";
        }else {
            o = o == null? 1 : o+1;
            redisTemplate.opsForValue().set("sync_exchange_lock_" + DateTime.now().toString("yyyy-MM-dd"),o,2, TimeUnit.DAYS);
        }
        log.info("人民币汇率手动开始更新...");
        handleExchange();
        log.info("人民币汇率手动结束更新...");

        return "ok";
    }

    /**
     * 汇率更新处理
     */
    public void handleExchange(){
        try {
            //调用api汇率
            String s = HttpClientUtils.getInstance().httpGet(getCurrenyurl,null,true);
            //数据
            List<JSONObject> data = new ArrayList<>();
            //获取当月所有天
            List<String> days = DateUtil.getDaysToMonth();
            int index = 0;
            String currency = "CNY";
            JSONObject jsonObject = JSONObject.parseObject(s);
            if (jsonObject.getInteger("status") == 0) {
                JSONObject result = jsonObject.getJSONObject("result");
                JSONObject list = result.getJSONObject("list");
                //遍历所有货币
                for (String key : list.keySet()) {
                    JSONObject value = (JSONObject) list.get(key);
                    //返回的货币需要计算为人民币为主的汇率
                    BigDecimal rate = new BigDecimal("1").divide(value.getBigDecimal("rate"),6, RoundingMode.HALF_UP);
                    String curs = key+","+currency;
                    //放入整月的汇率
                    for (String day : days) {
                        JSONObject newValue = new JSONObject();
                        newValue.put("rate",rate);
                        newValue.put("change_type",curs);
                        newValue.put("tdate",day);
                        data.add(newValue);
                    }
                    log.info("人民币汇率:change_type={},rate={}",curs,rate);
                    index ++ ;
                }
            }else{
                log.error("人民币汇率:更新汇率失败:"+s);
                FeishuUtils.sendMsgToGroupRobot5(SyncDataConstants.OPERATION_CHAT_Id,"人民币汇率:更新汇率失败:"+jsonObject.getString("msg"));
                return;
            }
            log.info("人民币汇率:汇率计算完成,货币数量:"+index);

            if (data == null || data.size() == 0) {
                return;
            }

            //入库
            try {
                exchangeMapper.batchInsertExchangeList(data);
                log.info("人民币汇率:入库成功,数量={}",data.size());
            }catch (Exception e) {
                log.error("人民币汇率:入库失败",e);
                FeishuUtils.sendMsgToGroupRobot5(SyncDataConstants.OPERATION_CHAT_Id,"人民币汇率:入库失败:"+e.getMessage());
            }

        }catch (Exception e) {
            log.error("人民币汇率：更新汇率抛出异常",e);
            FeishuUtils.sendMsgToGroupRobot5(SyncDataConstants.OPERATION_CHAT_Id,"人民币汇率：更新汇率抛出异常:"+e.getMessage());
        }

    }

}
