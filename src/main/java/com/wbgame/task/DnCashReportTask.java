package com.wbgame.task;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.mapper.adb.UmengMonitorMapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.DnCashReportInfoMapper;
import com.wbgame.pojo.advert.UmengAdIncomeReportVo;
import com.wbgame.pojo.jettison.report.dto.OperationReportDTO;
import com.wbgame.pojo.jettison.report.param.OperationReportParam;
import com.wbgame.service.game.AppService;
import com.wbgame.service.jettison.report.SpendReportService;
import com.wbgame.utils.DataTransUtils;
import com.wbgame.utils.OSSUploadUtil;
import com.wbgame.utils.StringUtils;
import com.wbgame.utils.export.MultiSheetExcelUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 定时向飞书表格中写入数据 任务
 * @Date 2024/10/15 16:26
 */
@Component
public class DnCashReportTask {

    Logger logger = LoggerFactory.getLogger(DnCashReportTask.class);

    @Autowired
    private AdMapper adMapper;

    @Autowired
    private UmengMonitorMapper umengMonitorMapper;

    @Autowired
    private AppService appService;

    @Autowired
    private DnwxBiAdtMapper dnwxBiAdtMapper;
    @Autowired
    private SpendReportService spendReportService;
    @Autowired
    private DnCashReportInfoMapper dnCashReportInfoMapper;

    private static final String FEISHU_EXCEL_LINK = "https://a.vigame.cn/feishu/";

    //获取标题名及字段名
    private final Map<String, String> umengBaseRowName = new LinkedHashMap<String, String>() {{
        //基础字段
        put("tdate", "日期");
        put("appname", "应用名称");
        put("appid", "应用ID");
        put("channel", "子渠道");
        put("media", "媒体");
        put("source", "数据来源");
        put("temp_id", "功能标识");
        put("temp_name", "功能名称");
        put("addnum", "新增");
        put("actnum", "活跃");
        put("avgnum", "新增占比");
        put("total_income", "总收入");
        put("dau_arpu", "dau_arpu");
        put("daily_duration", "日均在线时长");
    }};

    private final Map<String, String> umengTotalPvRowName = new LinkedHashMap<String, String>() {{
        //总人均pv
        put("all_total_pv", "总人均pv");
        put("total_splash_pv", "总人均pv-开屏");
        put("total_plaque_pv", "总人均pv-插屏");
        put("total_plaque_video_pv", "总人均pv-插屏视频");
        put("total_banner_pv", "总人均pv-banner");
        put("total_video_pv", "总人均pv-视频");
        put("total_native_msg_pv", "总人均pv-原生msg");
        put("total_suspend_icon_pv", "总人均pv-悬浮icon");
    }};
    private final Map<String, String> umengTotalClickRowName = new LinkedHashMap<String, String>() {{
        //总人均点击
        put("all_total_click", "总人均点击");
        put("total_splash_click", "总人均点击-开屏");
        put("total_plaque_click", "总人均点击-插屏");
        put("total_plaque_video_click", "总人均点击-插屏视频");
        put("total_banner_click", "总人均点击-banner");
        put("total_video_click", "总人均点击-视频");
        put("total_native_msg_click", "总人均点击-原生msg");
        put("total_suspend_icon_click", "总人均点击-悬浮icon");
    }};
    private final Map<String, String> umengECPMRowName = new LinkedHashMap<String, String>() {{
        //eCPM
        put("system_splash_ecpm", "ecpm-系统开屏");
        put("splash_ecpm", "ecpm-开屏");
        put("native_splash_ecpm", "ecpm-原生开屏");
        put("plaque_ecpm", "ecpm-插屏");
        put("native_new_plaque_ecpm", "ecpm-(msg/yuan)插屏");
        put("plaque_video_ecpm", "ecpm-插屏视频");
        put("banner_ecpm", "ecpm-banner");
        put("native_new_banner_ecpm", "ecpm-(msg/yuan)banner");
        put("video_ecpm", "ecpm-视频");
        put("native_msg_ecpm", "ecpm-原生msg");
        put("suspend_icon_ecpm", "ecpm-悬浮icon");
        put("native_plaque_ecpm", "ecpm-原生插屏");
        put("native_banner_ecpm", "ecpm-原生banner");
    }};
    private final Map<String, String> umengCTRRowName = new LinkedHashMap<String, String>() {{
        //CTR
        put("splash_ctr", "ctr-开屏");
        put("native_splash_ctr", "ctr-原生开屏");
        put("plaque_ctr", "ctr-插屏");
        put("native_new_plaque_ctr", "ctr-(msg/yuan)插屏");
        put("plaque_video_ctr", "ctr-插屏视频");
        put("banner_ctr", "ctr-banner");
        put("native_new_banner_ctr", "ctr-(msg/yuan)banner");
        put("video_ctr", "ctr-视频");
        put("native_msg_ctr", "ctr-原生msg");
        put("suspend_icon_ctr", "ctr-悬浮icon");
        put("native_plaque_ctr", "ctr-原生插屏");
        put("native_banner_ctr", "ctr-原生banner");
    }};
    private final Map<String, String> umengCPCRowName = new LinkedHashMap<String, String>() {{
        //CPC
        put("splash_cpc", "cpc-开屏");
        put("native_splash_cpc", "cpc-原生开屏");
        put("plaque_cpc", "cpc-插屏");
        put("native_new_plaque_cpc", "cpc-(msg/yuan)插屏");
        put("plaque_video_cpc", "cpc-插屏视频");
        put("banner_cpc", "cpc-banner");
        put("native_new_banner_cpc", "cpc-(msg/yuan)banner");
        put("video_cpc", "cpc-视频");
        put("native_msg_cpc", "cpc-原生msg");
        put("suspend_icon_cpc", "cpc-悬浮icon");
        put("native_plaque_cpc", "cpc-原生插屏");
        put("native_banner_cpc", "cpc-原生banner");
    }};
    private final Map<String, String> operationRowName = new LinkedHashMap<String, String>() {{
        put("day", "日期");
        put("spend", "消耗");
        put("app", "应用名称");
        put("channel", "子渠道");
        put("appid", "应用id");
        put("ad_platform", "媒体");
        put("buy_installs", "买量新增");
        put("buy_cost", "买量成本");
        put("activeLtv1", "激活广告LTV1");
        put("add_roi", "首日ROI(营销)");
        put("lt3", "Lt3");
        put("lt7", "Lt7");
        put("avg_duration", "人均使用时间");
        put("f_roi_total", "首日ROI(整包)");
        put("buy_spendRoi", "买量ROI(营销)");
        put("t_roi_channel", "整体ROI(渠道)");
        put("roi", "整体ROI(整包)");
        put("buy_spendRevenue", "买量收入(营销)");
        put("buy_profit", "买量毛利(营销)");
        put("natural_install", "自然新增");
        put("natural_revenue", "自然收入(联盟)");
        put("natural_spendRevenue", "自然收入(营销)");
        put("buy_rate", "买量/新增");
        put("add_num", "新增(开平)");
        put("active_num", "活跃(开平)");
        put("um_add_num", "新增(友盟)");
        put("um_active_num", "活跃(友盟)");
        put("revenue", "整体收入(整包)");
        put("revenue_profit", "整体毛利");
        put("revenue_profit_rate", "整体毛利率");
        put("cost", "整体成本");
        put("add_revenue", "新增用户买量收入");
        put("buy_revenue", "买量收入(联盟)");
        put("revenue_channel", "整体收入(渠道)");
        put("arpu", "整体arpu(整包)");
        put("install_arpu", "友盟新增arpu");
    }};

    //oss 桶名
    private static final String OSS_BUCKET_NAME = "dnwx-res";

    /**
     * 渠道产品广告数据查询数据，联运产品数据报表数据整理至指定表中（拉取3周内数据）
     */
    //设置每天的10:00/15:00 触发
    @Scheduled(cron = "00 00 10,15 * * ?")
    public void syncUmengAdData() {
        logger.info("执行整理渠道产品广告数据,联运产品数据报表数据");
        String startDate = DateTime.now().minusWeeks(3).toString("yyyy-MM-dd");
        String endDate = DateTime.now().toString("yyyy-MM-dd");
        //执行整理渠道产品广告数据同步至 umeng_ad_summary
        fetchUmengAdData(startDate, endDate);
        // 联运产品数据报表数据整合同步至 report_operation_summary_daily
        fetchReportOperationata(startDate, endDate);
        //拉取近一周的数据生成excel文件并上传至oss
        String lastWeek = DateTime.now().minusWeeks(1).toString("yyyy-MM-dd");
        //获取需要生成飞书的appid
        List<Map<String, String>> mapList = adMapper.queryListMapOne("SELECT * FROM dnwx_client.wbgui_sy_report");
        //根据 cha_id 分组操作
        Map<String, String> channelAppidMap = mapList.stream()
                .filter(map -> !StringUtils.isEmpty(map.get("cha_id")) && !StringUtils.isEmpty(map.get("appid")))
                .collect(Collectors.groupingBy(map -> map.get("cha_id"), Collectors.mapping(map -> map.get("appid"), Collectors.joining(","))));

        for (Map.Entry<String, String> entry : channelAppidMap.entrySet()) {
            createFeishuExcelToOss(lastWeek, endDate, entry.getKey(), entry.getValue());
        }
        logger.info("同步整理渠道产品广告数据,联运产品数据报表数据完成");
    }

    /**
     * 拉取近一周的数据生成excel文件并上传至oss
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param channel   渠道
     * @param appids    appid,多个使用逗号隔开
     */
    public void createFeishuExcelToOss(String startDate, String endDate, String channel, String appids) {
        String appidSql = DataTransUtils.transToSql(appids);
        String[] appidArray = appids.split(",");
        String umengSqlFormat = "SELECT * FROM umeng_ad_summary WHERE tdate BETWEEN '%s' AND '%s' AND channel = '%s' AND appid IN (%s)";
        List<Map<String, Object>> umengList = adMapper.queryListMap(String.format(umengSqlFormat, startDate, endDate, channel, appidSql));

        Map<String, Map<String, String>> umengMap = updateDataToOss(channel, appidArray, umengList, true);

        String operationSql = "SELECT * FROM report_operation_summary_daily WHERE STR_TO_DATE(SUBSTRING_INDEX(`day`, '(', 1), '%Y-%m-%d') BETWEEN '" + startDate + "' AND '" + endDate + "' AND channel = '" + channel + "' AND appid IN (" + appidSql + ")";
        List<Map<String, Object>> opreationList = adMapper.queryListMap(operationSql);
        Map<String, Map<String, String>> operationMap = updateDataToOss(channel, appidArray, opreationList, false);

        for (Map.Entry<String, Map<String, String>> operationEntry : operationMap.entrySet()) {
            String key = operationEntry.getKey();
            Map<String, String> value = operationEntry.getValue();
            if (umengMap.containsKey(key)) {
                Map<String, String> map = umengMap.get(key);
                value.put("adv2_link", map.get("adv2_link"));
                umengMap.remove(key);
                operationEntry.setValue(value);
            }
        }
        List<Map<String, String>> mapListStore = new ArrayList<>(umengMap.values());
        mapListStore.addAll(operationMap.values());

        if (!CollectionUtils.isEmpty(mapListStore)) {
            //新增数据
            dnCashReportInfoMapper.insertMap(mapListStore);
        }
    }

    /**
     * 将查询出来的数据生成excel上传至oss
     *
     * @param channel    渠道
     * @param appidArray 需要生成的appid集合
     * @param dataList   需要上传至oss的数据集合
     * @param umengFlag  是否变现数据标记
     * @return Map<String, Map < String, String>> 需要保存至链接表的信息数据，key为 tdate + appid + channel，value为链接信息
     */
    private Map<String, Map<String, String>> updateDataToOss(String channel, String[] appidArray, List<Map<String, Object>> dataList, boolean umengFlag) {
        //获取系统的临时目录
        String tempPath = System.getProperty("java.io.tmpdir");
        //根据 appid 分组操作
        Map<String, List<Map<String, Object>>> dataCollect = dataList.stream().collect(Collectors.groupingBy(data -> (String) data.getOrDefault("appid", Strings.EMPTY)));
        Map<String, Map<String, String>> mapListStore = new HashMap<>();
        //当前日期
        String todayDayTime = DateTime.now().toString("yyyyMMdd");
        for (String appid : appidArray) {
            Path filePath = null;
            try (SXSSFWorkbook workbook = new SXSSFWorkbook(5000)) {
                if (!dataCollect.containsKey(appid)) continue;
                // 目标文件名
                String prefix = umengFlag ? "adv2" : "put";
                String fileName = prefix + "-" + appid + "-" + channel + "-" + todayDayTime + ".xlsx";
                filePath = Paths.get(tempPath + fileName);
                OutputStream out = Files.newOutputStream(Paths.get(filePath.toString()));
                if (umengFlag) {
                    //友盟
                    umengWriteWorkbook(dataCollect, appid, workbook);
                } else {
                    //联运产品数据
                    //将文件导出到临时目录
                    MultiSheetExcelUtils.exportMapWithoutTitle(workbook, dataCollect.get(appid), operationRowName, "sheet1");
                }
                workbook.write(out);
                OSSUploadUtil.uploadDataOSS(OSS_BUCKET_NAME, "feishu/" + fileName, filePath.toFile(), null);
                //保存链接 链接格式：https://a.vigame.cn/feishu/文件名
                Map<String, String> adv2Link = buildCashReportInfo(fileName, appid, channel, umengFlag);
                mapListStore.put(DateTime.now().toString("yyyy-MM-dd") + appid + channel, adv2Link);
            } catch (IOException e) {
                logger.error("createUmengFeishuExcel 异常，异常原因:{}", e.getMessage());
                e.printStackTrace();
            } finally {
                if (filePath != null) {
                    //删除临时生成的文件
                    filePath.toFile().delete();
                }
            }
        }
        return mapListStore;
    }

    private void umengWriteWorkbook(Map<String, List<Map<String, Object>>> umengCollect, String appid, SXSSFWorkbook workbook) {
        //excel的sheet名称
        String sheetName = null;
        for (int sheetNum = 1; sheetNum <= 5; sheetNum++) {
            //每个sheet页共同的基础字段
            LinkedHashMap<String, String> basicRowNames = Maps.newLinkedHashMap(umengBaseRowName);
            switch (sheetNum) {
                case 1:
                    //sheet1
                    basicRowNames.putAll(umengTotalPvRowName);
                    sheetName = "总人均pv";
                    break;
                case 2:
                    //sheet2
                    basicRowNames.putAll(umengTotalClickRowName);
                    sheetName = "总人均点击";
                    break;
                case 3:
                    //sheet3
                    basicRowNames.putAll(umengECPMRowName);
                    sheetName = "eCPM";
                    break;
                case 4:
                    //sheet4
                    basicRowNames.putAll(umengCTRRowName);
                    sheetName = "CTR";
                    break;
                case 5:
                    //sheet5
                    basicRowNames.putAll(umengCPCRowName);
                    sheetName = "CPC";
                    break;
                default:
                    break;
            }
            //将文件导出到临时目录
            MultiSheetExcelUtils.exportMapWithoutTitle(workbook, umengCollect.get(appid), basicRowNames, sheetName);
        }
    }

    /**
     * 构建 CashReportInfo 数据
     *
     * @param fileName     文件名
     * @param appid        应用id
     * @param channel      渠道
     * @param adv2LinkFlag 是否变现数据 true/false
     * @return Map<String, String>
     */
    private Map<String, String> buildCashReportInfo(String fileName, String appid, String channel, boolean adv2LinkFlag) {
        String tdate = DateTime.now().toString("yyyy-MM-dd");
        Map<String, String> cashMap = new HashMap<>();
        cashMap.put("tdate", tdate);
        cashMap.put("appid", appid);
        cashMap.put("cha_id", channel);
        if (adv2LinkFlag) {
            cashMap.put("adv2_link", FEISHU_EXCEL_LINK + fileName);
        } else {
            cashMap.put("put_link", FEISHU_EXCEL_LINK + fileName);
        }
        cashMap.put("cuser", "admin");
        return cashMap;
    }


    /**
     * 渠道产品广告数据查询同步至 umeng_ad_summary
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     */
    private void fetchUmengAdData(String startDate, String endDate) {
        // 渠道产品广告数据查询
        //封装查询条件--渠道产品广告数据页面查询初始条件
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("tableName", "umeng_ad_income");
        paramMap.put("start_date", startDate);
        paramMap.put("end_date", endDate);
        paramMap.put("group", "b.tdate,b.appkey,b.media,b.channel,b.temp_id,b.source");
        paramMap.put("order_str", "b.tdate asc,b.appkey asc,b.media asc,b.channel asc,b.temp_id asc,b.source asc");
        paramMap.put("dataSource", "1");
        paramMap.put("source", "1");
        final int pageSize = 5000;
        long total = 5000;
        for (int i = 1; i <= (total + pageSize - 1) / pageSize; i++) {
            PageHelper.startPage(i, pageSize);
            List<UmengAdIncomeReportVo> fList = umengMonitorMapper.getUmengAdIncomeList(paramMap);
            PageInfo<UmengAdIncomeReportVo> pageInfo = new PageInfo<>(fList);
            //total = ((Page) fList).getTotal();
            total = pageInfo.getTotal();
            fList.forEach(t -> {
                //处理avgnum
                String avgnum = t.getAvgnum();
                t.setAvgnum(strToTwoPercent(avgnum));
                //处理在线时长
                String dailyDuration = t.getDaily_duration();
                t.setDaily_duration(StringUtils.secondsToHHmmss(dailyDuration));
                t.setBanner_ctr(t.getBanner_ctr() + "%");
                t.setPlaque_ctr(t.getPlaque_ctr() + "%");
                t.setSplash_ctr(t.getSplash_ctr() + "%");
                t.setVideo_ctr(t.getVideo_ctr() + "%");
                t.setNative_banner_ctr(t.getNative_banner_ctr() + "%");
                t.setNative_msg_ctr(t.getNative_msg_ctr() + "%");
                t.setNative_plaque_ctr(t.getNative_plaque_ctr() + "%");
                t.setNative_splash_ctr(t.getNative_splash_ctr() + "%");
                t.setNative_new_banner_ctr(t.getNative_new_banner_ctr() + "%");
                t.setNative_new_plaque_ctr(t.getNative_new_plaque_ctr() + "%");
                t.setPlaque_video_ctr(t.getPlaque_video_ctr() + "%");
                t.setSuspend_icon_ctr(t.getSuspend_icon_ctr() + "%");
            });
            //新增数据至 umeng_ad_total 表
            if (!CollectionUtils.isEmpty(fList)) {
                adMapper.insertUmengAdSummary(fList);
            }
        }
        logger.debug("渠道产品广告数据同步至umeng_ad_summary完成");
    }

    /**
     * 保留两位小数-百分比
     *
     * @param data
     * @return
     */
    private String strToTwoPercent(String data) {
        try {
            BigDecimal twoPercent = new BigDecimal(data)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, RoundingMode.UNNECESSARY);
            data = twoPercent.toString() + "%";
        } catch (Exception e) {

        }
        return data;
    }

    private void fetchReportOperationata(String startDate, String endDate) {
        //固定请求条件参数封装--页面初始条件赋值
        OperationReportParam param = new OperationReportParam();
        param.setStart_date(startDate);
        param.setEnd_date(endDate);
        param.setChoose("um");
        param.setGroup(Lists.newArrayList("day", "app", "ad_platform", "channel"));
        param.setOrder_str("day asc,app asc,ad_platform asc,channel asc");
        param.setKey("spend&>&0");
        param.setIndex("spend");
        param.setSymbol(">");
        param.setNumber(0.0);
        final int pageSize = 5000;
        long total = 5000;
        for (int i = 1; i <= (total + pageSize - 1) / pageSize; i++) {
            PageHelper.startPage(i, pageSize);
            Page<OperationReportDTO> reportPage = dnwxBiAdtMapper.getOperationReport(param);
            List<OperationReportDTO> resultList = reportPage.getResult();
            total = reportPage.getTotal();
            //查询后的数据处理操作
            if (!CollectionUtils.isEmpty(resultList)) {
                spendReportService.convertAppAndCountry(resultList, param);
                spendReportService.convertDateStr(resultList, param);
                //新增数据  adMapper
                adMapper.insertReportOperationDaily(resultList);
            }
        }
        logger.debug("联运产品数据报表数据整理并同步至 report_operation_summary_daily 完成");
    }


}
