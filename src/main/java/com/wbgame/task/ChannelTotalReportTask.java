package com.wbgame.task;

import com.wbgame.service.RedisService;
import com.wbgame.service.advert.ChannelTotaReportService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class ChannelTotalReportTask {

    @Autowired
    private ChannelTotaReportService channelTotaReportService;

    @Autowired
    private RedisService redisService;

    @Scheduled(cron="00 30 8,10,12,15,20 * * ?")
    public void synChannelTotalReport(){
        System.out.println("渠道汇总报表定时任务启动...");
        String startTime = DateTime.now().minusDays(3).toString("yyyy-MM-dd");
        String endTime = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        //验证是否需要锁住方法
        if (redisService.hasKey("channel_total_report_lock")) {
            System.out.println("该同步数据正在执行中");
            return;
        }
        try {
            redisService.set("channel_total_report_lock", "lock", 3600);
            channelTotaReportService.synChannelTotalReport(startTime,endTime,null);
            channelTotaReportService.synChannelTotalReport(DateTime.now().minusDays(8).toString("yyyy-MM-dd"),DateTime.now().minusDays(7).toString("yyyy-MM-dd"),null);
            channelTotaReportService.synChannelTotalReport(DateTime.now().minusDays(15).toString("yyyy-MM-dd"),DateTime.now().minusDays(14).toString("yyyy-MM-dd"),null);
        }catch (Exception e) {
            e.printStackTrace();
        }finally {
            redisService.del("channel_total_report_lock");
        }
    }

}
