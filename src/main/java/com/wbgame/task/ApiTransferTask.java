package com.wbgame.task;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.constants.PlatformConstants;
import com.wbgame.controller.mobile.set.apiTransfer.HwApiManager;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.master.mobile.ApiPacketParaConfigMapper;
import com.wbgame.mapper.redpack.AdvertRollUrlConfigMapper;
import com.wbgame.pojo.mobile.hw.HwAppInfo;
import com.wbgame.pojo.mobile.request.HuaWeiPacketAuditRecordParam;
import com.wbgame.service.mobile.ApiTransferKeyConfigService;
import com.wbgame.service.mobile.HwApiService;
import com.wbgame.utils.DateUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @description: api传包定时任务
 * @author: huangmb
 * @date: 2021/11/01
 **/
@Component
@RequestMapping("transfer")
public class ApiTransferTask {

    Logger logger = LoggerFactory.getLogger(ApiTransferTask.class);

    @Autowired
    private ApiTransferKeyConfigService apiTransferKeyConfigService;
    
	@Autowired
    private ApiPacketParaConfigMapper apiPacketParaConfigMapper;
	@Autowired
    private AdvertRollUrlConfigMapper advertRollUrlConfigMapper;
	@Autowired
    private Adv2Mapper adv2Mapper;

    @Autowired
    HwApiService hwApiService;

    @Scheduled(cron="0 0 6 * * ?")
    public void syncBaiduCashTotal() {
        logger.info("开始更新oppo传包token");
        apiTransferKeyConfigService.updateApiTransferToken();
        logger.info("结束更新oppo传包token");
    }

	/**
	 * @description: 同步 快手小游戏登录参数配置 到 dn_ecpm_ksuser_account
	 * @author: caow
	 * @date: 2024/08/22
	 **/
	@Scheduled(cron = "0 * */2 * * ?")
	public void syncKsAccount() {
		List<Map<String, Object>> list = advertRollUrlConfigMapper.selectKsAccountConfig();
		if(list != null && list.size() > 0) {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sql1", "INSERT IGNORE dn_ecpm_ksuser_account (ksapp_id, ksapp_secret, dnappid, advertiser_id, ks_account, createtime) VALUES ");
			paramMap.put("sql2", " (#{li.ksAppId}, #{li.ksAppSecret}, #{li.appid}, '********', 'flagA', now()) ");
			paramMap.put("sql3", " ");
			paramMap.put("list", list);
			adv2Mapper.batchExecSql(paramMap);
		}

	}


	/**
	 * @description: 华为分包提审
	 * @author: xugx
	 * @date: 2024/03/19
	 **/
    @Scheduled(cron = "10 */3 * * * ?")
    @RequestMapping("/hw/packet/audit")
    public void HuaWeiPacketAudit() {
    	List<HuaWeiPacketAuditRecordParam> list= apiPacketParaConfigMapper.queryHuaWeiPacketAuditRecordNeeded();
    	if(null==list||list.size()==0){
    		return ;
    	}
    	for (HuaWeiPacketAuditRecordParam huaWeiPacketAuditRecordParam : list) {
    		String clientId=huaWeiPacketAuditRecordParam.getHw_report_api_client_id();
    		String token=huaWeiPacketAuditRecordParam.getToken();
    		String hwappid=huaWeiPacketAuditRecordParam.getHwappid();
    		JSONObject handleRet = HwApiManager.submit(PlatformConstants.HW.HW_API_URL, clientId, token, hwappid);
    		huaWeiPacketAuditRecordParam.setAudit_msg(handleRet.getJSONObject("ret").getString("msg"));
    		huaWeiPacketAuditRecordParam.setAudit_time(DateUtil.getDateTime());
    		if (null!=handleRet&&"0".equals(handleRet.getJSONObject("ret").getString("code"))){
                //提审成功
    			huaWeiPacketAuditRecordParam.setAudit_status(1);
    			//刷新本地信息
            	HwAppInfo appInfo=HwApiManager.getAppInfo(PlatformConstants.HW.HW_API_URL,clientId,token,hwappid,"");
            	if (null!=appInfo){
                    hwApiService.updateHwAppDetailInfo(appInfo);
                }
    		}else{
    			huaWeiPacketAuditRecordParam.setAudit_status(2);
    		}
    		//更新记录
    		apiPacketParaConfigMapper.updateHuaWeiPacketAuditRecord(huaWeiPacketAuditRecordParam);
		}
    }
}
