package com.wbgame.task;

import com.wbgame.service.PayOrderService;
import com.wbgame.service.game.GamePayService;
import com.wbgame.utils.BlankUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

/**
 * 支付订单定时任务
 */
@Component
@RequestMapping("payOrderTask")
public class PayOrderTask {

    Logger logger = LoggerFactory.getLogger(PayOrderTask.class);

    @Autowired
    private PayOrderService payOrderService;

    @Resource
    private GamePayService gamePayService;


    @Scheduled(cron="0 0/10 * * * ?")
    @RequestMapping("synPayOrder")
    public void synPayOrder(){
        try {
            String startTime = DateTime.now().minusHours(2).toString("yyyy-MM-dd HH:mm:ss");
            String endTime =  DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            //乐影迁移
            syncLeyingOrder(startTime,endTime);
            logger.info("————————————————支付订单迁移定时任务开始————————————————————");
            int index = payOrderService.synSuccessPayOrder(startTime,endTime);
            logger.info("订单更新:"+index);
            logger.info("————————————————支付订单迁移定时任务结束————————————————————");
        } catch (Exception e) {
            logger.info("支付订单迁移定时任务出现异常,msg:"+e.getMessage());
            e.printStackTrace();
        }
    }


    @RequestMapping("syncLeyingOrder")
    public void syncLeyingOrder(String startTime,String endTime){
        try {
            logger.info("————————————————乐影支付订单迁移定时任务开始————————————————————");
            if (BlankUtils.checkBlank(startTime)) {
                startTime = DateTime.now().minusHours(2).toString("yyyy-MM-dd HH:mm:ss");
            }
            if (BlankUtils.checkBlank(endTime)) {
                endTime =  DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }
            boolean success = gamePayService.syncLeyingOrder(startTime,endTime);
            logger.info("乐影支付订单同步:"+success);
            logger.info("————————————————乐影支付订单迁移定时任务结束————————————————————————");
        } catch (Exception e) {
            logger.info("乐影支付订单迁移定时任务出现异常,msg:",e);

        }
    }

}
