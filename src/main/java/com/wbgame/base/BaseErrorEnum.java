package com.wbgame.base;

/**
 * BaseErrorEnum
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
public enum BaseErrorEnum implements BaseError {

    SUCCESS(1, "操作成功"),
    FAIL(0, "响应失败"),

    VALIDATE_FAILED(1000001, "参数校验失败"),
    ERROR(1000002, "未知错误"),

    // 1 00 0001
    // 1 标识位，00系统模块，0001模块下面的业务
    BIZ_ERROR(1000003, "业务异常"),
    UN_AUTHORIZED(1000004, "身份认证失败"),
    NO_PERMISSION(1000005, "没有访问权限"),
    MISSING_PARAMETER(1000006, "缺少参数"),
    RECORD_MISSING(1000007, "记录不存在"),
    
    
    CONFIG_MISSING(2000001, "秘钥配置不存在"),
    PLATFORM_APPID_MISSING(2000002, "未查询到平台产品配置信息"),
    PLATFORM_CODE_ERROR(2000003, "平台返回错误响应码"),
    PLATFORM_UPDATE_PAK_FAIL(2000004, "更新平台传包信息失败"),
    PLATFORM_CHECK_FAIL(2000005, "提交审核失败"),
    ;

    private final Integer code;

    private final String msg;

    BaseErrorEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }
}
