package com.wbgame.pojo;

public class WxIconPushVo {
	private String id;
	private String appid; // 区分不同应用
	private String channel; // 区分不同渠道 wechat，qq，wb，vivo
	private Integer imgtype; // 不同图片类型，0--icon, 1--banner, 2--plaque, 3--splash, 4--top, 5--bottom，6--moregame，7--row，8--outjump，9--grouppage
	private String icon; // 图标地址
	private String plist; // 帧动画文件信息
	private String push_image; // 图片地址
	private String push_appid; // 推广的小游戏ID
	private String open; // 打开类型 (展示图片image，打开连接url，打开小程序miniprogram)
	private String open_path; // 打开类型地址，miniprogram需要
	private Integer level; // 优先级
	private String status; // 状态，1开启，0停用
	
	private String game_name; // 推广游戏名称
	private String game_introduce; // 推广游戏介绍
	private String game_star; // 游戏星级指数（1到5）
	private String extra; // 额外参数
	private String placement; // 区分下展示位置
	private String placement_name; // 展示位置名称
	private String param; // 留用参数
	private String extraData; // 额外数据，头条用
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public Integer getImgtype() {
		return imgtype;
	}
	public void setImgtype(Integer imgtype) {
		this.imgtype = imgtype;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public String getPush_image() {
		return push_image;
	}
	public void setPush_image(String push_image) {
		this.push_image = push_image;
	}
	public String getPush_appid() {
		return push_appid;
	}
	public void setPush_appid(String push_appid) {
		this.push_appid = push_appid;
	}
	public String getOpen() {
		return open;
	}
	public void setOpen(String open) {
		this.open = open;
	}
	public Integer getLevel() {
		return level;
	}
	public void setLevel(Integer level) {
		this.level = level;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getGame_name() {
		return game_name;
	}
	public void setGame_name(String game_name) {
		this.game_name = game_name;
	}
	public String getGame_introduce() {
		return game_introduce;
	}
	public void setGame_introduce(String game_introduce) {
		this.game_introduce = game_introduce;
	}
	public String getGame_star() {
		return game_star;
	}
	public void setGame_star(String game_star) {
		this.game_star = game_star;
	}
	public String getOpen_path() {
		return open_path;
	}
	public void setOpen_path(String open_path) {
		this.open_path = open_path;
	}
	public String getExtra() {
		return extra;
	}
	public void setExtra(String extra) {
		this.extra = extra;
	}
	public String getPlacement() {
		return placement;
	}
	public void setPlacement(String placement) {
		this.placement = placement;
	}
	public String getParam() {
		return param;
	}
	public void setParam(String param) {
		this.param = param;
	}
	public String getPlist() {
		return plist;
	}
	public void setPlist(String plist) {
		this.plist = plist;
	}
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}
	public String getPlacement_name() {
		return placement_name;
	}
	public void setPlacement_name(String placement_name) {
		this.placement_name = placement_name;
	}
	public String getExtraData() {
		return extraData;
	}
	public void setExtraData(String extraData) {
		this.extraData = extraData;
	}
	
}
