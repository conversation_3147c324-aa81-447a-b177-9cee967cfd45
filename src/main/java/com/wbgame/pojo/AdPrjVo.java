package com.wbgame.pojo;

import java.io.Serializable;

/**
 * 广告开通项目配置
 * <AUTHOR>
 *
 */
public class AdPrjVo implements Serializable {
	 private String prjmid     ;//VARCHAR2(50) not null,
	 private String ad_sid     ;//VARCHAR2(50) not null,
	 private String ad_pos     ;//VARCHAR2(50),
	 private String occur_min  ;//NUMBER,
	 private String occur_max  ;//NUMBER,
	 private String statu      ;//NUMBER,
	 private String createtime ;//DATE
	 private String cityids;
	 private String qz_lelve;
	 private String per;
	 private String scale_value;//NUMBER
	 
	 public String getCityids() {
		return cityids;
	}

	public void setCityids(String cityids) {
		this.cityids = cityids;
	}

	public String getQz_lelve() {
		return qz_lelve;
	}

	public void setQz_lelve(String qzLelve) {
		qz_lelve = qzLelve;
	}


	public String getPrjmid() {
		return prjmid;
	}

	public void setPrjmid(String prjmid) {
		this.prjmid = prjmid;
	}

	public String getAd_sid() {
		return ad_sid;
	}

	public void setAd_sid(String adSid) {
		ad_sid = adSid;
	}

	public String getAd_pos() {
		return ad_pos;
	}

	public void setAd_pos(String adPos) {
		ad_pos = adPos;
	}

	public String getOccur_min() {
		return occur_min;
	}

	public void setOccur_min(String occurMin) {
		occur_min = occurMin;
	}

	public String getOccur_max() {
		return occur_max;
	}

	public void setOccur_max(String occurMax) {
		occur_max = occurMax;
	}

	public String getStatu() {
		return statu;
	}

	public void setStatu(String statu) {
		this.statu = statu;
	}

	public String getCreatetime() {
		return createtime;
	}

	public void setCreatetime(String createtime) {
		this.createtime = createtime;
	}

	public String getPer() {
		return per;
	}

	public void setPer(String per) {
		this.per = per;
	}

	public String getScale_value() {
		return scale_value;
	}

	public void setScale_value(String scale_value) {
		this.scale_value = scale_value;
	}
	
}
