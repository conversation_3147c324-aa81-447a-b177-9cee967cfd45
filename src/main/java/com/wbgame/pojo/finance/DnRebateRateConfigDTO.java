package com.wbgame.pojo.finance;

import com.wbgame.common.Constant;
import com.wbgame.common.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.groups.Default;
import java.util.Date;

@ApiModel("返点率配置")
public class DnRebateRateConfigDTO {

    @ApiModelProperty("返点率")
    @Pattern(regexp = Constant.REGEX_0_100, message = "请输入0-100以内数字(保留两位小数)", groups = {Default.class, UpdateGroup.class})
    private String rebateRate;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改时间")
    private Date updateTime;
    @ApiModelProperty("创建人")
    private String createUser;
    @ApiModelProperty("修改人")
    private String updateUser;
    @NotBlank(message = "投放媒体不能为空", groups = {Default.class, UpdateGroup.class})
    @ApiModelProperty("投放媒体")
    private String media;
    @NotBlank(message = "一级代理商不能为空", groups = {Default.class, UpdateGroup.class})
    @ApiModelProperty("一级代理商")
    private String primaryAgent;
    @Pattern(regexp = Constant.DATE_YYYY_MM, message = "月份错误", groups = {Default.class, UpdateGroup.class})
    @ApiModelProperty("月份")
    private String month;
    @ApiModelProperty("应用分类")
    private Integer app_category;

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media == null ? null : media.trim();
    }

    public String getPrimaryAgent() {
        return primaryAgent;
    }

    public void setPrimaryAgent(String primaryAgent) {
        this.primaryAgent = primaryAgent == null ? null : primaryAgent.trim();
    }

    public String getRebateRate() {
        return rebateRate;
    }

    public void setRebateRate(String rebateRate) {
        this.rebateRate = rebateRate == null ? null : rebateRate.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Integer getApp_category() {
        return app_category;
    }

    public void setApp_category(Integer app_category) {
        this.app_category = app_category;
    }
}