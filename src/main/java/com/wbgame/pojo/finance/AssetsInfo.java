package com.wbgame.pojo.finance;

/**
 * 资产信息实体
 */
public class AssetsInfo {

    private String oldassetsid;
    private String assetsid;
    private String assetsname;
    private String assetsnote;
    private String assetsuser;
    //设备状态    ”1:在用”、”2:在库”、”3:闲置”、”0:待报废”、”4:维修中”
    private String assetsstatus;
    private String assetsusage;
    private String company;
    private String buytime;
    private String assetstype;
    private int assetssum;
    private String userdepartment;
    private String addtime;
    private String createtime;
    private String atype;
    private String aunit;
    private Double amoney;
    
	public String getAunit() {
        return aunit;
    }

    public void setAunit(String aunit) {
        this.aunit = aunit;
    }

    public Double getAmoney() {
        return amoney;
    }

    public void setAmoney(Double amoney) {
        this.amoney = amoney;
    }

    public String getAtype() {
        return atype;
    }

    public void setAtype(String atype) {
        this.atype = atype;
    }

    public String getAddtime() {
        return addtime;
    }

    public void setAddtime(String addtime) {
        this.addtime = addtime;
    }

    public String getOldassetsid() {
        return oldassetsid;
    }

    public void setOldassetsid(String oldassetsid) {
        this.oldassetsid = oldassetsid;
    }

    public String getAssetsid() {
        return assetsid;
    }

    public void setAssetsid(String assetsid) {
        this.assetsid = assetsid;
    }

    public String getAssetsname() {
        return assetsname;
    }

    public void setAssetsname(String assetsname) {
        this.assetsname = assetsname;
    }

    public String getAssetsnote() {
        return assetsnote;
    }

    public void setAssetsnote(String assetsnote) {
        this.assetsnote = assetsnote;
    }

    public String getAssetsuser() {
        return assetsuser;
    }

    public void setAssetsuser(String assetsuser) {
        this.assetsuser = assetsuser;
    }

    public String getAssetsstatus() {
        return assetsstatus;
    }

    public void setAssetsstatus(String assetsstatus) {
        this.assetsstatus = assetsstatus;
    }

    public String getAssetsusage() {
        return assetsusage;
    }

    public void setAssetsusage(String assetsusage) {
        this.assetsusage = assetsusage;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getBuytime() {
        return buytime;
    }

    public void setBuytime(String buytime) {
        this.buytime = buytime;
    }

    public String getAssetstype() {
        return assetstype;
    }

    public void setAssetstype(String assetstype) {
        this.assetstype = assetstype;
    }

    public int getAssetssum() {
        return assetssum;
    }

    public void setAssetssum(int assetssum) {
        this.assetssum = assetssum;
    }

    public String getUserdepartment() {
        return userdepartment;
    }

    public void setUserdepartment(String userdepartment) {
        this.userdepartment = userdepartment;
    }

    public String getCreatetime() {
        return createtime;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }
}