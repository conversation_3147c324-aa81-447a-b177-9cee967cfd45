package com.wbgame.pojo.finance;

/**
 * 财务系统-平台收入汇总
 *
 * <AUTHOR>
 * @date: 2020年8月18日
 */
public class FinancePlfIncome {

    /**
     * 年
     */
    private String year;

    /**
     * 月
     */
    private String month;

    /**
     * 项目
     */
    private String project;

    /**
     * 开票公司名称
     */
    private String outcompany;

    /**
     * 渠道名称
     */
    private String cname;

    /**
     * 收款公司
     */
    private String incompany;

    /**
     * 收入来源
     */
    private String income;

    /**
     * 游戏名称
     */
    private String appid;

    /**
     * 美金汇率
     */
    private String dollarRate;

    /**
     * 美金后台数据
     */
    private String dollar;

    /**
     * 美金结算金额
     */
    private String dollarSettlement;

    /**
     * 后台数据
     */
    private String backData;

    /**
     * 预计收入
     */
    private String predictingIncome;

    /**
     * 结算计算
     */
    private String settlementAmount;

    /**
     * 业务提供美金数据
     */
    private String dollarData;

    /**
     * 业务提供预估数据
     */
    private String predictingData;

    /**
     * 差额
     */
    private String differences;

    /**
     * 差额情况说明
     */
    private String differenceNote;

    /**
     * 占比
     */
    private String proportion;

    /**
     * 同比涨幅
     */
    private String increase;

    public String getOutcompany() {
        return outcompany;
    }

    public void setOutcompany(String outcompany) {
        this.outcompany = outcompany == null ? null : outcompany.trim();
    }

    public String getIncompany() {
        return incompany;
    }

    public void setIncompany(String incompany) {
        this.incompany = incompany == null ? null : incompany.trim();
    }

    public String getIncome() {
        return income;
    }

    public void setIncome(String income) {
        this.income = income == null ? null : income.trim();
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid == null ? null : appid.trim();
    }

    public String getDollarRate() {
        return dollarRate;
    }

    public void setDollarRate(String dollarRate) {
        this.dollarRate = dollarRate == null ? null : dollarRate.trim();
    }

    public String getDollar() {
        return dollar;
    }

    public void setDollar(String dollar) {
        this.dollar = dollar == null ? null : dollar.trim();
    }

    public String getDollarSettlement() {
        return dollarSettlement;
    }

    public void setDollarSettlement(String dollarSettlement) {
        this.dollarSettlement = dollarSettlement == null ? null : dollarSettlement.trim();
    }

    public String getBackData() {
        return backData;
    }

    public void setBackData(String backData) {
        this.backData = backData == null ? null : backData.trim();
    }

    public String getPredictingIncome() {
        return predictingIncome;
    }

    public void setPredictingIncome(String predictingIncome) {
        this.predictingIncome = predictingIncome == null ? null : predictingIncome.trim();
    }

    public String getSettlementAmount() {
        return settlementAmount;
    }

    public void setSettlementAmount(String settlementAmount) {
        this.settlementAmount = settlementAmount == null ? null : settlementAmount.trim();
    }

    public String getDollarData() {
        return dollarData;
    }

    public void setDollarData(String dollarData) {
        this.dollarData = dollarData == null ? null : dollarData.trim();
    }

    public String getPredictingData() {
        return predictingData;
    }

    public void setPredictingData(String predictingData) {
        this.predictingData = predictingData == null ? null : predictingData.trim();
    }

    public String getDifferences() {
        return differences;
    }

    public void setDifferences(String differences) {
        this.differences = differences == null ? null : differences.trim();
    }

    public String getDifferenceNote() {
        return differenceNote;
    }

    public void setDifferenceNote(String differenceNote) {
        this.differenceNote = differenceNote == null ? null : differenceNote.trim();
    }

    public String getProportion() {
        return proportion;
    }

    public void setProportion(String proportion) {
        this.proportion = proportion == null ? null : proportion.trim();
    }

    public String getIncrease() {
        return increase;
    }

    public void setIncrease(String increase) {
        this.increase = increase == null ? null : increase.trim();
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getCname() {
        return cname;
    }

    public void setCname(String cname) {
        this.cname = cname;
    }
}