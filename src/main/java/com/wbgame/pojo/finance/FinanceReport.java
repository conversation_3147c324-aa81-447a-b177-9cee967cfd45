package com.wbgame.pojo.finance;

public class FinanceReport {

    private Integer id;
    // 现金花费
    private Double cash_cost;
    // 总花费
    private Double cost;
    // 赔付花费
    private Double reward_cost;
    // 现金花费 返点
    private Double cash_cost_rebate;
    // 总花费 返点
    private Double cost_rebate;
    // 赔付花费 返点
    private Double reward_cost_rebate;
    // 赔付到账金额
    private Double income;
    // 总转入
    private Double transfer_in;

    // 2021-09-15 新增内容 年返季返消耗 && 年返季返存入
    // 框返消耗
    private Double contract_rebate_real_charged;
    // 框返 消耗返点
    private Double contract_rebate_real_charged_rebate;

    private Double contract_rebate_real_recharged;
    // 日期
    private String day;
    // 平台
    private String ad_platform;
    // 账号
    private String account;
    // 账号备注
    private String accountRemark;
    // 应用  废弃
    private String app;
    // 创建时间
    private String createTime;
    // 现金余额
    private Double cash_balance;
    // 非现金余额
    private Double not_cash_balance;
    // 置换消耗（华为）
    private Double exchange_cost;
    // 耀星消耗（华为）
    private Double star_cost;
    // 返利金消耗（华为）
    private Double rebate_cost;
    // 赠送金消耗（华为）
    private Double gift_cost;
    // 二级代理商
    private String agent;
    // 投放人员
    private String putUser;
    // 新增
    private Integer installs;
    // 数值type
    private String type;
    // 总非现金消耗
    private Double virtualSpend;
    // 总虚非现金消耗-返点
    private Double virtualSpend_rebate;
    // 国内报表消耗
    private Double spend;
    // 国内报表消耗-返点
    private Double spend_rebate;
    // 返点
    private Double rebate;
    // 字符类型
    private String types;
    // 媒体
    private String media;
    //商店虚拟金
    private Double appstore_cost;
    //非商店虚拟金
    private Double feed_cost;
    //联盟虚拟金
    private Double league_cost;
    //通用虚拟金
    private Double general_cost;


    public FinanceReport sumIncome(FinanceReport report) {
        this.income += report.getIncome();
        this.transfer_in = report.getTransfer_in();
        return this;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Double getCash_cost() {
        return cash_cost;
    }

    public void setCash_cost(Double cash_cost) {
        this.cash_cost = cash_cost;
    }

    public Double getCost() {
        return cost;
    }

    public void setCost(Double cost) {
        this.cost = cost;
    }

    public Double getReward_cost() {
        return reward_cost;
    }

    public void setReward_cost(Double reward_cost) {
        this.reward_cost = reward_cost;
    }

    public Double getCash_cost_rebate() {
        return cash_cost_rebate;
    }

    public void setCash_cost_rebate(Double cash_cost_rebate) {
        this.cash_cost_rebate = cash_cost_rebate;
    }

    public Double getCost_rebate() {
        return cost_rebate;
    }

    public void setCost_rebate(Double cost_rebate) {
        this.cost_rebate = cost_rebate;
    }

    public Double getReward_cost_rebate() {
        return reward_cost_rebate;
    }

    public void setReward_cost_rebate(Double reward_cost_rebate) {
        this.reward_cost_rebate = reward_cost_rebate;
    }

    public Double getIncome() {
        return income;
    }

    public void setIncome(Double income) {
        this.income = income;
    }

    public Double getTransfer_in() {
        return transfer_in;
    }

    public void setTransfer_in(Double transfer_in) {
        this.transfer_in = transfer_in;
    }

    public Double getContract_rebate_real_charged() {
        return contract_rebate_real_charged;
    }

    public void setContract_rebate_real_charged(Double contract_rebate_real_charged) {
        this.contract_rebate_real_charged = contract_rebate_real_charged;
    }

    public Double getContract_rebate_real_charged_rebate() {
        return contract_rebate_real_charged_rebate;
    }

    public void setContract_rebate_real_charged_rebate(Double contract_rebate_real_charged_rebate) {
        this.contract_rebate_real_charged_rebate = contract_rebate_real_charged_rebate;
    }

    public Double getContract_rebate_real_recharged() {
        return contract_rebate_real_recharged;
    }

    public void setContract_rebate_real_recharged(Double contract_rebate_real_recharged) {
        this.contract_rebate_real_recharged = contract_rebate_real_recharged;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getAd_platform() {
        return ad_platform;
    }

    public void setAd_platform(String ad_platform) {
        this.ad_platform = ad_platform;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAccountRemark() {
        return accountRemark;
    }

    public void setAccountRemark(String accountRemark) {
        this.accountRemark = accountRemark;
    }

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Double getCash_balance() {
        return cash_balance;
    }

    public void setCash_balance(Double cash_balance) {
        this.cash_balance = cash_balance;
    }

    public Double getNot_cash_balance() {
        return not_cash_balance;
    }

    public void setNot_cash_balance(Double not_cash_balance) {
        this.not_cash_balance = not_cash_balance;
    }

    public Double getExchange_cost() {
        return exchange_cost;
    }

    public void setExchange_cost(Double exchange_cost) {
        this.exchange_cost = exchange_cost;
    }

    public Double getStar_cost() {
        return star_cost;
    }

    public void setStar_cost(Double star_cost) {
        this.star_cost = star_cost;
    }

    public Double getRebate_cost() {
        return rebate_cost;
    }

    public void setRebate_cost(Double rebate_cost) {
        this.rebate_cost = rebate_cost;
    }

    public Double getGift_cost() {
        return gift_cost;
    }

    public void setGift_cost(Double gift_cost) {
        this.gift_cost = gift_cost;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getPutUser() {
        return putUser;
    }

    public void setPutUser(String putUser) {
        this.putUser = putUser;
    }

    public Integer getInstalls() {
        return installs;
    }

    public void setInstalls(Integer installs) {
        this.installs = installs;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double getVirtualSpend() {
        return virtualSpend;
    }

    public void setVirtualSpend(Double virtualSpend) {
        this.virtualSpend = virtualSpend;
    }

    public Double getVirtualSpend_rebate() {
        return virtualSpend_rebate;
    }

    public void setVirtualSpend_rebate(Double virtualSpend_rebate) {
        this.virtualSpend_rebate = virtualSpend_rebate;
    }

    public Double getSpend() {
        return spend;
    }

    public void setSpend(Double spend) {
        this.spend = spend;
    }

    public Double getSpend_rebate() {
        return spend_rebate;
    }

    public void setSpend_rebate(Double spend_rebate) {
        this.spend_rebate = spend_rebate;
    }

    public Double getRebate() {
        return rebate;
    }

    public void setRebate(Double rebate) {
        this.rebate = rebate;
    }

    public String getTypes() {
        return types;
    }

    public void setTypes(String types) {
        this.types = types;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public Double getAppstore_cost() {
        return appstore_cost;
    }

    public void setAppstore_cost(Double appstore_cost) {
        this.appstore_cost = appstore_cost;
    }

    public Double getFeed_cost() {
        return feed_cost;
    }

    public void setFeed_cost(Double feed_cost) {
        this.feed_cost = feed_cost;
    }

    public Double getLeague_cost() {
        return league_cost;
    }

    public void setLeague_cost(Double league_cost) {
        this.league_cost = league_cost;
    }

    public Double getGeneral_cost() {
        return general_cost;
    }

    public void setGeneral_cost(Double general_cost) {
        this.general_cost = general_cost;
    }
}
