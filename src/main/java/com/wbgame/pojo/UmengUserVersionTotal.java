package com.wbgame.pojo;

import java.util.Date;

/**
    * 友盟版本号分布
    */
public class UmengUserVersionTotal {
    /**
    * appid
    */
    private Integer appid;

    /**
    * 日期
    */
    private Date tdate;

    /**
    * appkey
    */
    private String appKey;

    /**
    * 活跃
    */
    private Integer actNum;

    /**
    * 新增
    */
    private Integer addNum;

    /**
    * 启动次数
    */
    private Integer startNum;

    /**
    * 版本号
    */
    private String appVersion;

    public Integer getAppid() {
        return appid;
    }

    public void setAppid(Integer appid) {
        this.appid = appid;
    }

    public Date getTdate() {
        return tdate;
    }

    public void setTdate(Date tdate) {
        this.tdate = tdate;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public Integer getActNum() {
        return actNum;
    }

    public void setActNum(Integer actNum) {
        this.actNum = actNum;
    }

    public Integer getAddNum() {
        return addNum;
    }

    public void setAddNum(Integer addNum) {
        this.addNum = addNum;
    }

    public Integer getStartNum() {
        return startNum;
    }

    public void setStartNum(Integer startNum) {
        this.startNum = startNum;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }
}