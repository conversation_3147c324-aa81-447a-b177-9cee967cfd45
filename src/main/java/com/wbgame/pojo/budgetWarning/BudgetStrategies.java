package com.wbgame.pojo.budgetWarning;

import com.wbgame.common.strategyEnum.Relation;
import com.wbgame.common.strategyEnum.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/1
 * @description
 **/
public class BudgetStrategies {
    private Relation relation;
    private List<Strategy> strategies;

    public BudgetStrategies() {
    }

    public BudgetStrategies(Relation relation, List<Strategy> strategies) {
        this.relation = relation;
        this.strategies = strategies;
    }

    public Relation getRelation() {
        return relation;
    }

    public void setRelation(Relation relation) {
        this.relation = relation;
    }

    public List<Strategy> getStrategies() {
        return strategies;
    }

    public void setStrategies(List<Strategy> strategies) {
        this.strategies = strategies;
    }

    @Builder
    @AllArgsConstructor
    public static class Strategy {
        private Table table;
        private String field;
        private String compare;
        private String value;
        private String unity;

        public String getUnity() {
            return unity;
        }

        public void setUnity(String unity) {
            this.unity = unity;
        }

        public Table getTable() {
            return table;
        }

        public void setTable(Table table) {
            this.table = table;
        }

        public String getField() {
            return field;
        }

        public void setField(String field) {
            this.field = field;
        }

        public String getCompare() {
            return compare;
        }

        public void setCompare(String compare) {
            this.compare = compare;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
