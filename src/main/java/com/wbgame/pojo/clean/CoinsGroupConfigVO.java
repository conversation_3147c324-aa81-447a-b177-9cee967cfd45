package com.wbgame.pojo.clean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("计步数据配置")
public class CoinsGroupConfigVO {



    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("分组")
    private String group;

    @ApiModelProperty("新人红包")
    private Double newUserAward;

    @ApiModelProperty("是否开启解锁条件")
    private String isFreeze;

    @ApiModelProperty("解锁条件-活跃天数")
    private Integer activeDays;

    @ApiModelProperty("解锁条件-步数兑换")
    private Integer convertStep;

    @ApiModelProperty("解锁条件-广告次数")
    private Integer lookAdNum;

    @ApiModelProperty("审核提现-天数")
    private Integer auditDays;

    @ApiModelProperty("审核提现-步数")
    private Integer auditConvertStep;

    @ApiModelProperty("审核提现-广告次数")
    private Integer auditLookAdNum;

    @ApiModelProperty("金额最小值")
    private Double lowPrice;

    @ApiModelProperty("金额最大值")
    private Double highPrice;

    @ApiModelProperty("p配置")
    private Integer convertPriceUnit;

    @ApiModelProperty("提现金额范围分组")
    private String withdrawMoneys;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group == null ? null : group.trim();
    }

    public Double getNewUserAward() {
        return newUserAward;
    }

    public void setNewUserAward(Double newUserAward) {
        this.newUserAward = newUserAward;
    }

    public String getIsFreeze() {
        return isFreeze;
    }

    public void setIsFreeze(String isFreeze) {
        this.isFreeze = isFreeze == null ? null : isFreeze.trim();
    }

    public Integer getActiveDays() {
        return activeDays;
    }

    public void setActiveDays(Integer activeDays) {
        this.activeDays = activeDays;
    }

    public Integer getConvertStep() {
        return convertStep;
    }

    public void setConvertStep(Integer convertStep) {
        this.convertStep = convertStep;
    }

    public Integer getLookAdNum() {
        return lookAdNum;
    }

    public void setLookAdNum(Integer lookAdNum) {
        this.lookAdNum = lookAdNum;
    }

    public Integer getAuditDays() {
        return auditDays;
    }

    public void setAuditDays(Integer auditDays) {
        this.auditDays = auditDays;
    }

    public Integer getAuditConvertStep() {
        return auditConvertStep;
    }

    public void setAuditConvertStep(Integer auditConvertStep) {
        this.auditConvertStep = auditConvertStep;
    }

    public Integer getAuditLookAdNum() {
        return auditLookAdNum;
    }

    public void setAuditLookAdNum(Integer auditLookAdNum) {
        this.auditLookAdNum = auditLookAdNum;
    }

    public Double getLowPrice() {
        return lowPrice;
    }

    public void setLowPrice(Double lowPrice) {
        this.lowPrice = lowPrice;
    }

    public Double getHighPrice() {
        return highPrice;
    }

    public void setHighPrice(Double highPrice) {
        this.highPrice = highPrice;
    }

    public Integer getConvertPriceUnit() {
        return convertPriceUnit;
    }

    public void setConvertPriceUnit(Integer convertPriceUnit) {
        this.convertPriceUnit = convertPriceUnit;
    }

    public String getWithdrawMoneys() {
        return withdrawMoneys;
    }

    public void setWithdrawMoneys(String withdrawMoneys) {
        this.withdrawMoneys = withdrawMoneys == null ? null : withdrawMoneys.trim();
    }
}