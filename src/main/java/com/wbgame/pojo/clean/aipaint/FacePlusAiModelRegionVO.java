package com.wbgame.pojo.clean.aipaint;

import com.wbgame.pojo.clean.toonstory.ToonModelRegionRel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("模板地区分类关联-")
public class FacePlusAiModelRegionVO {

    private Long id;

    @ApiModelProperty("封面id")
    private Long modelId;

    @ApiModelProperty("地区id")
    private Long areaId;

    private String modifyUser;

    @ApiModelProperty("地区名")
    private String areaName;

    private Long modifyTime;

    @ApiModelProperty("模板地区分类关联地区和分类集合")
    private List<ToonModelRegionRel> relList;

    private Integer sort;

    @ApiModelProperty("'模板名称'")
    private String modelName;
    @ApiModelProperty("'制作数'")
    private Integer makeTemplateCount;
    @ApiModelProperty("'转化会员数'")
    private Integer tplConvertVipOunt;
    @ApiModelProperty("'封面图片'")
    private String coverUrl;
    @ApiModelProperty("'模板属性 1 付费 2 激励 3 免费'")
    private Byte tempType;

    @ApiModelProperty("转换率")
    private String conversionRate;

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(String conversionRate) {
        this.conversionRate = conversionRate;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public Integer getMakeTemplateCount() {
        return makeTemplateCount;
    }

    public void setMakeTemplateCount(Integer makeTemplateCount) {
        this.makeTemplateCount = makeTemplateCount;
    }

    public Integer getTplConvertVipOunt() {
        return tplConvertVipOunt;
    }

    public void setTplConvertVipOunt(Integer tplConvertVipOunt) {
        this.tplConvertVipOunt = tplConvertVipOunt;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public Byte getTempType() {
        return tempType;
    }

    public void setTempType(Byte tempType) {
        this.tempType = tempType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getModelId() {
        return modelId;
    }

    public void setModelId(Long modelId) {
        this.modelId = modelId;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser == null ? null : modifyUser.trim();
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public List<ToonModelRegionRel> getRelList() {
        return relList;
    }

    public void setRelList(List<ToonModelRegionRel> relList) {
        this.relList = relList;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}