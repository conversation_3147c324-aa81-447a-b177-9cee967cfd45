package com.wbgame.pojo.clean;


import com.wbgame.common.GeneralReportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("用户红包变动记录DTO")
public class IdiomRedpackChangeDTO extends GeneralReportParam {

    private Long id;

    @ApiModelProperty("安卓id")
    private String androidId;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("appid")
    private String appId;

    @ApiModelProperty("prjid")
    private String prjid;

    @ApiModelProperty("变更类型 1 摇钱树 2 狂点领现金 3 摇一摇 4 飞翔宝箱 5 热气球 6 提现 7 完成任务 8 签到 9 新手 10 关卡固定 11 看视频得红包")
    private Byte changeType;

    @ApiModelProperty("收益值")
    private String income;

    @ApiModelProperty("收益后余额")
    private Integer changedRedpack;

    @ApiModelProperty("当次ecpm")
    private Integer currentEcpm;

    @ApiModelProperty("累计ecpm")
    private Integer epcmTotal;

    @ApiModelProperty("平均ecpm")
    private String epcmAvg;

    @ApiModelProperty("当前关卡")
    private Integer currentLevel;

    @ApiModelProperty("是否高价广告")
    private Byte isHighAd;

    @ApiModelProperty("任务序号")
    private Integer taskSn;

    @ApiModelProperty("提现标识")
    private String withdrawMark;

    @ApiModelProperty("创建时间")
    private Long createTime;

    private String selectGroup;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId == null ? null : androidId.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public String getPrjid() {
        return prjid;
    }

    public void setPrjid(String prjid) {
        this.prjid = prjid == null ? null : prjid.trim();
    }

    public Byte getChangeType() {
        return changeType;
    }

    public void setChangeType(Byte changeType) {
        this.changeType = changeType;
    }

    public String getIncome() {
        return income;
    }

    public void setIncome(String income) {
        this.income = income == null ? null : income.trim();
    }

    public Integer getChangedRedpack() {
        return changedRedpack;
    }

    public void setChangedRedpack(Integer changedRedpack) {
        this.changedRedpack = changedRedpack;
    }

    public Integer getCurrentEcpm() {
        return currentEcpm;
    }

    public void setCurrentEcpm(Integer currentEcpm) {
        this.currentEcpm = currentEcpm;
    }

    public Integer getEpcmTotal() {
        return epcmTotal;
    }

    public void setEpcmTotal(Integer epcmTotal) {
        this.epcmTotal = epcmTotal;
    }

    public String getEpcmAvg() {
        return epcmAvg;
    }

    public void setEpcmAvg(String epcmAvg) {
        this.epcmAvg = epcmAvg == null ? null : epcmAvg.trim();
    }

    public Integer getCurrentLevel() {
        return currentLevel;
    }

    public void setCurrentLevel(Integer currentLevel) {
        this.currentLevel = currentLevel;
    }

    public Byte getIsHighAd() {
        return isHighAd;
    }

    public void setIsHighAd(Byte isHighAd) {
        this.isHighAd = isHighAd;
    }

    public Integer getTaskSn() {
        return taskSn;
    }

    public void setTaskSn(Integer taskSn) {
        this.taskSn = taskSn;
    }

    public String getWithdrawMark() {
        return withdrawMark;
    }

    public void setWithdrawMark(String withdrawMark) {
        this.withdrawMark = withdrawMark == null ? null : withdrawMark.trim();
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getSelectGroup() {
        return selectGroup;
    }

    public void setSelectGroup(String selectGroup) {
        this.selectGroup = selectGroup;
    }
}