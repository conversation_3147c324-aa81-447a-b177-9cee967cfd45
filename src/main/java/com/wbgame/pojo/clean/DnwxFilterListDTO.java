package com.wbgame.pojo.clean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Pattern;
import java.util.List;

@ApiModel("数盟白名单配置")
public class DnwxFilterListDTO {

    /**
     * 主键id
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    @Pattern(regexp = "^\\d*$", message = "appid错误")
    private String appid;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String appName;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String topic;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private String prjid;

    /**
     *创建人
     */
    private String createUser;


    /**
     *修改人
     */
    private String updateUser;

    private List<Long> idList;

    private String token;

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic == null ? null : topic.trim();
    }

    public String getPrjid() {
        return prjid;
    }

    public void setPrjid(String prjid) {
        this.prjid = prjid == null ? null : prjid.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }
}