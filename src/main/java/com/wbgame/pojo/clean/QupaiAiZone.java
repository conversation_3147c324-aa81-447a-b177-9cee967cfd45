package com.wbgame.pojo.clean;

/**
 * 
 * <AUTHOR>
 * @date: 2021年12月13日
 */
public class QupaiAiZone {

	private String id;

	private Integer sort;//排序
	private String aiTitle;//标题
	private String aiImg;//图片
	private String aiVideo;//视频
	private String isVip;//是否是会员
	private String aiType;//分组
	private String isHot;//是否热门
	private String faceId;//面容id
	private String mid;//素材id
	private String foreign;//是否是国外0 国内，1 国外
	private String actId;//活动id
	
	private String createTime;
	private String modifyTime;
	private String createUser;
	private String modifyUser;
	private String status;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public String getAiTitle() {
		return aiTitle;
	}
	public void setAiTitle(String aiTitle) {
		this.aiTitle = aiTitle;
	}
	public String getAiImg() {
		return aiImg;
	}
	public void setAiImg(String aiImg) {
		this.aiImg = aiImg;
	}
	public String getAiVideo() {
		return aiVideo;
	}
	public void setAiVideo(String aiVideo) {
		this.aiVideo = aiVideo;
	}
	public String getIsVip() {
		return isVip;
	}
	public void setIsVip(String isVip) {
		this.isVip = isVip;
	}
	public String getAiType() {
		return aiType;
	}
	public void setAiType(String aiType) {
		this.aiType = aiType;
	}
	public String getIsHot() {
		return isHot;
	}
	public void setIsHot(String isHot) {
		this.isHot = isHot;
	}
	public String getFaceId() {
		return faceId;
	}
	public void setFaceId(String faceId) {
		this.faceId = faceId;
	}
	public String getMid() {
		return mid;
	}
	public void setMid(String mid) {
		this.mid = mid;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public String getModifyUser() {
		return modifyUser;
	}
	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getForeign() {
		return foreign;
	}
	public void setForeign(String foreign) {
		this.foreign = foreign;
	}
	public String getActId() {
		return actId;
	}
	public void setActId(String actId) {
		this.actId = actId;
	}
}
