package com.wbgame.pojo.clean;

public class SuperSafeControlV3 {
    private Integer id;

    private String appid;

    private String cha;

    private String prjid;

    private String createTime;

    private String enable;

    private String queryInvTime;

    private String modifyTime;

    private String modifyUser;

    private String status;

    private String createUser;

    private String ruleOutList;

    private String delayRank;

    public String getRuleOutList() {
        return ruleOutList;
    }

    public void setRuleOutList(String ruleOutList) {
        this.ruleOutList = ruleOutList;
    }

    public String getDelayRank() {
        return delayRank;
    }

    public void setDelayRank(String delayRank) {
        this.delayRank = delayRank;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid == null ? null : appid.trim();
    }

    public String getCha() {
        return cha;
    }

    public void setCha(String cha) {
        this.cha = cha == null ? null : cha.trim();
    }

    public String getPrjid() {
        return prjid;
    }

    public void setPrjid(String prjid) {
        this.prjid = prjid == null ? null : prjid.trim();
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime == null ? null : createTime.trim();
    }

    public String getEnable() {
        return enable;
    }

    public void setEnable(String enable) {
        this.enable = enable == null ? null : enable.trim();
    }

    public String getQueryInvTime() {
        return queryInvTime;
    }

    public void setQueryInvTime(String queryInvTime) {
        this.queryInvTime = queryInvTime == null ? null : queryInvTime.trim();
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime == null ? null : modifyTime.trim();
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser == null ? null : modifyUser.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }
}