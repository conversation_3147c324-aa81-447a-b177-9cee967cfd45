package com.wbgame.pojo.clean;

import com.wbgame.common.GenericQueryParameters;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("ios清理用户分组")
public class UserGroupConfig extends GenericQueryParameters {

    private Long id;

    @ApiModelProperty("产品ID")
    private String appId;

    @ApiModelProperty("项目ID")
    private String prjId;

    @ApiModelProperty("渠道")
    private String cha;

    @ApiModelProperty("国家")
    private String country;

    @ApiModelProperty("状态 0 关闭 1 开启")
    private Byte status;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("修改人")
    private String modifyUser;

    @ApiModelProperty("修改时间")
    private String modifyTime;

    @ApiModelProperty("分组详情")
    private List<UserGroupDetail> userGroupDetailList;

    @ApiModelProperty("国家集合(查询使用)")
    private List<String> countryList;

    @ApiModelProperty("用户分组配置表-主键ID")
    private Long configId;

    @ApiModelProperty("分组名称")
    private String name;

    @ApiModelProperty("分组比例")
    private Integer rate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getPrjId() {
        return prjId;
    }

    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }

    public String getCha() {
        return cha;
    }

    public void setCha(String cha) {
        this.cha = cha;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public List<UserGroupDetail> getUserGroupDetailList() {
        return userGroupDetailList;
    }

    public void setUserGroupDetailList(List<UserGroupDetail> userGroupDetailList) {
        this.userGroupDetailList = userGroupDetailList;
    }

    public List<String> getCountryList() {
        return countryList;
    }

    public void setCountryList(List<String> countryList) {
        this.countryList = countryList;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getRate() {
        return rate;
    }

    public void setRate(Integer rate) {
        this.rate = rate;
    }
}