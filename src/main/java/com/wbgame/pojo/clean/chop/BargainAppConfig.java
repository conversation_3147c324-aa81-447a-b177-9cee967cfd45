package com.wbgame.pojo.clean.chop;

import com.wbgame.common.GeneralReportParam;
import com.wbgame.common.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.math.BigDecimal;
import java.util.List;

@ApiModel("产品参数配置")
public class BargainAppConfig extends GeneralReportParam {

    @ApiModelProperty("自增id")
    @NotNull(message = "id错误", groups = {UpdateGroup.class})
    private Integer id;

    @ApiModelProperty("appid")
    @NotBlank(groups = {Default.class, UpdateGroup.class}, message = "appid不能为空")
    private String appid;

    @ApiModelProperty("项目id")
    @NotBlank(groups = {Default.class, UpdateGroup.class}, message = "项目id不能为空")
    private String prjid;

    @ApiModelProperty("投放账号")
    private String account;

    @ApiModelProperty("预估新增")
    private Integer predictAdd;

    @ApiModelProperty("cost")
    private Integer cost;

    @ApiModelProperty("关键行为达成率 单位%")
    private Integer behaviorRate;

    @ApiModelProperty("[{\"productId\":1,\"productTotalPrice\":1000,\"productShowPrice\":1000,\"ratio\":12,\"levelId\":1}]productId 商品id，productTotalPrice 商品总价，productShowPrice 显示价，ratio 比例，levelId 关卡配置id")
    private Object proCfg;

    @ApiModelProperty("G数值，默认：1，取值范围（0,1]")
    private BigDecimal deductionCoefficient;

    @ApiModelProperty("切换判断系数：J数值，默认：0.01")
    private BigDecimal switchingCoefficient;

    @ApiModelProperty("COST扣减系数：G`数值，默认：0.08，取值范围在(0,1)")
    @Range(max = 1, message = "扣减系数错误", groups = {Default.class, UpdateGroup.class})
    private BigDecimal costCoefficient;

    @ApiModelProperty("刀值比例")
    private Integer rc;

    @ApiModelProperty("初始ECPM_Video：手动配置，默认：25000")
    private Integer defaultEcpmVideo;

    @ApiModelProperty("初始ECPM_Plaque:手动配置，默认：15000")
    private Integer defaultEcpmPlaque;

    @ApiModelProperty("初始ECPM_Msg:手动配置；默认：3000")
    private Integer defaultEcpmMsg;

    @ApiModelProperty("初始数值调用次数：手动配置，默认：2")
    private Integer defaultNum;

    @ApiModelProperty("产品初始成本：（初始ECPM_Video+初始ECPM_Plaque+初始ECPM_Msg）*初始数值调用次数/100000")
    private String initCost;

    @ApiModelProperty("均摊买量成本：（预估新增*COST*关键行为达成率）/(预估新增*关键行为达成率)")
    private BigDecimal avgPurchaseCost;

    @ApiModelProperty("均摊商品价格：(全部商品总成本)/(预估新增*关键行为达成率)")
    private BigDecimal avgCommodityPrice;

    @ApiModelProperty("商品集合")
    @NotEmpty(message = "商品不能为空", groups = {Default.class, UpdateGroup.class})
    private List<Integer> productIdList;

    @ApiModelProperty("游戏id集合")
    @NotEmpty(message = "游戏id集合不能为空", groups = {Default.class, UpdateGroup.class})
    private List<Integer> levelIdList;

    @ApiModelProperty("'状态 0:关闭，1:开启'")
    private Byte status;

    @ApiModelProperty("'创建人'")
    private String createUser;

    @ApiModelProperty("'修改人'")
    private String updateUser;

    @ApiModelProperty("'创建时间'")
    private String createTime;

    @ApiModelProperty("'修改时间'")
    private String updateTime;

    @ApiModelProperty("'banner图'")
    private String banner;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid == null ? null : appid.trim();
    }

    public String getPrjid() {
        return prjid;
    }

    public void setPrjid(String prjid) {
        this.prjid = prjid == null ? null : prjid.trim();
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account == null ? null : account.trim();
    }

    public Integer getPredictAdd() {
        return predictAdd;
    }

    public void setPredictAdd(Integer predictAdd) {
        this.predictAdd = predictAdd;
    }

    public Integer getCost() {
        return cost;
    }

    public void setCost(Integer cost) {
        this.cost = cost;
    }

    public Integer getBehaviorRate() {
        return behaviorRate;
    }

    public void setBehaviorRate(Integer behaviorRate) {
        this.behaviorRate = behaviorRate;
    }

    public BigDecimal getDeductionCoefficient() {
        return deductionCoefficient;
    }

    public void setDeductionCoefficient(BigDecimal deductionCoefficient) {
        this.deductionCoefficient = deductionCoefficient;
    }

    public BigDecimal getSwitchingCoefficient() {
        return switchingCoefficient;
    }

    public void setSwitchingCoefficient(BigDecimal switchingCoefficient) {
        this.switchingCoefficient = switchingCoefficient;
    }

    public BigDecimal getCostCoefficient() {
        return costCoefficient;
    }

    public void setCostCoefficient(BigDecimal costCoefficient) {
        this.costCoefficient = costCoefficient;
    }

    public Integer getRc() {
        return rc;
    }

    public void setRc(Integer rc) {
        this.rc = rc;
    }

    public Integer getDefaultEcpmVideo() {
        return defaultEcpmVideo;
    }

    public void setDefaultEcpmVideo(Integer defaultEcpmVideo) {
        this.defaultEcpmVideo = defaultEcpmVideo;
    }

    public Integer getDefaultEcpmPlaque() {
        return defaultEcpmPlaque;
    }

    public void setDefaultEcpmPlaque(Integer defaultEcpmPlaque) {
        this.defaultEcpmPlaque = defaultEcpmPlaque;
    }

    public Integer getDefaultEcpmMsg() {
        return defaultEcpmMsg;
    }

    public void setDefaultEcpmMsg(Integer defaultEcpmMsg) {
        this.defaultEcpmMsg = defaultEcpmMsg;
    }

    public Integer getDefaultNum() {
        return defaultNum;
    }

    public void setDefaultNum(Integer defaultNum) {
        this.defaultNum = defaultNum;
    }

    public String getInitCost() {
        return initCost;
    }

    public void setInitCost(String initCost) {
        this.initCost = initCost == null ? null : initCost.trim();
    }

    public BigDecimal getAvgPurchaseCost() {
        return avgPurchaseCost;
    }

    public void setAvgPurchaseCost(BigDecimal avgPurchaseCost) {
        this.avgPurchaseCost = avgPurchaseCost;
    }

    public BigDecimal getAvgCommodityPrice() {
        return avgCommodityPrice;
    }

    public void setAvgCommodityPrice(BigDecimal avgCommodityPrice) {
        this.avgCommodityPrice = avgCommodityPrice;
    }

    public Object getProCfg() {
        return proCfg;
    }

    public void setProCfg(Object proCfg) {
        this.proCfg = proCfg;
    }

    public List<Integer> getProductIdList() {
        return productIdList;
    }

    public void setProductIdList(List<Integer> productIdList) {
        this.productIdList = productIdList;
    }

    public List<Integer> getLevelIdList() {
        return levelIdList;
    }

    public void setLevelIdList(List<Integer> levelIdList) {
        this.levelIdList = levelIdList;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }
}