package com.wbgame.pojo.clean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 深度链接配置
 */
@ApiModel("深度链接配置VO")
public class FaceCreateConfigVO {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 推广渠道
     */
    @ApiModelProperty("推广渠道")
    private String promotesCha;

    /**
     * 创意id
     */
    @ApiModelProperty("创意id")
    private String createId;

    /**
     * 模板id
     */
    @ApiModelProperty("模板id")
    private Integer modelId;

    @ApiModelProperty("模板名称")
    private String tempTitle;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("修改时间")
    private String modifyTime;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("修改人")
    private String modifyUser;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String status;

    /**
     * 模板类型
     */
    @ApiModelProperty("模板类型 id")
    private Integer modelType;

    @ApiModelProperty("模板类型")
    private String modelTypeName;
    @ApiModelProperty("模板类型名称")
    private String typeName;


    @ApiModelProperty("封面图URL")
    private String tempUrl;
    @ApiModelProperty("样片地址")
    private String videoUrl;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPromotesCha() {
        return promotesCha;
    }

    public void setPromotesCha(String promotesCha) {
        this.promotesCha = promotesCha;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public Integer getModelId() {
        return modelId;
    }

    public void setModelId(Integer modelId) {
        this.modelId = modelId;
    }

    public String getTempTitle() {
        return tempTitle;
    }

    public void setTempTitle(String tempTitle) {
        this.tempTitle = tempTitle;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getModelType() {
        return modelType;
    }

    public void setModelType(Integer modelType) {
        this.modelType = modelType;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getModelTypeName() {
        return modelTypeName;
    }

    public void setModelTypeName(String modelTypeName) {
        this.modelTypeName = modelTypeName;
    }

    public String getTempUrl() {
        return tempUrl;
    }

    public void setTempUrl(String tempUrl) {
        this.tempUrl = tempUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

}