package com.wbgame.pojo.clean.toonstory;

import com.wbgame.common.GenericQueryParameters;
import com.wbgame.common.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.util.List;

@ApiModel("运营地区DTO")
public class OperatingAreaDTO extends GenericQueryParameters {

    private Long id;

    @ApiModelProperty(value = "产品ID")
//    @NotBlank(message = "appid不能为空", groups = {Default.class, UpdateGroup.class})
    private String appId;

    @ApiModelProperty(value = "运营地区名称")
    @NotBlank(message = "运营地区名称不能为空", groups = {Default.class, UpdateGroup.class})
    private String areaName;

    @ApiModelProperty("描述")
    private String descTxt;

    @ApiModelProperty(value = "状态 0 禁用 1 启用")
    @NotNull(message = "状态不能为空", groups = {Default.class, UpdateGroup.class})
    private Byte status;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("创建时间(毫秒时间戳)")
    private Long createTime;

    @ApiModelProperty("修改人")
    private String modifyUser;

    @ApiModelProperty("修改时间(毫秒时间戳)")
    private Long modifyTime;

    @ApiModelProperty(value = "地区")
    @NotEmpty(message = "地区不能为空", groups = {Default.class, UpdateGroup.class})
    private List<String> areaList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName == null ? null : areaName.trim();
    }

    public String getDescTxt() {
        return descTxt;
    }

    public void setDescTxt(String descTxt) {
        this.descTxt = descTxt == null ? null : descTxt.trim();
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser == null ? null : modifyUser.trim();
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public List<String> getAreaList() {
        return areaList;
    }

    public void setAreaList(List<String> areaList) {
        this.areaList = areaList;
    }
}