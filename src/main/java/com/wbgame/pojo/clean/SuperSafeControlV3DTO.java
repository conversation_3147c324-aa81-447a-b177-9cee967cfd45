package com.wbgame.pojo.clean;

import com.wbgame.common.QueryGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.groups.Default;
import java.util.List;

@ApiModel("新风控配置DTO")
public class SuperSafeControlV3DTO {

    @NotBlank(message = "appid不能为空", groups = Default.class)
    @ApiModelProperty("产品id")
    private String appid;

    @ApiModelProperty("渠道")
    private String cha;

    @ApiModelProperty("项目id")
    private String prjid;


    @ApiModelProperty("'业务开关,1是开,0是关, 关闭之后前端将无视风控结果逻辑")
    private String enable;

    @ApiModelProperty("接口查询间隔,单位秒,默认30*60秒")
    private String queryInvTime;


    @ApiModelProperty("修改人")
    private String modifyUser;

    @ApiModelProperty("开启关闭 1开启 0关闭")
    @Pattern(regexp = "^[0-1]$", message = "状态码错误")
    private String status;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("风控排除项,用于单项或者多项风控排除控制")
    private String ruleOutList;

    @ApiModelProperty("返回不同段位延迟,delay单位秒")
    private String delayRank;

    @ApiModelProperty("查询条件产品id")
    private List<String> appidList;

    @ApiModelProperty("查询条件渠道")
    private List<String> chaList;

    @ApiModelProperty("页码")
    @NotNull(message = "页码错误", groups = QueryGroup.class)
    private Integer start;

    @ApiModelProperty("条数")
    @NotNull(message = "条数错误", groups = QueryGroup.class)
    private Integer limit;


    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getCha() {
        return cha;
    }

    public void setCha(String cha) {
        this.cha = cha;
    }

    public List<String> getAppidList() {
        return appidList;
    }

    public void setAppidList(List<String> appidList) {
        this.appidList = appidList;
    }

    public List<String> getChaList() {
        return chaList;
    }

    public void setChaList(List<String> chaList) {
        this.chaList = chaList;
    }

    public String getPrjid() {
        return prjid;
    }

    public void setPrjid(String prjid) {
        this.prjid = prjid;
    }

    public String getEnable() {
        return enable;
    }

    public void setEnable(String enable) {
        this.enable = enable;
    }

    public String getQueryInvTime() {
        return queryInvTime;
    }

    public void setQueryInvTime(String queryInvTime) {
        this.queryInvTime = queryInvTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getRuleOutList() {
        return ruleOutList;
    }

    public void setRuleOutList(String ruleOutList) {
        this.ruleOutList = ruleOutList;
    }

    public String getDelayRank() {
        return delayRank;
    }

    public void setDelayRank(String delayRank) {
        this.delayRank = delayRank;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }
}