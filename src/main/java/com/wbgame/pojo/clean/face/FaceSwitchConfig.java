package com.wbgame.pojo.clean.face;

/**
 * face puls 开关配置
 * <AUTHOR>
 * @date: 2022年2月28日
 */
 public class FaceSwitchConfig {
	 	private String id;
		private String fristProductionPic;//首次制作
		private String faceCartoonPic;//漫画付费引导
		private String changeAgeOldPic;//人脸表老
		private String changeAgeYongPic;//人脸变年轻
		private String swapGenderPic;//人脸性别转换
		private String subscriptionPage;//订阅页视频地址
		private String guidePage;//人脸性别转换
		private String projectId;//腾讯活动id
		private String modelId;//腾讯模板id
		private String templateFaceID;//腾讯面容id
		private String tempUrl;//模板视频
		private String bynouOffOn;//买量用户展示开关1开启0关闭
		private String homeADSwitch;//首页页广告开关 1开启 0关闭
		private String splashADSwitch;//启动页广告开关 1开启 0关闭
		private String onlyfnoushowOnffOn;//
		private String createTime;
		private String modifyTime;
		private String createUser;
		private String modifyUser;
		private String status;
		private String bynoubsOffOn;
	/**
	 * 时光相机付费引导 1:开启 0：关闭
	 */
	private Short timeCameraOffOn;

	/**
	 * 完成页付费引导 0详情页 1结果页
	 */
	private Integer showPayGuideLoc;

	/**
	 * 1海外安卓 2海外ios 3国内安卓 4国内ios
	 */
	private String configType;


	public String getConfigType() {
		return configType;
	}

	public void setConfigType(String configType) {
		this.configType = configType;
	}

	public Short getTimeCameraOffOn() {
		return timeCameraOffOn;
	}

	public void setTimeCameraOffOn(Short timeCameraOffOn) {
		this.timeCameraOffOn = timeCameraOffOn;
	}

	public String getId() {
			return id;
		}
		public void setId(String id) {
			this.id = id;
		}
		public String getFristProductionPic() {
			return fristProductionPic;
		}
		public void setFristProductionPic(String fristProductionPic) {
			this.fristProductionPic = fristProductionPic;
		}
		public String getFaceCartoonPic() {
			return faceCartoonPic;
		}
		public void setFaceCartoonPic(String faceCartoonPic) {
			this.faceCartoonPic = faceCartoonPic;
		}
		public String getChangeAgeOldPic() {
			return changeAgeOldPic;
		}
		public void setChangeAgeOldPic(String changeAgeOldPic) {
			this.changeAgeOldPic = changeAgeOldPic;
		}
		public String getChangeAgeYongPic() {
			return changeAgeYongPic;
		}
		public void setChangeAgeYongPic(String changeAgeYongPic) {
			this.changeAgeYongPic = changeAgeYongPic;
		}
		public String getSubscriptionPage() {
			return subscriptionPage;
		}
		public void setSubscriptionPage(String subscriptionPage) {
			this.subscriptionPage = subscriptionPage;
		}
		public String getSwapGenderPic() {
			return swapGenderPic;
		}
		public void setSwapGenderPic(String swapGenderPic) {
			this.swapGenderPic = swapGenderPic;
		}
		public String getGuidePage() {
			return guidePage;
		}
		public void setGuidePage(String guidePage) {
			this.guidePage = guidePage;
		}
		public String getProjectId() {
			return projectId;
		}
		public void setProjectId(String projectId) {
			this.projectId = projectId;
		}
		public String getModelId() {
			return modelId;
		}
		public void setModelId(String modelId) {
			this.modelId = modelId;
		}
		public String getTemplateFaceID() {
			return templateFaceID;
		}
		public void setTemplateFaceID(String templateFaceID) {
			this.templateFaceID = templateFaceID;
		}
		public String getCreateTime() {
			return createTime;
		}
		public void setCreateTime(String createTime) {
			this.createTime = createTime;
		}
		public String getModifyTime() {
			return modifyTime;
		}
		public void setModifyTime(String modifyTime) {
			this.modifyTime = modifyTime;
		}
		public String getCreateUser() {
			return createUser;
		}
		public void setCreateUser(String createUser) {
			this.createUser = createUser;
		}
		public String getModifyUser() {
			return modifyUser;
		}
		public void setModifyUser(String modifyUser) {
			this.modifyUser = modifyUser;
		}
		public String getStatus() {
			return status;
		}
		public void setStatus(String status) {
			this.status = status;
		}
		public String getTempUrl() {
			return tempUrl;
		}
		public void setTempUrl(String tempUrl) {
			this.tempUrl = tempUrl;
		}
		public String getBynouOffOn() {
			return bynouOffOn;
		}
		public void setBynouOffOn(String bynouOffOn) {
			this.bynouOffOn = bynouOffOn;
		}
		public String getHomeADSwitch() {
			return homeADSwitch;
		}
		public void setHomeADSwitch(String homeADSwitch) {
			this.homeADSwitch = homeADSwitch;
		}
		public String getSplashADSwitch() {
			return splashADSwitch;
		}
		public void setSplashADSwitch(String splashADSwitch) {
			this.splashADSwitch = splashADSwitch;
		}
		public String getOnlyfnoushowOnffOn() {
			return onlyfnoushowOnffOn;
		}
		public void setOnlyfnoushowOnffOn(String onlyfnoushowOnffOn) {
			this.onlyfnoushowOnffOn = onlyfnoushowOnffOn;
		}

	public String getBynoubsOffOn() {
		return bynoubsOffOn;
	}
	public void setBynoubsOffOn(String bynoubsOffOn) {
		this.bynoubsOffOn = bynoubsOffOn;
	}

	public Integer getShowPayGuideLoc() {
		return showPayGuideLoc;
	}

	public void setShowPayGuideLoc(Integer showPayGuideLoc) {
		this.showPayGuideLoc = showPayGuideLoc;
	}
}