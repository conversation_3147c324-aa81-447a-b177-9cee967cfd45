package com.wbgame.pojo.clean.face;

import java.lang.reflect.Field;

/**
 * 模板配置表
 * <AUTHOR>
 * @date: 2022年2月28日
 */
 public class FaceModelConfig {
		private String id;
		private String mid;//腾讯云素材id
		private Integer classId;//分类id
		
		private String tempTitle;//封面名称
		private String tempUrl;//封面图
		private String videoUrl;//封面名称
		private String tempType;//模板类型1付费2.激励3免费
		
		private String state;//1.上架2.下架3.测试4.定时上架
		private String isTop;//是否置顶
		private String videoHeight;//高
		private String videoWidth;//宽
		private String projectId;//腾讯活动id
		private String templateFaceID;//腾讯模板id
		private String taskTime;//定时上架日期
		
		private String sort;//排序
		
		private String createTime;
		private String modifyTime;
		private String createUser;
		private String modifyUser;
		private String status;
		private Short region; // 1:国内 2:国外
		private Integer areaId; // 地区id

		//累计制作数
		private Integer makeTemplateCount = 0;
		//累计转化会员数
		private Integer tplConvertVipCount = 0;
		// 转化率
		private String conversionRate = "0.00%";


    private String start_date;

    private String end_date;

    private String upTime;

	public FaceModelConfig() {}

    public String getUpTime() {
        return upTime;
    }

    public void setUpTime(String upTime) {
        this.upTime = upTime;
    }

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public Integer getAreaId() {
		return areaId;
	}

	public void setAreaId(Integer areaId) {
		this.areaId = areaId;
	}

	public Short getRegion() {
		return region;
	}

	public void setRegion(Short region) {
		this.region = region;
	}

	public String getConversionRate() {
		return conversionRate;
	}

	public void setConversionRate(String conversionRate) {
		this.conversionRate = conversionRate;
	}

	public String getId() {
			return id;
		}
		public void setId(String id) {
			this.id = id;
		}
		public String getMid() {
			return mid;
		}
		public void setMid(String mid) {
			this.mid = mid;
		}
		public String getTempTitle() {
			return tempTitle;
		}
		public void setTempTitle(String tempTitle) {
			this.tempTitle = tempTitle;
		}
		public String getTempUrl() {
			return tempUrl;
		}
		public void setTempUrl(String tempUrl) {
			this.tempUrl = tempUrl;
		}
		public String getVideoUrl() {
			return videoUrl;
		}
		public void setVideoUrl(String videoUrl) {
			this.videoUrl = videoUrl;
		}
		public String getTempType() {
			return tempType;
		}
		public void setTempType(String tempType) {
			this.tempType = tempType;
		}
		public String getState() {
			return state;
		}
		public void setState(String state) {
			this.state = state;
		}
		public String getIsTop() {
			return isTop;
		}
		public void setIsTop(String isTop) {
			this.isTop = isTop;
		}
		public String getVideoHeight() {
			return videoHeight;
		}
		public void setVideoHeight(String videoHeight) {
			this.videoHeight = videoHeight;
		}
		public String getVideoWidth() {
			return videoWidth;
		}
		public void setVideoWidth(String videoWidth) {
			this.videoWidth = videoWidth;
		}
		public String getProjectId() {
			return projectId;
		}
		public void setProjectId(String projectId) {
			this.projectId = projectId;
		}
		public String getCreateTime() {
			return createTime;
		}
		public void setCreateTime(String createTime) {
			this.createTime = createTime;
		}
		public String getModifyTime() {
			return modifyTime;
		}
		public void setModifyTime(String modifyTime) {
			this.modifyTime = modifyTime;
		}
		public String getCreateUser() {
			return createUser;
		}
		public void setCreateUser(String createUser) {
			this.createUser = createUser;
		}
		public String getModifyUser() {
			return modifyUser;
		}
		public void setModifyUser(String modifyUser) {
			this.modifyUser = modifyUser;
		}
		public String getStatus() {
			return status;
		}
		public void setStatus(String status) {
			this.status = status;
		}
		public String getTemplateFaceID() {
			return templateFaceID;
		}
		public void setTemplateFaceID(String templateFaceID) {
			this.templateFaceID = templateFaceID;
		}
		public String getTaskTime() {
			return taskTime;
		}
		public void setTaskTime(String taskTime) {
			this.taskTime = taskTime;
		}
		public String getSort() {
			return sort;
		}
		public void setSort(String sort) {
			this.sort = sort;
		}
		public Integer getClassId() {
			return classId;
		}
		public void setClassId(Integer classId) {
			this.classId = classId;
		}

	public Integer getMakeTemplateCount() {
		return makeTemplateCount;
	}

	public void setMakeTemplateCount(Integer makeTemplateCount) {
		this.makeTemplateCount = makeTemplateCount;
	}

	public Integer getTplConvertVipCount() {
		return tplConvertVipCount;
	}

	public void setTplConvertVipCount(Integer tplConvertVipCount) {
		this.tplConvertVipCount = tplConvertVipCount;
	}

	public CopyFaceModelConfig convertCopyFaceModelConfig() {
		Class<CopyFaceModelConfig> copyFaceModelConfigClass = CopyFaceModelConfig.class;
		try {
			CopyFaceModelConfig copyFaceModelConfig = copyFaceModelConfigClass.newInstance();
			Field[] fields = copyFaceModelConfigClass.getDeclaredFields();
			for (Field field : fields) {
				Field faceField = this.getClass().getDeclaredField(field.getName());

				field.setAccessible(true);
				field.set(copyFaceModelConfig, faceField.get(this));
			}
			return copyFaceModelConfig;
		} catch (InstantiationException | IllegalAccessException | NoSuchFieldException e) {
			throw new RuntimeException(e);
		}
	}
}