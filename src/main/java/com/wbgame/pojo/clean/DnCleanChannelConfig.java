package com.wbgame.pojo.clean;

/***
 * 超级清理王-渠道新闻页参数配置
 * <AUTHOR>
 * @date 2020年6月24日
 */
public class DnCleanChannelConfig {
	/**渠道标识*/
    private String cname;
    
    /**渠道标识*/
    private String pid;

    /**渠道类型*/
    private String urlType;

    /**参数1*/
    private String value1;

    public String getPid() {
		return pid;
	}

	public void setPid(String pid) {
		this.pid = pid;
	}

	/**参数2*/
    private String value2;

    /**开通状态*/
    private Integer statusType;

    /**创建时间*/
    private String createTime;

    /**最后修改时间*/
    private String updateTime;

    /**最后修改人*/
    private String updateUser;
    
    /**最后修改人*/
    private String appid;

	public String getCname() {
		return cname;
	}

	public void setCname(String cname) {
		this.cname = cname;
	}

	public String getUrlType() {
		return urlType;
	}

	public void setUrlType(String urlType) {
		this.urlType = urlType;
	}

	public String getValue1() {
		return value1;
	}

	public void setValue1(String value1) {
		this.value1 = value1;
	}

	public String getValue2() {
		return value2;
	}

	public void setValue2(String value2) {
		this.value2 = value2;
	}

	public Integer getStatusType() {
		return statusType;
	}

	public void setStatusType(Integer statusType) {
		this.statusType = statusType;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}
    
}