package com.wbgame.pojo.clean.push;

public class ApkProductNumVo {
	
	private String tdate;
	private String tname;
	private String product_id; // 产品ID
	private String projectid; // 项目ID
	private int act_num; // 活跃
	private int add_num; // 新增
	private int outflow_num; // 流失
	private int silent_num; // 沉默
	private String outflow_rate; // 流失率（流失用户/活跃）
	private String silent_rate; // 沉默率（沉默用户/新增）
	
	public String getTdate() {
		return tdate;
	}
	public void setTdate(String tdate) {
		this.tdate = tdate;
	}
	public String getTname() {
		return tname;
	}
	public void setTname(String tname) {
		this.tname = tname;
	}
	public String getProduct_id() {
		return product_id;
	}
	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}
	public String getProjectid() {
		return projectid;
	}
	public void setProjectid(String projectid) {
		this.projectid = projectid;
	}
	public int getAct_num() {
		return act_num;
	}
	public void setAct_num(int act_num) {
		this.act_num = act_num;
	}
	public int getAdd_num() {
		return add_num;
	}
	public void setAdd_num(int add_num) {
		this.add_num = add_num;
	}
	public int getOutflow_num() {
		return outflow_num;
	}
	public void setOutflow_num(int outflow_num) {
		this.outflow_num = outflow_num;
	}
	public int getSilent_num() {
		return silent_num;
	}
	public void setSilent_num(int silent_num) {
		this.silent_num = silent_num;
	}
	public String getOutflow_rate() {
		return outflow_rate;
	}
	public void setOutflow_rate(String outflow_rate) {
		this.outflow_rate = outflow_rate;
	}
	public String getSilent_rate() {
		return silent_rate;
	}
	public void setSilent_rate(String silent_rate) {
		this.silent_rate = silent_rate;
	}
}
