package com.wbgame.pojo.clean.push;

public class ConfigVo {
	
	private String appid;
	private String seqid;
	private String limit_num;
	private String limit_date;
	public String getLimit_date() {
		return limit_date;
	}
	public void setLimit_date(String limitDate) {
		limit_date = limitDate;
	}
	public String getLimit_num() {
		return limit_num;
	}
	public void setLimit_num(String limitNum) {
		limit_num = limitNum;
	}
	public String getSeqid() {
		return seqid;
	}
	public void setSeqid(String seqid) {
		this.seqid = seqid;
	}
	private String  prjmid;//	varchar2(100)	n			????????????id
	private String		name;//		varchar2(100)	n			??????????
	private String		type;//		varchar2(20)	n			????????
	private String		rate;//		number	y			??????????????????0
	private String		ad_sid_str;//		varchar2(500)	n			????????????????????#????
	private String		agentpecent;//		number	y			??????????????????????????????????????????????70#30
	private String		round;//		varchar2(100)	y			3,5,7,10-15 ????????????3,5,7,10-15????????
	private String		statu;//		number	y			????????0????1????
	private String			delaytime;//		number	y			??????????????????????????????s????180
	private String			createdate;//		date	y			????????
	private String			activ_cityid;//		varchar2(500)	y	'all'		??????all??????????#????????????????????
	private String			activ_telecom;//		varchar2(10)	y	'all'		1????2????3????0????????????????all??????????#????
	private String		activ_statu;//		number	y	0		0??????????activ_cityid??????????1??????activ_cityid????????
	private String limit_second;
	private String showmodel;
	private String islock; // 1-锁定状态，不可修改 0-未锁定
	
	public String getLimit_second() {
		return limit_second;
	}
	public void setLimit_second(String limit_second) {
		this.limit_second = limit_second;
	}

	public String getPrjmid() {
		return prjmid;
	}
	public void setPrjmid(String prjmid) {
		this.prjmid = prjmid;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getRate() {
		return rate;
	}
	public void setRate(String rate) {
		this.rate = rate;
	}
	public String getAd_sid_str() {
		return ad_sid_str;
	}
	public void setAd_sid_str(String adSidStr) {
		ad_sid_str = adSidStr;
	}
	public String getAgentpecent() {
		return agentpecent;
	}
	public void setAgentpecent(String agentpecent) {
		this.agentpecent = agentpecent;
	}
	public String getRound() {
		return round;
	}
	public void setRound(String round) {
		this.round = round;
	}
	public String getStatu() {
		return statu;
	}
	public void setStatu(String statu) {
		this.statu = statu;
	}
	public String getDelaytime() {
		return delaytime;
	}
	public void setDelaytime(String delaytime) {
		this.delaytime = delaytime;
	}
	public String getCreatedate() {
		return createdate;
	}
	public void setCreatedate(String createdate) {
		this.createdate = createdate;
	}
	public String getActiv_cityid() {
		return activ_cityid;
	}
	public void setActiv_cityid(String activCityid) {
		activ_cityid = activCityid;
	}
	public String getActiv_telecom() {
		return activ_telecom;
	}
	public void setActiv_telecom(String activTelecom) {
		activ_telecom = activTelecom;
	}
	public String getActiv_statu() {
		return activ_statu;
	}
	public void setActiv_statu(String activStatu) {
		activ_statu = activStatu;
	}
	public String getShowmodel() {
		return showmodel;
	}
	public void setShowmodel(String showmodel) {
		this.showmodel = showmodel;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getIslock() {
		return islock;
	}
	public void setIslock(String islock) {
		this.islock = islock;
	}
	
}
