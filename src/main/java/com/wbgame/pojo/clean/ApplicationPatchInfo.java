package com.wbgame.pojo.clean;

/**
 * 应用补丁跟新
 * <AUTHOR>
 * @date: 2021年11月4日
 */
public class ApplicationPatchInfo {
	
	/**
	 * CREATE TABLE `super_app_patch` (
	  `id` int(11) NOT NULL AUTO_INCREMENT,
	  `appid` varchar(50) DEFAULT NULL,
	  `masterVersion` varchar(50) DEFAULT '' COMMENT '主包版本',
	  `patchVersion` int(9) DEFAULT NULL COMMENT '补丁版本',
	  `patchNote` text COMMENT '补丁备注',
	  `patchUrl` varchar(500) DEFAULT NULL COMMENT '补丁地址apk',
	  `status` int(4) DEFAULT NULL COMMENT '1准备下发 2下发中 3已撤回',
	  `able` int(4) DEFAULT NULL COMMENT '是否生效 1生效 0不生效',
	  `createTime` varchar(50) DEFAULT NULL,
	  `updateTime` varchar(50) DEFAULT NULL,
	  `createUser` varchar(50) DEFAULT NULL,
	  `updateUser` varchar(50) DEFAULT NULL,
	  `rate` int(9) DEFAULT NULL COMMENT '发放比例 单位%',
	  `md5` varchar(255) DEFAULT NULL COMMENT 'apk包md5值',
	  PRIMARY KEY (`id`)
	) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;
	 */
	private String id;
	private String appid;
	private String masterVersion;
	private String patchVersion;
	private String patchNote;
	private String patchUrl;
	private String status;
	private String able;
	private String createTime;
	private String updateTime;
	private String createUser;
	private String updateUser;
	private String rate;
	private String md5;
	private String addWhitelist; //否投放到白名单开关  1是 0否
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getMasterVersion() {
		return masterVersion;
	}
	public void setMasterVersion(String masterVersion) {
		this.masterVersion = masterVersion;
	}
	public String getPatchVersion() {
		return patchVersion;
	}
	public void setPatchVersion(String patchVersion) {
		this.patchVersion = patchVersion;
	}
	public String getPatchNote() {
		return patchNote;
	}
	public void setPatchNote(String patchNote) {
		this.patchNote = patchNote;
	}
	public String getPatchUrl() {
		return patchUrl;
	}
	public void setPatchUrl(String patchUrl) {
		this.patchUrl = patchUrl;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getAble() {
		return able;
	}
	public void setAble(String able) {
		this.able = able;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public String getUpdateUser() {
		return updateUser;
	}
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	public String getRate() {
		return rate;
	}
	public void setRate(String rate) {
		this.rate = rate;
	}
	public String getMd5() {
		return md5;
	}
	public void setMd5(String md5) {
		this.md5 = md5;
	}
	public String getAddWhitelist() {
		return addWhitelist;
	}
	public void setAddWhitelist(String addWhitelist) {
		this.addWhitelist = addWhitelist;
	}
	
	
}
