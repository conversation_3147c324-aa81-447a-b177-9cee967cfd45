package com.wbgame.pojo.clean;

/**
 * 
 * <AUTHOR>
 * @date: 2021年12月13日
 */
public class QupaiRecommendZone {
	/**
	 CREATE TABLE `qupai_recommend_zone` (
	  `appid` varchar(50) DEFAULT NULL,
	  `createTime` varchar(50) DEFAULT NULL,
	  `sort` int(5) DEFAULT NULL COMMENT '排序',
	  `zoneTitle` varchar(50) DEFAULT NULL COMMENT '分类',
	  `modifyTime` varchar(50) DEFAULT NULL,
	  `modifyUser` varchar(50) DEFAULT NULL,
	  `status` varchar(5) DEFAULT '1' COMMENT '开启关闭 1开启 0关闭',
	  `id` int(9) NOT NULL AUTO_INCREMENT,
	  `createUser` varchar(50) DEFAULT NULL,
	  PRIMARY KEY (`id`) USING BTREE,
	  KEY `appid` (`appid`),
	  KEY `status` (`status`),
	  KEY `updateTime` (`modifyTime`)
	) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

	 */
	
	private String id;
	private String appid;
	
	private Integer sort;
	private String zoneTitle;
	
	private String createTime;
	private String modifyTime;
	private String createUser;
	private String modifyUser;
	private String status;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public String getZoneTitle() {
		return zoneTitle;
	}
	public void setZoneTitle(String zoneTitle) {
		this.zoneTitle = zoneTitle;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public String getModifyUser() {
		return modifyUser;
	}
	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
}
