package com.wbgame.pojo.clean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 风控黑白名单配置
 */
@ApiModel("风控黑白名单配置--显示")
public class SafeMarkConfigVO {

    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 设备标识
     */
    @ApiModelProperty("设备标识")
    private String markId;

    /**
     * 白名单-lsn：white_lsn 白名单安卓id white_adid 黑名单-lsn：black_lsn 黑名单安卓id black_adid
     */
    @ApiModelProperty("白名单-lsn：white_lsn 白名单安卓id white_adid 黑名单-lsn：black_lsn 黑名单安卓id black_adid")
    private String poisonType;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("修改时间")
    private String modifyTime;

    @ApiModelProperty("修改人")
    private String modifyUser;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("是否是黑名单,0不是,1是")
    private Short isPoison;

    @ApiModelProperty("备注")
    private String description;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMarkId() {
        return markId;
    }

    public void setMarkId(String markId) {
        this.markId = markId;
    }

    public String getPoisonType() {
        return poisonType;
    }

    public void setPoisonType(String poisonType) {
        this.poisonType = poisonType;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Short getIsPoison() {
        return isPoison;
    }

    public void setIsPoison(Short isPoison) {
        this.isPoison = isPoison;
    }
}