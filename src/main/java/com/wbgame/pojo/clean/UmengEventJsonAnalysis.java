package com.wbgame.pojo.clean;

public class UmengEventJsonAnalysis {
    /**
    * 日期
    */
    private String ds;

    /**
    * 事件id
    */
    private String eventName;

    /**
    * 当前版本号
    */
    private String appVersion;

    /**
    * 安装渠道
    */
    private String installChannel;

    /**
    * 机型
    */
    private String stdDeviceModel;

    /**
    * json，解析
    */
    private String eventKvJson;

    /**
    * 设备标识
    */
    private String umid;

    public String getDs() {
        return ds;
    }

    public void setDs(String ds) {
        this.ds = ds;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getInstallChannel() {
        return installChannel;
    }

    public void setInstallChannel(String installChannel) {
        this.installChannel = installChannel;
    }

    public String getStdDeviceModel() {
        return stdDeviceModel;
    }

    public void setStdDeviceModel(String stdDeviceModel) {
        this.stdDeviceModel = stdDeviceModel;
    }

    public String getEventKvJson() {
        return eventKvJson;
    }

    public void setEventKvJson(String eventKvJson) {
        this.eventKvJson = eventKvJson;
    }

    public String getUmid() {
        return umid;
    }

    public void setUmid(String umid) {
        this.umid = umid;
    }
}