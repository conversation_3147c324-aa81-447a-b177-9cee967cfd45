package com.wbgame.pojo.clean;

/**
 * 应用安装激活
 * <AUTHOR>
 * @date: 2021年12月13日
 */
public class ApplicationIterationConfig {
	
	/**
	 CREATE TABLE `app_iteration_config` (
	  `appid` varchar(50) DEFAULT NULL,
	  `cha` varchar(50) DEFAULT NULL,
	  `prjid` varchar(50) DEFAULT NULL,
	  `creatTime` varchar(50) DEFAULT NULL,
	  `times` varchar(50) DEFAULT NULL COMMENT '每天弹出次数 ',
	  `url` varchar(50) DEFAULT NULL COMMENT '跟新提示图',
	  `force` varchar(10) DEFAULT NULL COMMENT '是否强跟新 1是 0否',
	  `modifyTime` varchar(50) DEFAULT NULL,
	  `modifyUser` varchar(50) DEFAULT NULL,
	  `status` varchar(5) DEFAULT '1' COMMENT '开启关闭 1开启 0关闭',
	  `id` int(9) NOT NULL AUTO_INCREMENT,
	  `cuser` varchar(50) DEFAULT NULL COMMENT '创建人',
	  `download` varchar(50) DEFAULT NULL COMMENT '跟新包地址',
	  `describe` text COMMENT '跟新描述',
	  PRIMARY KEY (`id`) USING BTREE,
	  KEY `pid` (`prjid`),
	  KEY `cha` (`cha`),
	  KEY `appid` (`appid`),
	  KEY `status` (`status`),
	  KEY `updateTime` (`modifyTime`)
	) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

	 */
	private String id;
	private String appid;
	private String cha;
	private String prjid;
	private String times;
	private String url;
	private String force;
	private String download;
	private String describe;
	
	private String createTime;
	private String modifyTime;
	private String createUser;
	private String modifyUser;
	private String status;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getCha() {
		return cha;
	}
	public void setCha(String cha) {
		this.cha = cha;
	}
	public String getPrjid() {
		return prjid;
	}
	public void setPrjid(String prjid) {
		this.prjid = prjid;
	}
	public String getTimes() {
		return times;
	}
	public void setTimes(String times) {
		this.times = times;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public String getForce() {
		return force;
	}
	public void setForce(String force) {
		this.force = force;
	}
	public String getDownload() {
		return download;
	}
	public void setDownload(String download) {
		this.download = download;
	}
	public String getDescribe() {
		return describe;
	}
	public void setDescribe(String describe) {
		this.describe = describe;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public String getModifyUser() {
		return modifyUser;
	}
	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	
}
