package com.wbgame.pojo.clean;

/**
 * 清理王-deeplink策略配置
 * <AUTHOR>
 * @date: 2021年3月05日
 */
public class SuperDeeplinkPolicy {
	private String policyId;//'策略id唯一标识主键自增',
    private String pidList;//项目id集合 多个逗号隔开
    private String chaList;//渠道 多个逗号隔开
    private String policyName;
    private String appidList;
    private String creatTime;
    private String modifyTime;
    private String modifyUser;
    private String city;
    private String cityStatus;
	public String getPolicyId() {
		return policyId;
	}
	public void setPolicyId(String policyId) {
		this.policyId = policyId;
	}
	public String getPidList() {
		return pidList;
	}
	public void setPidList(String pidList) {
		this.pidList = pidList;
	}
	public String getChaList() {
		return chaList;
	}
	public void setChaList(String chaList) {
		this.chaList = chaList;
	}
	public String getCreatTime() {
		return creatTime;
	}
	public void setCreatTime(String creatTime) {
		this.creatTime = creatTime;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getModifyUser() {
		return modifyUser;
	}
	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}
	public String getPolicyName() {
		return policyName;
	}
	public void setPolicyName(String policyName) {
		this.policyName = policyName;
	}
	public String getAppidList() {
		return appidList;
	}
	public void setAppidList(String appidList) {
		this.appidList = appidList;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getCityStatus() {
		return cityStatus;
	}
	public void setCityStatus(String cityStatus) {
		this.cityStatus = cityStatus;
	}
    
}