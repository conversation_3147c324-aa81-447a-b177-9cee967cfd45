package com.wbgame.pojo.advert;

/**
 * <AUTHOR>
 * @Classname RiskAnalysisRatioDataVo
 * @Description ads_risk_user_proportion_daily
 * @Date 2022/3/1 11:07
 */
public class RiskAnalysisRatioDataVo {

    private String tdate;
    private String appid;
    private String download_channel;
    private String user_source;
    private String app_name;
    private String user_active_type;
    private String high_risk_user_cnt_ratio;
    private String lower_risk_user_cnt_ratio;
    private String no_risk_user_cnt_ratio;

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getDownload_channel() {
        return download_channel;
    }

    public void setDownload_channel(String download_channel) {
        this.download_channel = download_channel;
    }

    public String getUser_source() {
        return user_source;
    }

    public void setUser_source(String user_source) {
        this.user_source = user_source;
    }

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }

    public String getUser_active_type() {
        return user_active_type;
    }

    public void setUser_active_type(String user_active_type) {
        this.user_active_type = user_active_type;
    }

    public String getHigh_risk_user_cnt_ratio() {
        return high_risk_user_cnt_ratio;
    }

    public void setHigh_risk_user_cnt_ratio(String high_risk_user_cnt_ratio) {
        this.high_risk_user_cnt_ratio = high_risk_user_cnt_ratio;
    }

    public String getLower_risk_user_cnt_ratio() {
        return lower_risk_user_cnt_ratio;
    }

    public void setLower_risk_user_cnt_ratio(String lower_risk_user_cnt_ratio) {
        this.lower_risk_user_cnt_ratio = lower_risk_user_cnt_ratio;
    }

    public String getNo_risk_user_cnt_ratio() {
        return no_risk_user_cnt_ratio;
    }

    public void setNo_risk_user_cnt_ratio(String no_risk_user_cnt_ratio) {
        this.no_risk_user_cnt_ratio = no_risk_user_cnt_ratio;
    }
}
