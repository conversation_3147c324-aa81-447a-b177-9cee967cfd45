package com.wbgame.pojo.advert;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * <AUTHOR>
 * @Classname UmengAdIncomeVo
 * @Description TODO
 * @Date 2022/10/31 11:09
 */
public class UmengAdIncomeReportVo {

    private String tdate;
    private String appid;
    private String channel;
    private String media;
    private String gameName;

    private String packagename;

    private String state;
    private String avgnum;
    private String addnum;
    private String actnum;
    private String appkey;
    private String appname;
    private String daily_duration;
    private String temp_id;
    private String temp_name;
    private String temp_single_name;
    
    //是否是x模式
    private String is_x;


    private String msg_pv;
    private String banner_pv;
    private String native_msg_pv;
    //原生msg
    private String native_msg_pv_rate;
    /**
     * 前一天的值，用于告警展示
     */
    private String before_native_msg_pv;
    private String native_new_banner_pv;
    private String plaque_video_pv;
    private String native_plaque_pv;
    private String native_plaque_pv_rate;
    private String native_splash_pv;
    private String native_splash_pv_rate;
    private String native_banner_pv;
    private String native_banner_pv_rate;
    /**
     * 前一天的值，用于告警展示
     */
    private String before_native_banner_pv;
    private String system_splash_pv;
    private String system_splash_pv_rate;
    private String plaque_pv;
    private String splash_pv;
    private String splash_pv_rate;
    private String suspend_icon_pv;
    private String video_pv;
    private String native_new_plaque_pv;
    //人均pv-(msg/yuan)插屏
    private String native_new_plaque_pv_rate;

    //人均pv-插屏
    private String plaque_pv_rate;
    //人均pv-插屏视频
    private String plaque_video_pv_rate;
    //人均pv-banner
    private String banner_pv_rate;
    //(msg/yuans)banner
    private String native_new_banner_pv_rate;
    //视频
    private String video_pv_rate;
    //悬浮icon
    private String suspend_icon_pv_rate;

    private String system_splash_avg_click_rate;

    private String splash_avg_click_rate;

    /**
     * 前一天的值，用于告警展示
     */
    private String before_native_new_plaque_pv;
    private String total_banner_pv;
    private String total_splash_pv;
    private String total_video_pv;
    private String total_plaque_pv;
    private String total_suspend_icon_pv;
    private String total_plaque_video_pv;
    private String total_native_msg_pv;

    private String total_banner_pv_rate;
    private String total_splash_pv_rate;
    private String total_video_pv_rate;
    private String total_plaque_pv_rate;
    private String total_suspend_icon_pv_rate;
    private String total_plaque_video_pv_rate;
    private String total_native_msg_pv_rate;

    private String total_banner_click;
    private String total_splash_click;
    private String total_video_click;
    private String total_plaque_click;
    private String total_suspend_icon_click;
    private String total_plaque_video_click;
    private String total_native_msg_click;

    private String total_banner_click_rate;
    private String total_splash_click_rate;
    private String total_video_click_rate;
    private String total_plaque_click_rate;
    private String total_suspend_icon_click_rate;
    private String total_plaque_video_click_rate;
    private String total_native_msg_click_rate;

    private String banner_avg_click;
    private String banner_avg_click_rate;
    private String native_splash_avg_click;
    private String native_splash_avg_click_rate;
    private String native_plaque_avg_click;
    private String native_plaque_avg_click_rate;
    private String native_new_plaque_avg_click;
    private String native_new_plaque_avg_click_rate;
    private String native_msg_avg_click;
    private String native_msg_avg_click_rate;
    private String video_avg_click;
    private String video_avg_click_rate;
    private String splash_avg_click;
    private String plaque_video_avg_click;
    private String plaque_video_avg_click_rate;
    private String system_splash_avg_click;
    private String suspend_icon_avg_click;
    private String suspend_icon_avg_click_rate;
    private String plaque_avg_click;
    private String plaque_avg_click_rate;
    private String native_banner_avg_click;
    private String native_banner_avg_click_rate;
    private String native_new_banner_avg_click;
    private String native_new_banner_avg_click_rate;

    /* 分类型的点击数 */
    private String banner_click;
    private String native_splash_click;
    private String native_plaque_click;
    private String native_new_plaque_click;
    private String native_msg_click;
    private String video_click;
    private String splash_click;
    private String plaque_video_click;
    private String system_splash_click;
    private String suspend_icon_click;
    private String plaque_click;
    private String native_banner_click;
    private String native_new_banner_click;



    private String plaque_video_ctr;
    private String plaque_video_ctr_rate;
    private String native_msg_ctr;
    private String native_msg_ctr_rate;
    private String splash_ctr;

    //ctr-开屏汇总
    private String total_splash_ctr;
    //ctr-开屏汇总增长率
    private String total_splash_ctr_rate;

    private String splash_ctr_rate;
    private String banner_ctr;
    private String banner_ctr_rate;
    private String video_ctr;
    private String video_ctr_rate;
    private String native_splash_ctr;
    private String native_splash_ctr_rate;
    private String plaque_ctr;
    private String plaque_ctr_rate;
    private String native_new_plaque_ctr;
    private String native_new_plaque_ctr_rate;
    private String native_new_banner_ctr;
    private String native_new_banner_ctr_rate;
    private String native_plaque_ctr;
    private String native_plaque_ctr_rate;
    private String native_banner_ctr;
    private String native_banner_ctr_rate;
    private String suspend_icon_ctr;
    private String suspend_icon_ctr_rate;


    private String banner_ecpm_rate;
    private String native_msg_ecpm_rate;
    private String native_plaque_ecpm_rate;
    private String suspend_icon_ecpm_rate;
    private String plaque_video_ecpm_rate;
    private String splash_ecpm_rate;
    private String system_splash_ecpm_rate;
    private String native_splash_ecpm_rate;
    private String native_new_plaque_ecpm_rate;
    private String native_banner_ecpm_rate;
    private String native_new_banner_ecpm_rate;
    private String video_ecpm_rate;
    private String plaque_ecpm_rate;
    private String dau_arpu_rate;

    private String avgNumRate;
    private String dailyDurationRate;



    private String video_arpu;
    private String total_banner_arpu;
    private String native_new_plaque_arpu;
    private String native_new_banner_arpu;
    private String total_suspend_icon_arpu;
    private String total_plaque_arpu;
    private String total_plaque_video_arpu;
    private String banner_arpu;
    private String total_splash_arpu;
    private String dau_arpu;


    private String msg_arpu;
    private String splash_arpu;
    private String total_video_arpu;
    private String total_native_msg_arpu;
    private String plaque_arpu;
    private String plaque_video_arpu;
    private String system_splash_arpu;
    private String suspend_icon_arpu;

    /* 分类型的dau_arpu和rate */
    private String total_video_arpu_rate;
    private String total_native_msg_arpu_rate;
    private String total_splash_arpu_rate;
    private String total_plaque_arpu_rate;
    private String total_plaque_video_arpu_rate;
    private String total_banner_arpu_rate;
    private String total_suspend_icon_arpu_rate;


    /* 分类型的展示数 */
    private String native_splash_show;
    private String splash_show;
    private String native_new_banner_show;
    private String native_plaque_show;
    private String native_new_plaque_show;
    private String native_banner_show;
    private String banner_show;
    private String suspend_icon_show;
    private String system_splash_show;
    private String video_show;
    private String plaque_video_show;
    private String native_msg_show;
    private String plaque_show;
    //所有的分类pv汇总
    private String all_total_show;


    private String banner_ecpm;
    private String native_banner_ecpm;
    private String plaque_video_ecpm;
    private String native_new_plaque_ecpm;
    private String native_msg_ecpm;
    private String native_splash_ecpm;
    private String suspend_icon_ecpm;
    private String splash_ecpm;
    private String system_splash_ecpm;
    private String native_plaque_ecpm;
    private String video_ecpm;
    private String plaque_ecpm;
    private String native_new_banner_ecpm;


    private String total_income;
    private String native_plaque_income;
    private String video_income;
    private String native_msg_income;
    private String suspend_icon_income;
    private String splash_income;
    private String banner_income;
    private String plaque_video_income;
    private String system_splash_income;
    private String native_banner_income;
    private String native_new_banner_income;
    private String native_splash_income;
    private String plaque_income;
    private String native_new_plaque_income;



    private String plaque_video_cpc;
    private String native_msg_cpc;
    private String native_new_banner_cpc;
    private String banner_cpc;
    private String native_new_plaque_cpc;
    private String video_cpc;
    private String native_splash_cpc;
    private String plaque_cpc;
    private String native_plaque_cpc;
    private String native_banner_cpc;
    private String suspend_icon_cpc;
    private String splash_cpc;


    /*
    cpc增长率字段
     */
    private String plaque_video_cpc_rate;
    private String native_msg_cpc_rate;
    private String native_new_banner_cpc_rate;
    private String banner_cpc_rate;
    private String native_new_plaque_cpc_rate;
    private String video_cpc_rate;
    private String native_splash_cpc_rate;
    private String plaque_cpc_rate;
    private String native_plaque_cpc_rate;
    private String native_banner_cpc_rate;
    private String suspend_icon_cpc_rate;
    private String splash_cpc_rate;


    //2023-06-28 新增
    private String avgnum_rate;             //新增占比周对比
    private String daily_duration_rate;     //日均在线时长周对比

    //来源为 1-媒体& 2-自统计
    private String source;

    //总展示数
    private String total_show_num;

    //总点击数
    private String total_click_num;




    //2024-01-03 新增
    private String banner_show_rate; // banner分配比例
    private String new_banner_show_rate; // 新样式banner分配比例
    private String sum_total_banner; // 总pv-banner banner_show+native_new_banner_show
    private String sum_pv_banner; // pv-banner占比
    private String sum_pv_new_banner; // pv-新样式banner占比

    private String all_total_pv; // 总人均pv=所有pv相加/活跃
    private String all_total_click; // 总人均点击=所有点击相加/活跃
    private String all_total_pv_rate;
    private String all_total_click_rate;

    private String active_temp_id;	//活跃模块 功能标识id

    private String active_temp_name;	//活跃模块 功能名称


    private String all_total_ctr;       //总ctr=所有广告类型的点击量/所有广告类型的pv
    private String all_total_ctr_rate;

    private String banner_request;//请求-banner
    private String plaque_request; //请求-插屏
    private String splash_request; // 开屏请求数
    private String video_request; // 视频请求
    private String native_banner_request; // 原生banner请求
    private String native_plaque_request; // 原生插屏请求
    private String native_splash_request; // 原生开屏请求
    private String plaque_video_request; // 插屏视频请求
    private String native_msg_request; // 原生msg 请求
    private String system_splash_request;   //系统开屏 请求
    private String native_new_plaque_request; //原生新样式插屏 请求
    private String native_new_banner_request; //原生新样式banner 请求
    private String suspend_icon_request;  //悬浮icon 请求

    private String all_total_request;	//总人均请求
    private String all_total_request_rate;	//总人均请求
    private String total_splash_request;	//总人均请求-开屏
    private String total_splash_request_rate;	//总人均请求-开屏
    private String total_plaque_request;		//总人均请求-插屏
    private String total_plaque_request_rate;
    private String total_plaque_video_request;	//总人均请求-插屏视频
    private String total_plaque_video_request_rate;
    private String total_banner_request;		//总人均请求-banner
    private String total_banner_request_rate;
    private String total_video_request;		//总人均请求-视频
    private String total_video_request_rate;
    private String total_native_msg_request;	//总人均请求-原生msg
    private String total_native_msg_request_rate;
    private String total_suspend_icon_request;	//总人均请求-悬浮icon
    private String total_suspend_icon_request_rate;

    private String system_splash_avg_request;	//人均请求-系统开屏
    private String splash_avg_request;		//人均请求-开屏
    private String native_splash_avg_request;	//人均请求原生开屏
    private String plaque_avg_request;			//人均请求-插屏
    private String native_new_plaque_avg_request;	//人均请求-（msg/yuan）插屏
    private String plaque_video_avg_request;	//人均请求-插屏视频
    private String banner_avg_request;		//人均请求-banner
    private String native_new_banner_avg_request;	//人均请求-（msg/yuan）banner
    private String video_avg_request;		//人均请求-视频
    private String native_msg_avg_request;	//人均请求-原生msg
    private String suspend_icon_avg_request;	//人均请求-悬浮icon

    private String banner_fill;//填充-banner
    private String plaque_fill;//填充-插屏
    private String splash_fill;//开屏填充数
    private String video_fill; // 视频填充
    private String native_banner_fill; // 原生banner填充
    private String native_plaque_fill; // 原生插屏填充
    private String native_splash_fill; // 原生开屏填充
    private String plaque_video_fill; // 插屏视频填充
    private String native_msg_fill; // 原生msg 填充
    private String system_splash_fill;   //系统开屏 填充
    private String native_new_plaque_fill; //原生新样式插屏 填充
    private String native_new_banner_fill; //原生新样式banner 填充
    private String suspend_icon_fill;  //悬浮icon 填充

    private String all_total_avg_fill;	//总人均填充
    private String all_total_avg_fill_rate;	//总人均填充
    private String total_splash_avg_fill;	//总人均填充-开屏
    private String total_splash_avg_fill_rate;	//总人均填充-开屏
    private String total_plaque_avg_fill;		//总人均填充-插屏
    private String total_plaque_avg_fill_rate;
    private String total_plaque_video_avg_fill;	//总人均填充-插屏视频
    private String total_plaque_video_avg_fill_rate;
    private String total_banner_avg_fill;		//总人均填充-banner
    private String total_banner_avg_fill_rate;
    private String total_video_avg_fill;		//总人均填充-视频
    private String total_video_avg_fill_rate;
    private String total_native_msg_avg_fill;	//总人均填充-原生msg
    private String total_native_msg_avg_fill_rate;
    private String total_suspend_icon_avg_fill;	//总人均填充-悬浮icon
    private String total_suspend_icon_avg_fill_rate;

    private String system_splash_avg_fill;	//人均填充-系统开屏
    private String splash_avg_fill;		//人均填充-开屏
    private String native_splash_avg_fill;	//人均填充原生开屏
    private String plaque_avg_fill;			//人均填充-插屏
    private String native_new_plaque_avg_fill;	//人均填充-（msg/yuan）插屏
    private String plaque_video_avg_fill;	//人均填充-插屏视频
    private String banner_avg_fill;		//人均填充-banner
    private String native_new_banner_avg_fill;	//人均填充-（msg/yuan）banner
    private String video_avg_fill;		//人均填充-视频
    private String native_msg_avg_fill;	//人均填充-原生msg
    private String suspend_icon_avg_fill;	//人均填充-悬浮icon


    private String all_total_fill;        //总填充率=所有广告类型填充/所有广告类型请求
    private String all_total_fill_rate;
    private String total_system_splash_fill;        //填充率-系统开屏
    private String total_system_splash_fill_rate;
    private String total_splash_fill;       //填充率-开屏
    private String total_splash_fill_rate;
    private String total_native_splash_fill;    //填充率-原生开屏
    private String total_native_splash_fill_rate;
    private String total_plaque_fill;       //填充率-插屏
    private String total_plaque_fill_rate;
    private String total_native_new_plaque_fill;    //填充率-（msg/yuans）插屏
    private String total_native_new_plaque_fill_rate;
    private String total_plaque_video_fill;         //填充率-插屏视频
    private String total_plaque_video_fill_rate;
    private String total_banner_fill;           //填充率-banner
    private String total_banner_fill_rate;
    private String total_native_new_banner_fill;    //填充率-（msg/yuans）banner
    private String total_native_new_banner_fill_rate;
    private String total_video_fill;        //填充率-视频
    private String total_video_fill_rate;
    private String total_native_msg_fill;   //填充率-原生msg
    private String total_native_msg_fill_rate;
    private String total_suspend_icon_fill; //填充率-悬浮icon
    private String total_suspend_icon_fill_rate;



    //活跃用户展示点击-开屏
    private String show_total_ad_active_cnt;
    private String show_total_ad_active_cnt_rate;
    private String click_total_ad_active_cnt;
    private String click_total_ad_active_cnt_rate;



    private String show_splash_ad_active_cnt;
    private String show_splash_ad_active_cnt_rate;
    private String click_splash_ad_active_cnt;
    private String click_splash_ad_active_cnt_rate;

    //插屏
    private String show_plaque_ad_active_cnt;
    private String show_plaque_ad_active_cnt_rate;
    private String click_plaque_ad_active_cnt;
    private String click_plaque_ad_active_cnt_rate;

    //banner
    private String show_banner_ad_active_cnt;
    private String show_banner_ad_active_cnt_rate;
    private String click_banner_ad_active_cnt;
    private String click_banner_ad_active_cnt_rate;

    private String show_video_ad_active_cnt;
    private String show_video_ad_active_cnt_rate;

    private String click_video_ad_active_cnt;
    private String click_video_ad_active_cnt_rate;


    private String show_msg_ad_active_cnt;
    private String show_msg_ad_active_cnt_rate;

    private String click_msg_ad_active_cnt;
    private String click_msg_ad_active_cnt_rate;


    private String show_icon_ad_active_cnt;
    private String show_icon_ad_active_cnt_rate;
    private String click_icon_ad_active_cnt;
    private String click_icon_ad_active_cnt_rate;


    private String appid_tag;

    private String ad_violation_type;

    private String large_ver;



    public String getNative_banner_pv_rate() {
        return native_banner_pv_rate;
    }

    public void setNative_banner_pv_rate(String native_banner_pv_rate) {
        this.native_banner_pv_rate = native_banner_pv_rate;
    }

    public String getBefore_native_banner_pv() {
        return before_native_banner_pv;
    }

    public void setBefore_native_banner_pv(String before_native_banner_pv) {
        this.before_native_banner_pv = before_native_banner_pv;
    }

    public String getBefore_native_msg_pv() {
        return before_native_msg_pv;
    }

    public void setBefore_native_msg_pv(String before_native_msg_pv) {
        this.before_native_msg_pv = before_native_msg_pv;
    }

    public String getBefore_native_new_plaque_pv() {
        return before_native_new_plaque_pv;
    }

    public void setBefore_native_new_plaque_pv(String before_native_new_plaque_pv) {
        this.before_native_new_plaque_pv = before_native_new_plaque_pv;
    }

    public String getNative_msg_pv_rate() {
        return native_msg_pv_rate;
    }

    public void setNative_msg_pv_rate(String native_msg_pv_rate) {
        this.native_msg_pv_rate = native_msg_pv_rate;
    }

    public String getNative_new_plaque_pv_rate() {
        return native_new_plaque_pv_rate;
    }

    public void setNative_new_plaque_pv_rate(String native_new_plaque_pv_rate) {
        this.native_new_plaque_pv_rate = native_new_plaque_pv_rate;
    }

    public String getAvgnum_rate() {
        return avgnum_rate;
    }

    public void setAvgnum_rate(String avgnum_rate) {
        this.avgnum_rate = avgnum_rate;
    }

    public String getDaily_duration_rate() {
        return daily_duration_rate;
    }

    public void setDaily_duration_rate(String daily_duration_rate) {
        this.daily_duration_rate = daily_duration_rate;
    }

    public String getSuspend_icon_ctr() {
        return suspend_icon_ctr;
    }

    public void setSuspend_icon_ctr(String suspend_icon_ctr) {
        this.suspend_icon_ctr = suspend_icon_ctr;
    }

    public String getNative_banner_ctr() {
        return native_banner_ctr;
    }

    public void setNative_banner_ctr(String native_banner_ctr) {
        this.native_banner_ctr = native_banner_ctr;
    }

    public String getNative_plaque_ctr() {
        return native_plaque_ctr;
    }

    public void setNative_plaque_ctr(String native_plaque_ctr) {
        this.native_plaque_ctr = native_plaque_ctr;
    }

    public String getNative_plaque_cpc() {
        return native_plaque_cpc;
    }

    public void setNative_plaque_cpc(String native_plaque_cpc) {
        this.native_plaque_cpc = native_plaque_cpc;
    }

    public String getNative_banner_cpc() {
        return native_banner_cpc;
    }

    public void setNative_banner_cpc(String native_banner_cpc) {
        this.native_banner_cpc = native_banner_cpc;
    }

    public String getSuspend_icon_cpc() {
        return suspend_icon_cpc;
    }

    public void setSuspend_icon_cpc(String suspend_icon_cpc) {
        this.suspend_icon_cpc = suspend_icon_cpc;
    }

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getAvgnum() {
        return avgnum;
    }

    public void setAvgnum(String avgnum) {
        this.avgnum = avgnum;
    }

    public String getAddnum() {
        return addnum;
    }

    public void setAddnum(String addnum) {
        this.addnum = addnum;
    }

    public String getActnum() {
        return actnum;
    }

    public void setActnum(String actnum) {
        this.actnum = actnum;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public String getDaily_duration() {
        return daily_duration;
    }

    public void setDaily_duration(String daily_duration) {
        this.daily_duration = daily_duration;
    }

    public String getMsg_pv() {
        return msg_pv;
    }

    public void setMsg_pv(String msg_pv) {
        this.msg_pv = msg_pv;
    }

    public String getBanner_pv() {
        return banner_pv;
    }

    public void setBanner_pv(String banner_pv) {
        this.banner_pv = banner_pv;
    }

    public String getNative_msg_pv() {
        return native_msg_pv;
    }

    public void setNative_msg_pv(String native_msg_pv) {
        this.native_msg_pv = native_msg_pv;
    }

    public String getNative_new_banner_pv() {
        return native_new_banner_pv;
    }

    public String getIs_x() {
		return is_x;
	}

	public void setIs_x(String is_x) {
		this.is_x = is_x;
	}

	public void setNative_new_banner_pv(String native_new_banner_pv) {
        this.native_new_banner_pv = native_new_banner_pv;
    }

    public String getPlaque_video_pv() {
        return plaque_video_pv;
    }

    public void setPlaque_video_pv(String plaque_video_pv) {
        this.plaque_video_pv = plaque_video_pv;
    }

    public String getNative_plaque_pv() {
        return native_plaque_pv;
    }

    public void setNative_plaque_pv(String native_plaque_pv) {
        this.native_plaque_pv = native_plaque_pv;
    }

    public String getNative_splash_pv() {
        return native_splash_pv;
    }

    public void setNative_splash_pv(String native_splash_pv) {
        this.native_splash_pv = native_splash_pv;
    }

    public String getNative_banner_pv() {
        return native_banner_pv;
    }

    public void setNative_banner_pv(String native_banner_pv) {
        this.native_banner_pv = native_banner_pv;
    }

    public String getSystem_splash_pv() {
        return system_splash_pv;
    }

    public void setSystem_splash_pv(String system_splash_pv) {
        this.system_splash_pv = system_splash_pv;
    }

    public String getPlaque_pv() {
        return plaque_pv;
    }

    public void setPlaque_pv(String plaque_pv) {
        this.plaque_pv = plaque_pv;
    }

    public String getSplash_pv() {
        return splash_pv;
    }

    public void setSplash_pv(String splash_pv) {
        this.splash_pv = splash_pv;
    }

    public String getSuspend_icon_pv() {
        return suspend_icon_pv;
    }

    public void setSuspend_icon_pv(String suspend_icon_pv) {
        this.suspend_icon_pv = suspend_icon_pv;
    }

    public String getVideo_pv() {
        return video_pv;
    }

    public void setVideo_pv(String video_pv) {
        this.video_pv = video_pv;
    }

    public String getNative_new_plaque_pv() {
        return native_new_plaque_pv;
    }

    public void setNative_new_plaque_pv(String native_new_plaque_pv) {
        this.native_new_plaque_pv = native_new_plaque_pv;
    }

    public String getTotal_banner_pv() {
        return total_banner_pv;
    }

    public void setTotal_banner_pv(String total_banner_pv) {
        this.total_banner_pv = total_banner_pv;
    }

    public String getTotal_splash_pv() {
        return total_splash_pv;
    }

    public void setTotal_splash_pv(String total_splash_pv) {
        this.total_splash_pv = total_splash_pv;
    }

    public String getTotal_video_pv() {
        return total_video_pv;
    }

    public void setTotal_video_pv(String total_video_pv) {
        this.total_video_pv = total_video_pv;
    }

    public String getTotal_plaque_pv() {
        return total_plaque_pv;
    }

    public void setTotal_plaque_pv(String total_plaque_pv) {
        this.total_plaque_pv = total_plaque_pv;
    }

    public String getTotal_suspend_icon_pv() {
        return total_suspend_icon_pv;
    }

    public void setTotal_suspend_icon_pv(String total_suspend_icon_pv) {
        this.total_suspend_icon_pv = total_suspend_icon_pv;
    }

    public String getTotal_plaque_video_pv() {
        return total_plaque_video_pv;
    }

    public void setTotal_plaque_video_pv(String total_plaque_video_pv) {
        this.total_plaque_video_pv = total_plaque_video_pv;
    }

    public String getTotal_native_msg_pv() {
        return total_native_msg_pv;
    }

    public void setTotal_native_msg_pv(String total_native_msg_pv) {
        this.total_native_msg_pv = total_native_msg_pv;
    }

    public String getBanner_click() {
        return banner_click;
    }

    public void setBanner_click(String banner_click) {
        this.banner_click = banner_click;
    }

    public String getNative_splash_click() {
        return native_splash_click;
    }

    public void setNative_splash_click(String native_splash_click) {
        this.native_splash_click = native_splash_click;
    }

    public String getNative_plaque_click() {
        return native_plaque_click;
    }

    public void setNative_plaque_click(String native_plaque_click) {
        this.native_plaque_click = native_plaque_click;
    }

    public String getNative_new_plaque_click() {
        return native_new_plaque_click;
    }

    public void setNative_new_plaque_click(String native_new_plaque_click) {
        this.native_new_plaque_click = native_new_plaque_click;
    }

    public String getNative_msg_click() {
        return native_msg_click;
    }

    public void setNative_msg_click(String native_msg_click) {
        this.native_msg_click = native_msg_click;
    }

    public String getVideo_click() {
        return video_click;
    }

    public void setVideo_click(String video_click) {
        this.video_click = video_click;
    }

    public String getSplash_click() {
        return splash_click;
    }

    public void setSplash_click(String splash_click) {
        this.splash_click = splash_click;
    }

    public String getPlaque_video_click() {
        return plaque_video_click;
    }

    public void setPlaque_video_click(String plaque_video_click) {
        this.plaque_video_click = plaque_video_click;
    }

    public String getSystem_splash_click() {
        return system_splash_click;
    }

    public void setSystem_splash_click(String system_splash_click) {
        this.system_splash_click = system_splash_click;
    }

    public String getSuspend_icon_click() {
        return suspend_icon_click;
    }

    public void setSuspend_icon_click(String suspend_icon_click) {
        this.suspend_icon_click = suspend_icon_click;
    }

    public String getPlaque_click() {
        return plaque_click;
    }

    public void setPlaque_click(String plaque_click) {
        this.plaque_click = plaque_click;
    }

    public String getNative_banner_click() {
        return native_banner_click;
    }

    public void setNative_banner_click(String native_banner_click) {
        this.native_banner_click = native_banner_click;
    }

    public String getNative_new_banner_click() {
        return native_new_banner_click;
    }

    public void setNative_new_banner_click(String native_new_banner_click) {
        this.native_new_banner_click = native_new_banner_click;
    }

    public String getPlaque_video_ctr() {
        return plaque_video_ctr;
    }

    public void setPlaque_video_ctr(String plaque_video_ctr) {
        this.plaque_video_ctr = plaque_video_ctr;
    }

    public String getNative_msg_ctr() {
        return native_msg_ctr;
    }

    public void setNative_msg_ctr(String native_msg_ctr) {
        this.native_msg_ctr = native_msg_ctr;
    }

    public String getSplash_ctr() {
        return splash_ctr;
    }

    public void setSplash_ctr(String splash_ctr) {
        this.splash_ctr = splash_ctr;
    }

    public String getBanner_ctr() {
        return banner_ctr;
    }

    public void setBanner_ctr(String banner_ctr) {
        this.banner_ctr = banner_ctr;
    }

    public String getVideo_ctr() {
        return video_ctr;
    }

    public void setVideo_ctr(String video_ctr) {
        this.video_ctr = video_ctr;
    }

    public String getNative_splash_ctr() {
        return native_splash_ctr;
    }

    public void setNative_splash_ctr(String native_splash_ctr) {
        this.native_splash_ctr = native_splash_ctr;
    }

    public String getPlaque_ctr() {
        return plaque_ctr;
    }

    public void setPlaque_ctr(String plaque_ctr) {
        this.plaque_ctr = plaque_ctr;
    }

    public String getNative_new_plaque_ctr() {
        return native_new_plaque_ctr;
    }

    public void setNative_new_plaque_ctr(String native_new_plaque_ctr) {
        this.native_new_plaque_ctr = native_new_plaque_ctr;
    }

    public String getNative_new_banner_ctr() {
        return native_new_banner_ctr;
    }

    public void setNative_new_banner_ctr(String native_new_banner_ctr) {
        this.native_new_banner_ctr = native_new_banner_ctr;
    }

    public String getBanner_ecpm_rate() {
        return banner_ecpm_rate;
    }

    public void setBanner_ecpm_rate(String banner_ecpm_rate) {
        this.banner_ecpm_rate = banner_ecpm_rate;
    }

    public String getNative_msg_ecpm_rate() {
        return native_msg_ecpm_rate;
    }

    public void setNative_msg_ecpm_rate(String native_msg_ecpm_rate) {
        this.native_msg_ecpm_rate = native_msg_ecpm_rate;
    }

    public String getNative_plaque_ecpm_rate() {
        return native_plaque_ecpm_rate;
    }

    public void setNative_plaque_ecpm_rate(String native_plaque_ecpm_rate) {
        this.native_plaque_ecpm_rate = native_plaque_ecpm_rate;
    }

    public String getSuspend_icon_ecpm_rate() {
        return suspend_icon_ecpm_rate;
    }

    public void setSuspend_icon_ecpm_rate(String suspend_icon_ecpm_rate) {
        this.suspend_icon_ecpm_rate = suspend_icon_ecpm_rate;
    }

    public String getPlaque_video_ecpm_rate() {
        return plaque_video_ecpm_rate;
    }

    public void setPlaque_video_ecpm_rate(String plaque_video_ecpm_rate) {
        this.plaque_video_ecpm_rate = plaque_video_ecpm_rate;
    }

    public String getSplash_ecpm_rate() {
        return splash_ecpm_rate;
    }

    public void setSplash_ecpm_rate(String splash_ecpm_rate) {
        this.splash_ecpm_rate = splash_ecpm_rate;
    }

    public String getSystem_splash_ecpm_rate() {
        return system_splash_ecpm_rate;
    }

    public void setSystem_splash_ecpm_rate(String system_splash_ecpm_rate) {
        this.system_splash_ecpm_rate = system_splash_ecpm_rate;
    }

    public String getNative_splash_ecpm_rate() {
        return native_splash_ecpm_rate;
    }

    public void setNative_splash_ecpm_rate(String native_splash_ecpm_rate) {
        this.native_splash_ecpm_rate = native_splash_ecpm_rate;
    }

    public String getNative_new_plaque_ecpm_rate() {
        return native_new_plaque_ecpm_rate;
    }

    public void setNative_new_plaque_ecpm_rate(String native_new_plaque_ecpm_rate) {
        this.native_new_plaque_ecpm_rate = native_new_plaque_ecpm_rate;
    }

    public String getNative_banner_ecpm_rate() {
        return native_banner_ecpm_rate;
    }

    public void setNative_banner_ecpm_rate(String native_banner_ecpm_rate) {
        this.native_banner_ecpm_rate = native_banner_ecpm_rate;
    }

    public String getNative_new_banner_ecpm_rate() {
        return native_new_banner_ecpm_rate;
    }

    public void setNative_new_banner_ecpm_rate(String native_new_banner_ecpm_rate) {
        this.native_new_banner_ecpm_rate = native_new_banner_ecpm_rate;
    }

    public String getVideo_ecpm_rate() {
        return video_ecpm_rate;
    }

    public void setVideo_ecpm_rate(String video_ecpm_rate) {
        this.video_ecpm_rate = video_ecpm_rate;
    }

    public String getPlaque_ecpm_rate() {
        return plaque_ecpm_rate;
    }

    public void setPlaque_ecpm_rate(String plaque_ecpm_rate) {
        this.plaque_ecpm_rate = plaque_ecpm_rate;
    }

    public String getDau_arpu_rate() {
        return dau_arpu_rate;
    }

    public void setDau_arpu_rate(String dau_arpu_rate) {
        this.dau_arpu_rate = dau_arpu_rate;
    }

    public String getVideo_arpu() {
        return video_arpu;
    }

    public void setVideo_arpu(String video_arpu) {
        this.video_arpu = video_arpu;
    }

    public String getTotal_banner_arpu() {
        return total_banner_arpu;
    }

    public void setTotal_banner_arpu(String total_banner_arpu) {
        this.total_banner_arpu = total_banner_arpu;
    }

    public String getNative_new_plaque_arpu() {
        return native_new_plaque_arpu;
    }

    public void setNative_new_plaque_arpu(String native_new_plaque_arpu) {
        this.native_new_plaque_arpu = native_new_plaque_arpu;
    }

    public String getNative_new_banner_arpu() {
        return native_new_banner_arpu;
    }

    public void setNative_new_banner_arpu(String native_new_banner_arpu) {
        this.native_new_banner_arpu = native_new_banner_arpu;
    }

    public String getTotal_suspend_icon_arpu() {
        return total_suspend_icon_arpu;
    }

    public void setTotal_suspend_icon_arpu(String total_suspend_icon_arpu) {
        this.total_suspend_icon_arpu = total_suspend_icon_arpu;
    }

    public String getTotal_plaque_arpu() {
        return total_plaque_arpu;
    }

    public void setTotal_plaque_arpu(String total_plaque_arpu) {
        this.total_plaque_arpu = total_plaque_arpu;
    }

    public String getTotal_plaque_video_arpu() {
        return total_plaque_video_arpu;
    }

    public void setTotal_plaque_video_arpu(String total_plaque_video_arpu) {
        this.total_plaque_video_arpu = total_plaque_video_arpu;
    }

    public String getBanner_arpu() {
        return banner_arpu;
    }

    public void setBanner_arpu(String banner_arpu) {
        this.banner_arpu = banner_arpu;
    }

    public String getTotal_splash_arpu() {
        return total_splash_arpu;
    }

    public void setTotal_splash_arpu(String total_splash_arpu) {
        this.total_splash_arpu = total_splash_arpu;
    }

    public String getDau_arpu() {
        return dau_arpu;
    }

    public void setDau_arpu(String dau_arpu) {
        this.dau_arpu = dau_arpu;
    }

    public String getMsg_arpu() {
        return msg_arpu;
    }

    public void setMsg_arpu(String msg_arpu) {
        this.msg_arpu = msg_arpu;
    }

    public String getSplash_arpu() {
        return splash_arpu;
    }

    public void setSplash_arpu(String splash_arpu) {
        this.splash_arpu = splash_arpu;
    }

    public String getTotal_video_arpu() {
        return total_video_arpu;
    }

    public void setTotal_video_arpu(String total_video_arpu) {
        this.total_video_arpu = total_video_arpu;
    }

    public String getTotal_native_msg_arpu() {
        return total_native_msg_arpu;
    }

    public void setTotal_native_msg_arpu(String total_native_msg_arpu) {
        this.total_native_msg_arpu = total_native_msg_arpu;
    }

    public String getPlaque_arpu() {
        return plaque_arpu;
    }

    public void setPlaque_arpu(String plaque_arpu) {
        this.plaque_arpu = plaque_arpu;
    }

    public String getPlaque_video_arpu() {
        return plaque_video_arpu;
    }

    public void setPlaque_video_arpu(String plaque_video_arpu) {
        this.plaque_video_arpu = plaque_video_arpu;
    }

    public String getSystem_splash_arpu() {
        return system_splash_arpu;
    }

    public void setSystem_splash_arpu(String system_splash_arpu) {
        this.system_splash_arpu = system_splash_arpu;
    }

    public String getSuspend_icon_arpu() {
        return suspend_icon_arpu;
    }

    public void setSuspend_icon_arpu(String suspend_icon_arpu) {
        this.suspend_icon_arpu = suspend_icon_arpu;
    }

    public String getNative_splash_show() {
        return native_splash_show;
    }

    public void setNative_splash_show(String native_splash_show) {
        this.native_splash_show = native_splash_show;
    }

    public String getSplash_show() {
        return splash_show;
    }

    public void setSplash_show(String splash_show) {
        this.splash_show = splash_show;
    }

    public String getNative_new_banner_show() {
        return native_new_banner_show;
    }

    public void setNative_new_banner_show(String native_new_banner_show) {
        this.native_new_banner_show = native_new_banner_show;
    }

    public String getNative_plaque_show() {
        return native_plaque_show;
    }

    public void setNative_plaque_show(String native_plaque_show) {
        this.native_plaque_show = native_plaque_show;
    }

    public String getNative_new_plaque_show() {
        return native_new_plaque_show;
    }

    public void setNative_new_plaque_show(String native_new_plaque_show) {
        this.native_new_plaque_show = native_new_plaque_show;
    }

    public String getNative_banner_show() {
        return native_banner_show;
    }

    public void setNative_banner_show(String native_banner_show) {
        this.native_banner_show = native_banner_show;
    }

    public String getBanner_show() {
        return banner_show;
    }

    public void setBanner_show(String banner_show) {
        this.banner_show = banner_show;
    }

    public String getSuspend_icon_show() {
        return suspend_icon_show;
    }

    public void setSuspend_icon_show(String suspend_icon_show) {
        this.suspend_icon_show = suspend_icon_show;
    }

    public String getSystem_splash_show() {
        return system_splash_show;
    }

    public void setSystem_splash_show(String system_splash_show) {
        this.system_splash_show = system_splash_show;
    }

    public String getVideo_show() {
        return video_show;
    }

    public void setVideo_show(String video_show) {
        this.video_show = video_show;
    }

    public String getPlaque_video_show() {
        return plaque_video_show;
    }

    public void setPlaque_video_show(String plaque_video_show) {
        this.plaque_video_show = plaque_video_show;
    }

    public String getNative_msg_show() {
        return native_msg_show;
    }

    public void setNative_msg_show(String native_msg_show) {
        this.native_msg_show = native_msg_show;
    }

    public String getPlaque_show() {
        return plaque_show;
    }

    public void setPlaque_show(String plaque_show) {
        this.plaque_show = plaque_show;
    }

    public String getBanner_ecpm() {
        return banner_ecpm;
    }

    public void setBanner_ecpm(String banner_ecpm) {
        this.banner_ecpm = banner_ecpm;
    }

    public String getNative_banner_ecpm() {
        return native_banner_ecpm;
    }

    public void setNative_banner_ecpm(String native_banner_ecpm) {
        this.native_banner_ecpm = native_banner_ecpm;
    }

    public String getPlaque_video_ecpm() {
        return plaque_video_ecpm;
    }

    public void setPlaque_video_ecpm(String plaque_video_ecpm) {
        this.plaque_video_ecpm = plaque_video_ecpm;
    }

    public String getNative_new_plaque_ecpm() {
        return native_new_plaque_ecpm;
    }

    public void setNative_new_plaque_ecpm(String native_new_plaque_ecpm) {
        this.native_new_plaque_ecpm = native_new_plaque_ecpm;
    }

    public String getNative_msg_ecpm() {
        return native_msg_ecpm;
    }

    public void setNative_msg_ecpm(String native_msg_ecpm) {
        this.native_msg_ecpm = native_msg_ecpm;
    }

    public String getNative_splash_ecpm() {
        return native_splash_ecpm;
    }

    public void setNative_splash_ecpm(String native_splash_ecpm) {
        this.native_splash_ecpm = native_splash_ecpm;
    }

    public String getSuspend_icon_ecpm() {
        return suspend_icon_ecpm;
    }

    public void setSuspend_icon_ecpm(String suspend_icon_ecpm) {
        this.suspend_icon_ecpm = suspend_icon_ecpm;
    }

    public String getSplash_ecpm() {
        return splash_ecpm;
    }

    public void setSplash_ecpm(String splash_ecpm) {
        this.splash_ecpm = splash_ecpm;
    }

    public String getSystem_splash_ecpm() {
        return system_splash_ecpm;
    }

    public void setSystem_splash_ecpm(String system_splash_ecpm) {
        this.system_splash_ecpm = system_splash_ecpm;
    }

    public String getNative_plaque_ecpm() {
        return native_plaque_ecpm;
    }

    public void setNative_plaque_ecpm(String native_plaque_ecpm) {
        this.native_plaque_ecpm = native_plaque_ecpm;
    }

    public String getVideo_ecpm() {
        return video_ecpm;
    }

    public void setVideo_ecpm(String video_ecpm) {
        this.video_ecpm = video_ecpm;
    }

    public String getPlaque_ecpm() {
        return plaque_ecpm;
    }

    public void setPlaque_ecpm(String plaque_ecpm) {
        this.plaque_ecpm = plaque_ecpm;
    }

    public String getNative_new_banner_ecpm() {
        return native_new_banner_ecpm;
    }

    public void setNative_new_banner_ecpm(String native_new_banner_ecpm) {
        this.native_new_banner_ecpm = native_new_banner_ecpm;
    }

    public String getTotal_income() {
        return total_income;
    }

    public void setTotal_income(String total_income) {
        this.total_income = total_income;
    }

    public String getNative_plaque_income() {
        return native_plaque_income;
    }

    public void setNative_plaque_income(String native_plaque_income) {
        this.native_plaque_income = native_plaque_income;
    }

    public String getVideo_income() {
        return video_income;
    }

    public void setVideo_income(String video_income) {
        this.video_income = video_income;
    }

    public String getNative_msg_income() {
        return native_msg_income;
    }

    public void setNative_msg_income(String native_msg_income) {
        this.native_msg_income = native_msg_income;
    }

    public String getSuspend_icon_income() {
        return suspend_icon_income;
    }

    public void setSuspend_icon_income(String suspend_icon_income) {
        this.suspend_icon_income = suspend_icon_income;
    }

    public String getSplash_income() {
        return splash_income;
    }

    public void setSplash_income(String splash_income) {
        this.splash_income = splash_income;
    }

    public String getBanner_income() {
        return banner_income;
    }

    public void setBanner_income(String banner_income) {
        this.banner_income = banner_income;
    }

    public String getPlaque_video_income() {
        return plaque_video_income;
    }

    public void setPlaque_video_income(String plaque_video_income) {
        this.plaque_video_income = plaque_video_income;
    }

    public String getSystem_splash_income() {
        return system_splash_income;
    }

    public void setSystem_splash_income(String system_splash_income) {
        this.system_splash_income = system_splash_income;
    }

    public String getNative_banner_income() {
        return native_banner_income;
    }

    public void setNative_banner_income(String native_banner_income) {
        this.native_banner_income = native_banner_income;
    }

    public String getNative_new_banner_income() {
        return native_new_banner_income;
    }

    public void setNative_new_banner_income(String native_new_banner_income) {
        this.native_new_banner_income = native_new_banner_income;
    }

    public String getNative_splash_income() {
        return native_splash_income;
    }

    public void setNative_splash_income(String native_splash_income) {
        this.native_splash_income = native_splash_income;
    }

    public String getPlaque_income() {
        return plaque_income;
    }

    public void setPlaque_income(String plaque_income) {
        this.plaque_income = plaque_income;
    }

    public String getNative_new_plaque_income() {
        return native_new_plaque_income;
    }

    public void setNative_new_plaque_income(String native_new_plaque_income) {
        this.native_new_plaque_income = native_new_plaque_income;
    }

    public String getPlaque_video_cpc() {
        return plaque_video_cpc;
    }

    public void setPlaque_video_cpc(String plaque_video_cpc) {
        this.plaque_video_cpc = plaque_video_cpc;
    }

    public String getNative_msg_cpc() {
        return native_msg_cpc;
    }

    public void setNative_msg_cpc(String native_msg_cpc) {
        this.native_msg_cpc = native_msg_cpc;
    }

    public String getNative_new_banner_cpc() {
        return native_new_banner_cpc;
    }

    public void setNative_new_banner_cpc(String native_new_banner_cpc) {
        this.native_new_banner_cpc = native_new_banner_cpc;
    }

    public String getBanner_cpc() {
        return banner_cpc;
    }

    public void setBanner_cpc(String banner_cpc) {
        this.banner_cpc = banner_cpc;
    }

    public String getNative_new_plaque_cpc() {
        return native_new_plaque_cpc;
    }

    public void setNative_new_plaque_cpc(String native_new_plaque_cpc) {
        this.native_new_plaque_cpc = native_new_plaque_cpc;
    }

    public String getVideo_cpc() {
        return video_cpc;
    }

    public void setVideo_cpc(String video_cpc) {
        this.video_cpc = video_cpc;
    }

    public String getNative_splash_cpc() {
        return native_splash_cpc;
    }

    public void setNative_splash_cpc(String native_splash_cpc) {
        this.native_splash_cpc = native_splash_cpc;
    }

    public String getPlaque_cpc() {
        return plaque_cpc;
    }

    public void setPlaque_cpc(String plaque_cpc) {
        this.plaque_cpc = plaque_cpc;
    }

    public String getTotal_banner_pv_rate() {
        return total_banner_pv_rate;
    }

    public void setTotal_banner_pv_rate(String total_banner_pv_rate) {
        this.total_banner_pv_rate = total_banner_pv_rate;
    }

    public String getTotal_splash_pv_rate() {
        return total_splash_pv_rate;
    }

    public void setTotal_splash_pv_rate(String total_splash_pv_rate) {
        this.total_splash_pv_rate = total_splash_pv_rate;
    }

    public String getTotal_video_pv_rate() {
        return total_video_pv_rate;
    }

    public void setTotal_video_pv_rate(String total_video_pv_rate) {
        this.total_video_pv_rate = total_video_pv_rate;
    }

    public String getTotal_plaque_pv_rate() {
        return total_plaque_pv_rate;
    }

    public void setTotal_plaque_pv_rate(String total_plaque_pv_rate) {
        this.total_plaque_pv_rate = total_plaque_pv_rate;
    }

    public String getTotal_suspend_icon_pv_rate() {
        return total_suspend_icon_pv_rate;
    }

    public void setTotal_suspend_icon_pv_rate(String total_suspend_icon_pv_rate) {
        this.total_suspend_icon_pv_rate = total_suspend_icon_pv_rate;
    }

    public String getTotal_plaque_video_pv_rate() {
        return total_plaque_video_pv_rate;
    }

    public void setTotal_plaque_video_pv_rate(String total_plaque_video_pv_rate) {
        this.total_plaque_video_pv_rate = total_plaque_video_pv_rate;
    }

    public String getTotal_native_msg_pv_rate() {
        return total_native_msg_pv_rate;
    }

    public void setTotal_native_msg_pv_rate(String total_native_msg_pv_rate) {
        this.total_native_msg_pv_rate = total_native_msg_pv_rate;
    }

    public String getNative_new_plaque_ctr_rate() {
        return native_new_plaque_ctr_rate;
    }

    public void setNative_new_plaque_ctr_rate(String native_new_plaque_ctr_rate) {
        this.native_new_plaque_ctr_rate = native_new_plaque_ctr_rate;
    }

    public String getPlaque_video_ctr_rate() {
        return plaque_video_ctr_rate;
    }

    public void setPlaque_video_ctr_rate(String plaque_video_ctr_rate) {
        this.plaque_video_ctr_rate = plaque_video_ctr_rate;
    }

    public String getNative_msg_ctr_rate() {
        return native_msg_ctr_rate;
    }

    public void setNative_msg_ctr_rate(String native_msg_ctr_rate) {
        this.native_msg_ctr_rate = native_msg_ctr_rate;
    }

    public String getSplash_ctr_rate() {
        return splash_ctr_rate;
    }

    public void setSplash_ctr_rate(String splash_ctr_rate) {
        this.splash_ctr_rate = splash_ctr_rate;
    }

    public String getBanner_ctr_rate() {
        return banner_ctr_rate;
    }

    public void setBanner_ctr_rate(String banner_ctr_rate) {
        this.banner_ctr_rate = banner_ctr_rate;
    }

    public String getVideo_ctr_rate() {
        return video_ctr_rate;
    }

    public void setVideo_ctr_rate(String video_ctr_rate) {
        this.video_ctr_rate = video_ctr_rate;
    }

    public String getNative_splash_ctr_rate() {
        return native_splash_ctr_rate;
    }

    public void setNative_splash_ctr_rate(String native_splash_ctr_rate) {
        this.native_splash_ctr_rate = native_splash_ctr_rate;
    }

    public String getPlaque_ctr_rate() {
        return plaque_ctr_rate;
    }

    public void setPlaque_ctr_rate(String plaque_ctr_rate) {
        this.plaque_ctr_rate = plaque_ctr_rate;
    }

    public String getNative_new_banner_ctr_rate() {
        return native_new_banner_ctr_rate;
    }

    public void setNative_new_banner_ctr_rate(String native_new_banner_ctr_rate) {
        this.native_new_banner_ctr_rate = native_new_banner_ctr_rate;
    }

    public String getNative_plaque_ctr_rate() {
        return native_plaque_ctr_rate;
    }

    public void setNative_plaque_ctr_rate(String native_plaque_ctr_rate) {
        this.native_plaque_ctr_rate = native_plaque_ctr_rate;
    }

    public String getNative_banner_ctr_rate() {
        return native_banner_ctr_rate;
    }

    public void setNative_banner_ctr_rate(String native_banner_ctr_rate) {
        this.native_banner_ctr_rate = native_banner_ctr_rate;
    }

    public String getSuspend_icon_ctr_rate() {
        return suspend_icon_ctr_rate;
    }

    public void setSuspend_icon_ctr_rate(String suspend_icon_ctr_rate) {
        this.suspend_icon_ctr_rate = suspend_icon_ctr_rate;
    }

    public String getAvgNumRate() {
        return avgNumRate;
    }

    public void setAvgNumRate(String avgNumRate) {
        this.avgNumRate = avgNumRate;
    }

    public String getDailyDurationRate() {
        return dailyDurationRate;
    }

    public void setDailyDurationRate(String dailyDurationRate) {
        this.dailyDurationRate = dailyDurationRate;
    }

    public String getBanner_show_rate() {
        return banner_show_rate;
    }

    public void setBanner_show_rate(String banner_show_rate) {
        this.banner_show_rate = banner_show_rate;
    }

    public String getNew_banner_show_rate() {
        return new_banner_show_rate;
    }

    public void setNew_banner_show_rate(String new_banner_show_rate) {
        this.new_banner_show_rate = new_banner_show_rate;
    }

    public String getSum_total_banner() {
        return sum_total_banner;
    }

    public void setSum_total_banner(String sum_total_banner) {
        this.sum_total_banner = sum_total_banner;
    }

    public String getSum_pv_banner() {
        return sum_pv_banner;
    }

    public void setSum_pv_banner(String sum_pv_banner) {
        this.sum_pv_banner = sum_pv_banner;
    }

    public String getSum_pv_new_banner() {
        return sum_pv_new_banner;
    }

    public void setSum_pv_new_banner(String sum_pv_new_banner) {
        this.sum_pv_new_banner = sum_pv_new_banner;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }

    public String getTemp_id() {
        return temp_id;
    }

    public void setTemp_id(String temp_id) {
        this.temp_id = temp_id;
    }

    public String getTemp_name() {
        return temp_name;
    }

    public void setTemp_name(String temp_name) {
        this.temp_name = temp_name;
    }

    public String getTemp_single_name() {
        return temp_single_name;
    }

    public void setTemp_single_name(String temp_single_name) {
        this.temp_single_name = temp_single_name;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getTotal_banner_click() {
        return total_banner_click;
    }

    public void setTotal_banner_click(String total_banner_click) {
        this.total_banner_click = total_banner_click;
    }

    public String getTotal_splash_click() {
        return total_splash_click;
    }

    public void setTotal_splash_click(String total_splash_click) {
        this.total_splash_click = total_splash_click;
    }

    public String getTotal_video_click() {
        return total_video_click;
    }

    public void setTotal_video_click(String total_video_click) {
        this.total_video_click = total_video_click;
    }

    public String getTotal_plaque_click() {
        return total_plaque_click;
    }

    public void setTotal_plaque_click(String total_plaque_click) {
        this.total_plaque_click = total_plaque_click;
    }

    public String getTotal_suspend_icon_click() {
        return total_suspend_icon_click;
    }

    public void setTotal_suspend_icon_click(String total_suspend_icon_click) {
        this.total_suspend_icon_click = total_suspend_icon_click;
    }

    public String getTotal_plaque_video_click() {
        return total_plaque_video_click;
    }

    public void setTotal_plaque_video_click(String total_plaque_video_click) {
        this.total_plaque_video_click = total_plaque_video_click;
    }

    public String getTotal_native_msg_click() {
        return total_native_msg_click;
    }

    public void setTotal_native_msg_click(String total_native_msg_click) {
        this.total_native_msg_click = total_native_msg_click;
    }

    public String getTotal_banner_click_rate() {
        return total_banner_click_rate;
    }

    public void setTotal_banner_click_rate(String total_banner_click_rate) {
        this.total_banner_click_rate = total_banner_click_rate;
    }

    public String getTotal_splash_click_rate() {
        return total_splash_click_rate;
    }

    public void setTotal_splash_click_rate(String total_splash_click_rate) {
        this.total_splash_click_rate = total_splash_click_rate;
    }

    public String getTotal_video_click_rate() {
        return total_video_click_rate;
    }

    public void setTotal_video_click_rate(String total_video_click_rate) {
        this.total_video_click_rate = total_video_click_rate;
    }

    public String getTotal_plaque_click_rate() {
        return total_plaque_click_rate;
    }

    public void setTotal_plaque_click_rate(String total_plaque_click_rate) {
        this.total_plaque_click_rate = total_plaque_click_rate;
    }

    public String getTotal_suspend_icon_click_rate() {
        return total_suspend_icon_click_rate;
    }

    public void setTotal_suspend_icon_click_rate(String total_suspend_icon_click_rate) {
        this.total_suspend_icon_click_rate = total_suspend_icon_click_rate;
    }

    public String getTotal_plaque_video_click_rate() {
        return total_plaque_video_click_rate;
    }

    public void setTotal_plaque_video_click_rate(String total_plaque_video_click_rate) {
        this.total_plaque_video_click_rate = total_plaque_video_click_rate;
    }

    public String getTotal_native_msg_click_rate() {
        return total_native_msg_click_rate;
    }

    public void setTotal_native_msg_click_rate(String total_native_msg_click_rate) {
        this.total_native_msg_click_rate = total_native_msg_click_rate;
    }

    public String getBanner_avg_click() {
        return banner_avg_click;
    }

    public void setBanner_avg_click(String banner_avg_click) {
        this.banner_avg_click = banner_avg_click;
    }

    public String getNative_splash_avg_click() {
        return native_splash_avg_click;
    }

    public void setNative_splash_avg_click(String native_splash_avg_click) {
        this.native_splash_avg_click = native_splash_avg_click;
    }

    public String getNative_plaque_avg_click() {
        return native_plaque_avg_click;
    }

    public void setNative_plaque_avg_click(String native_plaque_avg_click) {
        this.native_plaque_avg_click = native_plaque_avg_click;
    }

    public String getNative_new_plaque_avg_click() {
        return native_new_plaque_avg_click;
    }

    public void setNative_new_plaque_avg_click(String native_new_plaque_avg_click) {
        this.native_new_plaque_avg_click = native_new_plaque_avg_click;
    }

    public String getNative_msg_avg_click() {
        return native_msg_avg_click;
    }

    public void setNative_msg_avg_click(String native_msg_avg_click) {
        this.native_msg_avg_click = native_msg_avg_click;
    }

    public String getVideo_avg_click() {
        return video_avg_click;
    }

    public void setVideo_avg_click(String video_avg_click) {
        this.video_avg_click = video_avg_click;
    }

    public String getSplash_avg_click() {
        return splash_avg_click;
    }

    public void setSplash_avg_click(String splash_avg_click) {
        this.splash_avg_click = splash_avg_click;
    }

    public String getPlaque_video_avg_click() {
        return plaque_video_avg_click;
    }

    public void setPlaque_video_avg_click(String plaque_video_avg_click) {
        this.plaque_video_avg_click = plaque_video_avg_click;
    }

    public String getSystem_splash_avg_click() {
        return system_splash_avg_click;
    }

    public void setSystem_splash_avg_click(String system_splash_avg_click) {
        this.system_splash_avg_click = system_splash_avg_click;
    }

    public String getSuspend_icon_avg_click() {
        return suspend_icon_avg_click;
    }

    public void setSuspend_icon_avg_click(String suspend_icon_avg_click) {
        this.suspend_icon_avg_click = suspend_icon_avg_click;
    }

    public String getPlaque_avg_click() {
        return plaque_avg_click;
    }

    public void setPlaque_avg_click(String plaque_avg_click) {
        this.plaque_avg_click = plaque_avg_click;
    }

    public String getNative_banner_avg_click() {
        return native_banner_avg_click;
    }

    public void setNative_banner_avg_click(String native_banner_avg_click) {
        this.native_banner_avg_click = native_banner_avg_click;
    }

    public String getNative_new_banner_avg_click() {
        return native_new_banner_avg_click;
    }

    public void setNative_new_banner_avg_click(String native_new_banner_avg_click) {
        this.native_new_banner_avg_click = native_new_banner_avg_click;
    }

    public String getAll_total_pv() {
        return all_total_pv;
    }

    public void setAll_total_pv(String all_total_pv) {
        this.all_total_pv = all_total_pv;
    }

    public String getAll_total_click() {
        return all_total_click;
    }

    public void setAll_total_click(String all_total_click) {
        this.all_total_click = all_total_click;
    }

    public String getAll_total_pv_rate() {
        return all_total_pv_rate;
    }

    public void setAll_total_pv_rate(String all_total_pv_rate) {
        this.all_total_pv_rate = all_total_pv_rate;
    }

    public String getAll_total_click_rate() {
        return all_total_click_rate;
    }

    public void setAll_total_click_rate(String all_total_click_rate) {
        this.all_total_click_rate = all_total_click_rate;
    }


    public String getTotal_video_arpu_rate() {
        return total_video_arpu_rate;
    }

    public void setTotal_video_arpu_rate(String total_video_arpu_rate) {
        this.total_video_arpu_rate = total_video_arpu_rate;
    }

    public String getTotal_native_msg_arpu_rate() {
        return total_native_msg_arpu_rate;
    }

    public void setTotal_native_msg_arpu_rate(String total_native_msg_arpu_rate) {
        this.total_native_msg_arpu_rate = total_native_msg_arpu_rate;
    }

    public String getTotal_splash_arpu_rate() {
        return total_splash_arpu_rate;
    }

    public void setTotal_splash_arpu_rate(String total_splash_arpu_rate) {
        this.total_splash_arpu_rate = total_splash_arpu_rate;
    }

    public String getTotal_plaque_arpu_rate() {
        return total_plaque_arpu_rate;
    }

    public void setTotal_plaque_arpu_rate(String total_plaque_arpu_rate) {
        this.total_plaque_arpu_rate = total_plaque_arpu_rate;
    }

    public String getTotal_plaque_video_arpu_rate() {
        return total_plaque_video_arpu_rate;
    }

    public void setTotal_plaque_video_arpu_rate(String total_plaque_video_arpu_rate) {
        this.total_plaque_video_arpu_rate = total_plaque_video_arpu_rate;
    }

    public String getTotal_banner_arpu_rate() {
        return total_banner_arpu_rate;
    }

    public void setTotal_banner_arpu_rate(String total_banner_arpu_rate) {
        this.total_banner_arpu_rate = total_banner_arpu_rate;
    }

    public String getTotal_suspend_icon_arpu_rate() {
        return total_suspend_icon_arpu_rate;
    }

    public void setTotal_suspend_icon_arpu_rate(String total_suspend_icon_arpu_rate) {
        this.total_suspend_icon_arpu_rate = total_suspend_icon_arpu_rate;
    }

    public String getSplash_cpc() {
        return splash_cpc;
    }

    public void setSplash_cpc(String splash_cpc) {
        this.splash_cpc = splash_cpc;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }


    public String getTotal_show_num() {
        return total_show_num;
    }

    public void setTotal_show_num(String total_show_num) {
        this.total_show_num = total_show_num;
    }

    public String getTotal_click_num() {
        return total_click_num;
    }

    public void setTotal_click_num(String total_click_num) {
        this.total_click_num = total_click_num;
    }


    public String getPlaque_pv_rate() {
        return plaque_pv_rate;
    }

    public void setPlaque_pv_rate(String plaque_pv_rate) {
        this.plaque_pv_rate = plaque_pv_rate;
    }

    public String getPlaque_video_pv_rate() {
        return plaque_video_pv_rate;
    }

    public void setPlaque_video_pv_rate(String plaque_video_pv_rate) {
        this.plaque_video_pv_rate = plaque_video_pv_rate;
    }

    public String getBanner_pv_rate() {
        return banner_pv_rate;
    }

    public void setBanner_pv_rate(String banner_pv_rate) {
        this.banner_pv_rate = banner_pv_rate;
    }

    public String getNative_new_banner_pv_rate() {
        return native_new_banner_pv_rate;
    }

    public void setNative_new_banner_pv_rate(String native_new_banner_pv_rate) {
        this.native_new_banner_pv_rate = native_new_banner_pv_rate;
    }

    public String getVideo_pv_rate() {
        return video_pv_rate;
    }

    public void setVideo_pv_rate(String video_pv_rate) {
        this.video_pv_rate = video_pv_rate;
    }

    public String getSuspend_icon_pv_rate() {
        return suspend_icon_pv_rate;
    }

    public void setSuspend_icon_pv_rate(String suspend_icon_pv_rate) {
        this.suspend_icon_pv_rate = suspend_icon_pv_rate;
    }

    public String getTotal_splash_ctr() {
        return total_splash_ctr;
    }

    public void setTotal_splash_ctr(String total_splash_ctr) {
        this.total_splash_ctr = total_splash_ctr;
    }

    public String getTotal_splash_ctr_rate() {
        return total_splash_ctr_rate;
    }

    public void setTotal_splash_ctr_rate(String total_splash_ctr_rate) {
        this.total_splash_ctr_rate = total_splash_ctr_rate;
    }

    public String getActive_temp_id() {
        return active_temp_id;
    }

    public void setActive_temp_id(String active_temp_id) {
        this.active_temp_id = active_temp_id;
    }

    public String getActive_temp_name() {
        return active_temp_name;
    }

    public void setActive_temp_name(String active_temp_name) {
        this.active_temp_name = active_temp_name;
    }

    public String getAll_total_ctr() {
        return all_total_ctr;
    }

    public void setAll_total_ctr(String all_total_ctr) {
        this.all_total_ctr = all_total_ctr;
    }

    public String getAll_total_ctr_rate() {
        return all_total_ctr_rate;
    }

    public void setAll_total_ctr_rate(String all_total_ctr_rate) {
        this.all_total_ctr_rate = all_total_ctr_rate;
    }

    public String getBanner_request() {
        return banner_request;
    }

    public void setBanner_request(String banner_request) {
        this.banner_request = banner_request;
    }

    public String getPlaque_request() {
        return plaque_request;
    }

    public void setPlaque_request(String plaque_request) {
        this.plaque_request = plaque_request;
    }

    public String getSplash_request() {
        return splash_request;
    }

    public void setSplash_request(String splash_request) {
        this.splash_request = splash_request;
    }

    public String getVideo_request() {
        return video_request;
    }

    public void setVideo_request(String video_request) {
        this.video_request = video_request;
    }

    public String getNative_banner_request() {
        return native_banner_request;
    }

    public void setNative_banner_request(String native_banner_request) {
        this.native_banner_request = native_banner_request;
    }

    public String getNative_plaque_request() {
        return native_plaque_request;
    }

    public void setNative_plaque_request(String native_plaque_request) {
        this.native_plaque_request = native_plaque_request;
    }

    public String getNative_splash_request() {
        return native_splash_request;
    }

    public void setNative_splash_request(String native_splash_request) {
        this.native_splash_request = native_splash_request;
    }

    public String getPlaque_video_request() {
        return plaque_video_request;
    }

    public void setPlaque_video_request(String plaque_video_request) {
        this.plaque_video_request = plaque_video_request;
    }

    public String getNative_msg_request() {
        return native_msg_request;
    }

    public void setNative_msg_request(String native_msg_request) {
        this.native_msg_request = native_msg_request;
    }

    public String getSystem_splash_request() {
        return system_splash_request;
    }

    public void setSystem_splash_request(String system_splash_request) {
        this.system_splash_request = system_splash_request;
    }

    public String getNative_new_plaque_request() {
        return native_new_plaque_request;
    }

    public void setNative_new_plaque_request(String native_new_plaque_request) {
        this.native_new_plaque_request = native_new_plaque_request;
    }

    public String getNative_new_banner_request() {
        return native_new_banner_request;
    }

    public void setNative_new_banner_request(String native_new_banner_request) {
        this.native_new_banner_request = native_new_banner_request;
    }

    public String getSuspend_icon_request() {
        return suspend_icon_request;
    }

    public void setSuspend_icon_request(String suspend_icon_request) {
        this.suspend_icon_request = suspend_icon_request;
    }

    public String getAll_total_request() {
        return all_total_request;
    }

    public void setAll_total_request(String all_total_request) {
        this.all_total_request = all_total_request;
    }

    public String getAll_total_request_rate() {
        return all_total_request_rate;
    }

    public void setAll_total_request_rate(String all_total_request_rate) {
        this.all_total_request_rate = all_total_request_rate;
    }

    public String getTotal_splash_request() {
        return total_splash_request;
    }

    public void setTotal_splash_request(String total_splash_request) {
        this.total_splash_request = total_splash_request;
    }

    public String getTotal_splash_request_rate() {
        return total_splash_request_rate;
    }

    public void setTotal_splash_request_rate(String total_splash_request_rate) {
        this.total_splash_request_rate = total_splash_request_rate;
    }

    public String getTotal_plaque_request() {
        return total_plaque_request;
    }

    public void setTotal_plaque_request(String total_plaque_request) {
        this.total_plaque_request = total_plaque_request;
    }

    public String getTotal_plaque_request_rate() {
        return total_plaque_request_rate;
    }

    public void setTotal_plaque_request_rate(String total_plaque_request_rate) {
        this.total_plaque_request_rate = total_plaque_request_rate;
    }

    public String getTotal_plaque_video_request() {
        return total_plaque_video_request;
    }

    public void setTotal_plaque_video_request(String total_plaque_video_request) {
        this.total_plaque_video_request = total_plaque_video_request;
    }

    public String getTotal_plaque_video_request_rate() {
        return total_plaque_video_request_rate;
    }

    public void setTotal_plaque_video_request_rate(String total_plaque_video_request_rate) {
        this.total_plaque_video_request_rate = total_plaque_video_request_rate;
    }

    public String getTotal_banner_request() {
        return total_banner_request;
    }

    public void setTotal_banner_request(String total_banner_request) {
        this.total_banner_request = total_banner_request;
    }

    public String getTotal_banner_request_rate() {
        return total_banner_request_rate;
    }

    public void setTotal_banner_request_rate(String total_banner_request_rate) {
        this.total_banner_request_rate = total_banner_request_rate;
    }

    public String getTotal_video_request() {
        return total_video_request;
    }

    public void setTotal_video_request(String total_video_request) {
        this.total_video_request = total_video_request;
    }

    public String getTotal_video_request_rate() {
        return total_video_request_rate;
    }

    public void setTotal_video_request_rate(String total_video_request_rate) {
        this.total_video_request_rate = total_video_request_rate;
    }

    public String getTotal_native_msg_request() {
        return total_native_msg_request;
    }

    public void setTotal_native_msg_request(String total_native_msg_request) {
        this.total_native_msg_request = total_native_msg_request;
    }

    public String getTotal_native_msg_request_rate() {
        return total_native_msg_request_rate;
    }

    public void setTotal_native_msg_request_rate(String total_native_msg_request_rate) {
        this.total_native_msg_request_rate = total_native_msg_request_rate;
    }

    public String getTotal_suspend_icon_request() {
        return total_suspend_icon_request;
    }

    public void setTotal_suspend_icon_request(String total_suspend_icon_request) {
        this.total_suspend_icon_request = total_suspend_icon_request;
    }

    public String getTotal_suspend_icon_request_rate() {
        return total_suspend_icon_request_rate;
    }

    public void setTotal_suspend_icon_request_rate(String total_suspend_icon_request_rate) {
        this.total_suspend_icon_request_rate = total_suspend_icon_request_rate;
    }

    public String getSystem_splash_avg_request() {
        return system_splash_avg_request;
    }

    public void setSystem_splash_avg_request(String system_splash_avg_request) {
        this.system_splash_avg_request = system_splash_avg_request;
    }

    public String getSplash_avg_request() {
        return splash_avg_request;
    }

    public void setSplash_avg_request(String splash_avg_request) {
        this.splash_avg_request = splash_avg_request;
    }

    public String getNative_splash_avg_request() {
        return native_splash_avg_request;
    }

    public void setNative_splash_avg_request(String native_splash_avg_request) {
        this.native_splash_avg_request = native_splash_avg_request;
    }

    public String getPlaque_avg_request() {
        return plaque_avg_request;
    }

    public void setPlaque_avg_request(String plaque_avg_request) {
        this.plaque_avg_request = plaque_avg_request;
    }

    public String getNative_new_plaque_avg_request() {
        return native_new_plaque_avg_request;
    }

    public void setNative_new_plaque_avg_request(String native_new_plaque_avg_request) {
        this.native_new_plaque_avg_request = native_new_plaque_avg_request;
    }

    public String getPlaque_video_avg_request() {
        return plaque_video_avg_request;
    }

    public void setPlaque_video_avg_request(String plaque_video_avg_request) {
        this.plaque_video_avg_request = plaque_video_avg_request;
    }

    public String getBanner_avg_request() {
        return banner_avg_request;
    }

    public void setBanner_avg_request(String banner_avg_request) {
        this.banner_avg_request = banner_avg_request;
    }

    public String getNative_new_banner_avg_request() {
        return native_new_banner_avg_request;
    }

    public void setNative_new_banner_avg_request(String native_new_banner_avg_request) {
        this.native_new_banner_avg_request = native_new_banner_avg_request;
    }

    public String getVideo_avg_request() {
        return video_avg_request;
    }

    public void setVideo_avg_request(String video_avg_request) {
        this.video_avg_request = video_avg_request;
    }

    public String getNative_msg_avg_request() {
        return native_msg_avg_request;
    }

    public void setNative_msg_avg_request(String native_msg_avg_request) {
        this.native_msg_avg_request = native_msg_avg_request;
    }

    public String getSuspend_icon_avg_request() {
        return suspend_icon_avg_request;
    }

    public void setSuspend_icon_avg_request(String suspend_icon_avg_request) {
        this.suspend_icon_avg_request = suspend_icon_avg_request;
    }

    public String getBanner_fill() {
        return banner_fill;
    }

    public void setBanner_fill(String banner_fill) {
        this.banner_fill = banner_fill;
    }

    public String getPlaque_fill() {
        return plaque_fill;
    }

    public void setPlaque_fill(String plaque_fill) {
        this.plaque_fill = plaque_fill;
    }

    public String getSplash_fill() {
        return splash_fill;
    }

    public void setSplash_fill(String splash_fill) {
        this.splash_fill = splash_fill;
    }

    public String getVideo_fill() {
        return video_fill;
    }

    public void setVideo_fill(String video_fill) {
        this.video_fill = video_fill;
    }

    public String getNative_banner_fill() {
        return native_banner_fill;
    }

    public void setNative_banner_fill(String native_banner_fill) {
        this.native_banner_fill = native_banner_fill;
    }

    public String getNative_plaque_fill() {
        return native_plaque_fill;
    }

    public void setNative_plaque_fill(String native_plaque_fill) {
        this.native_plaque_fill = native_plaque_fill;
    }

    public String getNative_splash_fill() {
        return native_splash_fill;
    }

    public void setNative_splash_fill(String native_splash_fill) {
        this.native_splash_fill = native_splash_fill;
    }

    public String getPlaque_video_fill() {
        return plaque_video_fill;
    }

    public void setPlaque_video_fill(String plaque_video_fill) {
        this.plaque_video_fill = plaque_video_fill;
    }

    public String getNative_msg_fill() {
        return native_msg_fill;
    }

    public void setNative_msg_fill(String native_msg_fill) {
        this.native_msg_fill = native_msg_fill;
    }

    public String getSystem_splash_fill() {
        return system_splash_fill;
    }

    public void setSystem_splash_fill(String system_splash_fill) {
        this.system_splash_fill = system_splash_fill;
    }

    public String getNative_new_plaque_fill() {
        return native_new_plaque_fill;
    }

    public void setNative_new_plaque_fill(String native_new_plaque_fill) {
        this.native_new_plaque_fill = native_new_plaque_fill;
    }

    public String getNative_new_banner_fill() {
        return native_new_banner_fill;
    }

    public void setNative_new_banner_fill(String native_new_banner_fill) {
        this.native_new_banner_fill = native_new_banner_fill;
    }

    public String getSuspend_icon_fill() {
        return suspend_icon_fill;
    }

    public void setSuspend_icon_fill(String suspend_icon_fill) {
        this.suspend_icon_fill = suspend_icon_fill;
    }

    public String getAll_total_avg_fill() {
        return all_total_avg_fill;
    }

    public void setAll_total_avg_fill(String all_total_avg_fill) {
        this.all_total_avg_fill = all_total_avg_fill;
    }

    public String getAll_total_avg_fill_rate() {
        return all_total_avg_fill_rate;
    }

    public void setAll_total_avg_fill_rate(String all_total_avg_fill_rate) {
        this.all_total_avg_fill_rate = all_total_avg_fill_rate;
    }

    public String getTotal_splash_avg_fill() {
        return total_splash_avg_fill;
    }

    public void setTotal_splash_avg_fill(String total_splash_avg_fill) {
        this.total_splash_avg_fill = total_splash_avg_fill;
    }

    public String getTotal_splash_avg_fill_rate() {
        return total_splash_avg_fill_rate;
    }

    public void setTotal_splash_avg_fill_rate(String total_splash_avg_fill_rate) {
        this.total_splash_avg_fill_rate = total_splash_avg_fill_rate;
    }

    public String getTotal_plaque_avg_fill() {
        return total_plaque_avg_fill;
    }

    public void setTotal_plaque_avg_fill(String total_plaque_avg_fill) {
        this.total_plaque_avg_fill = total_plaque_avg_fill;
    }

    public String getTotal_plaque_avg_fill_rate() {
        return total_plaque_avg_fill_rate;
    }

    public void setTotal_plaque_avg_fill_rate(String total_plaque_avg_fill_rate) {
        this.total_plaque_avg_fill_rate = total_plaque_avg_fill_rate;
    }

    public String getTotal_plaque_video_avg_fill() {
        return total_plaque_video_avg_fill;
    }

    public void setTotal_plaque_video_avg_fill(String total_plaque_video_avg_fill) {
        this.total_plaque_video_avg_fill = total_plaque_video_avg_fill;
    }

    public String getTotal_plaque_video_avg_fill_rate() {
        return total_plaque_video_avg_fill_rate;
    }

    public void setTotal_plaque_video_avg_fill_rate(String total_plaque_video_avg_fill_rate) {
        this.total_plaque_video_avg_fill_rate = total_plaque_video_avg_fill_rate;
    }

    public String getTotal_banner_avg_fill() {
        return total_banner_avg_fill;
    }

    public void setTotal_banner_avg_fill(String total_banner_avg_fill) {
        this.total_banner_avg_fill = total_banner_avg_fill;
    }

    public String getTotal_banner_avg_fill_rate() {
        return total_banner_avg_fill_rate;
    }

    public void setTotal_banner_avg_fill_rate(String total_banner_avg_fill_rate) {
        this.total_banner_avg_fill_rate = total_banner_avg_fill_rate;
    }

    public String getTotal_video_avg_fill() {
        return total_video_avg_fill;
    }

    public void setTotal_video_avg_fill(String total_video_avg_fill) {
        this.total_video_avg_fill = total_video_avg_fill;
    }

    public String getTotal_video_avg_fill_rate() {
        return total_video_avg_fill_rate;
    }

    public void setTotal_video_avg_fill_rate(String total_video_avg_fill_rate) {
        this.total_video_avg_fill_rate = total_video_avg_fill_rate;
    }

    public String getTotal_native_msg_avg_fill() {
        return total_native_msg_avg_fill;
    }

    public void setTotal_native_msg_avg_fill(String total_native_msg_avg_fill) {
        this.total_native_msg_avg_fill = total_native_msg_avg_fill;
    }

    public String getTotal_native_msg_avg_fill_rate() {
        return total_native_msg_avg_fill_rate;
    }

    public void setTotal_native_msg_avg_fill_rate(String total_native_msg_avg_fill_rate) {
        this.total_native_msg_avg_fill_rate = total_native_msg_avg_fill_rate;
    }

    public String getTotal_suspend_icon_avg_fill() {
        return total_suspend_icon_avg_fill;
    }

    public void setTotal_suspend_icon_avg_fill(String total_suspend_icon_avg_fill) {
        this.total_suspend_icon_avg_fill = total_suspend_icon_avg_fill;
    }

    public String getTotal_suspend_icon_avg_fill_rate() {
        return total_suspend_icon_avg_fill_rate;
    }

    public void setTotal_suspend_icon_avg_fill_rate(String total_suspend_icon_avg_fill_rate) {
        this.total_suspend_icon_avg_fill_rate = total_suspend_icon_avg_fill_rate;
    }

    public String getSystem_splash_avg_fill() {
        return system_splash_avg_fill;
    }

    public void setSystem_splash_avg_fill(String system_splash_avg_fill) {
        this.system_splash_avg_fill = system_splash_avg_fill;
    }

    public String getSplash_avg_fill() {
        return splash_avg_fill;
    }

    public void setSplash_avg_fill(String splash_avg_fill) {
        this.splash_avg_fill = splash_avg_fill;
    }

    public String getNative_splash_avg_fill() {
        return native_splash_avg_fill;
    }

    public void setNative_splash_avg_fill(String native_splash_avg_fill) {
        this.native_splash_avg_fill = native_splash_avg_fill;
    }

    public String getPlaque_avg_fill() {
        return plaque_avg_fill;
    }

    public void setPlaque_avg_fill(String plaque_avg_fill) {
        this.plaque_avg_fill = plaque_avg_fill;
    }

    public String getNative_new_plaque_avg_fill() {
        return native_new_plaque_avg_fill;
    }

    public void setNative_new_plaque_avg_fill(String native_new_plaque_avg_fill) {
        this.native_new_plaque_avg_fill = native_new_plaque_avg_fill;
    }

    public String getPlaque_video_avg_fill() {
        return plaque_video_avg_fill;
    }

    public void setPlaque_video_avg_fill(String plaque_video_avg_fill) {
        this.plaque_video_avg_fill = plaque_video_avg_fill;
    }

    public String getBanner_avg_fill() {
        return banner_avg_fill;
    }

    public void setBanner_avg_fill(String banner_avg_fill) {
        this.banner_avg_fill = banner_avg_fill;
    }

    public String getNative_new_banner_avg_fill() {
        return native_new_banner_avg_fill;
    }

    public void setNative_new_banner_avg_fill(String native_new_banner_avg_fill) {
        this.native_new_banner_avg_fill = native_new_banner_avg_fill;
    }

    public String getVideo_avg_fill() {
        return video_avg_fill;
    }

    public void setVideo_avg_fill(String video_avg_fill) {
        this.video_avg_fill = video_avg_fill;
    }

    public String getNative_msg_avg_fill() {
        return native_msg_avg_fill;
    }

    public void setNative_msg_avg_fill(String native_msg_avg_fill) {
        this.native_msg_avg_fill = native_msg_avg_fill;
    }

    public String getSuspend_icon_avg_fill() {
        return suspend_icon_avg_fill;
    }

    public void setSuspend_icon_avg_fill(String suspend_icon_avg_fill) {
        this.suspend_icon_avg_fill = suspend_icon_avg_fill;
    }

    public String getAll_total_fill() {
        return all_total_fill;
    }

    public void setAll_total_fill(String all_total_fill) {
        this.all_total_fill = all_total_fill;
    }

    public String getAll_total_fill_rate() {
        return all_total_fill_rate;
    }

    public void setAll_total_fill_rate(String all_total_fill_rate) {
        this.all_total_fill_rate = all_total_fill_rate;
    }

    public String getTotal_system_splash_fill() {
        return total_system_splash_fill;
    }

    public void setTotal_system_splash_fill(String total_system_splash_fill) {
        this.total_system_splash_fill = total_system_splash_fill;
    }

    public String getTotal_system_splash_fill_rate() {
        return total_system_splash_fill_rate;
    }

    public void setTotal_system_splash_fill_rate(String total_system_splash_fill_rate) {
        this.total_system_splash_fill_rate = total_system_splash_fill_rate;
    }

    public String getTotal_splash_fill() {
        return total_splash_fill;
    }

    public void setTotal_splash_fill(String total_splash_fill) {
        this.total_splash_fill = total_splash_fill;
    }

    public String getTotal_splash_fill_rate() {
        return total_splash_fill_rate;
    }

    public void setTotal_splash_fill_rate(String total_splash_fill_rate) {
        this.total_splash_fill_rate = total_splash_fill_rate;
    }

    public String getTotal_native_splash_fill() {
        return total_native_splash_fill;
    }

    public void setTotal_native_splash_fill(String total_native_splash_fill) {
        this.total_native_splash_fill = total_native_splash_fill;
    }

    public String getTotal_native_splash_fill_rate() {
        return total_native_splash_fill_rate;
    }

    public void setTotal_native_splash_fill_rate(String total_native_splash_fill_rate) {
        this.total_native_splash_fill_rate = total_native_splash_fill_rate;
    }

    public String getTotal_plaque_fill() {
        return total_plaque_fill;
    }

    public void setTotal_plaque_fill(String total_plaque_fill) {
        this.total_plaque_fill = total_plaque_fill;
    }

    public String getTotal_plaque_fill_rate() {
        return total_plaque_fill_rate;
    }

    public void setTotal_plaque_fill_rate(String total_plaque_fill_rate) {
        this.total_plaque_fill_rate = total_plaque_fill_rate;
    }

    public String getTotal_native_new_plaque_fill() {
        return total_native_new_plaque_fill;
    }

    public void setTotal_native_new_plaque_fill(String total_native_new_plaque_fill) {
        this.total_native_new_plaque_fill = total_native_new_plaque_fill;
    }

    public String getTotal_native_new_plaque_fill_rate() {
        return total_native_new_plaque_fill_rate;
    }

    public void setTotal_native_new_plaque_fill_rate(String total_native_new_plaque_fill_rate) {
        this.total_native_new_plaque_fill_rate = total_native_new_plaque_fill_rate;
    }

    public String getTotal_plaque_video_fill() {
        return total_plaque_video_fill;
    }

    public void setTotal_plaque_video_fill(String total_plaque_video_fill) {
        this.total_plaque_video_fill = total_plaque_video_fill;
    }

    public String getTotal_plaque_video_fill_rate() {
        return total_plaque_video_fill_rate;
    }

    public void setTotal_plaque_video_fill_rate(String total_plaque_video_fill_rate) {
        this.total_plaque_video_fill_rate = total_plaque_video_fill_rate;
    }

    public String getTotal_banner_fill() {
        return total_banner_fill;
    }

    public void setTotal_banner_fill(String total_banner_fill) {
        this.total_banner_fill = total_banner_fill;
    }

    public String getTotal_banner_fill_rate() {
        return total_banner_fill_rate;
    }

    public void setTotal_banner_fill_rate(String total_banner_fill_rate) {
        this.total_banner_fill_rate = total_banner_fill_rate;
    }

    public String getTotal_native_new_banner_fill() {
        return total_native_new_banner_fill;
    }

    public void setTotal_native_new_banner_fill(String total_native_new_banner_fill) {
        this.total_native_new_banner_fill = total_native_new_banner_fill;
    }

    public String getTotal_native_new_banner_fill_rate() {
        return total_native_new_banner_fill_rate;
    }

    public void setTotal_native_new_banner_fill_rate(String total_native_new_banner_fill_rate) {
        this.total_native_new_banner_fill_rate = total_native_new_banner_fill_rate;
    }

    public String getTotal_video_fill() {
        return total_video_fill;
    }

    public void setTotal_video_fill(String total_video_fill) {
        this.total_video_fill = total_video_fill;
    }

    public String getTotal_video_fill_rate() {
        return total_video_fill_rate;
    }

    public void setTotal_video_fill_rate(String total_video_fill_rate) {
        this.total_video_fill_rate = total_video_fill_rate;
    }

    public String getTotal_native_msg_fill() {
        return total_native_msg_fill;
    }

    public void setTotal_native_msg_fill(String total_native_msg_fill) {
        this.total_native_msg_fill = total_native_msg_fill;
    }

    public String getTotal_native_msg_fill_rate() {
        return total_native_msg_fill_rate;
    }

    public void setTotal_native_msg_fill_rate(String total_native_msg_fill_rate) {
        this.total_native_msg_fill_rate = total_native_msg_fill_rate;
    }

    public String getTotal_suspend_icon_fill() {
        return total_suspend_icon_fill;
    }

    public void setTotal_suspend_icon_fill(String total_suspend_icon_fill) {
        this.total_suspend_icon_fill = total_suspend_icon_fill;
    }

    public String getTotal_suspend_icon_fill_rate() {
        return total_suspend_icon_fill_rate;
    }

    public void setTotal_suspend_icon_fill_rate(String total_suspend_icon_fill_rate) {
        this.total_suspend_icon_fill_rate = total_suspend_icon_fill_rate;
    }

    public String getPlaque_video_cpc_rate() {
        return plaque_video_cpc_rate;
    }

    public void setPlaque_video_cpc_rate(String plaque_video_cpc_rate) {
        this.plaque_video_cpc_rate = plaque_video_cpc_rate;
    }

    public String getNative_msg_cpc_rate() {
        return native_msg_cpc_rate;
    }

    public void setNative_msg_cpc_rate(String native_msg_cpc_rate) {
        this.native_msg_cpc_rate = native_msg_cpc_rate;
    }

    public String getNative_new_banner_cpc_rate() {
        return native_new_banner_cpc_rate;
    }

    public void setNative_new_banner_cpc_rate(String native_new_banner_cpc_rate) {
        this.native_new_banner_cpc_rate = native_new_banner_cpc_rate;
    }

    public String getBanner_cpc_rate() {
        return banner_cpc_rate;
    }

    public void setBanner_cpc_rate(String banner_cpc_rate) {
        this.banner_cpc_rate = banner_cpc_rate;
    }

    public String getNative_new_plaque_cpc_rate() {
        return native_new_plaque_cpc_rate;
    }

    public void setNative_new_plaque_cpc_rate(String native_new_plaque_cpc_rate) {
        this.native_new_plaque_cpc_rate = native_new_plaque_cpc_rate;
    }

    public String getVideo_cpc_rate() {
        return video_cpc_rate;
    }

    public void setVideo_cpc_rate(String video_cpc_rate) {
        this.video_cpc_rate = video_cpc_rate;
    }

    public String getNative_splash_cpc_rate() {
        return native_splash_cpc_rate;
    }

    public void setNative_splash_cpc_rate(String native_splash_cpc_rate) {
        this.native_splash_cpc_rate = native_splash_cpc_rate;
    }

    public String getPlaque_cpc_rate() {
        return plaque_cpc_rate;
    }

    public void setPlaque_cpc_rate(String plaque_cpc_rate) {
        this.plaque_cpc_rate = plaque_cpc_rate;
    }

    public String getNative_plaque_cpc_rate() {
        return native_plaque_cpc_rate;
    }

    public void setNative_plaque_cpc_rate(String native_plaque_cpc_rate) {
        this.native_plaque_cpc_rate = native_plaque_cpc_rate;
    }

    public String getNative_banner_cpc_rate() {
        return native_banner_cpc_rate;
    }

    public void setNative_banner_cpc_rate(String native_banner_cpc_rate) {
        this.native_banner_cpc_rate = native_banner_cpc_rate;
    }

    public String getSuspend_icon_cpc_rate() {
        return suspend_icon_cpc_rate;
    }

    public void setSuspend_icon_cpc_rate(String suspend_icon_cpc_rate) {
        this.suspend_icon_cpc_rate = suspend_icon_cpc_rate;
    }

    public String getSplash_cpc_rate() {
        return splash_cpc_rate;
    }

    public void setSplash_cpc_rate(String splash_cpc_rate) {
        this.splash_cpc_rate = splash_cpc_rate;
    }

    public String getAll_total_show() {
        return all_total_show;
    }

    public void setAll_total_show(String all_total_show) {
        this.all_total_show = all_total_show;
    }


    public String getShow_total_ad_active_cnt() {
        return show_total_ad_active_cnt;
    }

    public void setShow_total_ad_active_cnt(String show_total_ad_active_cnt) {
        this.show_total_ad_active_cnt = show_total_ad_active_cnt;
    }

    public String getShow_total_ad_active_cnt_rate() {
        return show_total_ad_active_cnt_rate;
    }

    public void setShow_total_ad_active_cnt_rate(String show_total_ad_active_cnt_rate) {
        this.show_total_ad_active_cnt_rate = show_total_ad_active_cnt_rate;
    }

    public String getClick_total_ad_active_cnt() {
        return click_total_ad_active_cnt;
    }

    public void setClick_total_ad_active_cnt(String click_total_ad_active_cnt) {
        this.click_total_ad_active_cnt = click_total_ad_active_cnt;
    }

    public String getClick_total_ad_active_cnt_rate() {
        return click_total_ad_active_cnt_rate;
    }

    public void setClick_total_ad_active_cnt_rate(String click_total_ad_active_cnt_rate) {
        this.click_total_ad_active_cnt_rate = click_total_ad_active_cnt_rate;
    }

    public String getShow_splash_ad_active_cnt() {
        return show_splash_ad_active_cnt;
    }

    public void setShow_splash_ad_active_cnt(String show_splash_ad_active_cnt) {
        this.show_splash_ad_active_cnt = show_splash_ad_active_cnt;
    }

    public String getShow_splash_ad_active_cnt_rate() {
        return show_splash_ad_active_cnt_rate;
    }

    public void setShow_splash_ad_active_cnt_rate(String show_splash_ad_active_cnt_rate) {
        this.show_splash_ad_active_cnt_rate = show_splash_ad_active_cnt_rate;
    }

    public String getClick_splash_ad_active_cnt() {
        return click_splash_ad_active_cnt;
    }

    public void setClick_splash_ad_active_cnt(String click_splash_ad_active_cnt) {
        this.click_splash_ad_active_cnt = click_splash_ad_active_cnt;
    }

    public String getClick_splash_ad_active_cnt_rate() {
        return click_splash_ad_active_cnt_rate;
    }

    public void setClick_splash_ad_active_cnt_rate(String click_splash_ad_active_cnt_rate) {
        this.click_splash_ad_active_cnt_rate = click_splash_ad_active_cnt_rate;
    }

    public String getShow_plaque_ad_active_cnt() {
        return show_plaque_ad_active_cnt;
    }

    public void setShow_plaque_ad_active_cnt(String show_plaque_ad_active_cnt) {
        this.show_plaque_ad_active_cnt = show_plaque_ad_active_cnt;
    }

    public String getShow_plaque_ad_active_cnt_rate() {
        return show_plaque_ad_active_cnt_rate;
    }

    public void setShow_plaque_ad_active_cnt_rate(String show_plaque_ad_active_cnt_rate) {
        this.show_plaque_ad_active_cnt_rate = show_plaque_ad_active_cnt_rate;
    }

    public String getClick_plaque_ad_active_cnt() {
        return click_plaque_ad_active_cnt;
    }

    public void setClick_plaque_ad_active_cnt(String click_plaque_ad_active_cnt) {
        this.click_plaque_ad_active_cnt = click_plaque_ad_active_cnt;
    }

    public String getClick_plaque_ad_active_cnt_rate() {
        return click_plaque_ad_active_cnt_rate;
    }

    public void setClick_plaque_ad_active_cnt_rate(String click_plaque_ad_active_cnt_rate) {
        this.click_plaque_ad_active_cnt_rate = click_plaque_ad_active_cnt_rate;
    }

    public String getShow_banner_ad_active_cnt() {
        return show_banner_ad_active_cnt;
    }

    public void setShow_banner_ad_active_cnt(String show_banner_ad_active_cnt) {
        this.show_banner_ad_active_cnt = show_banner_ad_active_cnt;
    }

    public String getShow_banner_ad_active_cnt_rate() {
        return show_banner_ad_active_cnt_rate;
    }

    public void setShow_banner_ad_active_cnt_rate(String show_banner_ad_active_cnt_rate) {
        this.show_banner_ad_active_cnt_rate = show_banner_ad_active_cnt_rate;
    }

    public String getClick_banner_ad_active_cnt() {
        return click_banner_ad_active_cnt;
    }

    public void setClick_banner_ad_active_cnt(String click_banner_ad_active_cnt) {
        this.click_banner_ad_active_cnt = click_banner_ad_active_cnt;
    }

    public String getClick_banner_ad_active_cnt_rate() {
        return click_banner_ad_active_cnt_rate;
    }

    public void setClick_banner_ad_active_cnt_rate(String click_banner_ad_active_cnt_rate) {
        this.click_banner_ad_active_cnt_rate = click_banner_ad_active_cnt_rate;
    }

    public String getShow_video_ad_active_cnt() {
        return show_video_ad_active_cnt;
    }

    public void setShow_video_ad_active_cnt(String show_video_ad_active_cnt) {
        this.show_video_ad_active_cnt = show_video_ad_active_cnt;
    }

    public String getShow_video_ad_active_cnt_rate() {
        return show_video_ad_active_cnt_rate;
    }

    public void setShow_video_ad_active_cnt_rate(String show_video_ad_active_cnt_rate) {
        this.show_video_ad_active_cnt_rate = show_video_ad_active_cnt_rate;
    }

    public String getClick_video_ad_active_cnt() {
        return click_video_ad_active_cnt;
    }

    public void setClick_video_ad_active_cnt(String click_video_ad_active_cnt) {
        this.click_video_ad_active_cnt = click_video_ad_active_cnt;
    }

    public String getClick_video_ad_active_cnt_rate() {
        return click_video_ad_active_cnt_rate;
    }

    public void setClick_video_ad_active_cnt_rate(String click_video_ad_active_cnt_rate) {
        this.click_video_ad_active_cnt_rate = click_video_ad_active_cnt_rate;
    }

    public String getShow_msg_ad_active_cnt() {
        return show_msg_ad_active_cnt;
    }

    public void setShow_msg_ad_active_cnt(String show_msg_ad_active_cnt) {
        this.show_msg_ad_active_cnt = show_msg_ad_active_cnt;
    }

    public String getShow_msg_ad_active_cnt_rate() {
        return show_msg_ad_active_cnt_rate;
    }

    public void setShow_msg_ad_active_cnt_rate(String show_msg_ad_active_cnt_rate) {
        this.show_msg_ad_active_cnt_rate = show_msg_ad_active_cnt_rate;
    }

    public String getClick_msg_ad_active_cnt() {
        return click_msg_ad_active_cnt;
    }

    public void setClick_msg_ad_active_cnt(String click_msg_ad_active_cnt) {
        this.click_msg_ad_active_cnt = click_msg_ad_active_cnt;
    }

    public String getClick_msg_ad_active_cnt_rate() {
        return click_msg_ad_active_cnt_rate;
    }

    public void setClick_msg_ad_active_cnt_rate(String click_msg_ad_active_cnt_rate) {
        this.click_msg_ad_active_cnt_rate = click_msg_ad_active_cnt_rate;
    }

    public String getShow_icon_ad_active_cnt() {
        return show_icon_ad_active_cnt;
    }

    public void setShow_icon_ad_active_cnt(String show_icon_ad_active_cnt) {
        this.show_icon_ad_active_cnt = show_icon_ad_active_cnt;
    }

    public String getShow_icon_ad_active_cnt_rate() {
        return show_icon_ad_active_cnt_rate;
    }

    public void setShow_icon_ad_active_cnt_rate(String show_icon_ad_active_cnt_rate) {
        this.show_icon_ad_active_cnt_rate = show_icon_ad_active_cnt_rate;
    }

    public String getClick_icon_ad_active_cnt() {
        return click_icon_ad_active_cnt;
    }

    public void setClick_icon_ad_active_cnt(String click_icon_ad_active_cnt) {
        this.click_icon_ad_active_cnt = click_icon_ad_active_cnt;
    }

    public String getClick_icon_ad_active_cnt_rate() {
        return click_icon_ad_active_cnt_rate;
    }

    public void setClick_icon_ad_active_cnt_rate(String click_icon_ad_active_cnt_rate) {
        this.click_icon_ad_active_cnt_rate = click_icon_ad_active_cnt_rate;
    }

    public String getNative_plaque_pv_rate() {
        return native_plaque_pv_rate;
    }

    public void setNative_plaque_pv_rate(String native_plaque_pv_rate) {
        this.native_plaque_pv_rate = native_plaque_pv_rate;
    }

    public String getNative_splash_pv_rate() {
        return native_splash_pv_rate;
    }

    public void setNative_splash_pv_rate(String native_splash_pv_rate) {
        this.native_splash_pv_rate = native_splash_pv_rate;
    }

    public String getSystem_splash_pv_rate() {
        return system_splash_pv_rate;
    }

    public void setSystem_splash_pv_rate(String system_splash_pv_rate) {
        this.system_splash_pv_rate = system_splash_pv_rate;
    }

    public String getSystem_splash_avg_click_rate() {
        return system_splash_avg_click_rate;
    }

    public void setSystem_splash_avg_click_rate(String system_splash_avg_click_rate) {
        this.system_splash_avg_click_rate = system_splash_avg_click_rate;
    }

    public String getSplash_avg_click_rate() {
        return splash_avg_click_rate;
    }

    public void setSplash_avg_click_rate(String splash_avg_click_rate) {
        this.splash_avg_click_rate = splash_avg_click_rate;
    }

    public String getBanner_avg_click_rate() {
        return banner_avg_click_rate;
    }

    public void setBanner_avg_click_rate(String banner_avg_click_rate) {
        this.banner_avg_click_rate = banner_avg_click_rate;
    }

    public String getNative_splash_avg_click_rate() {
        return native_splash_avg_click_rate;
    }

    public void setNative_splash_avg_click_rate(String native_splash_avg_click_rate) {
        this.native_splash_avg_click_rate = native_splash_avg_click_rate;
    }

    public String getNative_plaque_avg_click_rate() {
        return native_plaque_avg_click_rate;
    }

    public void setNative_plaque_avg_click_rate(String native_plaque_avg_click_rate) {
        this.native_plaque_avg_click_rate = native_plaque_avg_click_rate;
    }

    public String getNative_new_plaque_avg_click_rate() {
        return native_new_plaque_avg_click_rate;
    }

    public void setNative_new_plaque_avg_click_rate(String native_new_plaque_avg_click_rate) {
        this.native_new_plaque_avg_click_rate = native_new_plaque_avg_click_rate;
    }

    public String getNative_msg_avg_click_rate() {
        return native_msg_avg_click_rate;
    }

    public void setNative_msg_avg_click_rate(String native_msg_avg_click_rate) {
        this.native_msg_avg_click_rate = native_msg_avg_click_rate;
    }

    public String getVideo_avg_click_rate() {
        return video_avg_click_rate;
    }

    public void setVideo_avg_click_rate(String video_avg_click_rate) {
        this.video_avg_click_rate = video_avg_click_rate;
    }

    public String getPlaque_video_avg_click_rate() {
        return plaque_video_avg_click_rate;
    }

    public void setPlaque_video_avg_click_rate(String plaque_video_avg_click_rate) {
        this.plaque_video_avg_click_rate = plaque_video_avg_click_rate;
    }

    public String getSuspend_icon_avg_click_rate() {
        return suspend_icon_avg_click_rate;
    }

    public void setSuspend_icon_avg_click_rate(String suspend_icon_avg_click_rate) {
        this.suspend_icon_avg_click_rate = suspend_icon_avg_click_rate;
    }

    public String getPlaque_avg_click_rate() {
        return plaque_avg_click_rate;
    }

    public void setPlaque_avg_click_rate(String plaque_avg_click_rate) {
        this.plaque_avg_click_rate = plaque_avg_click_rate;
    }

    public String getNative_banner_avg_click_rate() {
        return native_banner_avg_click_rate;
    }

    public void setNative_banner_avg_click_rate(String native_banner_avg_click_rate) {
        this.native_banner_avg_click_rate = native_banner_avg_click_rate;
    }

    public String getNative_new_banner_avg_click_rate() {
        return native_new_banner_avg_click_rate;
    }

    public void setNative_new_banner_avg_click_rate(String native_new_banner_avg_click_rate) {
        this.native_new_banner_avg_click_rate = native_new_banner_avg_click_rate;
    }

    public String getSplash_pv_rate() {
        return splash_pv_rate;
    }

    public void setSplash_pv_rate(String splash_pv_rate) {
        this.splash_pv_rate = splash_pv_rate;
    }

    public String getPackagename() {
        return packagename;
    }

    public void setPackagename(String packagename) {
        this.packagename = packagename;
    }


    public String getAppid_tag() {
        return appid_tag;
    }

    public void setAppid_tag(String appid_tag) {
        this.appid_tag = appid_tag;
    }

    public String getAd_violation_type() {
        return ad_violation_type;
    }

    public void setAd_violation_type(String ad_violation_type) {
        this.ad_violation_type = ad_violation_type;
    }

    public String getLarge_ver() {
        return large_ver;
    }

    public void setLarge_ver(String large_ver) {
        this.large_ver = large_ver;
    }
}
