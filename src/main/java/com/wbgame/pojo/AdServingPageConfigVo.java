package com.wbgame.pojo;

/**
 * <AUTHOR>
 * @Classname AdServingPageConfigVo
 * @Description TODO
 * @Date 2021/6/24 18:01
 */
public class AdServingPageConfigVo extends CommonConfigInfo{

    private String id;
    private String icon;            //icon地址
    private String productName;     //产品名
    private String dominant;        //公司主体
    private String productDesc;     //产品描述
    private String downloadUrl;     //下载包地址
    private String version;         //版本
    private String packageSize;     //包大小
    private String pageUrl;         //投放地址
    private String domain;          //域名

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getPageUrl() {
        return pageUrl;
    }

    public void setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDominant() {
        return dominant;
    }

    public void setDominant(String dominant) {
        this.dominant = dominant;
    }

    public String getProductDesc() {
        return productDesc;
    }

    public void setProductDesc(String productDesc) {
        this.productDesc = productDesc;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPackageSize() {
        return packageSize;
    }

    public void setPackageSize(String packageSize) {
        this.packageSize = packageSize;
    }
}
