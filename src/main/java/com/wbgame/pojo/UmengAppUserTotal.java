package com.wbgame.pojo;

import java.util.Date;

/**
 * 新版友盟应用汇总明细
 */
public class UmengAppUserTotal {
    private Date tdate;

    private Integer appid;

    /**
     * 友盟key
     */
    private String appkey;

    /**
     * 产品名称
     */
    private String appname;

    /**
     * 新增 或  活跃构成新用户
     */
    private Integer addnum;

    /**
     * 活跃 或  活跃构成总用户
     */
    private Integer actnum;

    /**
     * 启动
     */
    private Integer startnum;

    /**
     * 累计新增
     */
    private Long alladdnum;

    /**
     * 日人均使用时长，单位秒
     */
    private Integer duration;

    /**
     * 次日留存
     */
    private Integer oneKeep;

    /**
     * 7日留存
     */
    private Integer sevenKeep;

    /**
     * 30日留存
     */
    private Integer thirthKeep;

    public Date getTdate() {
        return tdate;
    }

    public void setTdate(Date tdate) {
        this.tdate = tdate;
    }

    public Integer getAppid() {
        return appid;
    }

    public void setAppid(Integer appid) {
        this.appid = appid;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public Integer getAddnum() {
        return addnum;
    }

    public void setAddnum(Integer addnum) {
        this.addnum = addnum;
    }

    public Integer getActnum() {
        return actnum;
    }

    public void setActnum(Integer actnum) {
        this.actnum = actnum;
    }

    public Integer getStartnum() {
        return startnum;
    }

    public void setStartnum(Integer startnum) {
        this.startnum = startnum;
    }

    public Long getAlladdnum() {
        return alladdnum;
    }

    public void setAlladdnum(Long alladdnum) {
        this.alladdnum = alladdnum;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getOneKeep() {
        return oneKeep;
    }

    public void setOneKeep(Integer oneKeep) {
        this.oneKeep = oneKeep;
    }

    public Integer getSevenKeep() {
        return sevenKeep;
    }

    public void setSevenKeep(Integer sevenKeep) {
        this.sevenKeep = sevenKeep;
    }

    public Integer getThirthKeep() {
        return thirthKeep;
    }

    public void setThirthKeep(Integer thirthKeep) {
        this.thirthKeep = thirthKeep;
    }
}