package com.wbgame.pojo;

import java.math.BigDecimal;

/**
 * 红包产品参数配置
 * <AUTHOR>
 * @date 2020年1月6日
 */
public class RedpackConfigInfo {
	/**产品id */
    private Integer appid;

    /**应用名称*/
    private String appName;

    /**产品阈值*/
    private Integer maxValue;

    /**预存费用*/
    private BigDecimal depositFee;

    /**总消耗金额*/
    private BigDecimal totalFee;

    /**日消耗金额*/
    private BigDecimal dayFee;
    
    /**提现说明*/
    private String descUrl;

    /**奖励阈值*/
    private Integer boundsValue;
    
    /**视频数阈值*/
    private Integer videoNums;
    
    /**余额阈值*/
    private Integer balanceNums;

    private Integer appCategory;

    public Integer getVideoNums() {
		return videoNums;
	}

	public void setVideoNums(Integer videoNums) {
		this.videoNums = videoNums;
	}

	public Integer getBalanceNums() {
		return balanceNums;
	}

	public void setBalanceNums(Integer balanceNums) {
		this.balanceNums = balanceNums;
	}

	public Integer getAppid() {
        return appid;
    }

    public void setAppid(Integer appid) {
        this.appid = appid;
    }

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public Integer getMaxValue() {
		return maxValue;
	}

	public void setMaxValue(Integer maxValue) {
		this.maxValue = maxValue;
	}

	public BigDecimal getDepositFee() {
		return depositFee;
	}

	public void setDepositFee(BigDecimal depositFee) {
		this.depositFee = depositFee;
	}

	public BigDecimal getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(BigDecimal totalFee) {
		this.totalFee = totalFee;
	}

	public BigDecimal getDayFee() {
		return dayFee;
	}

	public void setDayFee(BigDecimal dayFee) {
		this.dayFee = dayFee;
	}

	public String getDescUrl() {
		return descUrl;
	}

	public void setDescUrl(String descUrl) {
		this.descUrl = descUrl;
	}

	public Integer getBoundsValue() {
		return boundsValue;
	}

	public void setBoundsValue(Integer boundsValue) {
		this.boundsValue = boundsValue;
	}

	public Integer getAppCategory() {
		return appCategory;
	}

	public void setAppCategory(Integer appCategory) {
		this.appCategory = appCategory;
	}
}