package com.wbgame.pojo;

import java.math.BigDecimal;

/**
 * 小游戏-换量数据查询-卖量收入报表
 * <AUTHOR>
 * @date 2020年4月27日
 */
public class AppExchangeVolume {
	private Integer id;
	
	/**日期*/
    private String tdate;

    /**渠道*/
    private String channelName;

    /**我方导出产品*/
    private String gameName;

    /**产品appid*/
    private String appid;

    /**合作渠道*/
    private String orgChannel;

    /**渠道买量产品*/
    private String orgGameName;

    /**买量产品appid*/
    private String orgAppid;

    /**单价*/
    private BigDecimal price;
    
    /**渠道新增*/
    private Integer channel;
    
    /**导出收入*/
    private BigDecimal derivedRevenue;

    /**计价方式*/
    private String pricingType;

    /**后台*/
    private String platform;

    /**后台地址*/
    private String platformUrl;
    
    /**时长*/
    private String times;

    /**留存*/
    private String twoRate;
    
    /**投放买量*/
    private String volumes;
    
    /**投放支出*/
    private String costs;
    
    
    public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate == null ? null : tdate.trim();
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName == null ? null : channelName.trim();
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName == null ? null : gameName.trim();
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid == null ? null : appid.trim();
    }

    public String getOrgChannel() {
        return orgChannel;
    }

    public void setOrgChannel(String orgChannel) {
        this.orgChannel = orgChannel == null ? null : orgChannel.trim();
    }

    public String getOrgGameName() {
        return orgGameName;
    }

    public void setOrgGameName(String orgGameName) {
        this.orgGameName = orgGameName == null ? null : orgGameName.trim();
    }

    public String getOrgAppid() {
        return orgAppid;
    }

    public void setOrgAppid(String orgAppid) {
        this.orgAppid = orgAppid == null ? null : orgAppid.trim();
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getChannel() {
		return channel;
	}

	public void setChannel(Integer channel) {
		this.channel = channel;
	}

	public String getPricingType() {
        return pricingType;
    }

    public void setPricingType(String pricingType) {
        this.pricingType = pricingType == null ? null : pricingType.trim();
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform == null ? null : platform.trim();
    }

    public String getPlatformUrl() {
        return platformUrl;
    }

    public void setPlatformUrl(String platformUrl) {
        this.platformUrl = platformUrl == null ? null : platformUrl.trim();
    }

	public BigDecimal getDerivedRevenue() {
		return derivedRevenue;
	}

	public void setDerivedRevenue(BigDecimal derivedRevenue) {
		this.derivedRevenue = derivedRevenue;
	}

	public String getTimes() {
		return times;
	}

	public void setTimes(String times) {
		this.times = times;
	}

	public String getTwoRate() {
		return twoRate;
	}

	public void setTwoRate(String twoRate) {
		this.twoRate = twoRate;
	}

	public String getVolumes() {
		return volumes;
	}

	public void setVolumes(String volumes) {
		this.volumes = volumes;
	}

	public String getCosts() {
		return costs;
	}

	public void setCosts(String costs) {
		this.costs = costs;
	}

}