package com.wbgame.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description xiaomi违规记录 vo
 * @Date 2024/11/18 16:00
 */
@Data
public class XiaomiViolationRecordVo {
    //开发者主体
    private String company;
    //处罚类型
    private String punish_type;
    //应用id
    private String appid;
    //动能名称
    private String appname;
    //子渠道
    private String channel;
    //上线名称
    private String online_name;
    //小米平台appid
    private String tappid;
    //包名
    private String package_name;
    //广告位名称
    private String adsense_name;
    //广告位id
    private String adsense_id;
    //广告位类型名称
    private String adsense_type;
    //违规类型
    private String violation_type;
    //违规时间
    private String violation_time;
    //违规原因
    private String violation_reason;
    //处理截止时间
    private String deadline;
    //状态
    private String status;
    //状态最后更新时间
    private String last_modified_time;

    //变现平台
    private String platform;
    //广告源类型
    private String sdk_adtype;
    //广告使用类型
    private String ad_usage_type;
    //用于区分广告位是应用内还是应用外（0-应用内，1-应用外，默认为0）
    private String is_out_app;
    //bidding模式，1-是 0-否
    private String bidding_mode;
    //广告策略
    private String ad_strategy;

    private String temp_id;

    private String temp_name;

    //最近7天收入
    private String last_week_revenue;

    //最近7天展示
    private String last_week_show;
    //最近7天点击
    private String last_week_click;

    //ecpm：近七天收入/近七天展示*1000
    private String ecpm;

    //ctr：近七天点击/近七天展示*100%
    private String ctr;

    //cpc：近七天收入/近七天点击
    private String cpc;

    //渠道产品自定义分组
    private String appid_tag;

    //总次数
    private Integer total_violation_count;

    //【横幅违规次数】、
    private Integer banner_violation_count;

    // 【原生违规次数】、
    private Integer yuans_violation_count;

    // 【插屏违规次数】、
    private Integer plaque_violation_count;

    // 【视频违规次数】、
    private Integer video_violation_count;

    // 应用违规次数
    private Integer app_violation_count;



}
