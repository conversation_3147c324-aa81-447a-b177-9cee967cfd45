package com.wbgame.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "单机兑换码参数")
public class ExchangeCodeParam extends CommonRequestParam{

    @ApiModelProperty(value = "兑换码",dataType = "String")
    private String code;

    @ApiModelProperty(value = "兑换值",dataType = "String")
    private String code_value;

    @ApiModelProperty(value = "状态",dataType = "Integer")
    private Integer status;

    @ApiModelProperty(value = "创建人",dataType = "String")
    private String create_owner;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode_value() {
        return code_value;
    }

    public void setCode_value(String code_value) {
        this.code_value = code_value;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreate_owner() {
        return create_owner;
    }

    public void setCreate_owner(String create_owner) {
        this.create_owner = create_owner;
    }
}
