package com.wbgame.pojo.param;

import io.swagger.annotations.ApiModelProperty;

public class PayRefundParam extends CommonPageParam{

    @ApiModelProperty(value = "支付方式",dataType = "String")
    private String paytype;

    @ApiModelProperty(value = "应用分类",dataType = "String")
    private String app_category;

    @ApiModelProperty(value = "项目id",dataType = "String")
    private String pid;

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }

    public String getApp_category() {
        return app_category;
    }

    public void setApp_category(String app_category) {
        this.app_category = app_category;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
}
