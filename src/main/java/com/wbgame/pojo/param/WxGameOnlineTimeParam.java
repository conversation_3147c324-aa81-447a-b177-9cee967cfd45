package com.wbgame.pojo.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * desc:小游戏设备在线时长和启动次数param
 * createBy:xugx
 * date：2023-03-23
 */
@ApiModel(value = "小游戏设备在线时长和启动次数")
public class WxGameOnlineTimeParam extends CommonPageParam{
	
	
    @ApiModelProperty(value = "品牌",dataType = "String")
	private String brand;

    @ApiModelProperty(value = "机型",dataType = "String")
	private String model;
    
    @ApiModelProperty(value = "系统",dataType = "String")
	private String osui;

	public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getOsui() {
		return osui;
	}

	public void setOsui(String osui) {
		this.osui = osui;
	}
    
}
