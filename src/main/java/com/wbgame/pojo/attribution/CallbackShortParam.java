package com.wbgame.pojo.attribution;

import com.wbgame.pojo.PageSizeParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/10/11 10:03
 */
@Data
public class CallbackShortParam extends PageSizeParam {

    @ApiModelProperty(value = "短链")
    private String shortcut;

    @ApiModelProperty(value = "短链唯一命名")
    private String special_name;

    @ApiModelProperty(value = "dn appid")
    private String appid;

    @ApiModelProperty(value = "应用分类")
    private String app_category;

    @ApiModelProperty(value = "媒体")
    private String media;

    @ApiModelProperty(value = "准入项目id，多条用逗号拼接")
    private String pid;

    @ApiModelProperty(value = "提供给oppo填写，上报需要投放应用的包名，快应用填写快应用id, 华为ads展示为密钥")
    private String pn;

    @ApiModelProperty(value = "准入渠道，多条用逗号拼接")
    private String channel;

    @ApiModelProperty(value = "是否启用 1 启用")
    private Integer enabled;

    @ApiModelProperty(value = "创建人")
    private String create_user;


    @ApiModelProperty(value = "更新人")
    private String update_user;

    @ApiModelProperty(value = "完整监控链路")
    private String monitor_link;

    @ApiModelProperty(value = "排序字段")
    private String order_str;


}
