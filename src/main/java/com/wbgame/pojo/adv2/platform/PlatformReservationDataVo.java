package com.wbgame.pojo.adv2.platform;

/**
 * <AUTHOR>
 * @Classname PlatformDataVo
 * @Description TODO
 * @Date 2023/03/21 14:26
 */
public class PlatformReservationDataVo {

    private String tdate;                   //日期
    private String platform;                //媒体
    private String appid;                   //产品id
    private String appname;                 //产品名
    private String tappname;                //渠道产品名称
    private String tappid;                  //平台产品id
    private String channel;                 //渠道子渠道
    private String taccount;                //平台主体账号


    private String details_pv;              //预约详情pv
    private String details_uv;              //预约详情UV
    private String details_view;            //预约详情浏览设备数
    private String preorder_users_today;    //当日预约量
    private String preorder_users_total;    //累计预约量
    private String preorder_users_cancel;    //预约取消用户数
    private String preorder_success_rate;   //详情页预约转化率
    private String icon_click_rate;         //icon点击率
    private String preorder_hot;            //预约热度
    private String preorder_exposure_rate;  //曝光预约转化率
    private String exposure_rate_middle_value;   //曝光预约转化率品类中间值
    private String preorder_rate_middle_value;   //详情页预约转化率品类中间值

    public String getTaccount() {
        return taccount;
    }

    public void setTaccount(String taccount) {
        this.taccount = taccount;
    }

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public String getTappname() {
        return tappname;
    }

    public void setTappname(String tappname) {
        this.tappname = tappname;
    }

    public String getTappid() {
        return tappid;
    }

    public void setTappid(String tappid) {
        this.tappid = tappid;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDetails_pv() {
        return details_pv;
    }

    public void setDetails_pv(String details_pv) {
        this.details_pv = details_pv;
    }

    public String getDetails_uv() {
        return details_uv;
    }

    public void setDetails_uv(String details_uv) {
        this.details_uv = details_uv;
    }

    public String getDetails_view() {
        return details_view;
    }

    public void setDetails_view(String details_view) {
        this.details_view = details_view;
    }

    public String getPreorder_users_today() {
        return preorder_users_today;
    }

    public void setPreorder_users_today(String preorder_users_today) {
        this.preorder_users_today = preorder_users_today;
    }

    public String getPreorder_users_total() {
        return preorder_users_total;
    }

    public void setPreorder_users_total(String preorder_users_total) {
        this.preorder_users_total = preorder_users_total;
    }

    public String getPreorder_users_cancel() {
        return preorder_users_cancel;
    }

    public void setPreorder_users_cancel(String preorder_users_cancel) {
        this.preorder_users_cancel = preorder_users_cancel;
    }

    public String getPreorder_success_rate() {
        return preorder_success_rate;
    }

    public void setPreorder_success_rate(String preorder_success_rate) {
        this.preorder_success_rate = preorder_success_rate;
    }

    public String getPreorder_hot() {
        return preorder_hot;
    }

    public void setPreorder_hot(String preorder_hot) {
        this.preorder_hot = preorder_hot;
    }

    public String getPreorder_exposure_rate() {
        return preorder_exposure_rate;
    }

    public void setPreorder_exposure_rate(String preorder_exposure_rate) {
        this.preorder_exposure_rate = preorder_exposure_rate;
    }

    public String getExposure_rate_middle_value() {
        return exposure_rate_middle_value;
    }

    public void setExposure_rate_middle_value(String exposure_rate_middle_value) {
        this.exposure_rate_middle_value = exposure_rate_middle_value;
    }

    public String getPreorder_rate_middle_value() {
        return preorder_rate_middle_value;
    }

    public void setPreorder_rate_middle_value(String preorder_rate_middle_value) {
        this.preorder_rate_middle_value = preorder_rate_middle_value;
    }

    public String getIcon_click_rate() {
        return icon_click_rate;
    }

    public void setIcon_click_rate(String icon_click_rate) {
        this.icon_click_rate = icon_click_rate;
    }
}
