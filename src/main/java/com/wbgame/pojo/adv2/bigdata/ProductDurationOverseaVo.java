package com.wbgame.pojo.adv2.bigdata;

import lombok.Data;

/**
 * 产品使用时长分布 vo
 */
@Data
public class ProductDurationOverseaVo {

    /**
     * 日期
     */
    private String tdate;

    /**
     * 产品ID
     */
    private String appid;

    /**
     * 产品名称
     */
    private String appname;

    /**
     * 子渠道
     */
    private String channel;

    /**
     * 0-30秒用户数
     */
    private String user_0_30s;

    /**
     * 30秒-1分钟用户数
     */
    private String user_30s_1m;

    /**
     * 1-2分钟用户数
     */
    private String user_1_2m;

    /**
     * 2-3分钟用户数
     */
    private String user_2_3m;

    /**
     * 3-5分钟用户数
     */
    private String user_3_5m;

    /**
     * 5-8分钟用户数
     */
    private String user_5_8m;

    /**
     * 8-10分钟用户数
     */
    private String user_8_10m;

    /**
     * 10分钟用户数
     */
    private String user_10m_plus;

    /**
     * 其他用户数
     */
    private String user_other;

    /**
     * stay_time用户数
     */
    private String stay_time_cnt;

    /**
     * 新增人数
     */
    private String regs;

    /**
     * 活跃人数
     */
    private String dau;


}
