package com.wbgame.pojo.adv2.bigdata;

import com.wbgame.pojo.PageSizeParam;
import lombok.Data;

import java.util.List;

/**
 * 网赚报表-新用户通过率及PV数据 dto
 */
@Data
public class NewUserRateDTO extends PageSizeParam {


    /**
     * 开始时间
     */
    private String start_date;

    /**
     * 结束时间
     */
    private String end_date;


    /**
     * 产品id
     */
    private String appid;

    /**
     * 应用分类
     */
    private String app_category;

    /**
     * 投放渠道
     */
    private String buy_id;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 项目ID
     */
    private String pid;

    /**
     * 账户
     */
    private String buy_act;

    /**
     * 根据账号备注查询出来的账号信息
     */
    //private List<String> accountList;

    private String accounts;


    /**
     * 账户备注
     */
    private String account_remark;

    /**
     * 分组
     */
    private String group;

    /**
     * 排序
     */
    private String order_str;

    //导出表列
    private String value;
    //导出文件名
    private String export_file_name;


}
