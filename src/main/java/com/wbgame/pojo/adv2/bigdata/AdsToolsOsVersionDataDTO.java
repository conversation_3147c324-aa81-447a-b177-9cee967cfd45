package com.wbgame.pojo.adv2.bigdata;

import com.wbgame.pojo.PageSizeParam;
import lombok.Data;

/**
 * 工具系统版本分布 vo
 */
@Data
public class AdsToolsOsVersionDataDTO extends PageSizeParam {

    /**
     * 起始时间
     */
    private String startDate;
    private String endDate;

    /**
     * 产品ID
     */
    private String appid;

    /**
     * 应用分类
     */
    private String app_category;

    /**
     * 国内标识 true:国内，false：海外
     */
    private String insideFlag;

    /**
     * 设备品牌
     */
    private String brand;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 系统版本
     */
    private String os_ver;

    //是否分页标记
    private boolean pageFlag;

    private String group;

    /**
     * 排序
     */
    private String order_str;

    //导出表列
    private String value;
    //导出文件名
    private String export_file_name;

}
