package com.wbgame.pojo.adv2;

public class DnExtendGroupMonitorVo {
	
	private String tdate; // 日期
	private String appid; // 产品
	private String cha_id; // 子渠道
	private String prjid; // 项目id
	private String actnum; // dau
	private int addnum; // 新增
	private String addrate; // 新增占比
	private String income; // 实时收入
	private String dau_arpu; // dau arpu
	private double plaque_per_pv; // 插屏人均pv
	private String plaque_per_pv_match; // 插屏人均pv环比
	private double plaque_ecpm; // 插屏ecpm
	private String plaque_ecpm_match; // 插屏ecpm环比
	private double video_per_pv; // 视频人均pv
	private String video_per_pv_match; // 视频人均pv环比
	private double video_ecpm; // 视频ecpm
	private String video_ecpm_match; // 视频ecpm环比
	private double msg_per_pv; // 信息流人均pv
	private String msg_per_pv_match; // 信息流人均pv环比
	private double msg_ecpm; // 信息流ecpm
	private String msg_ecpm_match; // 信息流ecpm环比
	
	
	public String getTdate() {
		return tdate;
	}
	public void setTdate(String tdate) {
		this.tdate = tdate;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getCha_id() {
		return cha_id;
	}
	public void setCha_id(String cha_id) {
		this.cha_id = cha_id;
	}
	public String getPrjid() {
		return prjid;
	}
	public void setPrjid(String prjid) {
		this.prjid = prjid;
	}
	public String getActnum() {
		return actnum;
	}
	public void setActnum(String actnum) {
		this.actnum = actnum;
	}
	public int getAddnum() {
		return addnum;
	}
	public void setAddnum(int addnum) {
		this.addnum = addnum;
	}
	public String getAddrate() {
		return addrate;
	}
	public void setAddrate(String addrate) {
		this.addrate = addrate;
	}
	public String getIncome() {
		return income;
	}
	public void setIncome(String income) {
		this.income = income;
	}
	public String getDau_arpu() {
		return dau_arpu;
	}
	public void setDau_arpu(String dau_arpu) {
		this.dau_arpu = dau_arpu;
	}
	public double getPlaque_per_pv() {
		return plaque_per_pv;
	}
	public void setPlaque_per_pv(double plaque_per_pv) {
		this.plaque_per_pv = plaque_per_pv;
	}
	public String getPlaque_per_pv_match() {
		return plaque_per_pv_match;
	}
	public void setPlaque_per_pv_match(String plaque_per_pv_match) {
		this.plaque_per_pv_match = plaque_per_pv_match;
	}
	public double getPlaque_ecpm() {
		return plaque_ecpm;
	}
	public void setPlaque_ecpm(double plaque_ecpm) {
		this.plaque_ecpm = plaque_ecpm;
	}
	public String getPlaque_ecpm_match() {
		return plaque_ecpm_match;
	}
	public void setPlaque_ecpm_match(String plaque_ecpm_match) {
		this.plaque_ecpm_match = plaque_ecpm_match;
	}
	public double getVideo_per_pv() {
		return video_per_pv;
	}
	public void setVideo_per_pv(double video_per_pv) {
		this.video_per_pv = video_per_pv;
	}
	public String getVideo_per_pv_match() {
		return video_per_pv_match;
	}
	public void setVideo_per_pv_match(String video_per_pv_match) {
		this.video_per_pv_match = video_per_pv_match;
	}
	public double getVideo_ecpm() {
		return video_ecpm;
	}
	public void setVideo_ecpm(double video_ecpm) {
		this.video_ecpm = video_ecpm;
	}
	public String getVideo_ecpm_match() {
		return video_ecpm_match;
	}
	public void setVideo_ecpm_match(String video_ecpm_match) {
		this.video_ecpm_match = video_ecpm_match;
	}
	public double getMsg_per_pv() {
		return msg_per_pv;
	}
	public void setMsg_per_pv(double msg_per_pv) {
		this.msg_per_pv = msg_per_pv;
	}
	public String getMsg_per_pv_match() {
		return msg_per_pv_match;
	}
	public void setMsg_per_pv_match(String msg_per_pv_match) {
		this.msg_per_pv_match = msg_per_pv_match;
	}
	public double getMsg_ecpm() {
		return msg_ecpm;
	}
	public void setMsg_ecpm(double msg_ecpm) {
		this.msg_ecpm = msg_ecpm;
	}
	public String getMsg_ecpm_match() {
		return msg_ecpm_match;
	}
	public void setMsg_ecpm_match(String msg_ecpm_match) {
		this.msg_ecpm_match = msg_ecpm_match;
	}
	
}
