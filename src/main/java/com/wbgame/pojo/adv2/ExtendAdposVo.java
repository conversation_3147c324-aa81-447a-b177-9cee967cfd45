package com.wbgame.pojo.adv2;

import java.lang.reflect.Field;

public class ExtendAdposVo {
	
	private String id;
	private String appid; // 应用
	private String cha_id; // 子渠道
	private String prjid; // 项目id
	private String user_group; // 用户群
	private String adpos_type; // 广告位类型
	private String adpos; // 广告位
	private String strategy; // 广告策略
	private String adstyle; // 广告样式
	private int showrate; // 展示比例
	private String statu; // 启用状态，0-失效 1-生效
	private String note; // 备注
	private String extraparam; // 其它参数
	private String createtime; // 创建时间
	private String lasttime; // 最后操作时间
	private String cuser; // 操作人
	
	private String delaySecond; // 广告延迟出现，单位秒
	private String startLv; // 第几关开始可以出现广告
	private String endLv; // 第几关结束出现广告
	private String lvInterval; // 广告出现间隔，单位次数
	private String xdelay; // 关闭按钮延迟时间，单位ms
	private String autoInterval; // 自动刷新间隔，单位秒 默认0
	private String out; // 用于区分广告位是应用内还是应用外（0-应用内，1-应用外，默认为0）
	
	/** 投放渠道 */
	private String buy_id;
	/** 投放账户 */
	private String buy_act;
	/** 自定义参数 */
	private String custom_param;

	/** 项目id分组筛选参数 */
	private String prjid_group_id;

	/**
	 * 给用户自定义的补充配置，格式jsonArray，例：
	 * [
	 *     {
	 *         "user_group":"all",
	 *         "adpos_type":"plaque",
	 *         "adstyle":"0"
	 *     },
	 *     {
	 *         "user_group":"xmpb_A",
	 *         "adpos_type":"plaque",
	 *         "adstyle":"0"
	 *     },
	 *     {
	 *         "user_group":"xmpb_B",
	 *         "adpos_type":"plaque",
	 *         "adstyle":"15"
	 *     }
	 * ]
	 * 指定adstyle 作为目标值，其他作为条件，条件满足的情况下，覆盖adstyle
	 */
	private String extra_config;

	private String config;
	/**
	 * 是否优先使用配置中的策略
	 */
	private Integer config_first;

	/**
	 * 过滤id
	 */
	private String filter_id;

	/** 渠道产品自定义分组筛选参数 */
	private String appid_tag;
	private String appid_tag_rev;

	/** 广告源 */
	private String adsid;
	/** sdk广告源类型 */
	private String sdktype;
	/** gap校准实际ctr */
	private String real_ctr;
	/** 广告位展示数 */
	private String show_num;
	/** 广告位点击数 */
	private String click_num;
	/** 注水位置CTR */
	private String ctr;
	/** 项目id活跃用户数 */
	private String dau;
	/** 记录id */
	private String record_id;

	public Integer getConfig_first() {
		return config_first;
	}

	public void setConfig_first(Integer config_first) {
		this.config_first = config_first;
	}

	public String getConfig() {
		return config;
	}

	public void setConfig(String config) {
		this.config = config;
	}

	public String getExtra_config() {
		return extra_config;
	}

	public void setExtra_config(String extra_config) {
		this.extra_config = extra_config;
	}

	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getCha_id() {
		return cha_id;
	}
	public void setCha_id(String cha_id) {
		this.cha_id = cha_id;
	}
	public String getAdpos_type() {
		return adpos_type;
	}
	public String getPrjid() {
		return prjid;
	}
	public void setPrjid(String prjid) {
		this.prjid = prjid;
	}
	public String getUser_group() {
		return user_group;
	}
	public void setUser_group(String user_group) {
		this.user_group = user_group;
	}
	public void setAdpos_type(String adpos_type) {
		this.adpos_type = adpos_type;
	}
	public String getAdpos() {
		return adpos;
	}
	public void setAdpos(String adpos) {
		this.adpos = adpos;
	}
	public String getStrategy() {
		return strategy;
	}
	public void setStrategy(String strategy) {
		this.strategy = strategy;
	}
	public String getAdstyle() {
		return adstyle;
	}
	public void setAdstyle(String adstyle) {
		this.adstyle = adstyle;
	}
	public int getShowrate() {
		return showrate;
	}
	public void setShowrate(int showrate) {
		this.showrate = showrate;
	}
	public String getExtraparam() {
		return extraparam;
	}
	public void setExtraparam(String extraparam) {
		this.extraparam = extraparam;
	}
	public String getStatu() {
		return statu;
	}
	public void setStatu(String statu) {
		this.statu = statu;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public String getCreatetime() {
		return createtime;
	}
	public void setCreatetime(String createtime) {
		this.createtime = createtime;
	}
	public String getDelaySecond() {
		return delaySecond;
	}
	public void setDelaySecond(String delaySecond) {
		this.delaySecond = delaySecond;
	}
	public String getStartLv() {
		return startLv;
	}
	public void setStartLv(String startLv) {
		this.startLv = startLv;
	}
	public String getLvInterval() {
		return lvInterval;
	}
	public void setLvInterval(String lvInterval) {
		this.lvInterval = lvInterval;
	}
	public String getEndLv() {
		return endLv;
	}
	public void setEndLv(String endLv) {
		this.endLv = endLv;
	}
	public String getLasttime() {
		return lasttime;
	}
	public void setLasttime(String lasttime) {
		this.lasttime = lasttime;
	}
	public String getCuser() {
		return cuser;
	}
	public void setCuser(String cuser) {
		this.cuser = cuser;
	}
	public String getBuy_id() {
		return buy_id;
	}
	public void setBuy_id(String buy_id) {
		this.buy_id = buy_id;
	}
	public String getBuy_act() {
		return buy_act;
	}
	public void setBuy_act(String buy_act) {
		this.buy_act = buy_act;
	}
	public String getXdelay() {
		return xdelay;
	}
	public void setXdelay(String xdelay) {
		this.xdelay = xdelay;
	}
	public String getAutoInterval() {
		return autoInterval;
	}
	public void setAutoInterval(String autoInterval) {
		this.autoInterval = autoInterval;
	}
	public String getOut() {
		return out;
	}
	public void setOut(String out) {
		this.out = out;
	}
	public String getCustom_param() {
		return custom_param;
	}
	public void setCustom_param(String custom_param) {
		this.custom_param = custom_param;
	}

	public String getPrjid_group_id() {
		return prjid_group_id;
	}

	public void setPrjid_group_id(String prjid_group_id) {
		this.prjid_group_id = prjid_group_id;
	}

	public String getFilter_id() {
		return filter_id;
	}

	public void setFilter_id(String filter_id) {
		this.filter_id = filter_id;
	}

	public String getAppid_tag() {
		return appid_tag;
	}

	public void setAppid_tag(String appid_tag) {
		this.appid_tag = appid_tag;
	}

	public String getAppid_tag_rev() {
		return appid_tag_rev;
	}

	public void setAppid_tag_rev(String appid_tag_rev) {
		this.appid_tag_rev = appid_tag_rev;
	}

	public String getAdsid() {
		return adsid;
	}

	public void setAdsid(String adsid) {
		this.adsid = adsid;
	}

	public String getSdktype() {
		return sdktype;
	}

	public void setSdktype(String sdktype) {
		this.sdktype = sdktype;
	}

	public String getReal_ctr() {
		return real_ctr;
	}

	public void setReal_ctr(String real_ctr) {
		this.real_ctr = real_ctr;
	}

	public String getShow_num() {
		return show_num;
	}

	public void setShow_num(String show_num) {
		this.show_num = show_num;
	}

	public String getClick_num() {
		return click_num;
	}

	public void setClick_num(String click_num) {
		this.click_num = click_num;
	}

	public String getCtr() {
		return ctr;
	}

	public void setCtr(String ctr) {
		this.ctr = ctr;
	}

	public String getDau() {
		return dau;
	}

	public void setDau(String dau) {
		this.dau = dau;
	}

	public String getRecord_id() {
		return record_id;
	}

	public void setRecord_id(String record_id) {
		this.record_id = record_id;
	}

	public boolean isMatch(String key, String value) throws NoSuchFieldException, IllegalAccessException {
		Class<ExtendAdposVo> clazz = ExtendAdposVo.class;
		Field f = clazz.getDeclaredField(key);
		f.setAccessible(true);
		return value.equals(f.get(this));
	}

	public void setSpecifyField(String key, String value) throws NoSuchFieldException, IllegalAccessException {
		Class<ExtendAdposVo> clazz = ExtendAdposVo.class;
		Field f = clazz.getDeclaredField(key);
		f.setAccessible(true);
		f.set(this, value);
	}
}
