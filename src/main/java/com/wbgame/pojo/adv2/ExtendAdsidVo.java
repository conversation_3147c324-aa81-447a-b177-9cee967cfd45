package com.wbgame.pojo.adv2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("广告源管理vo")
public class ExtendAdsidVo {

	@ApiModelProperty("所属应用")
	private String appid; // 所属应用
	@ApiModelProperty("所属子渠道")
	private String cha_id; // 所属子渠道
	@ApiModelProperty("广告id")
	private String adsid; // 广告id
	@ApiModelProperty("平台")
	private String agent; // 平台
	@ApiModelProperty("sdk-code")
	private String sdk_code; // sdk-code
	@ApiModelProperty("sdk-appid")
	private String sdk_appid; // sdk-appid
	@ApiModelProperty("sdk-appkey")
	private String sdk_appkey; // sdk-appkey
	@ApiModelProperty("// sdk广告源类型，更详细")
	private String sdk_adtype; // sdk广告源类型，更详细
	@ApiModelProperty("广告位类型")
	private String adpos_type; // 广告位类型
	@ApiModelProperty("广告使用类型")
	private String open_type; // 广告使用类型
	@ApiModelProperty("备注信息")
	private String note; // 备注信息
	@ApiModelProperty("创建时间")
	private String createtime; // 创建时间
	@ApiModelProperty("最后操作时间")
	private String lasttime; // 最后操作时间
	@ApiModelProperty("操作人")
	private String cuser; // 操作人

	@ApiModelProperty("底价")
	private String cpmFloor; // 底价
	@ApiModelProperty("// bidding模式，1-是 0-否")
	private String bidding; // bidding模式，1-是 0-否
	@ApiModelProperty("版位id")
	private String unit_id; // 版位id
	@ApiModelProperty("匹配key")
	private String mapkey;
	@ApiModelProperty("tobid的广告源id")
	private String tobid_id;
	@ApiModelProperty("自定义参数，json字符")
	private String params;
	@ApiModelProperty("用于区分广告位是应用内还是应用外（0-应用内，1-应用外，默认为0）")
	private String out;



	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getCha_id() {
		return cha_id;
	}
	public void setCha_id(String cha_id) {
		this.cha_id = cha_id;
	}
	public String getAdsid() {
		return adsid;
	}
	public void setAdsid(String adsid) {
		this.adsid = adsid;
	}
	public String getAgent() {
		return agent;
	}
	public void setAgent(String agent) {
		this.agent = agent;
	}
	public String getSdk_code() {
		return sdk_code;
	}
	public void setSdk_code(String sdk_code) {
		this.sdk_code = sdk_code;
	}
	public String getSdk_appid() {
		return sdk_appid;
	}
	public void setSdk_appid(String sdk_appid) {
		this.sdk_appid = sdk_appid;
	}
	public String getSdk_appkey() {
		return sdk_appkey;
	}
	public void setSdk_appkey(String sdk_appkey) {
		this.sdk_appkey = sdk_appkey;
	}
	public String getSdk_adtype() {
		return sdk_adtype;
	}
	public void setSdk_adtype(String sdk_adtype) {
		this.sdk_adtype = sdk_adtype;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public String getCreatetime() {
		return createtime;
	}
	public void setCreatetime(String createtime) {
		this.createtime = createtime;
	}
	public String getAdpos_type() {
		return adpos_type;
	}
	public void setAdpos_type(String adpos_type) {
		this.adpos_type = adpos_type;
	}
	public String getLasttime() {
		return lasttime;
	}
	public void setLasttime(String lasttime) {
		this.lasttime = lasttime;
	}
	public String getCuser() {
		return cuser;
	}
	public void setCuser(String cuser) {
		this.cuser = cuser;
	}
	public String getCpmFloor() {
		return cpmFloor;
	}
	public void setCpmFloor(String cpmFloor) {
		this.cpmFloor = cpmFloor;
	}
	public String getOpen_type() {
		return open_type;
	}
	public void setOpen_type(String open_type) {
		this.open_type = open_type;
	}
	public String getBidding() {
		return bidding;
	}
	public void setBidding(String bidding) {
		this.bidding = bidding;
	}
	public String getUnit_id() {
		return unit_id;
	}
	public void setUnit_id(String unit_id) {
		this.unit_id = unit_id;
	}
	public String getMapkey() {
		return mapkey;
	}
	public void setMapkey(String mapkey) {
		this.mapkey = mapkey;
	}

	public void setTobid_id(String tobid_id) {
		this.tobid_id = tobid_id;
	}
	public String getTobid_id() {
		return tobid_id;
	}

	public String getParams() {
		return params;
	}

	public void setParams(String params) {
		this.params = params;
	}

	public String getOut() {
		return out;
	}

	public void setOut(String out) {
		this.out = out;
	}
}
