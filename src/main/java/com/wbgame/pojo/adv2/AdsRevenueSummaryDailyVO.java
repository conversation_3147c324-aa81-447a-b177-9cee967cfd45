package com.wbgame.pojo.adv2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("变现大盘数据表")
public class AdsRevenueSummaryDailyVO {


    @ApiModelProperty("日期")
    private String tdate;

    @ApiModelProperty("变现平台")
    private String agent;

    @ApiModelProperty("应用分类")
    private String appCategory;

    @ApiModelProperty("应用分类")
    private String appCategoryName;

    @ApiModelProperty("应用")
    private String appid;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty("广告类型")
    private String adType;

    @ApiModelProperty("用户类型")
    private String isNewInstall;

    @ApiModelProperty("DAU")
    private Long activeTypeUser = 0L;
    private Long PreActiveTypeUser = 0L;

    @ApiModelProperty("请求")
    private BigDecimal adjustedRequestCnt;

    private BigDecimal preAdjustedRequestCnt;

    @ApiModelProperty("填充")
    private BigDecimal adjustedRequestSuccessCnt;
    private BigDecimal preAdjustedRequestSuccessCnt;

    @ApiModelProperty("展示")
    private BigDecimal adjustedAdCnt;
    private BigDecimal preAdjustedAdCnt;

    @ApiModelProperty("点击")
    private BigDecimal adjustedClickCnt;
    private BigDecimal preAdjustedClickCnt;

    @ApiModelProperty("收入")
    private BigDecimal adjustedAdRevenue;
    private BigDecimal preAdjustedAdRevenue;

    @ApiModelProperty("校准后的总收入")
    private BigDecimal adjustedTotalAdRevenue;

    @ApiModelProperty("人均pv")
    private BigDecimal pvPerCapita;
    private BigDecimal prePvPerCapita;

    @ApiModelProperty("ecpm")
    private BigDecimal ecpm;
    private BigDecimal preEcpm;

    @ApiModelProperty("arpu")
    private BigDecimal arpu;
    private BigDecimal preArpu;

    @ApiModelProperty("收入占比")
    private String incomeRatio;

    @ApiModelProperty("填充率")
    private String fillRate;

    @ApiModelProperty("展示率")
    private String impressionRate;

    @ApiModelProperty("点击率")
    private String ctr;

    @ApiModelProperty("填充渗透率")
    private String fillPermeability;

    @ApiModelProperty("展示渗透率")
    private String displayPenetration;

    @ApiModelProperty("组合")
    private String combination;


    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getAppCategory() {
        return appCategory;
    }

    public void setAppCategory(String appCategory) {
        this.appCategory = appCategory;
    }

    public String getAppCategoryName() {
        return appCategoryName;
    }

    public void setAppCategoryName(String appCategoryName) {
        this.appCategoryName = appCategoryName;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAdType() {
        return adType;
    }

    public void setAdType(String adType) {
        this.adType = adType;
    }

    public String getIsNewInstall() {
        return isNewInstall;
    }

    public void setIsNewInstall(String isNewInstall) {
        this.isNewInstall = isNewInstall;
    }

    public Long getActiveTypeUser() {
        return activeTypeUser;
    }

    public void setActiveTypeUser(Long activeTypeUser) {
        this.activeTypeUser = activeTypeUser;
    }

    public BigDecimal getAdjustedRequestCnt() {
        return adjustedRequestCnt;
    }

    public void setAdjustedRequestCnt(BigDecimal adjustedRequestCnt) {
        this.adjustedRequestCnt = adjustedRequestCnt;
    }

    public BigDecimal getAdjustedRequestSuccessCnt() {
        return adjustedRequestSuccessCnt;
    }

    public void setAdjustedRequestSuccessCnt(BigDecimal adjustedRequestSuccessCnt) {
        this.adjustedRequestSuccessCnt = adjustedRequestSuccessCnt;
    }

    public BigDecimal getAdjustedAdCnt() {
        return adjustedAdCnt;
    }

    public void setAdjustedAdCnt(BigDecimal adjustedAdCnt) {
        this.adjustedAdCnt = adjustedAdCnt;
    }

    public BigDecimal getAdjustedClickCnt() {
        return adjustedClickCnt;
    }

    public void setAdjustedClickCnt(BigDecimal adjustedClickCnt) {
        this.adjustedClickCnt = adjustedClickCnt;
    }

    public BigDecimal getAdjustedAdRevenue() {
        return adjustedAdRevenue;
    }

    public void setAdjustedAdRevenue(BigDecimal adjustedAdRevenue) {
        this.adjustedAdRevenue = adjustedAdRevenue;
    }

    public BigDecimal getAdjustedTotalAdRevenue() {
        return adjustedTotalAdRevenue;
    }

    public void setAdjustedTotalAdRevenue(BigDecimal adjustedTotalAdRevenue) {
        this.adjustedTotalAdRevenue = adjustedTotalAdRevenue;
    }

    public BigDecimal getPvPerCapita() {
        return pvPerCapita;
    }

    public void setPvPerCapita(BigDecimal pvPerCapita) {
        this.pvPerCapita = pvPerCapita;
    }

    public BigDecimal getEcpm() {
        return ecpm;
    }

    public void setEcpm(BigDecimal ecpm) {
        this.ecpm = ecpm;
    }

    public BigDecimal getArpu() {
        return arpu;
    }

    public void setArpu(BigDecimal arpu) {
        this.arpu = arpu;
    }

    public String getIncomeRatio() {
        return incomeRatio;
    }

    public void setIncomeRatio(String incomeRatio) {
        this.incomeRatio = incomeRatio;
    }

    public String getFillRate() {
        return fillRate;
    }

    public void setFillRate(String fillRate) {
        this.fillRate = fillRate;
    }

    public String getImpressionRate() {
        return impressionRate;
    }

    public void setImpressionRate(String impressionRate) {
        this.impressionRate = impressionRate;
    }

    public String getCtr() {
        return ctr;
    }

    public void setCtr(String ctr) {
        this.ctr = ctr;
    }

    public String getFillPermeability() {
        return fillPermeability;
    }

    public void setFillPermeability(String fillPermeability) {
        this.fillPermeability = fillPermeability;
    }

    public String getDisplayPenetration() {
        return displayPenetration;
    }

    public void setDisplayPenetration(String displayPenetration) {
        this.displayPenetration = displayPenetration;
    }


    public String getCombination() {
        return combination;
    }

    public void setCombination(String combination) {
        this.combination = combination;
    }

    public Long getPreActiveTypeUser() {
        return PreActiveTypeUser;
    }

    public void setPreActiveTypeUser(Long preActiveTypeUser) {
        PreActiveTypeUser = preActiveTypeUser;
    }

    public BigDecimal getPreAdjustedRequestCnt() {
        return preAdjustedRequestCnt;
    }

    public void setPreAdjustedRequestCnt(BigDecimal preAdjustedRequestCnt) {
        this.preAdjustedRequestCnt = preAdjustedRequestCnt;
    }

    public BigDecimal getPreAdjustedRequestSuccessCnt() {
        return preAdjustedRequestSuccessCnt;
    }

    public void setPreAdjustedRequestSuccessCnt(BigDecimal preAdjustedRequestSuccessCnt) {
        this.preAdjustedRequestSuccessCnt = preAdjustedRequestSuccessCnt;
    }

    public BigDecimal getPreAdjustedAdCnt() {
        return preAdjustedAdCnt;
    }

    public void setPreAdjustedAdCnt(BigDecimal preAdjustedAdCnt) {
        this.preAdjustedAdCnt = preAdjustedAdCnt;
    }

    public BigDecimal getPreAdjustedClickCnt() {
        return preAdjustedClickCnt;
    }

    public void setPreAdjustedClickCnt(BigDecimal preAdjustedClickCnt) {
        this.preAdjustedClickCnt = preAdjustedClickCnt;
    }

    public BigDecimal getPreAdjustedAdRevenue() {
        return preAdjustedAdRevenue;
    }

    public void setPreAdjustedAdRevenue(BigDecimal preAdjustedAdRevenue) {
        this.preAdjustedAdRevenue = preAdjustedAdRevenue;
    }

    public BigDecimal getPrePvPerCapita() {
        return prePvPerCapita;
    }

    public void setPrePvPerCapita(BigDecimal prePvPerCapita) {
        this.prePvPerCapita = prePvPerCapita;
    }

    public BigDecimal getPreEcpm() {
        return preEcpm;
    }

    public void setPreEcpm(BigDecimal preEcpm) {
        this.preEcpm = preEcpm;
    }

    public BigDecimal getPreArpu() {
        return preArpu;
    }

    public void setPreArpu(BigDecimal preArpu) {
        this.preArpu = preArpu;
    }
}