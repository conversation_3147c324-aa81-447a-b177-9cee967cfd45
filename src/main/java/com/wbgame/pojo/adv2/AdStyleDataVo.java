package com.wbgame.pojo.adv2;

/**
 * <AUTHOR>
 * @Classname AdTypeDataVo
 * @Description TODO
 * @Date 2022/2/16 9:59
 */
public class AdStyleDataVo {

    private String a_date;            //日期
    private String appid;             //产品id
    private String appname;           //产品名称
    private String download_channel;  //子渠道
    private String channel_type;      //渠道类型

    private String pid;               //项目id
    private String ad_type;           //广告类型
    private String sdk_adtype;        //sdk广告源类型
    private String open_type;         //广告使用类型

    private String ad_sub_style;      //广告样式
    private String xdelay;            //关闭按钮延迟时间
    private String ad_sid;            //广告源
    private String estimate_revenue;  //预估收入
    private String ecpm;              //ecpm

    private String platform_revenue;  //平台收入
    private String platform_show_cnt; //平台统计展示次数
    private String platform_click_cnt;//平台点击次数
    private String self_show_cnt;     //自统计展示次数
    private String self_click_cnt;    //自统计点击次数

    private String self_click_ratio;  //点击率
    private String cpc;               //cpc

    public String getA_date() {
        return a_date;
    }

    public void setA_date(String a_date) {
        this.a_date = a_date;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public String getDownload_channel() {
        return download_channel;
    }

    public void setDownload_channel(String download_channel) {
        this.download_channel = download_channel;
    }

    public String getChannel_type() {
        return channel_type;
    }

    public void setChannel_type(String channel_type) {
        this.channel_type = channel_type;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getAd_type() {
        return ad_type;
    }

    public void setAd_type(String ad_type) {
        this.ad_type = ad_type;
    }

    public String getSdk_adtype() {
        return sdk_adtype;
    }

    public void setSdk_adtype(String sdk_adtype) {
        this.sdk_adtype = sdk_adtype;
    }

    public String getOpen_type() {
        return open_type;
    }

    public void setOpen_type(String open_type) {
        this.open_type = open_type;
    }

    public String getAd_sub_style() {
        return ad_sub_style;
    }

    public void setAd_sub_style(String ad_sub_style) {
        this.ad_sub_style = ad_sub_style;
    }

    public String getXdelay() {
        return xdelay;
    }

    public void setXdelay(String xdelay) {
        this.xdelay = xdelay;
    }

    public String getAd_sid() {
        return ad_sid;
    }

    public void setAd_sid(String ad_sid) {
        this.ad_sid = ad_sid;
    }

    public String getEstimate_revenue() {
        return estimate_revenue;
    }

    public void setEstimate_revenue(String estimate_revenue) {
        this.estimate_revenue = estimate_revenue;
    }

    public String getEcpm() {
        return ecpm;
    }

    public void setEcpm(String ecpm) {
        this.ecpm = ecpm;
    }

    public String getPlatform_revenue() {
        return platform_revenue;
    }

    public void setPlatform_revenue(String platform_revenue) {
        this.platform_revenue = platform_revenue;
    }

    public String getPlatform_show_cnt() {
        return platform_show_cnt;
    }

    public void setPlatform_show_cnt(String platform_show_cnt) {
        this.platform_show_cnt = platform_show_cnt;
    }

    public String getPlatform_click_cnt() {
        return platform_click_cnt;
    }

    public void setPlatform_click_cnt(String platform_click_cnt) {
        this.platform_click_cnt = platform_click_cnt;
    }

    public String getSelf_show_cnt() {
        return self_show_cnt;
    }

    public void setSelf_show_cnt(String self_show_cnt) {
        this.self_show_cnt = self_show_cnt;
    }

    public String getSelf_click_cnt() {
        return self_click_cnt;
    }

    public void setSelf_click_cnt(String self_click_cnt) {
        this.self_click_cnt = self_click_cnt;
    }

    public String getSelf_click_ratio() {
        return self_click_ratio;
    }

    public void setSelf_click_ratio(String self_click_ratio) {
        this.self_click_ratio = self_click_ratio;
    }

    public String getCpc() {
        return cpc;
    }

    public void setCpc(String cpc) {
        this.cpc = cpc;
    }
}
