package com.wbgame.pojo;

public class NpActiveFree {
	
	private String prjmid; // 项目id
	private String free_num;
	private String plaquelimitinterval; // 间隔时间
	private String productid;
	private String carrier_type;
	private String seq_id;
	private String remark_info;
	private String sfcityids;
	private String mmid;
	private String clicklimitnum;
	private String update_time;
	private String splashlimitinterval;
	private String bannerupdateinterval;
	private String adopenlimitinterval;
	private String reloadinterval;
	private String expires; // 有效时间

	public String getAdopenlimitinterval() {
		return adopenlimitinterval;
	}

	public void setAdopenlimitinterval(String adopenlimitinterval) {
		this.adopenlimitinterval = adopenlimitinterval;
	}

	public String getSplashlimitinterval() {
		return splashlimitinterval;
	}
	public void setSplashlimitinterval(String splashlimitinterval) {
		this.splashlimitinterval = splashlimitinterval;
	}
	public String getPrjmid() {
		return prjmid;
	}
	public void setPrjmid(String prjmid) {
		this.prjmid = prjmid;
	}
	public String getFree_num() {
		return free_num;
	}
	public void setFree_num(String free_num) {
		this.free_num = free_num;
	}
	public String getPlaquelimitinterval() {
		return plaquelimitinterval;
	}
	public void setPlaquelimitinterval(String plaquelimitinterval) {
		this.plaquelimitinterval = plaquelimitinterval;
	}
	public String getProductid() {
		return productid;
	}
	public void setProductid(String productid) {
		this.productid = productid;
	}
	public String getCarrier_type() {
		return carrier_type;
	}
	public void setCarrier_type(String carrier_type) {
		this.carrier_type = carrier_type;
	}
	public String getSeq_id() {
		return seq_id;
	}
	public void setSeq_id(String seq_id) {
		this.seq_id = seq_id;
	}
	public String getRemark_info() {
		return remark_info;
	}
	public void setRemark_info(String remark_info) {
		this.remark_info = remark_info;
	}
	public String getSfcityids() {
		return sfcityids;
	}
	public void setSfcityids(String sfcityids) {
		this.sfcityids = sfcityids;
	}
	public String getMmid() {
		return mmid;
	}
	public void setMmid(String mmid) {
		this.mmid = mmid;
	}
	public String getClicklimitnum() {
		return clicklimitnum;
	}
	public void setClicklimitnum(String clicklimitnum) {
		this.clicklimitnum = clicklimitnum;
	}
	public String getUpdate_time() {
		return update_time;
	}
	public void setUpdate_time(String update_time) {
		this.update_time = update_time;
	}
	public String getBannerupdateinterval() {
		return bannerupdateinterval;
	}
	public void setBannerupdateinterval(String bannerupdateinterval) {
		this.bannerupdateinterval = bannerupdateinterval;
	}

	public String getReloadinterval() {
		return reloadinterval;
	}

	public void setReloadinterval(String reloadinterval) {
		this.reloadinterval = reloadinterval;
	}

	public String getExpires() {
		return expires;
	}

	public void setExpires(String expires) {
		this.expires = expires;
	}
	
}
