
package com.wbgame.pojo.mobile.hw;

import lombok.Builder;
import lombok.Data;

/**
 * Function description.
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
public class FileInfo {

    private String fileDestUlr;

    private String imageResolution;

    private String imageResolutionSingature;

    private int size;

    private String fileName;

	/**上传文件后的唯一记录id */
    private String id;

	public String getFileDestUlr() {
		return fileDestUlr;
	}

	public void setFileDestUlr(String fileDestUlr) {
		this.fileDestUlr = fileDestUlr;
	}

	public String getImageResolution() {
		return imageResolution;
	}

	public void setImageResolution(String imageResolution) {
		this.imageResolution = imageResolution;
	}

	public String getImageResolutionSingature() {
		return imageResolutionSingature;
	}

	public void setImageResolutionSingature(String imageResolutionSingature) {
		this.imageResolutionSingature = imageResolutionSingature;
	}

	public int getSize() {
		return size;
	}

	public void setSize(int size) {
		this.size = size;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}
}
