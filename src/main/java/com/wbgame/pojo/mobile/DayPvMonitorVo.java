package com.wbgame.pojo.mobile;

/**
 * <AUTHOR>
 * @Classname DayPvMonitorVo
 * @Description TODO
 * @Date 2022/1/17 14:26
 */
public class DayPvMonitorVo {

    private String tdate;                           //日期
    private String appid;                           //产品id
    private String appname;                         //产品名称
    private String ad_type;                         //广告类型
    private String big_data_pv;                     //大数据pv
    private String dn_pv;                           //自统计pv
    private String ad_platform_pv;                  //广告平台pv
    private String dn_pv_differential;              //自统计差值
    private String dn_pv_rate;                      //自统计差值率
    private String ad_platform_pv_differebtial;     //广告平台pv差值
    private String ad_platform_pv_rate;             //广告平台pv差值率

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public String getAd_type() {
        return ad_type;
    }

    public void setAd_type(String ad_type) {
        this.ad_type = ad_type;
    }

    public String getBig_data_pv() {
        return big_data_pv;
    }

    public void setBig_data_pv(String big_data_pv) {
        this.big_data_pv = big_data_pv;
    }

    public String getDn_pv() {
        return dn_pv;
    }

    public void setDn_pv(String dn_pv) {
        this.dn_pv = dn_pv;
    }

    public String getAd_platform_pv() {
        return ad_platform_pv;
    }

    public void setAd_platform_pv(String ad_platform_pv) {
        this.ad_platform_pv = ad_platform_pv;
    }

    public String getDn_pv_differential() {
        return dn_pv_differential;
    }

    public void setDn_pv_differential(String dn_pv_differential) {
        this.dn_pv_differential = dn_pv_differential;
    }

    public String getDn_pv_rate() {
        return dn_pv_rate;
    }

    public void setDn_pv_rate(String dn_pv_rate) {
        this.dn_pv_rate = dn_pv_rate;
    }

    public String getAd_platform_pv_differebtial() {
        return ad_platform_pv_differebtial;
    }

    public void setAd_platform_pv_differebtial(String ad_platform_pv_differebtial) {
        this.ad_platform_pv_differebtial = ad_platform_pv_differebtial;
    }

    public String getAd_platform_pv_rate() {
        return ad_platform_pv_rate;
    }

    public void setAd_platform_pv_rate(String ad_platform_pv_rate) {
        this.ad_platform_pv_rate = ad_platform_pv_rate;
    }
}
