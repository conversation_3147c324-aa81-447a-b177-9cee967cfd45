package com.wbgame.pojo.mobile;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("内购支付监控配置")
public class WxMachMonitorConfigVO {

    @ApiModelProperty("商户号")
    private String machid;

    @ApiModelProperty("微信appid")
    private String wxappid;

    @ApiModelProperty("状态 1:启动；0:禁用")
    private Integer status;

    @ApiModelProperty("创建人")
    private String createOwner;

    @ApiModelProperty("修改人")
    private String updateOwner;

    @ApiModelProperty("创建时间")
    private String  createTime;

    @ApiModelProperty("修改时间")
    private String updateTime;

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getMachid() {
        return machid;
    }

    public void setMachid(String machid) {
        this.machid = machid == null ? null : machid.trim();
    }

    public String getWxappid() {
        return wxappid;
    }

    public void setWxappid(String wxappid) {
        this.wxappid = wxappid == null ? null : wxappid.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateOwner() {
        return createOwner;
    }

    public void setCreateOwner(String createOwner) {
        this.createOwner = createOwner == null ? null : createOwner.trim();
    }

    public String getUpdateOwner() {
        return updateOwner;
    }

    public void setUpdateOwner(String updateOwner) {
        this.updateOwner = updateOwner == null ? null : updateOwner.trim();
    }
}