package com.wbgame.pojo.mobile;

/**
 * <AUTHOR>
 * @Classname PvpAwardVo
 * @Description TODO
 * @Date 2021/8/31 19:28
 */
public class PvpAwardVo {

    private String id;
    private String awardid;
    private String awardDesc;
    private String amount;
    private String devote;
    private String canReceive;
    private String statu;
    private String createtime;
    private String modifyTime;
    private String createUser;
    private String modifyUser;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAwardid() {
        return awardid;
    }

    public void setAwardid(String awardid) {
        this.awardid = awardid;
    }

    public String getAwardDesc() {
        return awardDesc;
    }

    public void setAwardDesc(String awardDesc) {
        this.awardDesc = awardDesc;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getDevote() {
        return devote;
    }

    public void setDevote(String devote) {
        this.devote = devote;
    }

    public String getCanReceive() {
        return canReceive;
    }

    public void setCanReceive(String canReceive) {
        this.canReceive = canReceive;
    }

    public String getStatu() {
        return statu;
    }

    public void setStatu(String statu) {
        this.statu = statu;
    }

    public String getCreatetime() {
        return createtime;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }
}
