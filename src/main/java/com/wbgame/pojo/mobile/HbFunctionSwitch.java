package com.wbgame.pojo.mobile;

import java.util.Date;

public class HbFunctionSwitch {

    /**
     * 项目id
     */
    private String pid;

    /**
     * 自然量开关
     */
    private Integer natural;

    /**
     * 额外奖励开关
     */
    private Integer extra;

    /**
     * 衰弱配置开关
     */
    private Integer decline;

    /**
     * 提现返利开关
     */
    private Integer rebate;

    /**
     * 新手次日提现开关
     */
    private Integer limit;

    /**
     * 创建时间
     */
    private String createStr;

    /**
     * 最后更新时间
     */
    private String updateStr;

    /**
     * 创建人
     */
    private String create_owner;

    /**
     * 最后更新人
     */
    private String update_owner;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public Integer getNatural() {
        return natural;
    }

    public void setNatural(Integer natural) {
        this.natural = natural;
    }

    public Integer getExtra() {
        return extra;
    }

    public void setExtra(Integer extra) {
        this.extra = extra;
    }

    public Integer getDecline() {
        return decline;
    }

    public void setDecline(Integer decline) {
        this.decline = decline;
    }

    public Integer getRebate() {
        return rebate;
    }

    public void setRebate(Integer rebate) {
        this.rebate = rebate;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getCreateStr() {
        return createStr;
    }

    public void setCreateStr(String createStr) {
        this.createStr = createStr;
    }

    public String getUpdateStr() {
        return updateStr;
    }

    public void setUpdateStr(String updateStr) {
        this.updateStr = updateStr;
    }

    public String getCreate_owner() {
        return create_owner;
    }

    public void setCreate_owner(String create_owner) {
        this.create_owner = create_owner;
    }

    public String getUpdate_owner() {
        return update_owner;
    }

    public void setUpdate_owner(String update_owner) {
        this.update_owner = update_owner;
    }
}
