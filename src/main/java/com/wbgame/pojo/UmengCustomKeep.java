package com.wbgame.pojo;

import java.math.BigDecimal;

public class Umeng<PERSON>ustomKeep {
    /**
     * 安装app版本号
     */
    private String installAppVersion;

    /**
     * 安装渠道
     */
    private String installChannel;

    /**
     * 日期
     */
    private String ds;

    private String appKey;

    /**
     * 新增
     */
    private Long addNum;

    /**
     * 留存
     */
    private Long keepNum;

    /**
     * 留存占比
     */
    private BigDecimal keepRate;

    public String getInstallAppVersion() {
        return installAppVersion;
    }

    public void setInstallAppVersion(String installAppVersion) {
        this.installAppVersion = installAppVersion;
    }

    public String getInstallChannel() {
        return installChannel;
    }

    public void setInstallChannel(String installChannel) {
        this.installChannel = installChannel;
    }

    public String getDs() {
        return ds;
    }

    public void setDs(String ds) {
        this.ds = ds;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public Long getAddNum() {
        return addNum;
    }

    public void setAddNum(Long addNum) {
        this.addNum = addNum;
    }

    public Long getKeepNum() {
        return keepNum;
    }

    public void setKeepNum(Long keepNum) {
        this.keepNum = keepNum;
    }

    public BigDecimal getKeepRate() {
        return keepRate;
    }

    public void setKeepRate(BigDecimal keepRate) {
        this.keepRate = keepRate;
    }
}