package com.wbgame.pojo;


/**
 * 小游戏-换量汇总查询
 * <AUTHOR>
 * @date 2020年5月7日
 */
public class AppExchangeVolumeTotalVo {
	
	/**日期*/
    private String tdate;

    /**渠道*/
    private String channel;

    /**产品新增*/
    private String totalNew;

    /**导入新增*/
    private String totalNewBuy;
    
    /**投放买量*/
    private String salesVolume;
    
    /**产品活跃*/
    private String totalDau;

    /**新增占比*/
    private String perNew;

    /**总导出量*/
    private String totalNewSell;

    /**总活跃导出率*/
    private String dauSellPer;

    /**导入支出*/
    private String totalBuy;
    
    /**投放支出*/
    private String expenditure;
    
    /**总支出*/
    private String totalExpenditure;

    /**总买量单价*/
    private String unitPrice;
    
    /**广告收入*/
    private String totalIncome;
    
    /**导出收入*/
    private String totalSell;
    
    /**导出dau arpu*/
    private String sellDauArpu;
    
    /**总收入*/
    private String allIncome;
    
    /**总dau arpu*/
    private String dauArpu;
    
    /**换量差额收入*/
    private String diffIncome;
    
    /**利润*/
    private String profit;
    
    /**产品支出*/
    private String appIncome;
    
    /**投放买量*/
    private String volume;
    
    /**投放支出*/
    private String cost;

	public String getAppIncome() {
		return appIncome;
	}

	public void setAppIncome(String appIncome) {
		this.appIncome = appIncome;
	}

	public String getTdate() {
		return tdate;
	}

	public void setTdate(String tdate) {
		this.tdate = tdate;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getTotalNew() {
		return totalNew;
	}

	public void setTotalNew(String totalNew) {
		this.totalNew = totalNew;
	}

	public String getTotalNewBuy() {
		return totalNewBuy;
	}

	public void setTotalNewBuy(String totalNewBuy) {
		this.totalNewBuy = totalNewBuy;
	}

	public String getSalesVolume() {
		return salesVolume;
	}

	public void setSalesVolume(String salesVolume) {
		this.salesVolume = salesVolume;
	}

	public String getTotalDau() {
		return totalDau;
	}

	public void setTotalDau(String totalDau) {
		this.totalDau = totalDau;
	}

	public String getPerNew() {
		return perNew;
	}

	public void setPerNew(String perNew) {
		this.perNew = perNew;
	}

	public String getTotalNewSell() {
		return totalNewSell;
	}

	public void setTotalNewSell(String totalNewSell) {
		this.totalNewSell = totalNewSell;
	}

	public String getDauSellPer() {
		return dauSellPer;
	}

	public void setDauSellPer(String dauSellPer) {
		this.dauSellPer = dauSellPer;
	}

	public String getTotalBuy() {
		return totalBuy;
	}

	public void setTotalBuy(String totalBuy) {
		this.totalBuy = totalBuy;
	}

	public String getExpenditure() {
		return expenditure;
	}

	public void setExpenditure(String expenditure) {
		this.expenditure = expenditure;
	}

	public String getTotalExpenditure() {
		return totalExpenditure;
	}

	public void setTotalExpenditure(String totalExpenditure) {
		this.totalExpenditure = totalExpenditure;
	}

	public String getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(String unitPrice) {
		this.unitPrice = unitPrice;
	}

	public String getTotalIncome() {
		return totalIncome;
	}

	public void setTotalIncome(String totalIncome) {
		this.totalIncome = totalIncome;
	}

	public String getTotalSell() {
		return totalSell;
	}

	public void setTotalSell(String totalSell) {
		this.totalSell = totalSell;
	}

	public String getSellDauArpu() {
		return sellDauArpu;
	}

	public void setSellDauArpu(String sellDauArpu) {
		this.sellDauArpu = sellDauArpu;
	}

	public String getAllIncome() {
		return allIncome;
	}

	public void setAllIncome(String allIncome) {
		this.allIncome = allIncome;
	}

	public String getDauArpu() {
		return dauArpu;
	}

	public void setDauArpu(String dauArpu) {
		this.dauArpu = dauArpu;
	}

	public String getDiffIncome() {
		return diffIncome;
	}

	public void setDiffIncome(String diffIncome) {
		this.diffIncome = diffIncome;
	}

	public String getProfit() {
		return profit;
	}

	public void setProfit(String profit) {
		this.profit = profit;
	}

	public String getVolume() {
		return volume;
	}

	public void setVolume(String volume) {
		this.volume = volume;
	}

	public String getCost() {
		return cost;
	}

	public void setCost(String cost) {
		this.cost = cost;
	}

}