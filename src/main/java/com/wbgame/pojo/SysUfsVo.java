package com.wbgame.pojo;

/**
 * <AUTHOR>
 * @Classname SysUfsVo
 * @Description 系统账号关联飞书账号
 * @Date 2021/4/22 10:07
 */
public class SysUfsVo {

    private String id;
    private String login_name;  //后台账号
    private String fsuser_id;   //飞书userid
    private String statu;       //状态   0关闭 1开启
    private String username;    //用户名称
    private String createUser;  //创建人
    private String createTime;
    private String modifyTime;
    private String modifyUser;
    private String department;
    private String post;

    public String getStatu() {
        return statu;
    }

    public void setStatu(String statu) {
        this.statu = statu;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getLogin_name() {
        return login_name;
    }

    public void setLogin_name(String login_name) {
        this.login_name = login_name;
    }

    public String getFsuser_id() {
        return fsuser_id;
    }

    public void setFsuser_id(String fsuser_id) {
        this.fsuser_id = fsuser_id;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }
}
