package com.wbgame.pojo.custom;

import com.wbgame.common.GeneralReportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("支付订单查询")
public class WbPayInfo extends GeneralReportParam {


    @ApiModelProperty("订单号")
    private String orderid;

    private String uid;

    @ApiModelProperty("支付金额")
    private Integer money;

    @ApiModelProperty("支付类型")
    private String paytype;

    @ApiModelProperty("支付类型")
    private List<String> paytypeList;

    @ApiModelProperty("设备id")
    private String imei;

    @ApiModelProperty("项目id")
    private String pid;

    @ApiModelProperty("产品id")
    private String appid;

    @ApiModelProperty("支付状态")
    private String orderstatus;

    @ApiModelProperty("计费id")
    private String payname;

    @ApiModelProperty("支付备注")
    private String paynote;

    @ApiModelProperty("创建时间")
    private String createtime;

    @ApiModelProperty("param1")
    private String param1;

    @ApiModelProperty("param2")
    private String param2;

    @ApiModelProperty("param3")
    private String param3;

    private String lsn;

    private String chaid;

    private String androidid;

    private String oaid;

    private String idfa;

    private String lsnNew;

    private String payid;

    @ApiModelProperty("用户id")
    private String userid;

    @ApiModelProperty("自定义参数")
    private String custom;

    private String buyId;

    @ApiModelProperty("包名")
    private String pn;

    @ApiModelProperty("机型")
    private String model;

    @ApiModelProperty("用户类型 0:老用户, 1:新用户")
    private String isNew;

    @ApiModelProperty("应用名")
    private String appName;

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid == null ? null : orderid.trim();
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid == null ? null : uid.trim();
    }

    public Integer getMoney() {
        return money;
    }

    public void setMoney(Integer money) {
        this.money = money;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype == null ? null : paytype.trim();
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei == null ? null : imei.trim();
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid == null ? null : pid.trim();
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid == null ? null : appid.trim();
    }

    public String getOrderstatus() {
        return orderstatus;
    }

    public void setOrderstatus(String orderstatus) {
        this.orderstatus = orderstatus == null ? null : orderstatus.trim();
    }

    public String getPayname() {
        return payname;
    }

    public void setPayname(String payname) {
        this.payname = payname == null ? null : payname.trim();
    }

    public String getPaynote() {
        return paynote;
    }

    public void setPaynote(String paynote) {
        this.paynote = paynote == null ? null : paynote.trim();
    }

    public String getCreatetime() {
        return createtime;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }

    public String getParam1() {
        return param1;
    }

    public void setParam1(String param1) {
        this.param1 = param1 == null ? null : param1.trim();
    }

    public String getParam2() {
        return param2;
    }

    public void setParam2(String param2) {
        this.param2 = param2 == null ? null : param2.trim();
    }

    public String getParam3() {
        return param3;
    }

    public void setParam3(String param3) {
        this.param3 = param3 == null ? null : param3.trim();
    }

    public String getLsn() {
        return lsn;
    }

    public void setLsn(String lsn) {
        this.lsn = lsn == null ? null : lsn.trim();
    }

    public String getChaid() {
        return chaid;
    }

    public void setChaid(String chaid) {
        this.chaid = chaid == null ? null : chaid.trim();
    }

    public String getAndroidid() {
        return androidid;
    }

    public void setAndroidid(String androidid) {
        this.androidid = androidid == null ? null : androidid.trim();
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid == null ? null : oaid.trim();
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa == null ? null : idfa.trim();
    }

    public String getLsnNew() {
        return lsnNew;
    }

    public void setLsnNew(String lsnNew) {
        this.lsnNew = lsnNew == null ? null : lsnNew.trim();
    }

    public String getPayid() {
        return payid;
    }

    public void setPayid(String payid) {
        this.payid = payid == null ? null : payid.trim();
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid == null ? null : userid.trim();
    }

    public String getCustom() {
        return custom;
    }

    public void setCustom(String custom) {
        this.custom = custom == null ? null : custom.trim();
    }

    public String getBuyId() {
        return buyId;
    }

    public void setBuyId(String buyId) {
        this.buyId = buyId == null ? null : buyId.trim();
    }

    public String getPn() {
        return pn;
    }

    public void setPn(String pn) {
        this.pn = pn == null ? null : pn.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getIsNew() {
        return isNew;
    }

    public void setIsNew(String isNew) {
        this.isNew = isNew;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public List<String> getPaytypeList() {
        return paytypeList;
    }

    public void setPaytypeList(List<String> paytypeList) {
        this.paytypeList = paytypeList;
    }
}