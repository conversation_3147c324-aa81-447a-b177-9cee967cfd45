package com.wbgame.pojo.custom;

public class GDTWorkMediumVo {
	
	private String member_id; // 会员ID
	private String app_id; 
	private String medium_id; // 媒体ID
	private String medium_name; // 媒体名字
	private String industry_id; // 行业 ID,参见附录
	private String os; // 操作系统
	private String keywords; // 媒体关键词
	private String description; // 描述
	private String package_name; // 包名
	private String market_status; // 市场状态 ONLINE,OFFLINE
	private String market_url; // 市场详情页地址 (market_status=ONLINE时必填)
	private String jail_break; // 是否支持越狱?1支持、0不支持
	
	
	private String placement_id; // 广告位ID
	private String placement_name; // 广告位名字
	private String placement_type; // 广告位类型
	private String placement_sub_type; // 广告位子类型
	private String ad_pull_mode; // 广告拉取方式
	
	public String getMember_id() {
		return member_id;
	}
	public void setMember_id(String member_id) {
		this.member_id = member_id;
	}
	public String getApp_id() {
		return app_id;
	}
	public void setApp_id(String app_id) {
		this.app_id = app_id;
	}
	public String getMedium_id() {
		return medium_id;
	}
	public void setMedium_id(String medium_id) {
		this.medium_id = medium_id;
	}
	public String getPlacement_id() {
		return placement_id;
	}
	public void setPlacement_id(String placement_id) {
		this.placement_id = placement_id;
	}
	public String getPlacement_name() {
		return placement_name;
	}
	public void setPlacement_name(String placement_name) {
		this.placement_name = placement_name;
	}
	public String getPlacement_type() {
		return placement_type;
	}
	public void setPlacement_type(String placement_type) {
		this.placement_type = placement_type;
	}
	public String getPlacement_sub_type() {
		return placement_sub_type;
	}
	public void setPlacement_sub_type(String placement_sub_type) {
		this.placement_sub_type = placement_sub_type;
	}
	public String getAd_pull_mode() {
		return ad_pull_mode;
	}
	public void setAd_pull_mode(String ad_pull_mode) {
		this.ad_pull_mode = ad_pull_mode;
	}
	public String getMedium_name() {
		return medium_name;
	}
	public void setMedium_name(String medium_name) {
		this.medium_name = medium_name;
	}
	public String getIndustry_id() {
		return industry_id;
	}
	public void setIndustry_id(String industry_id) {
		this.industry_id = industry_id;
	}
	public String getOs() {
		return os;
	}
	public void setOs(String os) {
		this.os = os;
	}
	public String getKeywords() {
		return keywords;
	}
	public void setKeywords(String keywords) {
		this.keywords = keywords;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getPackage_name() {
		return package_name;
	}
	public void setPackage_name(String package_name) {
		this.package_name = package_name;
	}
	public String getMarket_status() {
		return market_status;
	}
	public void setMarket_status(String market_status) {
		this.market_status = market_status;
	}
	public String getMarket_url() {
		return market_url;
	}
	public void setMarket_url(String market_url) {
		this.market_url = market_url;
	}
	public String getJail_break() {
		return jail_break;
	}
	public void setJail_break(String jail_break) {
		this.jail_break = jail_break;
	}
	
}
