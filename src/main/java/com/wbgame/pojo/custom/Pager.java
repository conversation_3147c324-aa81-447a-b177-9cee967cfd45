package com.wbgame.pojo.custom;

import java.util.List;


public class Pager<T> {

	//每页多少行记录
	private int pageSize = 20;
	//当前第几页
	private int pageNo = 1;
	//记录总数
	private int totalRows = -1;
	//开始记录数
	private int startRow;
	//总页数
	private int totalPages;
	//记录集合
	private List<T> resultList;
	
	public Pager(int pageNo, int pageSize, List<T> resultList, int totalRows) {
		super();
		this.pageNo = pageNo;
		this.pageSize = pageSize;
		this.resultList = resultList;
		this.totalRows = totalRows;
	}
	
	public Pager(){
	}
	
	public Pager(int pageNo) {
		int v_endrownum   = pageNo * pageSize;
		this.startRow = v_endrownum - pageSize + 1;
	}
	
	public Pager(int pageNo ,int pageSize) {
		int v_endrownum   = pageNo * pageSize;
		this.startRow = v_endrownum - pageSize + 1;
		if(pageSize != 0)
			this.pageSize = pageSize;
	}
	
	public int getPageSize() {
		return pageSize;
	}
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	public int getPageNo() {
		return pageNo;
	}
	public void setPageNo(int pageNo) {
		this.pageNo = pageNo;
	}
	public int getTotalRows() {
		return totalRows;
	}
	public void setTotalRows(int totalRows) {
		this.totalRows = totalRows;
	}
	public int getStartRow() {
		return startRow;
	}
	public void setStartRow(int startRow) {
		this.startRow = startRow;
	}
	public int getTotalPages() {
		return totalPages;
	}
	public void setTotalPages(int totalPages) {
		this.totalPages = totalPages;
	}
	public List getResultList() {
		return resultList;
	}
	public void setResultList(List resultList) {
		this.resultList = resultList;
	}

}

