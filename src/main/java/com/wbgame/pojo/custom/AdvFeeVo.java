package com.wbgame.pojo.custom;

import java.io.Serializable;
import java.util.Map;

public class AdvFeeVo implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String push_cha;
	private String pri_id;
	private String mmid;
	private String adv_dt;
	private Double give_fee;// 投放金额
	private Integer new_count;// 产品新增用户

	private Integer n_count;// 新增用户
	private String cpa;// 投放金额/新增用户
	private String two_retention;// 次日留存
	private Integer dau;
	private Double get_fee;// 当日计费收入
	private Double get_fee_expect;
	private String arpu;// 当日计费收入/新增用户 (新版 /dua)
	private Double adv_fee;// 当日广告收入
	private Double adv_fee_expect;// 当日广告预估收入
	private String adv_dau;// 当日广告收入/dau
	
	private Integer show_count;// 展现量
	private String show_dau;// 展现量/dau
	private String click_count;// 点击量
	private String click_rate;// 点击量/展现量
	private String ecpm;// 当日广告收入*1000/展现量
	private String cpc;// 当日广告收入/点击量

	private Double adv_total;
	private Double get_fee_total;
	private Double total_fee;
	private Double give_fee_total;
	private String total_arpu;
	private Double input_output_ratio;// 累计投放/累计总收入

	private String cha_type;
	private String appid;
	private String os_type;
	private String mapkey;
	private Map<String, Object> givefeeList;
	private Map<String, Object> advfeeList;
	private Map<String, Object> dauList;
	private Map<String, Object> showcountList;
	
	public String getCha_type() {
		return cha_type;
	}

	public void setCha_type(String chaType) {
		cha_type = chaType;
	}

	public Integer getN_count() {
		return n_count;
	}

	public void setN_count(Integer nCount) {
		n_count = nCount;
	}
	
	public String getPush_cha() {
		return push_cha;
	}

	public void setPush_cha(String pushCha) {
		push_cha = pushCha;
	}

	public String getPri_id() {
		return pri_id;
	}

	public void setPri_id(String priId) {
		pri_id = priId;
	}

	public String getMmid() {
		return mmid;
	}

	public void setMmid(String mmid) {
		this.mmid = mmid;
	}

	public String getAdv_dt() {
		return adv_dt;
	}

	public void setAdv_dt(String advDt) {
		adv_dt = advDt;
	}

	public Double getGive_fee() {
		return give_fee;
	}

	public void setGive_fee(Double giveFee) {
		give_fee = giveFee;
	}

	public Integer getNew_count() {
		return new_count;
	}

	public void setNew_count(Integer newCount) {
		new_count = newCount;
	}

	public String getCpa() {
		return cpa;
	}

	public void setCpa(String cpa) {
		this.cpa = cpa;
	}

	public String getTwo_retention() {
		return two_retention;
	}

	public void setTwo_retention(String twoRetention) {
		two_retention = twoRetention;
	}

	public Integer getDau() {
		return dau;
	}

	public void setDau(Integer dau) {
		this.dau = dau;
	}

	public Double getGet_fee() {
		return get_fee;
	}

	public void setGet_fee(Double getFee) {
		get_fee = getFee;
	}

	public Double getGet_fee_expect() {
		return get_fee_expect;
	}

	public void setGet_fee_expect(Double getFeeExpect) {
		get_fee_expect = getFeeExpect;
	}

	public String getArpu() {
		return arpu;
	}

	public void setArpu(String arpu) {
		this.arpu = arpu;
	}

	public Double getAdv_fee() {
		return adv_fee;
	}

	public void setAdv_fee(Double advFee) {
		adv_fee = advFee;
	}
	public String getAdv_dau() {
		return adv_dau;
	}

	public void setAdv_dau(String adv_dau) {
		this.adv_dau = adv_dau;
	}

	public Integer getShow_count() {
		return show_count;
	}

	public void setShow_count(Integer showCount) {
		show_count = showCount;
	}

	public String getShow_dau() {
		return show_dau;
	}

	public void setShow_dau(String showDau) {
		show_dau = showDau;
	}

	public String getClick_count() {
		return click_count;
	}

	public void setClick_count(String clickCount) {
		click_count = clickCount;
	}

	public String getClick_rate() {
		return click_rate;
	}

	public void setClick_rate(String clickRate) {
		click_rate = clickRate;
	}

	public String getEcpm() {
		return ecpm;
	}

	public void setEcpm(String ecpm) {
		this.ecpm = ecpm;
	}

	public String getCpc() {
		return cpc;
	}

	public void setCpc(String cpc) {
		this.cpc = cpc;
	}

	public Double getAdv_total() {
		return adv_total;
	}

	public void setAdv_total(Double advTotal) {
		adv_total = advTotal;
	}

	public Double getGet_fee_total() {
		return get_fee_total;
	}

	public void setGet_fee_total(Double getFeeTotal) {
		get_fee_total = getFeeTotal;
	}

	public Double getTotal_fee() {
		return total_fee;
	}

	public void setTotal_fee(Double totalFee) {
		total_fee = totalFee;
	}

	public Double getGive_fee_total() {
		return give_fee_total;
	}

	public void setGive_fee_total(Double giveFeeTotal) {
		give_fee_total = giveFeeTotal;
	}

	public Double getInput_output_ratio() {
		return input_output_ratio;
	}

	public void setInput_output_ratio(Double inputOutputRatio) {
		input_output_ratio = inputOutputRatio;
	}
	public Double getAdv_fee_expect() {
		return adv_fee_expect;
	}

	public void setAdv_fee_expect(Double advFeeExpect) {
		adv_fee_expect = advFeeExpect;
	}

	public String getOs_type() {
		return os_type;
	}
	public void setOs_type(String os_type) {
		this.os_type = os_type;
	}

	public String getMapkey() {
		return mapkey;
	}

	public void setMapkey(String mapkey) {
		this.mapkey = mapkey;
	}
	
	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public Map<String, Object> getGivefeeList() {
		return givefeeList;
	}

	public void setGivefeeList(Map<String, Object> givefeeList) {
		this.givefeeList = givefeeList;
	}

	public Map<String, Object> getAdvfeeList() {
		return advfeeList;
	}

	public void setAdvfeeList(Map<String, Object> advfeeList) {
		this.advfeeList = advfeeList;
	}

	public Map<String, Object> getDauList() {
		return dauList;
	}

	public void setDauList(Map<String, Object> dauList) {
		this.dauList = dauList;
	}

	public Map<String, Object> getShowcountList() {
		return showcountList;
	}

	public void setShowcountList(Map<String, Object> showcountList) {
		this.showcountList = showcountList;
	}

	public String getTotal_arpu() {
		return total_arpu;
	}

	public void setTotal_arpu(String total_arpu) {
		this.total_arpu = total_arpu;
	}
	
}
