package com.wbgame.pojo.custom;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;



public class PageForList<T> extends Pager {
	
	private Log logger = LogFactory.getLog(PageForList.class);

	/**
	 * 
	 * @param pageNo   当前页号
	 * @param pageSize 每页显示记录条数
	 * @param list     需要进行分页的list  
	 */
	public PageForList(int pageNo, int pageSize, List<T> list)
	{
		setTotalRows(list.size());
		setPageSize(pageSize);
			
		if(list.size() == 0)
		{
			setPageNo(1);
			setTotalPages(0);
	    	setStartRow(0);
	    	setResultList(list);
		}
		else
		{
			int totalPages = (int)Math.ceil((double)list.size() / (double)pageSize);
			if(pageNo > totalPages )
				pageNo = totalPages;
			else if(pageNo < 1 )
				pageNo = 1;
			setPageNo(pageNo);
			setTotalPages(totalPages);
			int start = (pageNo-1)*pageSize;
	    	int end   = pageNo*pageSize;
	    	setStartRow(start);
	    	// 这里使用new ArrayList创建一个新对象，否则一旦在后续操作list会影响到这个subList
	    	if(list.size() < end)
	    		setResultList(new ArrayList<>(list.subList(start, getTotalRows())));
	    	else 
	    		setResultList(new ArrayList<>(list.subList(start, end)));
		}
	}

}
