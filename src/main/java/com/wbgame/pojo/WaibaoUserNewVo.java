package com.wbgame.pojo;


public class WaibaoUserNewVo {
	
	private String login_name;
	private String password;
	private String nick_name; // 昵称
	private String role_id; // 角色ID
	private String role_name; // 角色名称
	private String app_group;
	private String token;
	
	private String email; // 公司
	private String phone; // 平台ID
	private String status; // 状态 1-启用，2-停用，3-离职
	private String date;
	
	private String page_url;//页面url验证
	private String type;//类型验证
	private String find_val;//过滤键
	private String app_list_find;//过滤应用
	private String channel_type_list_find;//渠道类型过滤
	private String media_channel_list_find_table;//媒体及媒体子渠道值
	private String agent_list_find;//代理商过滤
	private String put_user_list_find;//投放人员过滤
	private String artist_list_find;//美术人员过滤
	private String title_list_find;//标题过滤
	private String typelinks;//媒体及媒体子渠道过滤匹配返回值
	private String cha_type_find;
	
	private String company;// 测试添加

	private String keysx;// 测试添加
	private String text;// 测试添加

	private String app_key;// 角色授权的应用列表
	private String app_name;// 角色授权的应用列表

	private String role_app_slot;// 角色授权的应用列表

	private String user_id;// 用户账号
	private String user_name;// 用户
	private String org_id;// 权限标识
	private String sys;// 系统标识

	private String org_name;// 权限名称
	private String hidden_menu_list;// 隐藏的菜单分类
	private String page_list;// 已授权的菜单目录

	private String index;// 目录页面路径键
	private String style;// 目录类型
	private String icon;// 目录图标
	private String title;// 目录中文标题
	private String menu;// 目录归属
	private int off;// 目录开关
	private int leve;// 目录权重_失效
	private String slot;// 目录插槽
	private String level;
	private String sys_type;

	private int id;// 媒体及媒体子渠道过滤匹配返回值

	private String ad_art_group_id;//美术组id
	private String account_group;//账号分组
	private String tf_sub_channel_list;//投放子渠道

	public String getTf_sub_channel_list() {
		return tf_sub_channel_list;
	}

	public void setTf_sub_channel_list(String tf_sub_channel_list) {
		this.tf_sub_channel_list = tf_sub_channel_list;
	}

	public String getAd_art_group_id() {
		return ad_art_group_id;
	}

	public void setAd_art_group_id(String ad_art_group_id) {
		this.ad_art_group_id = ad_art_group_id;
	}

	public String getLevel() {
		return level;
	}

	public void setLevel(String level) {
		this.level = level;
	}
	public String getLogin_name() {
		return login_name;
	}
	public void setLogin_name(String login_name) {
		this.login_name = login_name;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getNick_name() {
		return nick_name;
	}
	public void setNick_name(String nick_name) {
		this.nick_name = nick_name;
	}
	public String getRole_id() {
		return role_id;
	}
	public void setRole_id(String role_id) {
		this.role_id = role_id;
	}
	public String getRole_name() {
		return role_name;
	}
	public void setRole_name(String role_name) {
		this.role_name = role_name;
	}
	public String getApp_group() {
		return app_group;
	}
	public void setApp_group(String app_group) {
		this.app_group = app_group;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public String getPage_url() {
		return page_url;
	}
	public void setPage_url(String page_url) {
		this.page_url = page_url;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getFind_val() {
		return find_val;
	}
	public void setFind_val(String find_val) {
		this.find_val = find_val;
	}
	public String getApp_list_find() {
		return app_list_find;
	}
	public void setApp_list_find(String app_list_find) {
		this.app_list_find = app_list_find;
	}
	public String getChannel_type_list_find() {
		return channel_type_list_find;
	}
	public void setChannel_type_list_find(String channel_type_list_find) {
		this.channel_type_list_find = channel_type_list_find;
	}
	public String getMedia_channel_list_find_table() {
		return media_channel_list_find_table;
	}
	public void setMedia_channel_list_find_table(String media_channel_list_find_table) {
		this.media_channel_list_find_table = media_channel_list_find_table;
	}
	public String getAgent_list_find() {
		return agent_list_find;
	}
	public void setAgent_list_find(String agent_list_find) {
		this.agent_list_find = agent_list_find;
	}
	public String getPut_user_list_find() {
		return put_user_list_find;
	}
	public void setPut_user_list_find(String put_user_list_find) {
		this.put_user_list_find = put_user_list_find;
	}
	public String getArtist_list_find() {
		return artist_list_find;
	}
	public void setArtist_list_find(String artist_list_find) {
		this.artist_list_find = artist_list_find;
	}
	public String getTitle_list_find() {
		return title_list_find;
	}
	public void setTitle_list_find(String title_list_find) {
		this.title_list_find = title_list_find;
	}
	public String getTypelinks() {
		return typelinks;
	}
	public void setTypelinks(String typelinks) {
		this.typelinks = typelinks;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public String getKeysx() {
		return keysx;
	}
	public void setKeysx(String keysx) {
		this.keysx = keysx;
	}
	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}
	public String getApp_key() {
		return app_key;
	}
	public void setApp_key(String app_key) {
		this.app_key = app_key;
	}
	public String getApp_name() {
		return app_name;
	}
	public void setApp_name(String app_name) {
		this.app_name = app_name;
	}
	public String getRole_app_slot() {
		return role_app_slot;
	}
	public void setRole_app_slot(String role_app_slot) {
		this.role_app_slot = role_app_slot;
	}
	public String getUser_id() {
		return user_id;
	}
	public void setUser_id(String user_id) {
		this.user_id = user_id;
	}
	public String getUser_name() {
		return user_name;
	}
	public void setUser_name(String user_name) {
		this.user_name = user_name;
	}
	public String getOrg_id() {
		return org_id;
	}
	public void setOrg_id(String org_id) {
		this.org_id = org_id;
	}
	public String getSys() {
		return sys;
	}
	public void setSys(String sys) {
		this.sys = sys;
	}
	public String getOrg_name() {
		return org_name;
	}
	public void setOrg_name(String org_name) {
		this.org_name = org_name;
	}
	public String getHidden_menu_list() {
		return hidden_menu_list;
	}
	public void setHidden_menu_list(String hidden_menu_list) {
		this.hidden_menu_list = hidden_menu_list;
	}
	public String getPage_list() {
		return page_list;
	}
	public void setPage_list(String page_list) {
		this.page_list = page_list;
	}
	public String getIndex() {
		return index;
	}
	public void setIndex(String index) {
		this.index = index;
	}
	public String getStyle() {
		return style;
	}
	public void setStyle(String style) {
		this.style = style;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getMenu() {
		return menu;
	}
	public void setMenu(String menu) {
		this.menu = menu;
	}
	public int getOff() {
		return off;
	}
	public void setOff(int off) {
		this.off = off;
	}
	public int getLeve() {
		return leve;
	}
	public void setLeve(int leve) {
		this.leve = leve;
	}
	public String getSlot() {
		return slot;
	}
	public void setSlot(String slot) {
		this.slot = slot;
	}

	public String getSys_type() {
		return sys_type;
	}

	public void setSys_type(String sys_type) {
		this.sys_type = sys_type;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getCha_type_find() {
		return cha_type_find;
	}

	public void setCha_type_find(String cha_type_find) {
		this.cha_type_find = cha_type_find;
	}

	public String getAccount_group() {
		return account_group;
	}

	public void setAccount_group(String account_group) {
		this.account_group = account_group;
	}
	
}
