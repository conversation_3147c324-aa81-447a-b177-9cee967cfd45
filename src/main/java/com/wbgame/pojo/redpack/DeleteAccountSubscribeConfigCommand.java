package com.wbgame.pojo.redpack;

import com.wbgame.common.GenericQueryParameters;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 账户订阅页删除 dto
 * @Date 2024/8/20 14:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeleteAccountSubscribeConfigCommand extends GenericQueryParameters {

    @ApiModelProperty(value = "id集合")
    List<Integer> idList;
}
