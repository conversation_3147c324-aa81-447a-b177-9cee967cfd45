package com.wbgame.pojo.redpack;

import java.math.BigDecimal;
import java.util.Date;

public class HbIssueWeakConfig {

    private String pid;

    private BigDecimal min_money;

    private BigDecimal max_money;

    private BigDecimal rate;

    private Date create_time;

    private Date update_time;

    private String create_str;

    private String update_str;

    private String create_owner;

    private String update_owner;

    private String order;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public BigDecimal getMin_money() {
        return min_money;
    }

    public void setMin_money(BigDecimal min_money) {
        this.min_money = min_money;
    }

    public BigDecimal getMax_money() {
        return max_money;
    }

    public void setMax_money(BigDecimal max_money) {
        this.max_money = max_money;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public String getCreate_str() {
        return create_str;
    }

    public void setCreate_str(String create_str) {
        this.create_str = create_str;
    }

    public String getUpdate_str() {
        return update_str;
    }

    public void setUpdate_str(String update_str) {
        this.update_str = update_str;
    }

    public String getCreate_owner() {
        return create_owner;
    }

    public void setCreate_owner(String create_owner) {
        this.create_owner = create_owner;
    }

    public String getUpdate_owner() {
        return update_owner;
    }

    public void setUpdate_owner(String update_owner) {
        this.update_owner = update_owner;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}
