package com.wbgame.pojo.yxcGame;

/**
 * yxc_gamecfg_info
 * <AUTHOR>
 * @date: 2020年10月28日
 */
public class GameCfgInfo {

    private String prjid;// 产品ID
    private String ver;// 渠道
    private String config;// 游戏ID数组配置
    private String searchText;// 搜索栏默认显示的游戏名称
    private String hotTextList;// 热门搜索关键字列表
    private String hotSortList;// 热门搜索分类
    
    private String typeArray;// 游戏类型数组配置 ["mxmn", "knfy"]
    private String learnArray;// 学习类型数组配置 ["yuwen", "shuxue"]
    private String gameHomeActivity   ;// 游戏主Activity（游戏悬浮窗用）
    private String myGamePakges;// 自有游戏包名列表
    private String shareConfig;// 分享配置
    private String adConfig;// 插屏广告间隔配置（gameInNum：启动插屏间隔 ； gameDownNum：下载插屏间隔；gameFailNum：子游戏返回大厅插屏间隔）
    private String installDsktop;// 安装到系统桌面包名列表
    
    
	public String getPrjid() {
		return prjid;
	}
	public void setPrjid(String prjid) {
		this.prjid = prjid;
	}
	public String getVer() {
		return ver;
	}
	public void setVer(String ver) {
		this.ver = ver;
	}
	public String getConfig() {
		return config;
	}
	public void setConfig(String config) {
		this.config = config;
	}
	public String getSearchText() {
		return searchText;
	}
	public void setSearchText(String searchText) {
		this.searchText = searchText;
	}
	public String getHotTextList() {
		return hotTextList;
	}
	public void setHotTextList(String hotTextList) {
		this.hotTextList = hotTextList;
	}
	public String getHotSortList() {
		return hotSortList;
	}
	public void setHotSortList(String hotSortList) {
		this.hotSortList = hotSortList;
	}
	public String getTypeArray() {
		return typeArray;
	}
	public void setTypeArray(String typeArray) {
		this.typeArray = typeArray;
	}
	public String getGameHomeActivity() {
		return gameHomeActivity;
	}
	public void setGameHomeActivity(String gameHomeActivity) {
		this.gameHomeActivity = gameHomeActivity;
	}
	public String getMyGamePakges() {
		return myGamePakges;
	}
	public void setMyGamePakges(String myGamePakges) {
		this.myGamePakges = myGamePakges;
	}
	public String getShareConfig() {
		return shareConfig;
	}
	public void setShareConfig(String shareConfig) {
		this.shareConfig = shareConfig;
	}
	public String getAdConfig() {
		return adConfig;
	}
	public void setAdConfig(String adConfig) {
		this.adConfig = adConfig;
	}
	public String getInstallDsktop() {
		return installDsktop;
	}
	public void setInstallDsktop(String installDsktop) {
		this.installDsktop = installDsktop;
	}
	public String getLearnArray() {
		return learnArray;
	}
	public void setLearnArray(String learnArray) {
		this.learnArray = learnArray;
	}
   
}