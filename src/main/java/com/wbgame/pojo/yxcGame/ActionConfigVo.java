package com.wbgame.pojo.yxcGame;

/**
 * @description: 活动配置
 * @author: huangmb
 * @date: 2020-10-30
 **/
public class ActionConfigVo {

    //项目id
    private Integer prjid;

    //版本
    private String ver;

    //活动配置
    private String config;

    //应用用户签名的key
    private String signkey;

    //日总金额上限，元
    private Integer day_sum;

    //每日每人提现次数上限
    private Integer day_count;

    //汇率, 比如:100000 ,代表100000金币兑换1元
    private Integer excRate;

    //单个游戏奖励领取的上限为150
    private Integer awardLimit;

    //任务列表
    private String taskList;

    //活跃任务,都是一次性任务
    private String activeTaskList;

    //版本检查
    private String appCheck;

    public String getVer() {
        return ver;
    }

    public void setVer(String ver) {
        this.ver = ver;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getSignkey() {
        return signkey;
    }

    public void setSignkey(String signkey) {
        this.signkey = signkey;
    }

    public Integer getDay_sum() {
        return day_sum;
    }

    public void setDay_sum(Integer day_sum) {
        this.day_sum = day_sum;
    }

    public Integer getDay_count() {
        return day_count;
    }

    public void setDay_count(Integer day_count) {
        this.day_count = day_count;
    }

    public Integer getExcRate() {
        return excRate;
    }

    public void setExcRate(Integer excRate) {
        this.excRate = excRate;
    }

    public Integer getAwardLimit() {
        return awardLimit;
    }

    public void setAwardLimit(Integer awardLimit) {
        this.awardLimit = awardLimit;
    }

    public String getTaskList() {
        return taskList;
    }

    public void setTaskList(String taskList) {
        this.taskList = taskList;
    }

    public String getActiveTaskList() {
        return activeTaskList;
    }

    public void setActiveTaskList(String activeTaskList) {
        this.activeTaskList = activeTaskList;
    }

    public String getAppCheck() {
        return appCheck;
    }

    public void setAppCheck(String appCheck) {
        this.appCheck = appCheck;
    }

    public Integer getPrjid() {
        return prjid;
    }

    public void setPrjid(Integer prjid) {
        this.prjid = prjid;
    }
}
