package com.wbgame.pojo;

public class Box2333InfoVo {
	
	private String wbappid;
	private String openid;
	private int hold_money; // 持有金额(单位分)
	private boolean is_sign; // 今日是否签到
	private boolean is_over; // 每人只能帮人助力一次
	private int day_times; // 可通过助力抽奖次数(每天3次)
	private int reward_num; // 已拥有抽奖数(和抽奖次数和只能为3)
	private String sub_list; // 当前用户助力好友openid，用逗号隔开
	private String today; // 用来在第二天重置次数
	
	private String box_openid; // 2333公众号openid
	private String wxname;
	private String wxicon;
	
	
	public String getWbappid() {
		return wbappid;
	}
	public void setWbappid(String wbappid) {
		this.wbappid = wbappid;
	}
	public String getOpenid() {
		return openid;
	}
	public void setOpenid(String openid) {
		this.openid = openid;
	}
	public int getHold_money() {
		return hold_money;
	}
	public void setHold_money(int hold_money) {
		this.hold_money = hold_money;
	}
	public boolean isIs_sign() {
		return is_sign;
	}
	public void setIs_sign(boolean is_sign) {
		this.is_sign = is_sign;
	}
	
	public boolean isIs_over() {
		return is_over;
	}
	public void setIs_over(boolean is_over) {
		this.is_over = is_over;
	}
	public int getDay_times() {
		return day_times;
	}
	public void setDay_times(int day_times) {
		this.day_times = day_times;
	}
	public int getReward_num() {
		return reward_num;
	}
	public void setReward_num(int reward_num) {
		this.reward_num = reward_num;
	}
	public String getSub_list() {
		return sub_list;
	}
	public void setSub_list(String sub_list) {
		this.sub_list = sub_list;
	}
	public String getToday() {
		return today;
	}
	public void setToday(String today) {
		this.today = today;
	}
	public String getWxname() {
		return wxname;
	}
	public void setWxname(String wxname) {
		this.wxname = wxname;
	}
	public String getWxicon() {
		return wxicon;
	}
	public void setWxicon(String wxicon) {
		this.wxicon = wxicon;
	}
	public String getBox_openid() {
		return box_openid;
	}
	public void setBox_openid(String box_openid) {
		this.box_openid = box_openid;
	}
	
}
