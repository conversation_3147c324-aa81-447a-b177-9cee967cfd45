package com.wbgame.pojo;

import java.util.Date;

/**
    * 友盟用户渠道分布
    */
public class UmengUserChannelTotal {
    /**
    * appid
    */
    private Integer appid;

    /**
    * 日期
    */
    private Date tdate;

    /**
    * appkey
    */
    private String appKey;

    /**
    * 活跃
    */
    private Integer actNum;

    /**
    * 新增
    */
    private Integer addNum;

    /**
    * 启动次数
    */
    private Integer startNum;

    /**
    * 安装渠道
    */
    private String installChannel;


    /**
     * 使用时长
     */
    private String duration;

    /**
     * 平均单次使用时长
     */
    private String dailyPerDuration;


    public Integer getAppid() {
        return appid;
    }

    public void setAppid(Integer appid) {
        this.appid = appid;
    }

    public Date getTdate() {
        return tdate;
    }

    public void setTdate(Date tdate) {
        this.tdate = tdate;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public Integer getActNum() {
        return actNum;
    }

    public void setActNum(Integer actNum) {
        this.actNum = actNum;
    }

    public Integer getAddNum() {
        return addNum;
    }

    public void setAddNum(Integer addNum) {
        this.addNum = addNum;
    }

    public Integer getStartNum() {
        return startNum;
    }

    public void setStartNum(Integer startNum) {
        this.startNum = startNum;
    }

    public String getInstallChannel() {
        return installChannel;
    }

    public void setInstallChannel(String installChannel) {
        this.installChannel = installChannel;
    }


    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getDailyPerDuration() {
        return dailyPerDuration;
    }

    public void setDailyPerDuration(String dailyPerDuration) {
        this.dailyPerDuration = dailyPerDuration;
    }
}