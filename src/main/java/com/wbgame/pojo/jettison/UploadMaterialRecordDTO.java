package com.wbgame.pojo.jettison;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Author: xugx
 * @Description: 素材上传详情记录
 * @Date: created in 2022/10/11/16:41
 */
public class UploadMaterialRecordDTO  {
	
	@ApiModelProperty("ID")
	private int id;
	
	@ApiModelProperty("任务ID")
	private int task_id;
	
    @ApiModelProperty("素材名称")
	private String material_name;
    
    @ApiModelProperty("素材路径名称")
	private String path_name;
    
    @ApiModelProperty("素材类型")
	private String material_type;
    
	/**
	 * 上传状态  0:失败  1：成功
	 */
    @ApiModelProperty("上传状态")
	private int upload_status;
	
	/**
	 * 同步状态 0:失败  1：成功  2:未做同步
	 */
    @ApiModelProperty("同步状态 ")
	private int sync_status;
    
    @ApiModelProperty("错误信息")
	private String error_msg;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getTask_id() {
		return task_id;
	}

	public void setTask_id(int task_id) {
		this.task_id = task_id;
	}

	public String getMaterial_name() {
		return material_name;
	}

	public void setMaterial_name(String material_name) {
		this.material_name = material_name;
	}

	public String getPath_name() {
		return path_name;
	}

	public void setPath_name(String path_name) {
		this.path_name = path_name;
	}

	public String getMaterial_type() {
		return material_type;
	}

	public void setMaterial_type(String material_type) {
		this.material_type = material_type;
	}

	public int getUpload_status() {
		return upload_status;
	}

	public void setUpload_status(int upload_status) {
		this.upload_status = upload_status;
	}

	public int getSync_status() {
		return sync_status;
	}

	public void setSync_status(int sync_status) {
		this.sync_status = sync_status;
	}

	public UploadMaterialRecordDTO() {
		super();
	}
	
	public String getError_msg() {
		return error_msg;
	}

	public void setError_msg(String error_msg) {
		this.error_msg = error_msg;
	}

	public UploadMaterialRecordDTO(int task_id, String material_name, String path_name, String material_type,
			int upload_status, int sync_status,String error_msg) {
		super();
		this.task_id = task_id;
		this.material_name = material_name;
		this.path_name = path_name;
		this.material_type = material_type;
		this.upload_status = upload_status;
		this.sync_status = sync_status;
		this.error_msg = error_msg;
	}
	
}
