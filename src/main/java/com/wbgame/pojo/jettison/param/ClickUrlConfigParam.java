package com.wbgame.pojo.jettison.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 点击监测链接配置查询
 * <AUTHOR>
 * @Date 2025/3/17 17:00
 */
@Data
@ApiModel("点击监测链接配置查询")
public class ClickUrlConfigParam {

    @ApiModelProperty("平台")
    private String ad_platform;

    @ApiModelProperty("appid/包名")
    private String key_name;

    @ApiModelProperty("应用名")
    private String game_name;

    @ApiModelProperty("类型  0 无  1 商店 2 联盟")
    private Integer type;

    @ApiModelProperty("创建人")
    private String create_owner;

    @ApiModelProperty(value = "自定义列参数",dataType = "String")
    private String value;

    @ApiModelProperty(value = "页面名称",dataType = "String")
    private String export_file_name;

}
