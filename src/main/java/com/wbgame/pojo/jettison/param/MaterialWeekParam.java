package com.wbgame.pojo.jettison.param;

import java.util.List;

public class MaterialWeekParam extends BaseParam {
	/**
	 *素材类型  video  image
	 */
	private String material_type;
	
	/**
	 *设计师列表
	 */
	private List<String> artist ;
	
	/**
	 *素材周
	 */
	private List<String> tdate_week;
	
	/**
	 *产品id
	 */
	private String appid;

	/**
	 *创意人
	 */
	private List<String> creatives;	
	/**
	 *3D制作人
	 */
	private List<String> producer3d;	
	

	public List<String> getCreatives() {
		return creatives;
	}

	public void setCreatives(List<String> creatives) {
		this.creatives = creatives;
	}

	public List<String> getProducer3d() {
		return producer3d;
	}

	public void setProducer3d(List<String> producer3d) {
		this.producer3d = producer3d;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public List<String> getTdate_week() {
		return tdate_week;
	}

	public void setTdate_week(List<String> tdate_week) {
		this.tdate_week = tdate_week;
	}

	public String getMaterial_type() {
		return material_type;
	}

	public void setMaterial_type(String material_type) {
		this.material_type = material_type;
	}

	public List<String> getArtist() {
		return artist;
	}

	public void setArtist(List<String> artist) {
		this.artist = artist;
	}
	
	
}
