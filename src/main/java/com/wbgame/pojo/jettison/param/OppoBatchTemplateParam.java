package com.wbgame.pojo.jettison.param;

import com.wbgame.pojo.jettison.vo.OppoBatchCreativeVo;
import com.wbgame.pojo.jettison.vo.VivoBatchCreativeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description oppo批量计划创建模板参数
 * <AUTHOR>
 * @Date 2024/10/29 10:37
 */
@Data
@ApiModel("oppo批量计划创建模板参数")
public class OppoBatchTemplateParam {

    @ApiModelProperty(value = "模板id")
    private Long temp_id;

    @ApiModelProperty(value = "模板名称")
    private String temp_name;

    @ApiModelProperty(value = "广告账号")
    private String accounts;

    @ApiModelProperty(value = "推广目标")
    private Integer extension_type;

    @ApiModelProperty(value = "竞价策略")
    private Integer delivery_mode;

    @ApiModelProperty(value = "是否限制日预算")
    private Integer day_limit;

    @ApiModelProperty(value = "日预算(单位分)")
    private Long campaign_day_budget;

    @ApiModelProperty(value = "投放策略")
    private Integer pacing_status;

    @ApiModelProperty(value = "投放状态")
    private Integer campaign_status;

    @ApiModelProperty(value = "广告计划名称")
    private String campaign_name;

    @ApiModelProperty(value = "广告组数")
    private Integer group_num;

    @ApiModelProperty(value = "广告组名称")
    private String group_name;

    @ApiModelProperty(value = "应用id")
    private Integer oppo_appid;

    @ApiModelProperty(value = "推广流量")
    private Integer extension_flow;

    @ApiModelProperty(value = "流量场景")
    private Integer flow_scene;

    @ApiModelProperty(value = "广告类型")
    private Integer advertise_type;

    @ApiModelProperty(value = "投放开始日期")
    private String begin_time;

    @ApiModelProperty(value = "投放结束日期")
    private String endTime;

    @ApiModelProperty(value = "投放时段")
    private String time_set;

    @ApiModelProperty(value = "推广时段限制")
    private Integer time_limit;

    @ApiModelProperty(value = "按天分配计划")
    private Integer is_day_allocation;

    @ApiModelProperty(value = "出价方式")
    private  Integer billing_type;

    @ApiModelProperty(value = "基础出价（单位：分）")
    private Double price;

    @ApiModelProperty(value = "目标转化类型")
    private Integer ocpc_type;

    @ApiModelProperty(value = "目标转化出价（单位：分）")
    private Double ocpc_price;

    @ApiModelProperty(value = "深度优化目标")
    private Integer deep_ocpc_type;

    @ApiModelProperty(value = "深度优化出价")
    private Double deep_ocpc_price;

    @ApiModelProperty(value = "是否开启免一阶 1-开启，0-关闭")
    private Integer default_second_stage;

    @ApiModelProperty(value = "落地页建站类型")
    private Integer page_type;

    @ApiModelProperty(value = "落地页id")
    private Integer page_id;

    @ApiModelProperty(value = "下载并打开 0-未开启 1-开启")
    private Integer auto_openid_flag;

    @ApiModelProperty(value = "目標地址")
    private String page_url;

    @ApiModelProperty(value = "广告创意名称")
    private String ad_name;

    @ApiModelProperty(value = "创意数")
    private Integer creative_num;

    @ApiModelProperty(value = "地域")
    private String region;

    @ApiModelProperty(value = "性别id列表")
    private String sex;

    @ApiModelProperty(value = "年龄id列表")
    private String age;

    @ApiModelProperty(value = "安装定向")
    private Integer install_app;

    @ApiModelProperty(value = "最近卸载时间：0 不限 7 15 30 90 180")
    private Integer unstaill_time;

    @ApiModelProperty(value = "品牌名称")
    private String brand_name;

    @ApiModelProperty(value = "点击检测地址")
    private String click_url;

    @ApiModelProperty(value = "文案")
    private String content;

    @ApiModelProperty(value = "创意设置")
    private List<OppoBatchCreativeVo> creatives;

    private String create_owner;

    private String update_owner;

}
