package com.wbgame.pojo.jettison.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/30 18:57
 */
@Data
@ApiModel("创意素材库查询视图")
public class CreativeMaterialVo {

    @ApiModelProperty(value = "素材库id")
    private Long id;

    @ApiModelProperty(value = "素材名称")
    private String fileName;

    @ApiModelProperty(value = "创建日期")
    private String day;

    @ApiModelProperty(value = "md5(带格式)")
    private String signature;

    @ApiModelProperty(value = "md5")
    private String signature2;

    @ApiModelProperty(value = "素材地址")
    private String url;

    @ApiModelProperty(value = "素材类型")
    private String type;

    @ApiModelProperty(value = "素材格式")
    private String format;

    @ApiModelProperty(value = "大小(单位MB)")
    private String size;

    @ApiModelProperty(value = "高")
    private int height;

    @ApiModelProperty(value = "宽")
    private int wide;

    private Double spend;

    private Double spend7;

    private Double spend30;

}
