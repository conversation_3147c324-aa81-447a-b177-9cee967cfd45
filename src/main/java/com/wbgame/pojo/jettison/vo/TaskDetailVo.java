package com.wbgame.pojo.jettison.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("任务详情")
public class TaskDetailVo {

	@ApiModelProperty("账号")
    private String accountId;

    @ApiModelProperty("广告组ID")
    private String groupId;


    @ApiModelProperty("广告组名称")
    private String groupName;
    

    @ApiModelProperty("计划ID")
    private String campaignId;
    

    @ApiModelProperty("计划名称")
    private String campaignName;
    

    @ApiModelProperty("状态")
    private String status;
    

    @ApiModelProperty("错误日志")
    private String errMsg;


	public String getAccountId() {
		return accountId;
	}


	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}


	public String getGroupId() {
		return groupId;
	}


	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}


	public String getGroupName() {
		return groupName;
	}


	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}



	public String getCampaignId() {
		return campaignId;
	}


	public void setCampaignId(String campaignId) {
		this.campaignId = campaignId;
	}


	public String getCampaignName() {
		return campaignName;
	}


	public void setCampaignName(String campaignName) {
		this.campaignName = campaignName;
	}


	public String getStatus() {
		return status;
	}


	public void setStatus(String status) {
		this.status = status;
	}


	public String getErrMsg() {
		return errMsg;
	}


	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}


}
