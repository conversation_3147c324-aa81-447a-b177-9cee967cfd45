package com.wbgame.pojo.jettison.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/17 10:06
 */
@Data
@ApiModel("拉取项目日志视图")
public class EmptyProjectLogVo {

    @ApiModelProperty(value = "任务id")
    private String task_id;

    @ApiModelProperty(value = "账户")
    private String account;

    @ApiModelProperty(value = "项目id")
    private String project_id;

    @ApiModelProperty(value = "项目批次id")
    private String project_task_id;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "消息")
    private String message;

    @ApiModelProperty(value = "创建时间")
    private String create_time;

    @ApiModelProperty(value = "创建人")
    private String create_owner;

    @ApiModelProperty(value = "媒体")
    private String ad_platform;

    @ApiModelProperty(value = "类型")
    private String type;

}
