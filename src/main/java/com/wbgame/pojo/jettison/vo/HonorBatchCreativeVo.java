package com.wbgame.pojo.jettison.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description vivo创意设置
 * <AUTHOR>
 * @Date 2024/10/29 12:18
 */
@Data
@ApiModel("vivo创意设置")
public class HonorBatchCreativeVo {

    @ApiModelProperty(value = "模板id")
    private Long temp_id;

    @ApiModelProperty(value = "创意规格")
    private Integer ad_creative_spec_id;

    @ApiModelProperty(value = "素材md5")
    private String signature;

    @ApiModelProperty(value = "素材地址url")
    private String url;

    @ApiModelProperty(value = "封面md5")
    private String cover_signature;

    @ApiModelProperty(value = "封面地址url")
    private String cover_url;

    @ApiModelProperty(value = "logo md5")
    private String logo_signature;

    @ApiModelProperty(value = "logo地址url")
    private String logo_url;

    @ApiModelProperty(value = "素材类型   image/video")
    private String type;
}
