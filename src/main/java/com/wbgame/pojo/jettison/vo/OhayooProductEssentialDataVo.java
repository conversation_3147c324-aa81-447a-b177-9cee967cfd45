package com.wbgame.pojo.jettison.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description: Ohayoo产品基础数据dto
 * @author: xugx
 * @date: 2023/05/05
 **/
@ApiModel("Ohayoo产品 基础数据")
public class OhayooProductEssentialDataVo {
    @ApiModelProperty("日期")
    private String tdate;
    @ApiModelProperty("渠道类型")
    private String channelType;
    @ApiModelProperty("产品ID")
    private String appid;
    @ApiModelProperty("产品名称")
    private String appName;
    @ApiModelProperty("应用分类")
    private String appCategory;
    @ApiModelProperty("返点消耗")
    private double spend;
    /**
     * 买量新增
     */
    @ApiModelProperty("买量新增")
    private long add_user;
    
    /**
     * 买量cpa
     */
    @ApiModelProperty("买量cpa")
    private double purchaseCpa;
    /**
     * 首日ROI
     */
    @ApiModelProperty("首日ROI")
    private String roi;
    /**
     * 累计ROI
     */
    @ApiModelProperty("累计ROI")
    private String acmulRoi;
    /**
     * 总新增
     */
    @ApiModelProperty("总新增")
    private int addNum;
    /**
     * 自然量占比  （新增用户-买量新增）/新增用户
     */
    @ApiModelProperty("自然量占比")
    private String  naturalPro;
   
    /**
     * spend/addNum
     */
    @ApiModelProperty("cpa")
    private double cpa; 
    /**
     *新增占比  新增用户/活跃用户
     */
    @ApiModelProperty("新增占比")
    private String  newPro;
    @ApiModelProperty("活跃用户数")
    private int actNum;
    
    @ApiModelProperty("次留")
    private String keep_num1;
    @ApiModelProperty("2留")
    private String keep_num2;
    @ApiModelProperty("3留")
    private String keep_num3;
    @ApiModelProperty("4留")
    private String keep_num4;
    @ApiModelProperty("5留")
    private String keep_num5;
    @ApiModelProperty("6留")
    private String keep_num6;
    @ApiModelProperty("7留")
    private String keep_num7;
    @ApiModelProperty("14留")
    private String keep_num14;
    @ApiModelProperty("30留")
    private String keep_num30;
    @ApiModelProperty("广告收入")
    private double ad_revenue;
    @ApiModelProperty("活跃时长")
    private String daily_duration;
    @ApiModelProperty("单次游戏时长")
    private String daily_per_duration;
    @ApiModelProperty("启动次数")
    private double launch;
    @ApiModelProperty("广告展示量")
    private long ad_pv;
    /**
     * 预估收益/活跃用户
     */
    @ApiModelProperty("arpu")
    private double arpu; 
    
    /**
     * 预估收益/活跃用户
     */
    @ApiModelProperty("视频人均pv")
    private double avgPv; 
    /**
     * 广告收入/展示量*1000
     */
    @ApiModelProperty("视频ecpm")
    private double ecpm; 
    
	public double getAvgPv() {
		return avgPv;
	}

	public void setAvgPv(double avgPv) {
		this.avgPv = avgPv;
	}

	public double getEcpm() {
		return ecpm;
	}

	public void setEcpm(double ecpm) {
		this.ecpm = ecpm;
	}

	public double getPurchaseCpa() {
		return purchaseCpa;
	}

	public void setPurchaseCpa(double purchaseCpa) {
		this.purchaseCpa = purchaseCpa;
	}

	public String getRoi() {
		return roi;
	}

	public void setRoi(String roi) {
		this.roi = roi;
	}

	public String getAcmulRoi() {
		return acmulRoi;
	}

	public void setAcmulRoi(String acmulRoi) {
		this.acmulRoi = acmulRoi;
	}

	public String getNaturalPro() {
		return naturalPro;
	}

	public void setNaturalPro(String naturalPro) {
		this.naturalPro = naturalPro;
	}

	public double getCpa() {
		return cpa;
	}

	public void setCpa(double cpa) {
		this.cpa = cpa;
	}

	public String getNewPro() {
		return newPro;
	}

	public void setNewPro(String newPro) {
		this.newPro = newPro;
	}

	public double getArpu() {
		return arpu;
	}

	public void setArpu(double arpu) {
		this.arpu = arpu;
	}

	public long getAd_pv() {
		return ad_pv;
	}

	public void setAd_pv(long ad_pv) {
		this.ad_pv = ad_pv;
	}

	public int getAddNum() {
		return addNum;
	}

	public void setAddNum(int addNum) {
		this.addNum = addNum;
	}

	public int getActNum() {
		return actNum;
	}

	public void setActNum(int actNum) {
		this.actNum = actNum;
	}

	public String getKeep_num1() {
		return keep_num1;
	}

	public void setKeep_num1(String keep_num1) {
		this.keep_num1 = keep_num1;
	}

	public String getKeep_num2() {
		return keep_num2;
	}

	public void setKeep_num2(String keep_num2) {
		this.keep_num2 = keep_num2;
	}

	public String getKeep_num3() {
		return keep_num3;
	}

	public void setKeep_num3(String keep_num3) {
		this.keep_num3 = keep_num3;
	}

	public String getKeep_num4() {
		return keep_num4;
	}

	public void setKeep_num4(String keep_num4) {
		this.keep_num4 = keep_num4;
	}

	public String getKeep_num5() {
		return keep_num5;
	}

	public void setKeep_num5(String keep_num5) {
		this.keep_num5 = keep_num5;
	}

	public String getKeep_num6() {
		return keep_num6;
	}

	public void setKeep_num6(String keep_num6) {
		this.keep_num6 = keep_num6;
	}

	public String getKeep_num7() {
		return keep_num7;
	}

	public void setKeep_num7(String keep_num7) {
		this.keep_num7 = keep_num7;
	}

	public String getKeep_num14() {
		return keep_num14;
	}

	public void setKeep_num14(String keep_num14) {
		this.keep_num14 = keep_num14;
	}

	public String getKeep_num30() {
		return keep_num30;
	}

	public void setKeep_num30(String keep_num30) {
		this.keep_num30 = keep_num30;
	}

	public double getAd_revenue() {
		return ad_revenue;
	}

	public void setAd_revenue(double ad_revenue) {
		this.ad_revenue = ad_revenue;
	}

	public String getDaily_duration() {
		return daily_duration;
	}

	public void setDaily_duration(String daily_duration) {
		this.daily_duration = daily_duration;
	}

	public String getDaily_per_duration() {
		return daily_per_duration;
	}

	public void setDaily_per_duration(String daily_per_duration) {
		this.daily_per_duration = daily_per_duration;
	}

	public double getLaunch() {
		return launch;
	}

	public void setLaunch(double launch) {
		this.launch = launch;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public String getAppCategory() {
		return appCategory;
	}

	public void setAppCategory(String appCategory) {
		this.appCategory = appCategory;
	}

	public double getSpend() {
		return spend;
	}

	public void setSpend(double spend) {
		this.spend = spend;
	}

	public long getAdd_user() {
		return add_user;
	}

	public void setAdd_user(long add_user) {
		this.add_user = add_user;
	}

	public String getTdate() {
		return tdate;
	}

	public void setTdate(String tdate) {
		this.tdate = tdate;
	}

	public OhayooProductEssentialDataVo(String date, String channelType, String appid, String appName,
			String appCategory, double spend, long add_user) {
		super();
		this.tdate = date;
		this.channelType = channelType;
		this.appid = appid;
		this.appName = appName;
		this.appCategory = appCategory;
		this.spend = spend;
		this.add_user = add_user;
	}

	public OhayooProductEssentialDataVo() {
		super();
	}
    
    
}
