package com.wbgame.pojo.jettison.report.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 新增用户报表接口查询对象
 * @Create 2023-03-07
 */
@ApiModel(value = "新增用户报表接口查询对象")
public class AddUserReportParam extends BaseReportParam {

    @ApiModelProperty(value = "平台")
    private List<String> ad_platform;

    @ApiModelProperty(value = "排序字段")
    private String prop;

    @ApiModelProperty(value = "排序类型")
    private String order;

    @ApiModelProperty(value = "自定义列")
    private List<String> customizes;

    public List<String> getAd_platform() {
        return ad_platform;
    }

    public void setAd_platform(List<String> ad_platform) {
        this.ad_platform = ad_platform;
    }

    @Override
    public String getProp() {
        return prop;
    }

    @Override
    public void setProp(String prop) {
        this.prop = prop;
    }

    @Override
    public String getOrder() {
        return order;
    }

    @Override
    public void setOrder(String order) {
        this.order = order;
    }

    public List<String> getCustomizes() {
        return customizes;
    }

    public void setCustomizes(List<String> customizes) {
        this.customizes = customizes;
    }
}
