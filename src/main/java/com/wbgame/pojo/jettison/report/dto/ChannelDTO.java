package com.wbgame.pojo.jettison.report.dto;

/**
 * <AUTHOR>
 * @Description 子渠道内容
 * @Create 2022-12-13
 */
public class ChannelDTO {

    private String cha_id;

    private Integer cha_type;

    private String cha_media;

    private String cha_sub_launch;

    private Integer cha_id_byte;

    private String cha_name_byte;

    private String cha_sub_byte;

    private String cha_sub_name_byte;

    private String cha_remark;

    private String type_name;

    public String getCha_id() {
        return cha_id;
    }

    public void setCha_id(String cha_id) {
        this.cha_id = cha_id;
    }

    public Integer getCha_type() {
        return cha_type;
    }

    public void setCha_type(Integer cha_type) {
        this.cha_type = cha_type;
    }

    public String getCha_media() {
        return cha_media;
    }

    public void setCha_media(String cha_media) {
        this.cha_media = cha_media;
    }

    public String getCha_sub_launch() {
        return cha_sub_launch;
    }

    public void setCha_sub_launch(String cha_sub_launch) {
        this.cha_sub_launch = cha_sub_launch;
    }

    public Integer getCha_id_byte() {
        return cha_id_byte;
    }

    public void setCha_id_byte(Integer cha_id_byte) {
        this.cha_id_byte = cha_id_byte;
    }

    public String getCha_name_byte() {
        return cha_name_byte;
    }

    public void setCha_name_byte(String cha_name_byte) {
        this.cha_name_byte = cha_name_byte;
    }

    public String getCha_sub_byte() {
        return cha_sub_byte;
    }

    public void setCha_sub_byte(String cha_sub_byte) {
        this.cha_sub_byte = cha_sub_byte;
    }

    public String getCha_sub_name_byte() {
        return cha_sub_name_byte;
    }

    public void setCha_sub_name_byte(String cha_sub_name_byte) {
        this.cha_sub_name_byte = cha_sub_name_byte;
    }

    public String getCha_remark() {
        return cha_remark;
    }

    public void setCha_remark(String cha_remark) {
        this.cha_remark = cha_remark;
    }

    public String getType_name() {
        return type_name;
    }

    public void setType_name(String type_name) {
        this.type_name = type_name;
    }
}
