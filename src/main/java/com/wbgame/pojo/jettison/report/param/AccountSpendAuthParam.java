package com.wbgame.pojo.jettison.report.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "账户开发应用对象")
public class AccountSpendAuthParam {

    @ApiModelProperty(value = "账号列表")
    private List<String> accountList;

    @ApiModelProperty(value = "开发者应用id")
    private Integer developer_id;

    private String updateUser;

    public List<String> getAccountList() {
        return accountList;
    }

    public void setAccountList(List<String> accountList) {
        this.accountList = accountList;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Integer getDeveloper_id() {
        return developer_id;
    }

    public void setDeveloper_id(Integer developer_id) {
        this.developer_id = developer_id;
    }
}
