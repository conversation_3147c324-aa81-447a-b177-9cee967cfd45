package com.wbgame.pojo.jettison;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 描述
 * @Create 2020-09-28 09:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "素材可视化条件")
public class MaterialCondition {

    @ApiModelProperty(value = "素材创建日期")
    private List<String> days;

    @ApiModelProperty(value = "素材投放日期")
    private List<String> putDays;

    @ApiModelProperty(value = "美术人员")
    private List<String> artists;

    @ApiModelProperty(value = "应用")
    private List<String> apps;

    @ApiModelProperty(value = "素材类型")
    private List<String> types;

    @ApiModelProperty(value = "维度")
    private List<String> groups;

    private boolean putWeekGroup;

    private boolean weekGroup;

    private boolean appGroup;

}
