package com.wbgame.pojo.operate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "微信分享内容配置")
public class ShareContentConfigVo {

    @ApiModelProperty(value = "id",dataType = "Integer")
    private Integer id;

    @ApiModelProperty(value = "备注",dataType = "String")
    private String remark;

    @ApiModelProperty(value = "标题",dataType = "String")
    private String title;

    @ApiModelProperty(value = "图片地址",dataType = "String")
    private String image_url;

    @ApiModelProperty(value = "图片编号",dataType = "String")
    private String image_number;

    @ApiModelProperty(value = "创建时间",dataType = "String")
    private String create_time;

    @ApiModelProperty(value = "更新时间",dataType = "String")
    private String update_time;

    @ApiModelProperty(value = "创建人",dataType = "String")
    private String create_owner;

    @ApiModelProperty(value = "更新人",dataType = "String")
    private String update_owner;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImage_url() {
        return image_url;
    }

    public void setImage_url(String image_url) {
        this.image_url = image_url;
    }

    public String getImage_number() {
        return image_number;
    }

    public void setImage_number(String image_number) {
        this.image_number = image_number;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getCreate_owner() {
        return create_owner;
    }

    public void setCreate_owner(String create_owner) {
        this.create_owner = create_owner;
    }

    public String getUpdate_owner() {
        return update_owner;
    }

    public void setUpdate_owner(String update_owner) {
        this.update_owner = update_owner;
    }
}
