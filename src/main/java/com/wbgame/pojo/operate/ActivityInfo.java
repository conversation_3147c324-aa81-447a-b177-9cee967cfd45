package com.wbgame.pojo.operate;

import com.wbgame.common.GenericQueryParameters;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

@ApiModel("活动参与及付费配置表")
public class ActivityInfo extends GenericQueryParameters {

    @ApiModelProperty("test")
    private Integer id;

    @ApiModelProperty("'产品id'")
    private Integer appId;

    @ApiModelProperty("'产品id'")
    private String appName;

    @ApiModelProperty("'活动id'")
    private String activityId;

    @ApiModelProperty("'活动名'")
    private String activityName;

    @ApiModelProperty("'状态'")
    private Byte status;

    @ApiModelProperty("'创建人'")
    private String createUser;

    @ApiModelProperty("'修改人'")
    private String updateUser;

    @ApiModelProperty("'创建时间'")
    private String createTime;

    @ApiModelProperty("'修改时间'")
    private String updateTime;

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAppId() {
        return appId;
    }

    public void setAppId(Integer appId) {
        this.appId = appId;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId == null ? null : activityId.trim();
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName == null ? null : activityName.trim();
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ActivityInfo that = (ActivityInfo) o;
        return Objects.equals(appId, that.appId)  && Objects.equals(activityId, that.activityId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(appId, activityId);
    }
}