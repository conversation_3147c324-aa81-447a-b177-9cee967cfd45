package com.wbgame.pojo.operate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "通用报表参数")
public class CommonReportVo {

    @ApiModelProperty(value = "开始时间",dataType = "String")
    private String startTime;

    @ApiModelProperty(value = "结束时间",dataType = "String")
    private String endTime;

    @ApiModelProperty(value = "appid",dataType = "String")
    private String appid;

    @ApiModelProperty(value = "子渠道",dataType = "String")
    private String channel;

    @ApiModelProperty(value = "版本号",dataType = "String")
    private String version;

    @ApiModelProperty(value = "排序",dataType = "String")
    private String order;

    @ApiModelProperty(value = "分组",dataType = "String")
    private String group;

    @ApiModelProperty(value = "自定义列参数",dataType = "String",example = "appid,产品id;channel,渠道")
    private String value;

    @ApiModelProperty(value = "当前页",dataType = "Integer")
    private Integer start;

    @ApiModelProperty(value = "页最大数",dataType = "Integer")
    private Integer limit;

    @ApiModelProperty(value = "页面名称",dataType = "Integer")
    private String report;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getReport() {
        return report;
    }

    public void setReport(String report) {
        this.report = report;
    }
}
