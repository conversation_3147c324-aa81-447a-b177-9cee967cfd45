package com.wbgame.pojo.operate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * @author: xiaoxh
 * @createDate: 2023/02/20 020
 * @class: UserCompositionAnalysisDTO
 * @description:
 */
@ApiModel("用户构成分析报表")
public class UserCompositionAnalysisVO {

    @ApiModelProperty("日期")
    private String tdate;

    @ApiModelProperty("app")
    private String appid;

    @ApiModelProperty("产品名")
    private String app_name;

    @ApiModelProperty("注册时间距当前日期:(分31组： 1天，2天...... 30天，30天以上);(分6组,轻度用户,中度用户,重度用户,超重度用户,硬核用户)")
    private String grp;

    @ApiModelProperty("总用户数")
    private String total_user_cnt;

    @ApiModelProperty("总iap用户")
    private String total_iap_user;

    @ApiModelProperty("总收入")
    private String total_income;

    @ApiModelProperty("付费率")
    private String pay_rate;

    @ApiModelProperty("ARPU")
    private String arpu;

    @ApiModelProperty("ARRPU")
    private String arrpu;

    @ApiModelProperty("次留")
    private String stay2;

    @ApiModelProperty("3留")
    private String stay3;

    @ApiModelProperty("7留")
    private String stay7;

    @ApiModelProperty("14留")
    private String stay14;

    @ApiModelProperty("30留")
    private String stay30;

    @ApiModelProperty("60留")
    private String stay60;

    @ApiModelProperty("总用户数（日平均）")
    private String day_avg_user;


    // 活跃度相关字段
    @ApiModelProperty(value = "活跃用户数")
    private Integer active_user_cnt;
    @ApiModelProperty(value = "人均时长(分钟)")
    private String avg_active_time;
    @ApiModelProperty(value = "中位数时长(分钟)")
    private String mid_active_time;
    @ApiModelProperty(value = "最小时长(分钟)")
    private String min_active_time;
    @ApiModelProperty(value = "最大时长(分钟)")
    private String max_active_time;
    @ApiModelProperty(value = "活跃用户数（日平均）")
    private String active_day_avg_user;


    public Integer getActive_user_cnt() {
        return active_user_cnt;
    }
    public void setActive_user_cnt(Integer active_user_cnt) {
        this.active_user_cnt = active_user_cnt;
    }
    public String getAvg_active_time() {
        return avg_active_time;
    }
    public void setAvg_active_time(String avg_active_time) {
        this.avg_active_time = avg_active_time;
    }
    public String getMid_active_time() {
        return mid_active_time;
    }
    public void setMid_active_time(String mid_active_time) {
        this.mid_active_time = mid_active_time;
    }
    public String getMin_active_time() {
        return min_active_time;
    }
    public void setMin_active_time(String min_active_time) {
        this.min_active_time = min_active_time;
    }
    public String getMax_active_time() {
        return max_active_time;
    }
    public void setMax_active_time(String max_active_time) {
        this.max_active_time = max_active_time;
    }
    public String getActive_day_avg_user() {
        return active_day_avg_user;
    }
    public void setActive_day_avg_user(String active_day_avg_user) {
        this.active_day_avg_user = active_day_avg_user;
    }
    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }

    public String getGrp() {
        return grp;
    }

    public void setGrp(String grp) {
        this.grp = grp;
    }

    public String getTotal_user_cnt() {
        return total_user_cnt;
    }

    public void setTotal_user_cnt(String total_user_cnt) {
        this.total_user_cnt = total_user_cnt;
    }

    public String getTotal_iap_user() {
        return total_iap_user;
    }

    public void setTotal_iap_user(String total_iap_user) {
        this.total_iap_user = total_iap_user;
    }

    public String getTotal_income() {
        return total_income;
    }

    public void setTotal_income(String total_income) {
        this.total_income = total_income;
    }

    public String getPay_rate() {
        return pay_rate;
    }

    public void setPay_rate(String pay_rate) {
        this.pay_rate = pay_rate;
    }

    public String getArpu() {
        return arpu;
    }

    public void setArpu(String arpu) {
        this.arpu = arpu;
    }

    public String getArrpu() {
        return arrpu;
    }

    public void setArrpu(String arrpu) {
        this.arrpu = arrpu;
    }

    public String getStay2() {
        return stay2;
    }

    public void setStay2(String stay2) {
        this.stay2 = stay2;
    }

    public String getStay3() {
        return stay3;
    }

    public void setStay3(String stay3) {
        this.stay3 = stay3;
    }

    public String getStay7() {
        return stay7;
    }

    public void setStay7(String stay7) {
        this.stay7 = stay7;
    }

    public String getStay14() {
        return stay14;
    }

    public void setStay14(String stay14) {
        this.stay14 = stay14;
    }

    public String getStay30() {
        return stay30;
    }

    public void setStay30(String stay30) {
        this.stay30 = stay30;
    }

    public String getStay60() {
        return stay60;
    }

    public void setStay60(String stay60) {
        this.stay60 = stay60;
    }

    public String getDay_avg_user() {
        return day_avg_user;
    }

    public void setDay_avg_user(String day_avg_user) {
        this.day_avg_user = day_avg_user;
    }
}
