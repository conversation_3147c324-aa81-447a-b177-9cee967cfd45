package com.wbgame.pojo.operate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "米大师秘钥配置")
@Data
public class PayWarnConfigVo {

    @ApiModelProperty(value = "appid",dataType = "String")
    private String appid;

    @ApiModelProperty(value = "创建时间",dataType = "String")
    private String createtime;

    @ApiModelProperty(value = "更新时间",dataType = "String")
    private String updatetime;

    @ApiModelProperty(value = "创建人",dataType = "String")
    private String create_owner;

    @ApiModelProperty(value = "更新人",dataType = "String")
    private String update_owner;

}
