package com.wbgame.pojo.operate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbgame.common.GenericQueryParameters;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel("抖音小程序支付配置")
public class TtAppletPayConfig extends GenericQueryParameters {

    @ApiModelProperty("'公司产品ID'")
    private String appid;

    @ApiModelProperty("'小程序 APP_ID'")
    private String tt_id;

    @ApiModelProperty("'秘钥'")
    private String app_secret;

    @ApiModelProperty("应用公钥版本，默认填1")
    private String public_version;

    @ApiModelProperty("'私钥'")
    private String private_key;

    @ApiModelProperty("创建人")
    private String create_user;

    @ApiModelProperty("修改人")
    private String update_user;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date create_time;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date update_time;


    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getTt_id() {
        return tt_id;
    }

    public void setTt_id(String tt_id) {
        this.tt_id = tt_id;
    }

    public String getApp_secret() {
        return app_secret;
    }

    public void setApp_secret(String app_secret) {
        this.app_secret = app_secret;
    }

    public String getPublic_version() {
        return public_version;
    }

    public void setPublic_version(String public_version) {
        this.public_version = public_version;
    }

    public String getPrivate_key() {
        return private_key;
    }

    public void setPrivate_key(String private_key) {
        this.private_key = private_key;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }
}