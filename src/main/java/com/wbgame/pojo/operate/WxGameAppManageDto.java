package com.wbgame.pojo.operate;

import com.wbgame.common.GeneralReportParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel("小游戏产品管理")
public class WxGameAppManageDto  extends GeneralReportParam {


    @ApiModelProperty("id")
    private int id;

    @ApiModelProperty("产品名称")
    private String appname;
    
    @ApiModelProperty("微信appid")
    private String wxappid;
    
    @ApiModelProperty("一级品类")
    private String category;

    @ApiModelProperty("二级品类")
    private String sub_category;

    @ApiModelProperty("变更日期")
    private String update_time;

    @ApiModelProperty("操作人")
    private String update_user;
    
    @ApiModelProperty("status")
    private Integer status;
    
    @ApiModelProperty("limit")
    private Integer limit;
    
    @ApiModelProperty("start")
    private Integer start;
    
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getSub_category() {
		return sub_category;
	}

	public void setSub_category(String sub_category) {
		this.sub_category = sub_category;
	}

	public String getUpdate_time() {
		return update_time;
	}

	public void setUpdate_time(String update_time) {
		this.update_time = update_time;
	}

	public String getUpdate_user() {
		return update_user;
	}

	public void setUpdate_user(String update_user) {
		this.update_user = update_user;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getLimit() {
		return limit;
	}

	public void setLimit(Integer limit) {
		this.limit = limit;
	}

	public Integer getStart() {
		return start;
	}

	public void setStart(Integer start) {
		this.start = start;
	}

	public WxGameAppManageDto(String category, String sub_category, String update_user,String appname,String wxappid) {
		super();
		this.category = category;
		this.sub_category = sub_category;
		this.update_user = update_user;
		this.appname = appname;
		this.wxappid = wxappid;
	}

	public WxGameAppManageDto() {
		super();
	}

	public String getAppname() {
		return appname;
	}

	public void setAppname(String appname) {
		this.appname = appname;
	}

	public String getWxappid() {
		return wxappid;
	}

	public void setWxappid(String wxappid) {
		this.wxappid = wxappid;
	}
}