package com.wbgame.pojo.operate;

import io.swagger.annotations.ApiModelProperty;

public class ChannelTotalReportVo {

    @ApiModelProperty(value = "日期",dataType = "String")
    private String tdate;

    @ApiModelProperty(value = "应用名称",dataType = "String")
    private String app_name;

    @ApiModelProperty(value = "应用id",dataType = "String")
    private String appid;

    @ApiModelProperty(value = "应用分类",dataType = "String")
    private String app_category;

    @ApiModelProperty(value = "媒体",dataType = "String")
    private String cha_media;

    @ApiModelProperty(value = "子渠道",dataType = "String")
    private String channel;

    @ApiModelProperty(value = "消耗",dataType = "String")
    private String spend;

    @ApiModelProperty(value = "友盟新增",dataType = "String")
    private String add_num;

    @ApiModelProperty(value = "友盟活跃",dataType = "String")
    private String act_num;

    @ApiModelProperty(value = "变现收入分成后",dataType = "String")
    private String cash_revenue_after;

    @ApiModelProperty(value = "付费买量收入分成前",dataType = "String")
    private String pay_buy_revenue_share_fre;

    @ApiModelProperty(value = "付费收入分成前",dataType = "String")
    private String pay_revenue_share_fre;

    @ApiModelProperty(value = "付费收入分成后",dataType = "String")
    private String pay_revenue_share_after;

    @ApiModelProperty(value = "买量新增",dataType = "String")
    private String installs;

    @ApiModelProperty(value = "注册量",dataType = "String")
    private String register;

    @ApiModelProperty(value = "总新增付费数",dataType = "String")
    private String all_add_pay_num;

    @ApiModelProperty(value = "新增付费数",dataType = "String")
    private String add_pay_num;

    @ApiModelProperty(value = "24h新增金额",dataType = "String")
    private String pay_money_24;

    @ApiModelProperty(value = "24h新增付费人数",dataType = "String")
    private String pay_num_24;

    @ApiModelProperty(value = "首日付费收入",dataType = "String")
    private String pay_revenue1;

    @ApiModelProperty(value = "7日付费收入",dataType = "String")
    private String pay_revenue7;

    @ApiModelProperty(value = "首日变现收入",dataType = "String")
    private String cash_revenue1;

    @ApiModelProperty(value = "7日变现收入",dataType = "String")
    private String cash_revenue7;

    @ApiModelProperty(value = "整体成本",dataType = "String")
    private String all_cost;

    @ApiModelProperty(value = "整体arpu",dataType = "String")
    private String all_arpu;

    @ApiModelProperty(value = "广告arpu",dataType = "String")
    private String ad_arpu;

    @ApiModelProperty(value = "付费arpu",dataType = "String")
    private String pay_arpu;

    @ApiModelProperty(value = "整体roi分成后",dataType = "String")
    private String all_roi_share_after;

    @ApiModelProperty(value = "新增占比",dataType = "String")
    private String add_rate;

    @ApiModelProperty(value = "自然量付费收入分成前",dataType = "String")
    private String natural_pay_revenue_share_fre;

    @ApiModelProperty(value = "新增成本",dataType = "String")
    private String add_cost;

    @ApiModelProperty(value = "注册成本",dataType = "String")
    private String register_cost;

    @ApiModelProperty(value = "总新增付费率",dataType = "String")
    private String all_add_pay_rate;

    @ApiModelProperty(value = "总新增付费成本",dataType = "String")
    private String all_add_pay_cost;

    @ApiModelProperty(value = "新增付费ARPU",dataType = "String")
    private String add_pay_arpu;

    @ApiModelProperty(value = "24h新增ROI",dataType = "String")
    private String add_roi_24;

    @ApiModelProperty(value = "24h新增付费均价",dataType = "String")
    private String add_pay_avg_24;

    @ApiModelProperty(value = "1日付费LTV",dataType = "String")
    private String pay_ltv_1;

    @ApiModelProperty(value = "7日付费LTV",dataType = "String")
    private String pay_ltv_7;

    @ApiModelProperty(value = "1日内购ROI",dataType = "String")
    private String pay_roi_1;

    @ApiModelProperty(value = "7日内购ROI",dataType = "String")
    private String pay_roi_7;

    @ApiModelProperty(value = "1日变现ROI",dataType = "String")
    private String cash_roi_1;

    @ApiModelProperty(value = "7日变现ROI",dataType = "String")
    private String cash_roi_7;

    @ApiModelProperty(value = "次留",dataType = "String")
    private String keep1;

    @ApiModelProperty(value = "3留",dataType = "String")
    private String keep3;

    @ApiModelProperty(value = "7留",dataType = "String")
    private String keep7;

    @ApiModelProperty(value = "14留",dataType = "String")
    private String keep14;

    @ApiModelProperty(value = "新增付费率",dataType = "String")
    private String add_pay_rate;

    @ApiModelProperty(value = "新增付费成本",dataType = "String")
    private String add_pay_cost;

    @ApiModelProperty(value = "总付费用户数",dataType = "String")
    private String pay_user_num;

    @ApiModelProperty(value = "总广告展示数",dataType = "String")
    private String total_advert_show;

    @ApiModelProperty(value = "总视频展示数",dataType = "String")
    private String video_show;

    @ApiModelProperty(value = "总视频收入",dataType = "String")
    private String video_income;

    @ApiModelProperty(value = "7日ltv增长率",dataType = "String")
    private String add_ltv_7_rate;

    @ApiModelProperty(value = "人均广告PV",dataType = "String")
    private String avg_advert_pv;

    @ApiModelProperty(value = "视频人均ecpm",dataType = "String")
    private String video_avg_ecpm;

    @ApiModelProperty(value = "付费arppu",dataType = "String")
    private String pay_arppu;

    @ApiModelProperty(value = "活跃付费率",dataType = "String")
    private String act_pay_rate;

    @ApiModelProperty(value = "总收入（分成后）",dataType = "String")
    private String share_after_total_income;

    // 新增字段 -- 共担券
    @ApiModelProperty(value = "共担券",dataType = "String")
    private String kobe_coupon;

    //新增字段 -- 汇总的共担券
    @ApiModelProperty(value = "汇总共担券",dataType = "String")
    private String total_kobe_coupon;

    @ApiModelProperty(value = "毛利",dataType = "String")
    private String profit;

    @ApiModelProperty(value = "毛利率",dataType = "String")
    private String profit_rate;

    @ApiModelProperty(value = "首次付费数",dataType = "String")
    private String all_add_pay_num_bigdata;

    @ApiModelProperty(value = "首次付费率",dataType = "String")
    private String all_add_pay_rate_bigdata;

    @ApiModelProperty(value = "自然量新增",dataType = "String")
    private String natural_add;

    @ApiModelProperty(value = "自然量新增成本",dataType = "String")
    private String natural_add_cost;

    @ApiModelProperty(value = "自然量新增付费数",dataType = "String")
    private String natural_add_pay;

    @ApiModelProperty(value = "自然量新增付费成本",dataType = "String")
    private String natural_add_pay_cost;

    @ApiModelProperty(value = "自然量付费数",dataType = "String")
    private String natural_pay_num;

    @ApiModelProperty(value = "自然量付费成本",dataType = "String")
    private String natural_pay_cost;

    @ApiModelProperty(value = "商店-消耗",dataType = "String")
    private String shop_spend;

    @ApiModelProperty(value = "商店-消耗占比",dataType = "String")
    private String shop_spend_rate;

    @ApiModelProperty(value = "商店-下载成本",dataType = "String")
    private String shop_download_cost;

    @ApiModelProperty(value = "商店-付费ROI",dataType = "String")
    private String shop_pay_roi;

    @ApiModelProperty(value = "商店-展示",dataType = "String")
    private String shop_impressions;

    @ApiModelProperty(value = "商店-点击",dataType = "String")
    private String shop_clicks;

    @ApiModelProperty(value = "商店-点击率",dataType = "String")
    private String shop_clicks_rate;

    @ApiModelProperty(value = "商店-下载量",dataType = "String")
    private String shop_download;

    @ApiModelProperty(value = "商店-下载率",dataType = "String")
    private String shop_download_rate;

    @ApiModelProperty(value = "商店-买量新增",dataType = "String")
    private String shop_installs;

    @ApiModelProperty(value = "商店-买量成本",dataType = "String")
    private String shop_installs_cost;

    @ApiModelProperty(value = "商店-注册量",dataType = "String")
    private String shop_register;

    @ApiModelProperty(value = "商店-注册成本",dataType = "String")
    private String shop_register_cost;

    @ApiModelProperty(value = "商店-付费量",dataType = "String")
    private String shop_gamePayCount;

    @ApiModelProperty(value = "商店-付费成本",dataType = "String")
    private String shop_gamePayCount_cost;

    @ApiModelProperty(value = "商店-付费金额",dataType = "String")
    private String shop_payRevenue;

    @ApiModelProperty(value = "商店-付费ROI1",dataType = "String")
    private String shop_pay_roi1;

    @ApiModelProperty(value = "商店-付费ROI3",dataType = "String")
    private String shop_pay_roi3;

    @ApiModelProperty(value = "商店-付费ROI7",dataType = "String")
    private String shop_pay_roi7;

    @ApiModelProperty(value = "商店-付费ROI30",dataType = "String")
    private String shop_pay_roi30;

    @ApiModelProperty(value = "商店-首日付费金额",dataType = "String")
    private String shop_payRevenue1;

    @ApiModelProperty(value = "商店-3日付费金额",dataType = "String")
    private String shop_payRevenue3;

    @ApiModelProperty(value = "商店-7日付费金额",dataType = "String")
    private String shop_payRevenue7;

    @ApiModelProperty(value = "商店-30日付费金额",dataType = "String")
    private String shop_payRevenue30;

    @ApiModelProperty(value = "非商店-消耗",dataType = "String")
    private String other_spend;

    @ApiModelProperty(value = "非商店-消耗占比",dataType = "String")
    private String other_spend_rate;

    @ApiModelProperty(value = "非商店-下载成本",dataType = "String")
    private String other_download_cost;

    @ApiModelProperty(value = "非商店-付费ROI",dataType = "String")
    private String other_pay_roi;

    @ApiModelProperty(value = "非商店-展示",dataType = "String")
    private String other_impressions;

    @ApiModelProperty(value = "非商店-点击",dataType = "String")
    private String other_clicks;

    @ApiModelProperty(value = "非商店-点击率",dataType = "String")
    private String other_clicks_rate;

    @ApiModelProperty(value = "非商店-下载量",dataType = "String")
    private String other_download;

    @ApiModelProperty(value = "非商店-下载率",dataType = "String")
    private String other_download_rate;

    @ApiModelProperty(value = "非商店-买量新增",dataType = "String")
    private String other_installs;

    @ApiModelProperty(value = "非商店-买量成本",dataType = "String")
    private String other_installs_cost;

    @ApiModelProperty(value = "非商店-注册量",dataType = "String")
    private String other_register;

    @ApiModelProperty(value = "非商店-注册成本",dataType = "String")
    private String other_register_cost;

    @ApiModelProperty(value = "非商店-付费量",dataType = "String")
    private String other_gamePayCount;

    @ApiModelProperty(value = "非商店-付费成本",dataType = "String")
    private String other_gamePayCount_cost;

    @ApiModelProperty(value = "非商店-付费金额",dataType = "String")
    private String other_payRevenue;

    @ApiModelProperty(value = "非商店-付费ROI1",dataType = "String")
    private String other_pay_roi1;

    @ApiModelProperty(value = "非商店-付费ROI3",dataType = "String")
    private String other_pay_roi3;

    @ApiModelProperty(value = "非商店-付费ROI7",dataType = "String")
    private String other_pay_roi7;

    @ApiModelProperty(value = "非商店-付费ROI30",dataType = "String")
    private String other_pay_roi30;

    @ApiModelProperty(value = "非商店-首日付费金额",dataType = "String")
    private String other_payRevenue1;

    @ApiModelProperty(value = "非商店-3日付费金额",dataType = "String")
    private String other_payRevenue3;

    @ApiModelProperty(value = "非商店-7日付费金额",dataType = "String")
    private String other_payRevenue7;

    @ApiModelProperty(value = "非商店-30日付费金额",dataType = "String")
    private String other_payRevenue30;

    @ApiModelProperty(value = "商店-共担券",dataType = "String")
    private String shop_kobe_coupon;
    @ApiModelProperty(value = "非商店-共担券",dataType = "String")
    private String other_kobe_coupon;


    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }

    public String getApp_category() {
        return app_category;
    }

    public void setApp_category(String app_category) {
        this.app_category = app_category;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getSpend() {
        return spend;
    }

    public void setSpend(String spend) {
        this.spend = spend;
    }

    public String getAdd_num() {
        return add_num;
    }

    public void setAdd_num(String add_num) {
        this.add_num = add_num;
    }

    public String getAct_num() {
        return act_num;
    }

    public void setAct_num(String act_num) {
        this.act_num = act_num;
    }

    public String getCash_revenue_after() {
        return cash_revenue_after;
    }

    public void setCash_revenue_after(String cash_revenue_after) {
        this.cash_revenue_after = cash_revenue_after;
    }

    public String getPay_buy_revenue_share_fre() {
        return pay_buy_revenue_share_fre;
    }

    public void setPay_buy_revenue_share_fre(String pay_buy_revenue_share_fre) {
        this.pay_buy_revenue_share_fre = pay_buy_revenue_share_fre;
    }

    public String getPay_revenue_share_fre() {
        return pay_revenue_share_fre;
    }

    public void setPay_revenue_share_fre(String pay_revenue_share_fre) {
        this.pay_revenue_share_fre = pay_revenue_share_fre;
    }

    public String getPay_revenue_share_after() {
        return pay_revenue_share_after;
    }

    public void setPay_revenue_share_after(String pay_revenue_share_after) {
        this.pay_revenue_share_after = pay_revenue_share_after;
    }

    public String getInstalls() {
        return installs;
    }

    public void setInstalls(String installs) {
        this.installs = installs;
    }

    public String getRegister() {
        return register;
    }

    public void setRegister(String register) {
        this.register = register;
    }

    public String getAll_add_pay_num() {
        return all_add_pay_num;
    }

    public void setAll_add_pay_num(String all_add_pay_num) {
        this.all_add_pay_num = all_add_pay_num;
    }

    public String getAdd_pay_num() {
        return add_pay_num;
    }

    public void setAdd_pay_num(String add_pay_num) {
        this.add_pay_num = add_pay_num;
    }

    public String getPay_money_24() {
        return pay_money_24;
    }

    public void setPay_money_24(String pay_money_24) {
        this.pay_money_24 = pay_money_24;
    }

    public String getPay_num_24() {
        return pay_num_24;
    }

    public void setPay_num_24(String pay_num_24) {
        this.pay_num_24 = pay_num_24;
    }

    public String getPay_revenue1() {
        return pay_revenue1;
    }

    public void setPay_revenue1(String pay_revenue1) {
        this.pay_revenue1 = pay_revenue1;
    }

    public String getPay_revenue7() {
        return pay_revenue7;
    }

    public void setPay_revenue7(String pay_revenue7) {
        this.pay_revenue7 = pay_revenue7;
    }

    public String getCash_revenue1() {
        return cash_revenue1;
    }

    public void setCash_revenue1(String cash_revenue1) {
        this.cash_revenue1 = cash_revenue1;
    }

    public String getCash_revenue7() {
        return cash_revenue7;
    }

    public void setCash_revenue7(String cash_revenue7) {
        this.cash_revenue7 = cash_revenue7;
    }

    public String getAll_cost() {
        return all_cost;
    }

    public void setAll_cost(String all_cost) {
        this.all_cost = all_cost;
    }

    public String getAll_arpu() {
        return all_arpu;
    }

    public void setAll_arpu(String all_arpu) {
        this.all_arpu = all_arpu;
    }

    public String getAd_arpu() {
        return ad_arpu;
    }

    public void setAd_arpu(String ad_arpu) {
        this.ad_arpu = ad_arpu;
    }

    public String getPay_arpu() {
        return pay_arpu;
    }

    public void setPay_arpu(String pay_arpu) {
        this.pay_arpu = pay_arpu;
    }

    public String getAll_roi_share_after() {
        return all_roi_share_after;
    }

    public void setAll_roi_share_after(String all_roi_share_after) {
        this.all_roi_share_after = all_roi_share_after;
    }

    public String getAdd_rate() {
        return add_rate;
    }

    public void setAdd_rate(String add_rate) {
        this.add_rate = add_rate;
    }

    public String getNatural_pay_revenue_share_fre() {
        return natural_pay_revenue_share_fre;
    }

    public void setNatural_pay_revenue_share_fre(String natural_pay_revenue_share_fre) {
        this.natural_pay_revenue_share_fre = natural_pay_revenue_share_fre;
    }

    public String getAdd_cost() {
        return add_cost;
    }

    public void setAdd_cost(String add_cost) {
        this.add_cost = add_cost;
    }

    public String getRegister_cost() {
        return register_cost;
    }

    public void setRegister_cost(String register_cost) {
        this.register_cost = register_cost;
    }

    public String getAll_add_pay_rate() {
        return all_add_pay_rate;
    }

    public void setAll_add_pay_rate(String all_add_pay_rate) {
        this.all_add_pay_rate = all_add_pay_rate;
    }

    public String getAll_add_pay_cost() {
        return all_add_pay_cost;
    }

    public void setAll_add_pay_cost(String all_add_pay_cost) {
        this.all_add_pay_cost = all_add_pay_cost;
    }

    public String getAdd_pay_arpu() {
        return add_pay_arpu;
    }

    public void setAdd_pay_arpu(String add_pay_arpu) {
        this.add_pay_arpu = add_pay_arpu;
    }

    public String getAdd_roi_24() {
        return add_roi_24;
    }

    public void setAdd_roi_24(String add_roi_24) {
        this.add_roi_24 = add_roi_24;
    }

    public String getAdd_pay_avg_24() {
        return add_pay_avg_24;
    }

    public void setAdd_pay_avg_24(String add_pay_avg_24) {
        this.add_pay_avg_24 = add_pay_avg_24;
    }

    public String getPay_ltv_1() {
        return pay_ltv_1;
    }

    public void setPay_ltv_1(String pay_ltv_1) {
        this.pay_ltv_1 = pay_ltv_1;
    }

    public String getPay_ltv_7() {
        return pay_ltv_7;
    }

    public void setPay_ltv_7(String pay_ltv_7) {
        this.pay_ltv_7 = pay_ltv_7;
    }

    public String getPay_roi_1() {
        return pay_roi_1;
    }

    public void setPay_roi_1(String pay_roi_1) {
        this.pay_roi_1 = pay_roi_1;
    }

    public String getPay_roi_7() {
        return pay_roi_7;
    }

    public void setPay_roi_7(String pay_roi_7) {
        this.pay_roi_7 = pay_roi_7;
    }

    public String getCash_roi_1() {
        return cash_roi_1;
    }

    public void setCash_roi_1(String cash_roi_1) {
        this.cash_roi_1 = cash_roi_1;
    }

    public String getCash_roi_7() {
        return cash_roi_7;
    }

    public void setCash_roi_7(String cash_roi_7) {
        this.cash_roi_7 = cash_roi_7;
    }

    public String getKeep1() {
        return keep1;
    }

    public void setKeep1(String keep1) {
        this.keep1 = keep1;
    }

    public String getKeep3() {
        return keep3;
    }

    public void setKeep3(String keep3) {
        this.keep3 = keep3;
    }

    public String getKeep7() {
        return keep7;
    }

    public void setKeep7(String keep7) {
        this.keep7 = keep7;
    }

    public String getKeep14() {
        return keep14;
    }

    public void setKeep14(String keep14) {
        this.keep14 = keep14;
    }

    public String getAdd_pay_rate() {
        return add_pay_rate;
    }

    public void setAdd_pay_rate(String add_pay_rate) {
        this.add_pay_rate = add_pay_rate;
    }

    public String getCha_media() {
        return cha_media;
    }

    public void setCha_media(String cha_media) {
        this.cha_media = cha_media;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAdd_pay_cost() {
        return add_pay_cost;
    }

    public void setAdd_pay_cost(String add_pay_cost) {
        this.add_pay_cost = add_pay_cost;
    }

    public String getPay_user_num() {
        return pay_user_num;
    }

    public void setPay_user_num(String pay_user_num) {
        this.pay_user_num = pay_user_num;
    }

    public String getVideo_show() {
        return video_show;
    }

    public void setVideo_show(String video_show) {
        this.video_show = video_show;
    }

    public String getVideo_income() {
        return video_income;
    }

    public void setVideo_income(String video_income) {
        this.video_income = video_income;
    }

    public String getAdd_ltv_7_rate() {
        return add_ltv_7_rate;
    }

    public void setAdd_ltv_7_rate(String add_ltv_7_rate) {
        this.add_ltv_7_rate = add_ltv_7_rate;
    }

    public String getAvg_advert_pv() {
        return avg_advert_pv;
    }

    public void setAvg_advert_pv(String avg_advert_pv) {
        this.avg_advert_pv = avg_advert_pv;
    }

    public String getVideo_avg_ecpm() {
        return video_avg_ecpm;
    }

    public void setVideo_avg_ecpm(String video_avg_ecpm) {
        this.video_avg_ecpm = video_avg_ecpm;
    }

    public String getPay_arppu() {
        return pay_arppu;
    }

    public void setPay_arppu(String pay_arppu) {
        this.pay_arppu = pay_arppu;
    }

    public String getAct_pay_rate() {
        return act_pay_rate;
    }

    public void setAct_pay_rate(String act_pay_rate) {
        this.act_pay_rate = act_pay_rate;
    }

    public String getTotal_advert_show() {
        return total_advert_show;
    }

    public void setTotal_advert_show(String total_advert_show) {
        this.total_advert_show = total_advert_show;
    }

    public String getShare_after_total_income() {
        return share_after_total_income;
    }

    public void setShare_after_total_income(String share_after_total_income) {
        this.share_after_total_income = share_after_total_income;
    }

    public String getProfit() {
        return profit;
    }

    public void setProfit(String profit) {
        this.profit = profit;
    }

    public String getProfit_rate() {
        return profit_rate;
    }

    public void setProfit_rate(String profit_rate) {
        this.profit_rate = profit_rate;
    }

    public String getAll_add_pay_num_bigdata() {
        return all_add_pay_num_bigdata;
    }

    public void setAll_add_pay_num_bigdata(String all_add_pay_num_bigdata) {
        this.all_add_pay_num_bigdata = all_add_pay_num_bigdata;
    }

    public String getAll_add_pay_rate_bigdata() {
        return all_add_pay_rate_bigdata;
    }

    public void setAll_add_pay_rate_bigdata(String all_add_pay_rate_bigdata) {
        this.all_add_pay_rate_bigdata = all_add_pay_rate_bigdata;
    }

    public String getNatural_add() {
        return natural_add;
    }

    public void setNatural_add(String natural_add) {
        this.natural_add = natural_add;
    }

    public String getNatural_add_cost() {
        return natural_add_cost;
    }

    public void setNatural_add_cost(String natural_add_cost) {
        this.natural_add_cost = natural_add_cost;
    }

    public String getNatural_add_pay() {
        return natural_add_pay;
    }

    public void setNatural_add_pay(String natural_add_pay) {
        this.natural_add_pay = natural_add_pay;
    }

    public String getNatural_add_pay_cost() {
        return natural_add_pay_cost;
    }

    public void setNatural_add_pay_cost(String natural_add_pay_cost) {
        this.natural_add_pay_cost = natural_add_pay_cost;
    }

    public String getNatural_pay_num() {
        return natural_pay_num;
    }

    public void setNatural_pay_num(String natural_pay_num) {
        this.natural_pay_num = natural_pay_num;
    }

    public String getNatural_pay_cost() {
        return natural_pay_cost;
    }

    public void setNatural_pay_cost(String natural_pay_cost) {
        this.natural_pay_cost = natural_pay_cost;
    }

    public String getShop_spend() {
        return shop_spend;
    }

    public void setShop_spend(String shop_spend) {
        this.shop_spend = shop_spend;
    }

    public String getShop_impressions() {
        return shop_impressions;
    }

    public void setShop_impressions(String shop_impressions) {
        this.shop_impressions = shop_impressions;
    }

    public String getShop_clicks() {
        return shop_clicks;
    }

    public void setShop_clicks(String shop_clicks) {
        this.shop_clicks = shop_clicks;
    }

    public String getShop_clicks_rate() {
        return shop_clicks_rate;
    }

    public void setShop_clicks_rate(String shop_clicks_rate) {
        this.shop_clicks_rate = shop_clicks_rate;
    }

    public String getShop_download() {
        return shop_download;
    }

    public void setShop_download(String shop_download) {
        this.shop_download = shop_download;
    }

    public String getShop_download_rate() {
        return shop_download_rate;
    }

    public void setShop_download_rate(String shop_download_rate) {
        this.shop_download_rate = shop_download_rate;
    }

    public String getShop_installs() {
        return shop_installs;
    }

    public void setShop_installs(String shop_installs) {
        this.shop_installs = shop_installs;
    }

    public String getShop_installs_cost() {
        return shop_installs_cost;
    }

    public void setShop_installs_cost(String shop_installs_cost) {
        this.shop_installs_cost = shop_installs_cost;
    }

    public String getShop_register() {
        return shop_register;
    }

    public void setShop_register(String shop_register) {
        this.shop_register = shop_register;
    }

    public String getShop_register_cost() {
        return shop_register_cost;
    }

    public void setShop_register_cost(String shop_register_cost) {
        this.shop_register_cost = shop_register_cost;
    }

    public String getShop_gamePayCount() {
        return shop_gamePayCount;
    }

    public void setShop_gamePayCount(String shop_gamePayCount) {
        this.shop_gamePayCount = shop_gamePayCount;
    }

    public String getShop_gamePayCount_cost() {
        return shop_gamePayCount_cost;
    }

    public void setShop_gamePayCount_cost(String shop_gamePayCount_cost) {
        this.shop_gamePayCount_cost = shop_gamePayCount_cost;
    }

    public String getShop_payRevenue() {
        return shop_payRevenue;
    }

    public void setShop_payRevenue(String shop_payRevenue) {
        this.shop_payRevenue = shop_payRevenue;
    }

    public String getShop_pay_roi1() {
        return shop_pay_roi1;
    }

    public void setShop_pay_roi1(String shop_pay_roi1) {
        this.shop_pay_roi1 = shop_pay_roi1;
    }

    public String getShop_pay_roi3() {
        return shop_pay_roi3;
    }

    public void setShop_pay_roi3(String shop_pay_roi3) {
        this.shop_pay_roi3 = shop_pay_roi3;
    }

    public String getShop_pay_roi7() {
        return shop_pay_roi7;
    }

    public void setShop_pay_roi7(String shop_pay_roi7) {
        this.shop_pay_roi7 = shop_pay_roi7;
    }

    public String getShop_pay_roi30() {
        return shop_pay_roi30;
    }

    public void setShop_pay_roi30(String shop_pay_roi30) {
        this.shop_pay_roi30 = shop_pay_roi30;
    }

    public String getShop_payRevenue1() {
        return shop_payRevenue1;
    }

    public void setShop_payRevenue1(String shop_payRevenue1) {
        this.shop_payRevenue1 = shop_payRevenue1;
    }

    public String getShop_payRevenue3() {
        return shop_payRevenue3;
    }

    public void setShop_payRevenue3(String shop_payRevenue3) {
        this.shop_payRevenue3 = shop_payRevenue3;
    }

    public String getShop_payRevenue7() {
        return shop_payRevenue7;
    }

    public void setShop_payRevenue7(String shop_payRevenue7) {
        this.shop_payRevenue7 = shop_payRevenue7;
    }

    public String getShop_payRevenue30() {
        return shop_payRevenue30;
    }

    public void setShop_payRevenue30(String shop_payRevenue30) {
        this.shop_payRevenue30 = shop_payRevenue30;
    }

    public String getOther_spend() {
        return other_spend;
    }

    public void setOther_spend(String other_spend) {
        this.other_spend = other_spend;
    }

    public String getOther_impressions() {
        return other_impressions;
    }

    public void setOther_impressions(String other_impressions) {
        this.other_impressions = other_impressions;
    }

    public String getOther_clicks() {
        return other_clicks;
    }

    public void setOther_clicks(String other_clicks) {
        this.other_clicks = other_clicks;
    }

    public String getOther_clicks_rate() {
        return other_clicks_rate;
    }

    public void setOther_clicks_rate(String other_clicks_rate) {
        this.other_clicks_rate = other_clicks_rate;
    }

    public String getOther_download() {
        return other_download;
    }

    public void setOther_download(String other_download) {
        this.other_download = other_download;
    }

    public String getOther_download_rate() {
        return other_download_rate;
    }

    public void setOther_download_rate(String other_download_rate) {
        this.other_download_rate = other_download_rate;
    }

    public String getOther_installs() {
        return other_installs;
    }

    public void setOther_installs(String other_installs) {
        this.other_installs = other_installs;
    }

    public String getOther_installs_cost() {
        return other_installs_cost;
    }

    public void setOther_installs_cost(String other_installs_cost) {
        this.other_installs_cost = other_installs_cost;
    }

    public String getOther_register() {
        return other_register;
    }

    public void setOther_register(String other_register) {
        this.other_register = other_register;
    }

    public String getOther_register_cost() {
        return other_register_cost;
    }

    public void setOther_register_cost(String other_register_cost) {
        this.other_register_cost = other_register_cost;
    }

    public String getOther_gamePayCount() {
        return other_gamePayCount;
    }

    public void setOther_gamePayCount(String other_gamePayCount) {
        this.other_gamePayCount = other_gamePayCount;
    }

    public String getOther_gamePayCount_cost() {
        return other_gamePayCount_cost;
    }

    public void setOther_gamePayCount_cost(String other_gamePayCount_cost) {
        this.other_gamePayCount_cost = other_gamePayCount_cost;
    }

    public String getOther_payRevenue() {
        return other_payRevenue;
    }

    public void setOther_payRevenue(String other_payRevenue) {
        this.other_payRevenue = other_payRevenue;
    }

    public String getOther_pay_roi1() {
        return other_pay_roi1;
    }

    public void setOther_pay_roi1(String other_pay_roi1) {
        this.other_pay_roi1 = other_pay_roi1;
    }

    public String getOther_pay_roi3() {
        return other_pay_roi3;
    }

    public void setOther_pay_roi3(String other_pay_roi3) {
        this.other_pay_roi3 = other_pay_roi3;
    }

    public String getOther_pay_roi7() {
        return other_pay_roi7;
    }

    public void setOther_pay_roi7(String other_pay_roi7) {
        this.other_pay_roi7 = other_pay_roi7;
    }

    public String getOther_pay_roi30() {
        return other_pay_roi30;
    }

    public void setOther_pay_roi30(String other_pay_roi30) {
        this.other_pay_roi30 = other_pay_roi30;
    }

    public String getOther_payRevenue1() {
        return other_payRevenue1;
    }

    public void setOther_payRevenue1(String other_payRevenue1) {
        this.other_payRevenue1 = other_payRevenue1;
    }

    public String getOther_payRevenue3() {
        return other_payRevenue3;
    }

    public void setOther_payRevenue3(String other_payRevenue3) {
        this.other_payRevenue3 = other_payRevenue3;
    }

    public String getOther_payRevenue7() {
        return other_payRevenue7;
    }

    public void setOther_payRevenue7(String other_payRevenue7) {
        this.other_payRevenue7 = other_payRevenue7;
    }

    public String getOther_payRevenue30() {
        return other_payRevenue30;
    }

    public void setOther_payRevenue30(String other_payRevenue30) {
        this.other_payRevenue30 = other_payRevenue30;
    }

    public String getShop_spend_rate() {
        return shop_spend_rate;
    }

    public void setShop_spend_rate(String shop_spend_rate) {
        this.shop_spend_rate = shop_spend_rate;
    }

    public String getShop_download_cost() {
        return shop_download_cost;
    }

    public void setShop_download_cost(String shop_download_cost) {
        this.shop_download_cost = shop_download_cost;
    }

    public String getShop_pay_roi() {
        return shop_pay_roi;
    }

    public void setShop_pay_roi(String shop_pay_roi) {
        this.shop_pay_roi = shop_pay_roi;
    }

    public String getOther_spend_rate() {
        return other_spend_rate;
    }

    public void setOther_spend_rate(String other_spend_rate) {
        this.other_spend_rate = other_spend_rate;
    }

    public String getOther_download_cost() {
        return other_download_cost;
    }

    public void setOther_download_cost(String other_download_cost) {
        this.other_download_cost = other_download_cost;
    }

    public String getOther_pay_roi() {
        return other_pay_roi;
    }

    public void setOther_pay_roi(String other_pay_roi) {
        this.other_pay_roi = other_pay_roi;
    }


    public String getKobe_coupon() {
        return kobe_coupon;
    }

    public void setKobe_coupon(String kobe_coupon) {
        this.kobe_coupon = kobe_coupon;
    }

    public String getTotal_kobe_coupon() {
        return total_kobe_coupon;
    }

    public void setTotal_kobe_coupon(String total_kobe_coupon) {
        this.total_kobe_coupon = total_kobe_coupon;
    }

    public String getShop_kobe_coupon() {
        return shop_kobe_coupon;
    }

    public void setShop_kobe_coupon(String shop_kobe_coupon) {
        this.shop_kobe_coupon = shop_kobe_coupon;
    }

    public String getOther_kobe_coupon() {
        return other_kobe_coupon;
    }

    public void setOther_kobe_coupon(String other_kobe_coupon) {
        this.other_kobe_coupon = other_kobe_coupon;
    }
}
