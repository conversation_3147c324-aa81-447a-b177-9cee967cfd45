package com.wbgame.pojo.operate;

import java.math.BigDecimal;
import java.util.Date;

public class WxAmountWithdrawMonitor {

    private String mchid;

    private BigDecimal amount;

    private String scanTime;

    private Date createTime;

    private Date updateTime;

    private String createOwner;

    private String updateOwner;

    private Integer status;

    private BigDecimal alarmAmount;

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid == null ? null : mchid.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getScanTime() {
        return scanTime;
    }

    public void setScanTime(String scanTime) {
        this.scanTime = scanTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateOwner() {
        return createOwner;
    }

    public void setCreateOwner(String createOwner) {
        this.createOwner = createOwner == null ? null : createOwner.trim();
    }

    public String getUpdateOwner() {
        return updateOwner;
    }

    public void setUpdateOwner(String updateOwner) {
        this.updateOwner = updateOwner == null ? null : updateOwner.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getAlarmAmount() {
        return alarmAmount;
    }

    public void setAlarmAmount(BigDecimal alarmAmount) {
        this.alarmAmount = alarmAmount;
    }
}