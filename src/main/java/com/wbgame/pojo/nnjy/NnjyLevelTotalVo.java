package com.wbgame.pojo.nnjy;

public class NnjyLevelTotalVo {
	
	private String tdate;
	private int appid;
	private int pid;
	private int lv; // 关卡id
	private int joinNum; // 进关人数
	private int joinCount; // 进关次数
	private int successNum; // 成功人数
	private int successCount; // 成功次数
	private String successAvg; // 成功平均时长
	private int failCount; // 失败次数
	private String failAvg; // 失败平均时长
	private String overAvg; // 通关所需次数
	private int outCount; // 退出次数
	private int retryCount; // 重试次数
	private int stop1Num; // 关卡内停留人数，进关人数-通关人数  (关卡内停留率%=关卡内停留人数*100/进关人数)
	private int stop2Num; // 关卡外停留人数，上一关通关人数-进关人数
	private int toolCount; // 消耗道具数
	private int feeNum; // 付费用户数
	private String feeAmount; // 付费金额
	private String goldAvg; // 玩家当前金币数量 平均
	private String reviveTimes; //复活次数,type为2、成功时必传
	private String passDays; //通过天数,type为2、成功时必传
	
	
	public String getReviveTimes() {
		return reviveTimes;
	}
	public void setReviveTimes(String reviveTimes) {
		this.reviveTimes = reviveTimes;
	}
	public String getPassDays() {
		return passDays;
	}
	public void setPassDays(String passDays) {
		this.passDays = passDays;
	}
	private String region; // 地区ID
	
	public String getTdate() {
		return tdate;
	}
	public void setTdate(String tdate) {
		this.tdate = tdate;
	}
	public int getAppid() {
		return appid;
	}
	public void setAppid(int appid) {
		this.appid = appid;
	}
	public int getPid() {
		return pid;
	}
	public void setPid(int pid) {
		this.pid = pid;
	}
	public int getLv() {
		return lv;
	}
	public void setLv(int lv) {
		this.lv = lv;
	}
	public int getJoinNum() {
		return joinNum;
	}
	public void setJoinNum(int joinNum) {
		this.joinNum = joinNum;
	}
	public int getJoinCount() {
		return joinCount;
	}
	public void setJoinCount(int joinCount) {
		this.joinCount = joinCount;
	}
	public int getSuccessNum() {
		return successNum;
	}
	public void setSuccessNum(int successNum) {
		this.successNum = successNum;
	}
	public String getSuccessAvg() {
		return successAvg;
	}
	public void setSuccessAvg(String successAvg) {
		this.successAvg = successAvg;
	}
	public int getFailCount() {
		return failCount;
	}
	public void setFailCount(int failCount) {
		this.failCount = failCount;
	}
	public String getFailAvg() {
		return failAvg;
	}
	public void setFailAvg(String failAvg) {
		this.failAvg = failAvg;
	}
	public String getOverAvg() {
		return overAvg;
	}
	public void setOverAvg(String overAvg) {
		this.overAvg = overAvg;
	}
	public int getOutCount() {
		return outCount;
	}
	public void setOutCount(int outCount) {
		this.outCount = outCount;
	}
	public int getRetryCount() {
		return retryCount;
	}
	public void setRetryCount(int retryCount) {
		this.retryCount = retryCount;
	}
	public int getStop1Num() {
		return stop1Num;
	}
	public void setStop1Num(int stop1Num) {
		this.stop1Num = stop1Num;
	}
	public int getStop2Num() {
		return stop2Num;
	}
	public void setStop2Num(int stop2Num) {
		this.stop2Num = stop2Num;
	}
	public int getToolCount() {
		return toolCount;
	}
	public void setToolCount(int toolCount) {
		this.toolCount = toolCount;
	}
	public int getFeeNum() {
		return feeNum;
	}
	public void setFeeNum(int feeNum) {
		this.feeNum = feeNum;
	}
	public String getFeeAmount() {
		return feeAmount;
	}
	public void setFeeAmount(String feeAmount) {
		this.feeAmount = feeAmount;
	}
	public int getSuccessCount() {
		return successCount;
	}
	public void setSuccessCount(int successCount) {
		this.successCount = successCount;
	}
	public String getGoldAvg() {
		return goldAvg;
	}
	public void setGoldAvg(String goldAvg) {
		this.goldAvg = goldAvg;
	}
	public String getRegion() {
		return region;
	}
	public void setRegion(String region) {
		this.region = region;
	}

}
