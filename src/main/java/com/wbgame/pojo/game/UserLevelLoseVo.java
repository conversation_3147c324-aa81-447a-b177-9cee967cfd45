package com.wbgame.pojo.game;

/**
 * <AUTHOR>
 * @Classname UserLoseVo
 * @Description TODO
 * @Date 2022/5/5 10:58
 */
public class UserLevelLoseVo {

    private String data_date;
    private String appid;
    private String pid;
    private String download_channel;
    private String level_id;
    private String lose_count;                  //流失人数（总）
    private String user_count_new;              //当日停留在该关卡人数（新）
    private String user_count_old;              //当日停留在该关卡人数（老）
    private String loss_user_count_new;         //流失人数（新）
    private String loss_user_count_old;         //流失人数（老）
    private String lose_level_ratio_new;        //关卡流失率（新）
    private String lose_level_ratio_old;        //关卡流失率（老）

    public String getLose_level_ratio_new() {
        return lose_level_ratio_new;
    }

    public void setLose_level_ratio_new(String lose_level_ratio_new) {
        this.lose_level_ratio_new = lose_level_ratio_new;
    }

    public String getLose_level_ratio_old() {
        return lose_level_ratio_old;
    }

    public void setLose_level_ratio_old(String lose_level_ratio_old) {
        this.lose_level_ratio_old = lose_level_ratio_old;
    }

    public String getData_date() {
        return data_date;
    }

    public void setData_date(String data_date) {
        this.data_date = data_date;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getDownload_channel() {
        return download_channel;
    }

    public void setDownload_channel(String download_channel) {
        this.download_channel = download_channel;
    }

    public String getLevel_id() {
        return level_id;
    }

    public void setLevel_id(String level_id) {
        this.level_id = level_id;
    }

    public String getLose_count() {
        return lose_count;
    }

    public void setLose_count(String lose_count) {
        this.lose_count = lose_count;
    }

    public String getUser_count_new() {
        return user_count_new;
    }

    public void setUser_count_new(String user_count_new) {
        this.user_count_new = user_count_new;
    }

    public String getUser_count_old() {
        return user_count_old;
    }

    public void setUser_count_old(String user_count_old) {
        this.user_count_old = user_count_old;
    }

    public String getLoss_user_count_new() {
        return loss_user_count_new;
    }

    public void setLoss_user_count_new(String loss_user_count_new) {
        this.loss_user_count_new = loss_user_count_new;
    }

    public String getLoss_user_count_old() {
        return loss_user_count_old;
    }

    public void setLoss_user_count_old(String loss_user_count_old) {
        this.loss_user_count_old = loss_user_count_old;
    }
}
