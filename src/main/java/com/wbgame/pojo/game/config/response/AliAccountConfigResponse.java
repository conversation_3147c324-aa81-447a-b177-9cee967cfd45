package com.wbgame.pojo.game.config.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname AliAccountConfigResponse
 * @Description TODO
 * @Date 2025/3/13 20:24
 */

@Data
public class AliAccountConfigResponse {

    @ApiModelProperty(value = "唯一资源id")
    private Long id;

    @ApiModelProperty(value = "支付宝商户号")
    private String account;

    @ApiModelProperty(value = "商户号主体")
    private String account_name;

    @ApiModelProperty(value = "每日限额阈值(分)")
    private Long quota;

    @ApiModelProperty(value = "支付宝主体编号")
    private String sub_code;

    @ApiModelProperty(value = "创建时间")
    private String create_time;

    @ApiModelProperty(value = "创建人")
    private String create_user;

    @ApiModelProperty(value = "更新时间")
    private String update_time;

    @ApiModelProperty(value = "更新人")
    private String update_user;

    @ApiModelProperty(value = "收款公司主体")
    private String company;

    @ApiModelProperty(value = "商户号类型")
    private String type;

}
