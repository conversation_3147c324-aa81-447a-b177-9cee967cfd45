package com.wbgame.pojo.game.report.query;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname PayDailyQueryVo
 * @Description TODO
 * @Date 2022/10/26 18:21
 */
public class PayDailyQueryVo {

    @ApiModelProperty(value = "维度",example = "tdate,appid,channel")
    private List<String> group;

    @ApiModelProperty(value = "维度",example = "daily \\ month")
    private String date_group;

    @ApiModelProperty(value = "开始时间", dataType = "String",required=true)
    private String start_date;

    @ApiModelProperty(value = "结束时间", dataType = "String",required=true)
    private String end_date;

    @ApiModelProperty(value = "应用id", dataType = "String",required=true)
    private List<String> appid;

    @ApiModelProperty("子渠道")
    private List<String> channel;

    @ApiModelProperty("页码")
    private Integer start;

    @ApiModelProperty("条数")
    private Integer limit;

    @ApiModelProperty(value = "排序",example = "可排序字段")
    private String order_str;

    @ApiModelProperty("自定义列参数：value,key;value2,key2")
    private String value;

    @ApiModelProperty("文件名")
    private String export_file_name;

    public List<String> getChannel() {
        return channel;
    }

    public void setChannel(List<String> channel) {
        this.channel = channel;
    }

    public List<String> getGroup() {
        return group;
    }

    public void setGroup(List<String> group) {
        this.group = group;
    }

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public List<String> getAppid() {
        return appid;
    }

    public void setAppid(List<String> appid) {
        this.appid = appid;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getOrder_str() {
        return order_str;
    }

    public void setOrder_str(String order_str) {
        this.order_str = order_str;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getExport_file_name() {
        return export_file_name;
    }

    public void setExport_file_name(String export_file_name) {
        this.export_file_name = export_file_name;
    }

    public String getDate_group() {
        return date_group;
    }

    public void setDate_group(String date_group) {
        this.date_group = date_group;
    }
}
