package com.wbgame.pojo.game.report.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname PayTotalQueryVo
 * @Description TODO
 * @Date 2023/7/20 9:44
 */
@ApiModel(value = "支付汇总查询类")
public class PayTotalQueryVo {

    @ApiModelProperty(value = "维度",example = "tdate,appid,pid,app_category_name,payid")
    private List<String> group_idList;

    @ApiModelProperty(value = "开始时间", dataType = "String",required=true)
    private String start_date;

    @ApiModelProperty(value = "结束时间", dataType = "String",required=true)
    private String end_date;

    @ApiModelProperty(value = "项目id", dataType = "String")
    private String pid;

    @ApiModelProperty(value = "区服id", dataType = "String")
    private String zone_id;

    @ApiModelProperty(value = "应用id",example = "38823")
    private List<String> appidList;

    @ApiModelProperty(value = "支付类型",example = "11,118")
    private List<String> payidList;

    @ApiModelProperty(value = "排序",example = "tdate,appid,pnum,price,pay_arpu")
    private String order_str;

    @ApiModelProperty("自定义列参数：value,key;value2,key2")
    private String value;

    @ApiModelProperty("文件名")
    private String export_file_name;

    @ApiModelProperty(value = "当前页",dataType = "Integer")
    private Integer start;

    @ApiModelProperty(value = "页最大数",dataType = "Integer")
    private Integer limit;

    private String group;

    public String getZone_id() {
        return zone_id;
    }

    public void setZone_id(String zone_id) {
        this.zone_id = zone_id;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getOrder_str() {
        return order_str;
    }

    public void setOrder_str(String order_str) {
        this.order_str = order_str;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getExport_file_name() {
        return export_file_name;
    }

    public void setExport_file_name(String export_file_name) {
        this.export_file_name = export_file_name;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public List<String> getGroup_idList() {
        return group_idList;
    }

    public void setGroup_idList(List<String> group_idList) {
        this.group_idList = group_idList;
    }

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public List<String> getAppidList() {
        return appidList;
    }

    public void setAppidList(List<String> appidList) {
        this.appidList = appidList;
    }

    public List<String> getPayidList() {
        return payidList;
    }

    public void setPayidList(List<String> payidList) {
        this.payidList = payidList;
    }
}
