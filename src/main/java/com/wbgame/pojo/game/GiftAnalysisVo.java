package com.wbgame.pojo.game;

/**
 * <AUTHOR>
 * @Classname GiftAnalysisVo
 * @Description ads_gift_analyse_daily
 * @Date 2022/3/9 14:31
 */
public class GiftAnalysisVo {

    private String appid;               //产品id。
    private String pid;                 //项目id
    private String appname;             //产品
    private String download_channel;    //渠道
    private String gift_item_id;        //礼包id
    private String sale_sum;            //总销售额(元)
    private String sale_count;          //销量（总）
    private String sale_count_new;      //销量（新）
    private String sale_count_old;      //销量（老）
    private String buy_count;           //购买人数（总）
    private String buy_count_new;       //购买人数（新）
    private String buy_count_old;       //购买人数（老）
    private String first_buy_count;     //首次购买人数（总）
    private String first_buy_count_new; //首次购买人数（新）
    private String first_buy_count_old; //首次购买人数（老）
    private String new_user_count;      //新增用户
    private String active_user_count;   //活跃用户
    private String buy_date;            //日期
    private String currency_type;       //货币类型
    private String gift_item_type;     //礼包类型
    private String price;              //单价
    private String gift_item_content;  //礼包内容
    private String gift_item_name;     //礼包名称
    private String gift_buy_type;      //购买方式

    private String sale_sum_old;       //销售额（老）
    private String sale_sum_new;
    
    private String gift_penetration_rate;//礼包渗透率

    private String tag_r;
    private String group_id;
    
    //销售额（新）

    public String getSale_sum_old() {
        return sale_sum_old;
    }

    public String getGift_penetration_rate() {
		return gift_penetration_rate;
	}

	public void setGift_penetration_rate(String gift_penetration_rate) {
		this.gift_penetration_rate = gift_penetration_rate;
	}

	public void setSale_sum_old(String sale_sum_old) {
        this.sale_sum_old = sale_sum_old;
    }

    public String getSale_sum_new() {
        return sale_sum_new;
    }

    public void setSale_sum_new(String sale_sum_new) {
        this.sale_sum_new = sale_sum_new;
    }

    public String getGift_buy_type() {
        return gift_buy_type;
    }

    public void setGift_buy_type(String gift_buy_type) {
        this.gift_buy_type = gift_buy_type;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getGift_item_type() {
        return gift_item_type;
    }

    public void setGift_item_type(String gift_item_type) {
        this.gift_item_type = gift_item_type;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getGift_item_content() {
        return gift_item_content;
    }

    public void setGift_item_content(String gift_item_content) {
        this.gift_item_content = gift_item_content;
    }

    public String getGift_item_name() {
        return gift_item_name;
    }

    public void setGift_item_name(String gift_item_name) {
        this.gift_item_name = gift_item_name;
    }

    public String getCurrency_type() {
        return currency_type;
    }

    public void setCurrency_type(String currency_type) {
        this.currency_type = currency_type;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public String getDownload_channel() {
        return download_channel;
    }

    public void setDownload_channel(String download_channel) {
        this.download_channel = download_channel;
    }

    public String getGift_item_id() {
        return gift_item_id;
    }

    public void setGift_item_id(String gift_item_id) {
        this.gift_item_id = gift_item_id;
    }

    public String getSale_sum() {
        return sale_sum;
    }

    public void setSale_sum(String sale_sum) {
        this.sale_sum = sale_sum;
    }

    public String getSale_count() {
        return sale_count;
    }

    public void setSale_count(String sale_count) {
        this.sale_count = sale_count;
    }

    public String getSale_count_new() {
        return sale_count_new;
    }

    public void setSale_count_new(String sale_count_new) {
        this.sale_count_new = sale_count_new;
    }

    public String getSale_count_old() {
        return sale_count_old;
    }

    public void setSale_count_old(String sale_count_old) {
        this.sale_count_old = sale_count_old;
    }

    public String getBuy_count() {
        return buy_count;
    }

    public void setBuy_count(String buy_count) {
        this.buy_count = buy_count;
    }

    public String getBuy_count_new() {
        return buy_count_new;
    }

    public void setBuy_count_new(String buy_count_new) {
        this.buy_count_new = buy_count_new;
    }

    public String getBuy_count_old() {
        return buy_count_old;
    }

    public void setBuy_count_old(String buy_count_old) {
        this.buy_count_old = buy_count_old;
    }

    public String getFirst_buy_count() {
        return first_buy_count;
    }

    public void setFirst_buy_count(String first_buy_count) {
        this.first_buy_count = first_buy_count;
    }

    public String getFirst_buy_count_new() {
        return first_buy_count_new;
    }

    public void setFirst_buy_count_new(String first_buy_count_new) {
        this.first_buy_count_new = first_buy_count_new;
    }

    public String getFirst_buy_count_old() {
        return first_buy_count_old;
    }

    public void setFirst_buy_count_old(String first_buy_count_old) {
        this.first_buy_count_old = first_buy_count_old;
    }

    public String getNew_user_count() {
        return new_user_count;
    }

    public void setNew_user_count(String new_user_count) {
        this.new_user_count = new_user_count;
    }

    public String getActive_user_count() {
        return active_user_count;
    }

    public void setActive_user_count(String active_user_count) {
        this.active_user_count = active_user_count;
    }

    public String getBuy_date() {
        return buy_date;
    }

    public void setBuy_date(String buy_date) {
        this.buy_date = buy_date;
    }

    public String getTag_r() {
        return tag_r;
    }

    public void setTag_r(String tag_r) {
        this.tag_r = tag_r;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }
}
