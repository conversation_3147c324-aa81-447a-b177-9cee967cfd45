package com.wbgame.pojo;

import java.util.Date;

public class NpPostVo {
	private String tdate;
	private Object param1;
	private Object param2;
	private Object param3;
	private Object param4;
	private Object param5;
	private Object param6;
	private Object param7;
	private Object param8;
	private String cNum; //设备型号
	private String holder; //持有人
	private String cType; //设备类型
	private String sponsor ; //发起人
	private String createStr;
	private String updateStr;
	private String owner;

	private Date create_time;

	private Date update_time;
	
	public String getTdate() {
		return tdate;
	}
	public void setTdate(String tdate) {
		this.tdate = tdate;
	}
	public Object getParam1() {
		return param1;
	}
	public void setParam1(Object param1) {
		this.param1 = param1;
	}
	public Object getParam2() {
		return param2;
	}
	public void setParam2(Object param2) {
		this.param2 = param2;
	}
	public Object getParam3() {
		return param3;
	}
	public void setParam3(Object param3) {
		this.param3 = param3;
	}
	public Object getParam4() {
		return param4;
	}
	public void setParam4(Object param4) {
		this.param4 = param4;
	}
	public Object getParam5() {
		return param5;
	}
	public void setParam5(Object param5) {
		this.param5 = param5;
	}
	public Object getParam6() {
		return param6;
	}
	public void setParam6(Object param6) {
		this.param6 = param6;
	}
	public Object getParam7() {
		return param7;
	}
	public void setParam7(Object param7) {
		this.param7 = param7;
	}
	public Object getParam8() {
		return param8;
	}
	public void setParam8(Object param8) {
		this.param8 = param8;
	}
	public String getcNum() {
		return cNum;
	}
	public void setcNum(String cNum) {
		this.cNum = cNum;
	}
	public String getHolder() {
		return holder;
	}
	public void setHolder(String holder) {
		this.holder = holder;
	}
	public String getcType() {
		return cType;
	}
	public void setcType(String cType) {
		this.cType = cType;
	}
	public String getSponsor() {
		return sponsor;
	}
	public void setSponsor(String sponsor) {
		this.sponsor = sponsor;
	}

	public String getCreateStr() {
		return createStr;
	}

	public void setCreateStr(String createStr) {
		this.createStr = createStr;
	}

	public String getUpdateStr() {
		return updateStr;
	}

	public void setUpdateStr(String updateStr) {
		this.updateStr = updateStr;
	}

	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	public Date getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Date create_time) {
		this.create_time = create_time;
	}

	public Date getUpdate_time() {
		return update_time;
	}

	public void setUpdate_time(Date update_time) {
		this.update_time = update_time;
	}
}
