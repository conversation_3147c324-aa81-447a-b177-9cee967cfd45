package com.wbgame.pojo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 小游戏广告数据
 * <AUTHOR>
 * @date 2020年3月3日
 */
public class GameAdInfo {
	/**日期*/
    private String cDate;

    /**游戏*/
    private Integer cGame;

    /**渠道*/
    private String channel;

    /**投放金额*/
    private BigDecimal amount;

    /**新增人数*/
    private Integer addNum;

    /**dau*/
    private Integer dau;
   
	/**广告当日收入*/
    private BigDecimal income;

    /**分发收入*/
    private BigDecimal distribution;
    
    /**广告dau arpu*/
    private Double dauARpu1;
    
    /**分发dau arpu*/
    private Double dauARpu2;
    
    /**总dau arpu*/
    private Double dauARpu3;
    
    /**cpa*/
    private Double cpa;
    
    public Double getCpa() {
		return cpa;
	}

	public void setCpa(Double cpa) {
		this.cpa = cpa;
	}

	public Double getDauARpu1() {
		return dauARpu1;
	}

	public void setDauARpu1(Double dauARpu1) {
		this.dauARpu1 = dauARpu1;
	}

	public Double getDauARpu2() {
		return dauARpu2;
	}

	public void setDauARpu2(Double dauARpu2) {
		this.dauARpu2 = dauARpu2;
	}

	public Double getDauARpu3() {
		return dauARpu3;
	}

	public void setDauARpu3(Double dauARpu3) {
		this.dauARpu3 = dauARpu3;
	}

	public String getcDate() {
        return cDate;
    }

    public void setcDate(String cDate) {
        this.cDate = cDate;
    }

    public Integer getcGame() {
        return cGame;
    }

    public void setcGame(Integer cGame) {
        this.cGame = cGame;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getAddNum() {
        return addNum;
    }

    public void setAddNum(Integer addNum) {
        this.addNum = addNum;
    }

    public Integer getDau() {
        return dau;
    }

    public void setDau(Integer dau) {
        this.dau = dau;
    }

    public BigDecimal getIncome() {
        return income;
    }

    public void setIncome(BigDecimal income) {
        this.income = income;
    }

    public BigDecimal getDistribution() {
        return distribution;
    }

    public void setDistribution(BigDecimal distribution) {
        this.distribution = distribution;
    }
}