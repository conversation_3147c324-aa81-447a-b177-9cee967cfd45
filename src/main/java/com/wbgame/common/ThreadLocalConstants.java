package com.wbgame.common;

import com.wbgame.aop.CommonControllerAop;
import com.wbgame.pojo.CurrUserVo;

/**
 * <AUTHOR>
 * @date 4.25
 * @description
 **/
public class ThreadLocalConstants {


    /**
     * 登录拦截用户名,
     * @see com.wbgame.annotation.ControllerLoggingEnhancer
     * @see CommonControllerAop
     * */
    public final static ThreadLocal<String> LOGIN_USER_NAME = new ThreadLocal<>();
    public final static ThreadLocal<CurrUserVo> LOGIN_USER = new ThreadLocal<>();

    public final static ThreadLocal<String> ORDER_STR = new ThreadLocal<>();
}
