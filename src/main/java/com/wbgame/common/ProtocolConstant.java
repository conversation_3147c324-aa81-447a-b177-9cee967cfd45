package com.wbgame.common;

import com.wbgame.utils.ApplicationContextUtils;
import org.springframework.core.env.Environment;

/**
 * @description: 协议常量
 * @author: huangmb
 * @date: 2020/12/04
 **/
public final class ProtocolConstant {

    public final static  String ad_cfg_cn = "ad_cfg_cn";

    public final static  String ad_cfg_en = "ad_cfg_en";

    public final static  String ad_new_cfg = "ad_new_cfg";

    public final static  String game_list_cfg = "game_list_cfg";

    public final static  String mm_cfg_cn = "mm_cfg_cn";

    public final static  String mm_cfg_en = "mm_cfg_en";

    public final static  String push_cfg = "push_cfg";

    public final static  String update_cfg = "update_cfg";

    public final static  String wx_param_cfg = "wx_param_cfg";

    public final static  String app_cfg = "app_cfg";

    public final static String mm_cfg_cn_url = "https://cfg.vigame.cn/MmChnl/";

    /**
     * 红包大额活动协议测试-正式地址
     */
    public final static String hb_large_test_url ;


    /**
     * 红包独立项目地址
     */
    public final static String hb_new_redpack_url;

    /**
     * 在线协议v5版本地址
     */
    public final static String GAME_ONLINE_SERVER_URL;

    /**
     * pvp模块地址
     */
    public final static String pvp_url;

    /**
     * 友盟任务同步url
     */
    public final static String umeng_url;

    /**
     * 飞书发送消息
     */
    public final static String fs_send_url = "https://edc.vigame.cn:6115/fs/sendMsg";
    public final static String SSDS_DOMAIN ;

    public final static String DN_LOGIN_URL;

    /**
     * 海外支付域名
     */
    public final static String hw_pay_server_url;

    static {
        //根据启动环境设置全局变量
        Environment environment = ApplicationContextUtils.get(Environment.class);
        String type = environment.getProperty("spring.profiles.active");
        System.out.println("ProtocolConstant:红包操作启动环境:" + type);
        if ("test".equals(type)) {
            hb_large_test_url = "http://192.168.1.38:6708";
            hb_new_redpack_url = "http://192.168.8.9:6708";
            GAME_ONLINE_SERVER_URL = "http://192.168.8.9:6708";
            pvp_url ="http://192.168.1.38:6409";
            umeng_url = "http://192.168.1.38:4780";
            DN_LOGIN_URL = "http://192.168.1.111:6105/all/login";
            hw_pay_server_url = "http://192.168.1.111:7401";
            SSDS_DOMAIN = "https://192.168.1.111:6105";
        }else{
            hb_large_test_url = "https://ddz.vigame.cn:6601";
            hb_new_redpack_url = "https://yxc.vzhifu.net";
            GAME_ONLINE_SERVER_URL = "https://c.vigame.cn";
            pvp_url= "https://ddz.vigame.cn:6601";
            umeng_url = "http://120.78.81.55:4780";
            DN_LOGIN_URL = "https://edc.vigame.cn:6115/all/login";
            hw_pay_server_url = "https://p.345ddz.com";
            SSDS_DOMAIN = "https://edc.vigame.cn:6115";
        }
    }

}
