package com.wbgame.common.strategyEnum;

import org.jetbrains.annotations.Contract;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @date 2023/12/1
 * @description
 **/
public enum Platform {
    HUAWEI("华为"),
    VIVO("vivo"),
    OPPO("oppo"),
    XIAOMI("小米"),
    GDT("广点通"),
    TOUTIAO("头条"),
    KUAISHOU("快手");

    public final String media;

    Platform(String media) {
        this.media = media;
    }

    public static Platform of(@NotNull String value) {
        return Platform.valueOf(value.toUpperCase());
    }

    @NotNull
    @Contract(pure = true)
    public String out() {
        return this.name().toLowerCase();
    }

}
