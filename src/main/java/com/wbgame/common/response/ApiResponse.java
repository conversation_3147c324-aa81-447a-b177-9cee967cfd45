package com.wbgame.common.response;

import java.io.Serializable;

import org.springframework.util.Assert;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wbgame.base.BaseError;
import com.wbgame.base.BaseErrorEnum;
import com.wbgame.enums.JudgeEnum;

import io.swagger.annotations.ApiModel;

@ApiModel("响应实体")
public class ApiResponse<T> implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8895989952182737733L;

	public static Integer CODE_SUCCESS = BaseErrorEnum.SUCCESS.getCode();

    private Integer code;

    private String msg;

    private Integer decryptStatus = JudgeEnum.FALSE.getCode();

    private T data;

    /**
     * 将传入的 result 对象，转换成另外一个泛型结果的对象
     * <p>
     * 因为 A 方法返回的 ApiResponse 对象，不满足调用其的 B 方法的返回，所以需要进行转换。
     *
     * @param result 传入的 result 对象
     * @param <T>    返回的泛型
     * @return 新的 ApiResponse 对象
     */
    public static <T> ApiResponse<T> fail(BaseError result) {
        return fail(result.getCode(), result.getMsg());
    }

    public static <T> ApiResponse<T> fail(Integer code, String msg) {
        Assert.isTrue(!CODE_SUCCESS.equals(code), "code 必须是错误的！");
        ApiResponse<T> result = new ApiResponse<>();
        result.code = code;
        result.msg = msg;
        return result;
    }

    public static <T> ApiResponse<T> fail(String msg) {
        ApiResponse<T> result = new ApiResponse<>();
        result.code = BaseErrorEnum.FAIL.getCode();
        result.msg = msg;
        return result;
    }

    public static <T> ApiResponse<T> fail() {
        ApiResponse<T> result = new ApiResponse<>();
        result.code = BaseErrorEnum.FAIL.getCode();
        result.msg = "";
        return result;
    }

    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> result = new ApiResponse<>();
        result.code = CODE_SUCCESS;
        result.data = data;
        result.msg = "";
        return result;
    }

    public static <T> ApiResponse<T> success() {
        ApiResponse<T> result = new ApiResponse<>();
        result.code = CODE_SUCCESS;
        result.msg = "";
        return result;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getDecryptStatus() {
        return decryptStatus;
    }

    public void setDecryptStatus(Integer decryptStatus) {
        this.decryptStatus = decryptStatus;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @JsonIgnore
    public boolean isSuccess() {
        return CODE_SUCCESS.equals(code);
    }

    @JsonIgnore
    public boolean isFail() {
        return !isSuccess();
    }

    @Override
    public String toString() {
        return "ApiResponse{" +
                "code=" + code +
                ", decryptStatus='" + decryptStatus + '\'' +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}
