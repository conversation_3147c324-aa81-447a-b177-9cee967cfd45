package com.wbgame.common;

import io.swagger.annotations.Api;
import org.apache.tomcat.util.http.fileupload.FileUploadBase;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * @description: 全局异常处理器
 * @author: huangmb
 * @date: 2020-10-16
 **/
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理其他异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value =Exception.class)
    public String exceptionHandler(HttpServletRequest req, Exception e){
        e.printStackTrace();
        return ReturnJson.toErrorJson("操作失败！异常信息:"+e.getMessage());
    }

    /**
     * 自定义异常
     * @param e
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = ApiException.class)
    public String apiHandle(ApiException e) {
        if (e.getErrorCode() != null) {
            return ReturnJson.error(e.getErrorCode());
        }
        return ReturnJson.toErrorJson(e.getMessage());
    }

}
