package com.wbgame.common.constants;

/**
 * <AUTHOR>
 * @Classname PlatformConstants
 * @Description 各广告平台常量类
 * @Date 2023/11/27 15:46
 */
public class PlatformConstants {

    /** 同步状态-成功*/
    public static final String SYNC_APPINFO_STATE_SUCCESS = "1";

    //token失效
    public static final String SYNC_APPINFO_STATE_TOKEN_ERROR = "2";

    /** 同步状态-失败*/
    public static final String SYNC_APPINFO_STATE_UNKOWN_ERROR = "3";

    /** 同步状态-账号不匹配*/
    public static final String SYNC_APPINFO_STATE_NOMATCH = "4";
    
    public class HW{
    	  /** 华为api地址*/
        public static final String HW_API_URL = "https://connect-api.cloud.huawei.com/api/";

        /** 华为api token*/
        public static final String HW_TOKEN_URL = "https://connect-api.cloud.huawei.com/api/oauth2/v1/token";

        /** 华为api 上传地址获取*/
        public static final String HW_UPLOAD_ADDRESS_URL = "https://connect-api.cloud.huawei.com/api/publish/v2/upload-url";

        /** 华为api 应用信息更新*/
        public static final String HW_UPDATE_APP_INFO_URL = "https://connect-api.cloud.huawei.com/api/publish/v2/app-file-info";

        /** 华为api 更新语言描述信息*/
        public static final String HW_APP_LANGUAGE_INFO_URL = "https://connect-api.cloud.huawei.com/api/publish/v2/app-language-info";

        /** 华为api 应用提交*/
        public static final String HW_APP_SUBMIT_URL="https://connect-api.cloud.huawei.com/api/publish/v2/app-submit";

        /** 华为api 应用信息*/
        public static final String HW_APP_INFO_URL = "https://connect-api.cloud.huawei.com/api/publish/v2/app-info";

        /** 接收华为回执地址*/
        public static final  String HW_CALLBACK_URL = "https://u.vigame.cn/huaweipush/getCallback";
    }


}
