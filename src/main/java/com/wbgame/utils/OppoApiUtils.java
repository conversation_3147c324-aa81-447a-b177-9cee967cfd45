package com.wbgame.utils;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: oppo传包工具
 * @author: huangmb
 * @date: 2021/11/01
 **/
@Slf4j
@Component
public class OppoApiUtils {

    private static final String url = "https://oop-openapi-cn.heytapmobi.com";

    private static final String CHARSET_UTF8 = "utf-8";

    private static RedisTemplate<String, Object> injectRedisTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @PostConstruct
    public void init() {
        injectRedisTemplate = redisTemplate;
    }


    /**
     * 获取token
     * @param clientId
     * @param clientSecret
     * @return
     */
    public static String getToken(String clientId,String clientSecret){
        /* 添加先从缓存获取逻辑 */
        String token = (String)injectRedisTemplate.opsForValue().get("oppo_api_access_token:"+clientId);
        if(token != null){
            return token;
        }

        String str = HttpClientUtils.getInstance().httpGet(url + "/developer/v1/token?client_id=" + clientId + "&client_secret=" + clientSecret);
        JSONObject obj = JSONObject.parseObject(str);
        if (obj.getIntValue("errno") != 0 || obj.getJSONObject("data") == null) {
            return null;
        }
        String newToken = obj.getJSONObject("data").getString("access_token");

        if(newToken != null){
            // 将token存入缓存，有效期12小时
            injectRedisTemplate.opsForValue().set("oppo_api_access_token:"+clientId, newToken, 20, TimeUnit.HOURS);
        }
        return newToken;
    }
    /**
     * 直接更新token
     * @param clientId
     * @param clientSecret
     * @return
     */
    public static String updateToken(String clientId,String clientSecret){
        String str = HttpClientUtils.getInstance().httpGet(url + "/developer/v1/token?client_id=" + clientId + "&client_secret=" + clientSecret);
        JSONObject obj = JSONObject.parseObject(str);
        if (obj.getIntValue("errno") != 0 || obj.getJSONObject("data") == null) {
            return null;
        }
        String newToken = obj.getJSONObject("data").getString("access_token");
        if(newToken != null){
            // 将token存入缓存，有效期12小时
            injectRedisTemplate.opsForValue().set("oppo_api_access_token:"+clientId, newToken, 20, TimeUnit.HOURS);
        }
        return newToken;
    }

    /**
     * 上传文件到vivo
     * @return
     */
    public static String uploadFile(String type, String token, String accessSecret, MultipartFile file) throws Exception{
        Map<String,Object> params = new HashMap<>();
        //公共参数
        params.put("access_token", token);
        params.put("timestamp", System.currentTimeMillis()/1000);
        params.put("api_sign",sign(accessSecret, params));
        String re = sendRequest(new URL(url+"/resource/v1/upload/get-upload-url"), params);
        JSONObject jsonObject = JSONObject.parseObject(re);
        if (jsonObject == null || jsonObject.getIntValue("errno") != 0) {
            return re;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        params.clear();
        params.put("type",type); //文件类型，包括照片、APK 包、其它，值是：photo、apk、resource
        params.put("sign",data.get("sign").toString());
        //http请求
        String result = "";
        CloseableHttpClient client = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        try{
            HttpPost httpPost = new HttpPost(data.get("upload_url")+"");
            MultipartEntityBuilder builder = MultipartEntityBuilder.create().setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            builder.setCharset(Charset.forName("UTF-8")).addBinaryBody("file", file.getInputStream(), ContentType.MULTIPART_FORM_DATA, file.getOriginalFilename());
            for(String key : params.keySet()){
                builder.addTextBody(key,params.get(key).toString());
            }
            HttpEntity httpEntity = builder.build();
            httpPost.setEntity(httpEntity);
            response = client.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                result= EntityUtils.toString(responseEntity, Charset.forName("UTF-8"));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 上传文件到oppo
     * @return
     */
    public static String uploadFile(String type, String token, String accessSecret, File file) throws Exception{
        Map<String,Object> params = new HashMap<>();
        //公共参数
        params.put("access_token", token);
        params.put("timestamp", System.currentTimeMillis()/1000);
        params.put("api_sign",sign(accessSecret, params));
        String re = sendRequest(new URL(url+"/resource/v1/upload/get-upload-url"), params);
        JSONObject jsonObject = JSONObject.parseObject(re);
        if (jsonObject == null || jsonObject.getIntValue("errno") != 0) {
            return re;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        params.clear();
        params.put("type",type); //文件类型，包括照片、APK 包、其它，值是：photo、apk、resource
        params.put("sign",data.get("sign").toString());
        //http请求
        String result = "";
        CloseableHttpClient client = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        try{
            HttpPost httpPost = new HttpPost(data.get("upload_url")+"");
            MultipartEntityBuilder builder = MultipartEntityBuilder.create().setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            builder.setCharset(Charset.forName("UTF-8")).addBinaryBody("file", file);
            for(String key : params.keySet()){
                builder.addTextBody(key,params.get(key).toString());
            }
            HttpEntity httpEntity = builder.build();
            httpPost.setEntity(httpEntity);
            response = client.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                result= EntityUtils.toString(responseEntity, Charset.forName("UTF-8"));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 获取详情
     * @param params
     * @param token
     * @param accessSecret
     * @return
     * @throws Exception
     */
    public static String detail (Map<String,Object> params,String token, String accessSecret) throws Exception{
        //公共参数
        params.put("access_token", token);
        params.put("timestamp", System.currentTimeMillis()/1000);
        params.put("api_sign",sign(accessSecret, params));
        String re = sendRequest(url+"/resource/v1/app/info", params);
        return re;
    }

    /**
     * 获取详情
     * @param params
     * @param token
     * @param accessSecret
     * @return
     * @throws Exception
     */
    public static String taskStatus (Map<String,Object> params,String token, String accessSecret) throws Exception{
        //公共参数
        params.put("access_token", token);
        params.put("timestamp", System.currentTimeMillis()/1000);
        params.put("api_sign",sign(accessSecret, params));
        String re = sendRequest(new URL(url+"/resource/v1/app/task-state"), params);
        return re;
    }

    /**
     * 版本升级
     * @return
     */
    public static String versionUp(Map<String,Object> params,String token, String accessSecret) throws Exception{
        //公共参数
        params.put("access_token", token);
        params.put("timestamp", System.currentTimeMillis()/1000);
        params.put("api_sign",sign(accessSecret, params));
        String result = callApi(new URL(url + "/resource/v1/app/upd"), params);
        return result;
    }

    /**
     * 更新资料
     * @return
     */
    public static String updateData(Map<String,Object> params,String token, String accessSecret) throws Exception{
        //公共参数
        params.put("access_token", token);
        params.put("timestamp", System.currentTimeMillis()/1000);
        params.put("api_sign",sign(accessSecret, params));
        String result = callApi(new URL(url + "/resource/v1/app/updm"), params);
        return result;
    }

    /**
     * 普通文本提交 POST
     */
    private static String callApi(URL url, Map<String, Object> params) throws IOException {
        String query = buildQuery(params, "utf-8");
        byte[] content = {};
        if (query != null) {
            content = query.getBytes("utf-8");
        }

        HttpURLConnection conn = null;
        OutputStream out = null;
        String rsp = null;
        try {
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setRequestProperty("Host", url.getHost());
            conn.setRequestProperty("Accept", "application/json");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=" + "utf-8");
            out = conn.getOutputStream();
            out.write(content);
            rsp = getResponseToString(conn);
        } finally {
            if (out != null) {
                out.close();
            }
            if (conn != null) {
                conn.disconnect();
            }
        }

        return rsp;
    }

    /**
     * 普通表单提交
     * @param url
     * @param params
     * @return
     * @throws IOException
     */
    public static String sendRequest(URL url, Map<String, Object> params) throws IOException {
        String query = buildQuery(params, "utf-8");
        byte[] content = {};
        if (query != null) {
            content = query.getBytes("utf-8");
        }

        HttpURLConnection conn = null;
        OutputStream out = null;
        String rsp = null;
        try {
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setRequestProperty("Host", url.getHost());
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=" + "utf-8");
            out = conn.getOutputStream();
            out.write(content);
            rsp = getResponseToString(conn);
        } finally {
            if (out != null) {
                out.close();
            }
            if (conn != null) {
                conn.disconnect();
            }
        }
        return rsp;
    }

    /**
     * 发起普通GET请求
     * 这里的HTTP请求方法仅作调试参考，具体请求方式根据需求进行调整
     * @param requestUrl
     * @param params
     * @return
     * @throws IOException
     */
    public static String sendRequest(String requestUrl, Map<String, Object> params) throws IOException {
        String query = buildQuery(params, CHARSET_UTF8);
        URL url = new URL(requestUrl + "?" + query);

        HttpURLConnection conn = null;
        OutputStream out = null;
        String rsp = null;
        try {
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setDoInput(true);
            conn.setRequestProperty("Host", url.getHost());
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=" + CHARSET_UTF8);
            // 若HTTP响应为 405 Method Not Allowed ，说明请求的HTTP方法不正确
            // 具体请求的HTTP方法(GET/POST/...)，请依照开放平台对应接口文档中标注的方法发起接口调用
            rsp = getResponseToString(conn);
        } finally {
            if (out != null) {
                out.close();
            }
            if (conn != null) {
                conn.disconnect();
            }
        }
        return rsp;
    }

    private static String buildQuery(Map<String, Object> params, String charset) throws IOException {
        if (params == null || params.isEmpty()) {
            return null;
        }

        StringBuilder query = new StringBuilder();
        Set<Map.Entry<String, Object>> entries = params.entrySet();
        boolean hasParam = false;

        for (Map.Entry<String, Object> entry : entries) {
            String name = entry.getKey();
            Object value = entry.getValue();
            // 忽略值为null的参数
            if (value != null) {
                if (hasParam) {
                    query.append("&");
                } else {
                    hasParam = true;
                }
                query.append(name).append("=").append(URLEncoder.encode(String.valueOf(value), charset));
            }
        }

        return query.toString();
    }

    private static String getResponseToString(HttpURLConnection conn) throws IOException {
        String charset = getResponseCharset(conn.getContentType());
        if (conn.getResponseCode() < 400) {
            return getStreamToString(conn.getInputStream(), charset);
        } else {// Client Error 4xx and Server Error 5xx
            throw new IOException(conn.getResponseCode() + " " + conn.getResponseMessage());
        }
    }

    private static String getStreamToString(InputStream stream, String charset) throws IOException {
        try {
            Reader reader = new InputStreamReader(stream, charset);
            StringBuilder response = new StringBuilder();

            final char[] buff = new char[1024];
            int read = 0;
            while ((read = reader.read(buff)) > 0) {
                response.append(buff, 0, read);
            }

            return response.toString();
        } finally {
            if (stream != null) {
                stream.close();
            }
        }
    }

    private static String getResponseCharset(String ctype) {
        String charset = "utf-8";

        if (isNotEmpty(ctype)) {
            String[] params = ctype.split(";");
            for (String param : params) {
                param = param.trim();
                if (param.startsWith("charset")) {
                    String[] pair = param.split("=", 2);
                    if (pair.length == 2) {
                        if (isNotEmpty(pair[1])) {
                            charset = pair[1].trim();
                        }
                    }
                    break;
                }
            }
        }

        return charset;
    }

    private static boolean isNotEmpty(String value) {
        int strLen;
        if (value == null || (strLen = value.length()) == 0) {
            return false;
        }
        for (int i = 0; i < strLen; i++) {
            if ((Character.isWhitespace(value.charAt(i)) == false)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 对请求参数进行签名
     * @param secret
     * @param paramsMap
     * @return String
     * @throws IOException
     */
    public static String sign(String secret, Map<String, Object> paramsMap)
            throws IOException {
        List keysList = new ArrayList<>(paramsMap.keySet());
        Collections.sort(keysList);
        StringBuilder sb = new StringBuilder();
        List<String> paramList = new ArrayList<>();
        for (Object key : keysList) {
            Object object = paramsMap.get(key);
            if (object == null){
                continue;
            }
            String value = key + "=" + object;
            paramList.add(value);
        }
        String signStr = String.join("&", paramList);
        return hmacSHA256(signStr, secret);
    }

    /**
     * HMAC_SHA256 计算签名
     * @param data 需要加密的参数
     * @param key 签名密钥
     * @return String 返回加密后字符串
     */
    public static String hmacSHA256(String data, String key) {
        try {
            byte[] secretByte = key.getBytes(Charset.forName("UTF-8"));
            SecretKeySpec signingKey = new SecretKeySpec(secretByte, "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(signingKey);
            byte[] dataByte = data.getBytes(Charset.forName("UTF-8"));
            byte[] by = mac.doFinal(dataByte);
            return byteArr2HexStr(by);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 字节数组转换为十六进制
     * @param bytes
     * @return String
     */
    private static String byteArr2HexStr(byte[] bytes) {
        int length= bytes.length;
        // 每个byte用两个字符才能表示，所以字符串的长度是数组长度的两倍
        StringBuilder sb = new StringBuilder(length* 2);
        for (int i = 0; i < length; i++) {
            // 将得到的字节转16进制
            String strHex = Integer.toHexString(bytes[i] & 0xFF);
            // 每个字节由两个字符表示，位数不够，高位补0
            sb.append((strHex.length() == 1) ? "0" + strHex : strHex);
        }
        return sb.toString();
    }

}
