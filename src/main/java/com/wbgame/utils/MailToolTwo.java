package com.wbgame.utils;

import com.sun.mail.smtp.SMTPAddressFailedException;
import com.sun.mail.smtp.SMTPSendFailedException;
import com.sun.mail.util.MailSSLSocketFactory;

import javax.mail.*;
import javax.mail.event.TransportEvent;
import javax.mail.event.TransportListener;
import javax.mail.internet.*;
import java.security.GeneralSecurityException;
import java.util.Properties;

public class MailToolTwo {
    public static String sendMail(String contentMsg,Address[] address) {
    	String state = "success";
    	Properties props = new Properties();

        // 开启debug调试
        props.setProperty("mail.debug", "false");
        // 发送服务器需要身份验证
        props.setProperty("mail.smtp.auth", "true");
        // 设置邮件服务器主机名
        props.setProperty("mail.host", "smtp.qq.com");
        // 发送邮件协议名称
        props.setProperty("mail.transport.protocol", "smtp");
        // 设置连接超时 Socket连接超时值
        props.setProperty("mail.smtp.connectiontimeout", "3000");
        //Socket I/O超时
        props.setProperty("mail.smtp.timeout", "3000");
        
        try {
	        MailSSLSocketFactory sf = new MailSSLSocketFactory();
	        sf.setTrustAllHosts(true);
	        props.put("mail.smtp.ssl.enable", "true");
	        props.put("mail.smtp.ssl.socketFactory", sf);
        
	        Session session = Session.getInstance(props);
	        Message msg = new MimeMessage(session);
	        
	        msg.setFrom(new InternetAddress("<EMAIL>"));
			msg.setSubject("数据预警");
//	        StringBuilder builder = new StringBuilder();//发送内容
//	        builder.append("\n点击此链接验证账户");
//	        builder.append(""+System.currentTimeMillis());
//	        msg.setText(builder.toString());
	        
	        
            Multipart multipart = new MimeMultipart();
	        // 添加邮件正文
            BodyPart contentPart = new MimeBodyPart();
            contentPart.setContent("<span style='color:red'>"+contentMsg+"</span>", "text/html;charset=UTF-8");
            multipart.addBodyPart(contentPart);
            
            // 将multipart对象放到message中
            msg.setContent(multipart);
            // 保存邮件
            msg.saveChanges();
	        
	        Transport transport = session.getTransport();
	        transport.addTransportListener(new TransportListener() {//监听发送是否成功
				
				@Override
				public void messagePartiallyDelivered(TransportEvent e) {
//					System.out.println("结果  --- "+"messagePartiallyDelivered 消息部分发送成功");
					
				}
				@Override
				public void messageNotDelivered(TransportEvent e) {
//					System.out.println("结果  ### "+"messageNotDelivered 消息未发送");
					
				}
				@Override
				public void messageDelivered(TransportEvent e) {
//					System.out.println("结果  === "+"messageDelivered 消息发送成功");
					
				}
			});
	        transport.connect("smtp.qq.com", "<EMAIL>", "rhwnvgpuoiqibgeh");
	        transport.sendMessage(msg, address);
	        transport.close();
	        
	    }catch (SMTPSendFailedException e){
			state=("发送方地址有误");
    	} catch (AuthenticationFailedException e) {
			state=("发送方账号或密码有误");
		} catch (GeneralSecurityException e) {
			state=("安全性异常");
		} catch (SMTPAddressFailedException e) {
			state=("接收方地址有误");
		} catch (SendFailedException e) {
			state=("接收方邮箱未找到或拒绝访问");
		} catch (NoSuchProviderException e) {
			state="提供商异常";
		} catch (MessagingException e) {
			state="网络异常";
		}
        
		return state;
	}
	public static String sendMails(String contentMsg,Address[] address) {
		String state = "success";
		Properties props = new Properties();

		// 开启debug调试
		props.setProperty("mail.debug", "false");
		// 发送服务器需要身份验证
		props.setProperty("mail.smtp.auth", "true");
		// 设置邮件服务器主机名
		props.setProperty("mail.host", "smtp.qq.com");
		// 发送邮件协议名称
		props.setProperty("mail.transport.protocol", "smtp");
		// 设置连接超时 Socket连接超时值
		props.setProperty("mail.smtp.connectiontimeout", "3000");
		//Socket I/O超时
		props.setProperty("mail.smtp.timeout", "3000");

		try {
			MailSSLSocketFactory sf = new MailSSLSocketFactory();
			sf.setTrustAllHosts(true);
			props.put("mail.smtp.ssl.enable", "true");
			props.put("mail.smtp.ssl.socketFactory", sf);

			Session session = Session.getInstance(props);
			Message msg = new MimeMessage(session);

			msg.setFrom(new InternetAddress("<EMAIL>"));
			msg.setSubject("数据预警");
			Multipart multipart = new MimeMultipart();
			// 添加邮件正文
			BodyPart contentPart = new MimeBodyPart();
			contentPart.setContent(contentMsg, "text/html;charset=UTF-8");
			multipart.addBodyPart(contentPart);

			// 将multipart对象放到message中
			msg.setContent(multipart);
			// 保存邮件
			msg.saveChanges();

			Transport transport = session.getTransport();
			transport.addTransportListener(new TransportListener() {//监听发送是否成功

				@Override
				public void messagePartiallyDelivered(TransportEvent e) {
//					System.out.println("结果  --- "+"messagePartiallyDelivered 消息部分发送成功");

				}
				@Override
				public void messageNotDelivered(TransportEvent e) {
//					System.out.println("结果  ### "+"messageNotDelivered 消息未发送");

				}
				@Override
				public void messageDelivered(TransportEvent e) {
//					System.out.println("结果  === "+"messageDelivered 消息发送成功");

				}
			});
			transport.connect("smtp.qq.com", "<EMAIL>", "rhwnvgpuoiqibgeh");
			transport.sendMessage(msg, address);
			transport.close();

		}catch (SMTPSendFailedException e){
			state=("发送方地址有误");
		} catch (AuthenticationFailedException e) {
			state=("发送方账号或密码有误");
		} catch (GeneralSecurityException e) {
			state=("安全性异常");
		} catch (SMTPAddressFailedException e) {
			state=("接收方地址有误");
		} catch (SendFailedException e) {
			state=("接收方邮箱未找到或拒绝访问");
		} catch (NoSuchProviderException e) {
			state="提供商异常";
		} catch (MessagingException e) {
			state="网络异常";
		}

		return state;
	}
    
	public static void main(String[] args) {
		try {
//			File file = new File("F:/i_000ccBbU.jpg");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
    
}