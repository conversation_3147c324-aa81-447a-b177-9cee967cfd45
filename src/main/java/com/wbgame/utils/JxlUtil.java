package com.wbgame.utils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wbgame.utils.ipip.City;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jxcell.View;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.UnderlineStyle;
import jxl.write.DateFormat;
import jxl.write.DateTime;
import jxl.write.Label;
import jxl.write.NumberFormat;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import org.springframework.core.env.Environment;

public class JxlUtil {
	
	static Logger logger = LoggerFactory.getLogger(JxlUtil.class);
	
	/**
	 * 另存文件的临时存放目录
	 */
//	public static String DEFAULTDIR = "/home/<USER>/src/";
//	public static String DEFAULTDIR = "F:/";
	public static String DEFAULTDIR ;
//	public static String DEFAULTDIR = "/home/<USER>/project/ROOT/";

	//静态初始化配置路径,实现本地开发与测试环境分离
	static {
		Environment environment = ApplicationContextUtils.get(Environment.class);
		DEFAULTDIR=environment.getProperty("tempPath");
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cmsz.common.java.services.ISaveToExcel#doSave(java.lang.String,
	 *      java.util.Map, java.util.List, java.util.Map, java.util.Map,
	 *      java.lang.String, javax.servlet.http.HttpServletResponse) 另存Excel文件
	 *      主要处理日期和数字类型 ,并且判断文件是否加密
	 */
	public static boolean doSave(String fileName, Map<String, String> headerMap,
			List<Map<String, Object>> contentMap, Map<String, String> dtFormat,
			Map<String, String> numFormat, String menuId,
			HttpServletRequest request, HttpServletResponse response) {
		
		response.setHeader("Access-Control-Allow-Origin", "*");
		View m_view = null;
		File file = null;
		try {
			// 加密标志
//			boolean encryptFlag = this.isEncrypt(request, menuId);

			/*
			 * File dir = new File(Constants.DEFAULTDIR); if(! dir.exists()){
			 * boolean flag = dir.mkdirs(); //目录如果不存在，创建这个目录(包含父目录)
			 * System.out.println(flag); }
			 */
//			String path = File.separator;
			String path = JxlUtil.DEFAULTDIR;
			/*String fname = fileName;
			if (null != fileName && fileName.lastIndexOf(File.separator) != -1) {
				path = fileName.substring(0, fileName
						.lastIndexOf(File.separator) + 1);
				fname = fileName.substring(
						fileName.lastIndexOf(File.separator) + 1, fileName.length());
			}*/
			
			String newFileName = path + "export_" + System.currentTimeMillis()
					+ ".xls";
			file = new File(newFileName);
			OutputStream out = new FileOutputStream(file); // 输出流 对象
			WritableWorkbook wookbook = Workbook.createWorkbook(out); // 创建一个Excel文档
			WritableSheet sheet = wookbook.createSheet("Sheet1", 0); // 创建一页

			// 定义文档格式 包括头和内容单元格格式
			WritableFont titleFont = new WritableFont(WritableFont.ARIAL, 10,
					WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE,
					Colour.BLACK);
			WritableCellFormat titleFormat = new WritableCellFormat(titleFont);
			titleFormat.setAlignment(Alignment.CENTRE); // 标题居中

			WritableFont contFont = new WritableFont(WritableFont.ARIAL, 10,
					WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE,
					Colour.BLACK);
			WritableCellFormat contFormat = new WritableCellFormat(contFont);
			// contFormat.setAlignment(Alignment.RIGHT); // 内容居右
			// 不设置格式，使之默认字符串左对齐，数字和日期右对齐

			DateFormat df = null; // 针对不同的日期格式
			NumberFormat nf = null; // 针对不同的数字格式
			WritableCellFormat format = null;

			// 用于保存日期格式和数字格式对象
			Map<String, WritableCellFormat> dfMap = new LinkedHashMap<String, WritableCellFormat>();
			// 等于空，说明没有日期字段，不需要设置格式
			if (null != dtFormat) {
				Set<String> dfKey = dtFormat.keySet();
				Iterator<String> dfIt = dfKey.iterator();

				for (int i = 0; i < dfKey.size(); i++) {
					String dfField = dfIt.next();

					df = new DateFormat(dtFormat.get(dfField));// 用于日期的
					format = new WritableCellFormat(contFont, df);

					dfMap.put(dfField, format); // 将日期格式对象保存
				}
			}
			// 等于空，说明没有数字字段，不需要设置格式
			if (null != numFormat) {
				Set<String> nfKey = numFormat.keySet();
				Iterator<String> nfIt = nfKey.iterator();

				for (int i = 0; i < nfKey.size(); i++) {
					String nfField = nfIt.next();

					nf = new NumberFormat(numFormat.get(nfField)); // 处理数字
					format = new WritableCellFormat(contFont, nf);

					dfMap.put(nfField, format); // 将日期格式对象保存
				}
			}

			int row = 0; // 控制换行

			// 展示表头
			Label label = null;
			jxl.write.Number num = null;
			jxl.write.DateTime date = null;

			Set<String> heads = headerMap.keySet();
			Iterator<String> it = heads.iterator();

			for (int i = 0; i < heads.size(); i++) {
				String title = headerMap.get(it.next());

				label = new Label(i, 0, title, titleFormat); // 设置标题
				sheet.addCell(label);
				sheet.setColumnView(i, 30); // 设置列宽
			}

			row++; // 新增一行

			// 展示数据
			for (int j = 0; j < contentMap.size(); j++) {
				Map<String, Object> temp = contentMap.get(j);
				it = heads.iterator();

				for (int k = 0; k < heads.size(); k++) {
					String fieldName = it.next();
					Object cont = temp.get(fieldName);

					if (null == cont || cont.toString().isEmpty()) {
						continue;
					}

					format = dfMap.get(fieldName);

					if (cont instanceof Date) {

						if (null != format) {

							date = new DateTime(k, row, (Date) cont, format);
						} else {

							date = new DateTime(k, row, (Date) cont);
						}
						sheet.addCell(date); // 添加内容
					} else if (cont instanceof Integer) {

						if (null != format) {

							num = new jxl.write.Number(k, row, Double
									.parseDouble(cont.toString()), format);
						} else {

							num = new jxl.write.Number(k, row, Double
									.parseDouble(cont.toString()));
						}
						sheet.addCell(num); // 添加内容
					} else if (cont instanceof Long) {

						if (null != format) {

							num = new jxl.write.Number(k, row, Double
									.parseDouble(cont.toString()), format);
						} else {

							num = new jxl.write.Number(k, row, Double
									.parseDouble(cont.toString()));
						}
						sheet.addCell(num); // 添加内容
					} else if (cont instanceof Float) {

						if (null != format) {

							num = new jxl.write.Number(k, row, Double
									.parseDouble(cont.toString()), format);
						} else {

							num = new jxl.write.Number(k, row, Double
									.parseDouble(cont.toString()));
						}
						sheet.addCell(num); // 添加内容
					} else if (cont instanceof Double) {

						if (null != format) {

							num = new jxl.write.Number(k, row, Double
									.parseDouble(cont.toString()), format);
						} else {

							num = new jxl.write.Number(k, row, Double
									.parseDouble(cont.toString()));
						}
						sheet.addCell(num); // 添加内容
					} else {
						label = new Label(k, row, cont.toString(), contFormat);
						sheet.addCell(label); // 添加内容
					}
				}

				row++; // 行自增
			}

			wookbook.write(); // 写数据
			wookbook.close(); // 关闭流
			out.close();

			
			/*if (encryptFlag) { // 判断文件是否加密输出

				m_view = new View();
				CurrUserVO certUser = (CurrUserVO) request.getSession()
						.getAttribute("Certificate");
				InputStream in = new FileInputStream(file);

				m_view.read(in);

				m_view.write(file.getPath(), certUser.getTempass());

				in.close();
				logger.debug("加密已经完成");
			}*/
			
			// 文件创建后，将文件以流的形式输出
			outPutExcel(fileName, file, response);
			return true;

		} catch (IOException e) {
			e.printStackTrace();
			logger.error("另存文件时出现文件流读写异常");
		} catch (RowsExceededException e) {
			logger.error("要生成的文件数据行过多,另存失败");
		} catch (WriteException e) {
			logger.error("将数据写入文件时出现错误,另存失败");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("生成另存文件时出现错误");
		} finally {
			if (null != file) {
				boolean isDelete = file.delete(); // 删除文件
				logger.info(isDelete + "");
			}
			if (m_view != null) {

				m_view.destroy();
			}
		}
		return false;
	}
	
	/**
	 * @param fi
	 *            输出文件
	 * @param response
	 *            响应对象 将文件以流的形式输出
	 * @throws IOException
	 */
	public static void outPutExcel(String fileName, File fi,
			HttpServletResponse response) throws IOException {

		BufferedInputStream bis = null; // 缓存输入流
		BufferedOutputStream bos = null; // 缓存输出流

		response.reset(); // 先清空一下response

		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setCharacterEncoding("ISO-8859-1"); // 设置编码
		response.setContentType(getContentType(fi.getPath())); // 设置类型
		response.addHeader("Content-disposition", "attachment;filename="
				+ new String(fileName.getBytes(), "ISO-8859-1")); // 设置文件名

		response.setContentLength(Integer
				.parseInt((String.valueOf(fi.length())))); // 设置文件长度

		bis = new BufferedInputStream(new FileInputStream(fi));
		bos = new BufferedOutputStream(response.getOutputStream());

		byte[] buff = new byte[4096]; // 每次读取流的字节数
		int bytesRead;

		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}

		bos.flush(); // 清理一下缓存
		bos.close();
		response.getOutputStream().flush();
		response.getOutputStream().close();
		bis.close(); // 关闭流对象

	}
	
	/**
	 * @param menuId
	 *            当前操作页面的菜单编号
	 * @return 另存文件是否加密 true-加密 false-不加密
	 * @throws Exception
	 */
	/*private boolean isEncrypt(HttpServletRequest request, String menuId)
			throws Exception {

		if (StringUtils.isEmpty(menuId)) {

			return false;
		}

		CurrUserVO certUser = (CurrUserVO) request.getSession().getAttribute(
				"Certificate");
		if (null == certUser || certUser.getTempass().equals("")) {

			return false;
		}
		List<String> menus = (List<String>) request.getSession().getAttribute(
				"userPassMenu");

		if (null == menus || menus.isEmpty()) {

			return false;
		}

		if (menus.contains(menuId)) {

			return true;
		} else {

			return false;
		}
	}*/
	
	/**
	 * @param fileName
	 *            文件名
	 * @return 返回文件名后缀 用于设置文件的返回类型
	 */
	public static String getContentType(String fileName) {
		String fileNameTemp = fileName.toLowerCase(); // 先转为小写
		String ret = "";
		if (fileNameTemp.endsWith("txt")) {
			ret = "text/plain";
		} else if (fileNameTemp.endsWith("gif")) {
			ret = "image/gif";
		} else if (fileNameTemp.endsWith("jpg")) {
			ret = "image/jpeg";
		} else if (fileNameTemp.endsWith("jpeg")) {
			ret = "image/jpeg";
		} else if (fileNameTemp.endsWith("jpe")) {
			ret = "image/jpeg";
		} else if (fileNameTemp.endsWith("zip")) {
			ret = "application/zip";
		} else if (fileNameTemp.endsWith("rar")) {
			ret = "application/rar";
		} else if (fileNameTemp.endsWith("doc")) {
			ret = "application/msword";
		} else if (fileNameTemp.endsWith("ppt")) {
			ret = "application/vnd.ms-powerpoint";
		} else if (fileNameTemp.endsWith("xls")) {
			ret = "application/vnd.ms-excel";
		} else if (fileNameTemp.endsWith("html")) {
			ret = "text/html";
		} else if (fileNameTemp.endsWith("htm")) {
			ret = "text/html";
		} else if (fileNameTemp.endsWith("tif")) {
			ret = "image/tiff";
		} else if (fileNameTemp.endsWith("tiff")) {
			ret = "image/tiff";
		} else if (fileNameTemp.endsWith("pdf")) {
			ret = "application/pdf";
		} else if (fileNameTemp.endsWith("mp3")) {
			ret = "application/mp3";
		}
		return ret;
	}
	
	
	/**
     * 设置标题样式
     * @return
     */
    public static WritableCellFormat getTitle() {
        WritableFont font = new WritableFont(WritableFont.TIMES, 12);
        WritableCellFormat format = new WritableCellFormat(font);
        try {
            font.setColour(Colour.BLUE);//蓝色字体
            format.setAlignment(jxl.format.Alignment.CENTRE);
            format.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            format.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
        }
        catch (WriteException e1) {
            e1.printStackTrace();
        }
        return format;
    }
    
	/**
     * 设置其他单元格样式
     * @return
     */
	public static WritableCellFormat getNormolCell() {//12号字体,上下左右居中,带黑色边框
        WritableFont font = new WritableFont(WritableFont.TIMES, 11);
        WritableCellFormat format = new WritableCellFormat(font);
        try {
            format.setAlignment(jxl.format.Alignment.CENTRE);
            format.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            //format.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
        }
        catch (WriteException e) {
            e.printStackTrace();
        }
        return format;
    }
}
