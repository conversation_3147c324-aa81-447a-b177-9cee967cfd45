package com.wbgame.utils.export;

import com.wbgame.service.IFileDowService;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

/**
 * @author: zhangY
 * @createDate: 2023/03/09 009
 * @class: DownFile
 * @description:
 */
public abstract class AbstractDownFile {

    public abstract <E> Entity tryDownList(int total, int sheetSize, String fileName, Map<String, String> rowName,
                           Supplier<List<E>> listSupplier, IFileDowService fileDowService);

    // 0 可进行状态，1 进行中
    public static final AtomicInteger DOWN_STATUS = new AtomicInteger(0);

    public static final Map<String, Entity> DOWNLOAD_MAP = new ConcurrentHashMap<>();

    public static class Entity  implements Serializable {


        private static final long serialVersionUID = -1244263184078938448L;
        // 1 进行中，2 完成, 3 已删除, 4 异常,
        private int status;

        // 标识
        private String name;

        // 文件名
        private String fileName;

        private String url;


        public Entity(int status, String name, String fileName, String url) {
            this.status = status;
            this.name = name;
            this.url = url;
            this.fileName = fileName;
        }

        @Override
        public String toString() {
            return "Entity{" +
                    "status=" + status +
                    ", name='" + name + '\'' +
                    ", fileName='" + fileName + '\'' +
                    ", url='" + url + '\'' +
                    '}';
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

}
