package com.wbgame.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

/**
 * 该工具类是为了获取spring容器管理的bean
 *
 */
@Configuration
public class ApplicationContextUtils implements ApplicationContextAware {

    //定义静态的ApplicationContext对象
    private static ApplicationContext applicationContext;

    //重写 方法 并赋值
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationContextUtils.applicationContext = applicationContext;
    }

    //调用上下文的getBean方法获取容器中的对象
    public static <T> T get(Class<T> clazz){
        return applicationContext.getBean(clazz);
    }

    public static Object get(String name) {
        return applicationContext.getBean(name);
    }

}

