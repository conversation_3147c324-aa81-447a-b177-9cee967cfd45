package com.wbgame.utils;

import org.springframework.util.ObjectUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 数据转换工具类
 * @Date 2024/8/7 11:48
 */
public class DataTransUtils {

    /**
     * 字段值添加'':对指定注解的字段的值加上 ''
     *
     * @param t               数据对象
     * @param annotationClazz 注解类
     * @param <T>             泛型
     */
    public static <T> void transToSqls(T t, Class<? extends Annotation> annotationClazz) {
        try {
            if (t == null || annotationClazz == null) {
                return;
            }
            Class<?> clazz = t.getClass();
            for (Field field : clazz.getDeclaredFields()) {
                if (!field.isAnnotationPresent(annotationClazz)) continue;
                field.setAccessible(true);
                Object obj = field.get(t);
                if (obj == null) continue;
                String value = obj.toString();
                field.set(t, transToSql(value));
            }
        } catch (IllegalAccessException e) {
        }
    }




    /**
     * 将用逗号拼接的字符串转换成sql:每个字段添加 ''
     * @param params 需要处理的字段 示例: hello,hi,string
     * @return 处理结果: 示例: 'hello','hi','string'
     */
    public static String transToSql(String params){
        if (StringUtils.isEmpty(params)) return null;
        String[] fields = params.split(",");
        StringBuilder sql = new StringBuilder();
        for (String field : fields) {
            sql.append("'").append(field).append("',");
        }
        sql.deleteCharAt(sql.length() - 1);
        return sql.toString();
    }

    /**
     * 将用逗号拼接的字符串转换成sql:每个字段添加 ''
     * @param aliseName 表名 b
     * @param order_str 需要处理的字段 示例: appid asc,addnum asc,appname asc
     * @return 处理结果: 示例: b.appid asc,b.addnum asc,b.appname asc
     */
    public static String generateOrderStrParam(String aliseName,String order_str){
        //排序特殊 因为total_income字段数据库为字符串
        if (!BlankUtils.checkBlank(order_str)){
            String[] split = order_str.split(",");
            StringBuilder sort = new StringBuilder();
            for (String sortField : split) {
                sort.append(aliseName).append(".").append(sortField).append(",");
            }
            order_str = sort.deleteCharAt(sort.length() - 1).toString();
        }else {
            order_str = "";
        }
        return order_str;
    }


    /**
     * 字段根据指定顺序排序操作
     * @param orderStr 所有排序字段
     * @param orderField 排序字段
     * @param sort 指定排序规则
     * @return 处理后的指定字段的排序规则
     */
    public static String specifySorting(String orderStr,String orderField,String ...sort){
        String specifySorting = orderStr;
        if (sort == null || sort.length == 0) {
            return specifySorting;
        }
        if (BlankUtils.checkBlank(orderStr) || BlankUtils.checkBlank(orderField)) {
            return specifySorting;
        }
        if (orderStr.contains(orderField)) {
            String sortFields = transToSql(String.join(",", sort));
            String format = "FIELD(%s, %s)";
            specifySorting = orderStr.replace(orderField,String.format(format,orderField,sortFields));
        }
        return specifySorting;
    }

    /**
     * 对指定字段添加百分号的操作
     *
     * @param dataVo 需要添加百分号的数据对象
     * @param fields 添加百分号的相关字段
     * @param <T>    对象类型
     */
    public static <T> void addDataPercentage(T dataVo, String fields) {
        if (dataVo == null || BlankUtils.checkBlank(fields)) {
            return;
        }
        try {
            List<String> fieldList = Arrays.asList(fields.split(","));
            Class<?> clazz = dataVo.getClass();
            Field[] declaredFields = clazz.getDeclaredFields();
            for (Field field : declaredFields) {
                if (!fieldList.contains(field.getName())) continue;
                field.setAccessible(true);
                Object value = field.get(dataVo);
                String valueStr = ObjectUtils.isEmpty(value) ? null : value.toString();
                String percentage = BlankUtils.checkBlank(valueStr) ? "0.0%" : valueStr + "%";
                field.set(dataVo, percentage);
            }
        } catch (IllegalAccessException e) {
        }
    }
}
