package com.wbgame.utils;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtils {
    /** 获取两个日期之间的全部天数 **/
    public static List<Date> getDateList(Date start, Date end) {
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(start);

        Calendar endCal = Calendar.getInstance();
        endCal.setTime(end);

        List<Date> list = new ArrayList<Date>();
        while (endCal.compareTo(startCal) >= 0) {
            list.add(startCal.getTime());
            startCal.add(Calendar.DATE, 1);
        }
        return list;
    }

    private static SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
    /** 获取两个日期之间的全部天数 **/
    public static List<String> getDateList(String startDate, String endDate) throws ParseException {
        if (startDate.equals(endDate)) {
            return Lists.newArrayList(startDate);
        }

        Date start = yyyyMMdd.parse(startDate);
        Date end = yyyyMMdd.parse(endDate);

        Calendar startCal = Calendar.getInstance();
        startCal.setTime(start);

        Calendar endCal = Calendar.getInstance();
        endCal.setTime(end);

        List<String> list = new ArrayList<>();
        while (endCal.compareTo(startCal) >= 0) {
            list.add(yyyyMMdd.format(startCal.getTime()));
            startCal.add(Calendar.DATE, 1);
        }
        return list;
    }

    /** 格式化 yyyy-MM-dd */
    private static SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    public static Date to_yyyy_MM_dd(String str) {
        Date date = null;
        try {
            date = yyyy_MM_dd.parse(str);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return date;
    }

    /**
     * 获取两个日期之间的所有日期
     *
     * @param startTime
     *            开始日期
     * @param endTime
     *            结束日期
     * @return
     */
    public static List<String> getDays(String startTime, String endTime) {

        // 返回的日期集合
        List<String> days = new ArrayList<String>();

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        DateFormat resultDateFormat = new SimpleDateFormat("yyyyMMdd");
        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);

            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                days.add(resultDateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return days;
    }

    /** 昨天 **/
    public static Date getYesterdayDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static String formatDateByPattern(Date date,String patttern){
        SimpleDateFormat df = new SimpleDateFormat(patttern);
        String format = df.format(date);
        return format ;
    }

    /** 相对于date，N天前的一天 */
    public static Date getDate(Date date, int n) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, n);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    private final static SimpleDateFormat yyyy_MM_dd_HH_mm_ss = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static String parseTimestampToDateStr(long timestamp) {
        return yyyy_MM_dd_HH_mm_ss.format(new Date(timestamp));
    }
    public static long parseDateStrToTimestamp(String dateStr) throws ParseException {
        return yyyy_MM_dd_HH_mm_ss.parse(dateStr).getTime();
    }

    /**
     * yyyyMMdd to yyyy-MM-dd
     * */
    private static final String HYPHEN = "-";
    public static String changeDateStringToHyphen(String origin) {
        if (!StringUtils.isNumeric(origin) || origin.length() != 8) {
            return origin;
        }
        StringBuilder sb = new StringBuilder(origin.substring(0, 4))
                .append(HYPHEN)
                .append(origin, 4, 6)
                .append(HYPHEN)
                .append(origin, 6, 8);
        return sb.toString();
    }

    /** 获取两个日期之间的全部天数, yyyy-MM-dd **/
    public static List<String> getHyphenDateList(String startDate, String endDate) throws ParseException {
        if (startDate.equals(endDate)) {
            return Lists.newArrayList(changeDateStringToHyphen(startDate));
        }

        Date start = yyyy_MM_dd.parse(startDate);
        Date end = yyyy_MM_dd.parse(endDate);

        Calendar startCal = Calendar.getInstance();
        startCal.setTime(start);

        Calendar endCal = Calendar.getInstance();
        endCal.setTime(end);

        List<String> list = new ArrayList<>();
        while (endCal.compareTo(startCal) >= 0) {
            list.add(yyyy_MM_dd.format(startCal.getTime()));
            startCal.add(Calendar.DATE, 1);
        }
        return list;
    }

    /**
     * yyyy-MM-dd to yyyyMMdd
     * */
    public static String dateDeleteHyphen(String origin) {
        return origin.replaceAll("-", "");
    }

    /**
     * 转换时间戳只  yyyy-MM-dd 格式
     * @param timestamp
     * @return
     */
    public static String parseTimestampToHyphenDateStr(long timestamp) {
        return yyyy_MM_dd.format(new Date(timestamp));
    }
}
