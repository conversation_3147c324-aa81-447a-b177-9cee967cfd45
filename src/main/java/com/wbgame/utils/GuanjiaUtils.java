package com.wbgame.utils;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.joda.time.DateTime;

import java.util.*;
import java.util.stream.Collectors;

public class GuanjiaUtils {
	public final static String KEY = "cc038ea6792e7609fcfa284da61c950c";
	public final static String CPID = "110165";

	public static void main(String[] args) {

		search("20240702112825804349");
	}


	/**
	 * 余额查询
	 * {
	 *     cpid : "10001",
	 *     money : "100.000",
	 *     status : "0000"
	 * }
	 *	0000-查询成功 1001-校验错误 1002-功能暂停
	 * @return
	 */
	public static String money() {
		String url = "http://api.7654322.com/money";
		String datetime = DateTime.now().toString("yyyyMMddHHmmss");

		try {
			// 将上面的数据按顺序存入linkMap
			LinkedHashMap<String, String> linkMap = new LinkedHashMap<String, String>();
			linkMap.put("cpid", CPID);
			linkMap.put("datetime", datetime);


			String signStr = linkMap.entrySet().stream()
					.filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
					.map(entry -> entry.getKey() + "=" + entry.getValue())
					.collect(Collectors.joining("&"));
			signStr = signStr + KEY;

			String md5 = DigestUtils.md5Hex(signStr);
			linkMap.put("sign", md5);

			String httpPost = HttpClientUtils.getInstance().httpPostQuick(url, linkMap);
			return httpPost;
		}catch (Exception e){
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 订单提交
	 * {
	 *     cpid : "10001",
	 *     orderid : "10000000000000000001",
	 *     status : "0000"
	 * }
	 *
	 * 0000	成功：仅代表订单提交成功，和充值结果无关
	 * 1001	失败：校验错误
	 * 1002	失败：功能暂停
	 * 1003	失败：订单号错误
	 * 1004	失败：手机号错误
	 * 1005	失败：订单号已存在，建议通过查询接口确认订单状态
	 * 1006	失败：余额不足
	 * 1007	失败：无效策略
	 * 1008	失败：策略关闭
	 * 1009	失败：策略组关闭
	 * 1010	失败：无效渠道
	 * 1011	失败：渠道关闭
	 * 1012	失败：无效成本
	 * 1013	失败：成本关闭
	 * 1014	失败：相同号码提交频繁
	 *
	 * @return
	 */
	public static String submit(Map<String,String> params) {
		String url = "http://api.7654322.com/submit";
		String datetime = DateTime.now().toString("yyyyMMddHHmmss");
		try {
			// 将上面的数据按顺序存入sortMap
			LinkedHashMap<String, String> linkMap = new LinkedHashMap<String, String>();
			linkMap.put("cpid", CPID);
			linkMap.put("type", "1");
			linkMap.put("orderid", params.get("out_trade_no"));
			linkMap.put("mobile", params.get("phone"));
			linkMap.put("amount", params.get("recharge_amount"));
			linkMap.put("datetime", datetime);

			String signStr = linkMap.entrySet().stream()
					.filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
					.map(entry -> entry.getKey() + "=" + entry.getValue())
					.collect(Collectors.joining("&"));
			signStr = signStr + KEY;

			String md5 = DigestUtils.md5Hex(signStr);
			linkMap.put("sign", md5);

			String httpPost = HttpClientUtils.getInstance().httpPostQuick(url, linkMap);
			return httpPost;
		}catch (Exception e){
			e.printStackTrace();
			return e.getMessage();
		}
	}

	/**
	 * 订单查询
	 * {
	 *     cpid : "10001",
	 *     orderid : "10000000000000000001",
	 *     tradeno : "",
	 *     status : "0001"
	 * }
	 * 0000-充值失败 0001-充值成功 0002-处理中 1001-校验错误 1002-功能暂停 1003-订单号错误 1004-查无此单
	 * @return
	 */
	public static String search(String orderid) {
		String url = "http://api.7654322.com/search";
		String datetime = DateTime.now().toString("yyyyMMddHHmmss");
		try {
			// 将上面的数据按顺序存入sortMap
			LinkedHashMap<String, String> linkMap = new LinkedHashMap<String, String>();
			linkMap.put("cpid", CPID);
			linkMap.put("orderid", orderid); // Note: This is redundant as it's the same as out_trade_no
			linkMap.put("datetime", datetime);

			String signStr = linkMap.entrySet().stream()
					.filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
					.map(entry -> entry.getKey() + "=" + entry.getValue())
					.collect(Collectors.joining("&"));
			signStr = signStr + KEY;

			String md5 = DigestUtils.md5Hex(signStr);
			linkMap.put("sign", md5);

			String httpPost = HttpClientUtils.getInstance().httpPostQuick(url, linkMap);
			return httpPost;
		}catch (Exception e){
			e.printStackTrace();
			return e.getMessage();
		}
	}


	/**
	 * 对请求参数名ASCII码从小到大排序后签名
	 *
	 * @param params 参数Map
	 */
	public static String createSign(Map<String, String> params) {
		SortedMap<String, String> sort = new TreeMap<String, String>(params);
		Iterator<Map.Entry<String, String>> it = sort.entrySet().iterator();
		StringBuffer result = new StringBuffer();
		while (it.hasNext()) {

			Map.Entry<String, String> entry = it.next();
			if (entry.getValue() != null && entry.getValue().length() > 0) {
				result.append(entry.getKey())
						.append("=")
						.append(entry.getValue())
						.append("&");
			}
		}
		return DigestUtils.md5Hex(result.toString().substring(0,-1)).toUpperCase();
	}
	/**
	 * 对请求参数名ASCII码从小到大排序后签名
	 *
	 * @param params 参数Map
	 * @param KEY 签名KEY
	 */
	public static String createSign(Map<String, String> params, String KEY) {
		SortedMap<String, String> sort = new TreeMap<String, String>(params);
		Iterator<Map.Entry<String, String>> it = sort.entrySet().iterator();
		StringBuffer result = new StringBuffer();
		while (it.hasNext()) {

			Map.Entry<String, String> entry = it.next();
			if (entry.getValue() != null && entry.getValue().length() > 0) {
				result.append(entry.getKey())
						.append("=")
						.append(entry.getValue())
						.append("&");
			}
		}
		result.append("apikey=").append(KEY);
		return DigestUtils.md5Hex(result.toString()).toUpperCase();
	}

}
