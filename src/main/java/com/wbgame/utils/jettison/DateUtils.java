package com.wbgame.utils.jettison;

import com.google.common.collect.Lists;

import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 描述
 * @Create 2020-04-23 10:25
 */
public class DateUtils {

    public static final String fmt = "yyyy-MM-dd";

    public static List<String> getBetweenDates(String startTime, String endTime) {
        List<String> dateList = new ArrayList<>();
        try {
            SimpleDateFormat format = new SimpleDateFormat(fmt);
            Date d1 = new SimpleDateFormat(fmt).parse(startTime);//定义起始日期
            Date d2 = new SimpleDateFormat(fmt).parse(endTime);//定义结束日期  可以去当前月也可以手动写日期。
            Calendar dd = Calendar.getInstance();//定义日期实例
            dd.setTime(d1);//设置日期起始时间
            dateList.add(format.format(d1));
            while (dd.getTime().before(d2)) {//判断是否到结束日期
                dd.add(Calendar.DATE, 1);//进行当前日期加1
                SimpleDateFormat sdf = new SimpleDateFormat(fmt);
                String str = sdf.format(dd.getTime());
                dateList.add(str);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return dateList;
    }

    public static String getYesterday() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat df = new SimpleDateFormat(fmt);
        String yesterday = df.format(calendar.getTime());
        return yesterday;
    }

    public static String getBeforeYesterday() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -2);
        SimpleDateFormat df = new SimpleDateFormat(fmt);
        String yesterday = df.format(calendar.getTime());
        return yesterday;
    }

    public static String getDay(int number) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, number);
        SimpleDateFormat df = new SimpleDateFormat(fmt);
        String day = df.format(calendar.getTime());
        return day;
    }

    public static LocalDate StringToLocalDate(String date) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(fmt);
        LocalDate localDate = LocalDate.parse(date, dateTimeFormatter);
        return localDate;
    }

    public static long getDaysBetween(String startDay, String endDay) {
        LocalDate start = StringToLocalDate(startDay);
        LocalDate end = StringToLocalDate(endDay);
        long number = end.until(start, ChronoUnit.DAYS);
        return Math.abs(number);
    }

    /**
     * 判断当前时间是否超过 15:40
     * @return
     */
    public static boolean isExceed() {
        LocalTime now = LocalTime.now();
        int hour = now.getHour();
        int minute = now.getMinute();

        final int trueHour = 15;
        final int trueMinute = 40;

        if (hour < trueHour) {
            return false;
        }

        if (hour == trueHour && minute < trueMinute) {
            return false;
        }

        return true;
    }

    /**
     * 获取当月的第一天和最后一天
     * @return
     */
    public static List<String> getThisMonthFirstAndEnd() {
        LocalDateTime date = LocalDateTime.now();
        LocalDateTime firstday = date.with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime lastday = date.with(TemporalAdjusters.lastDayOfMonth());
        String firstDay = firstday.format(DateTimeFormatter.ofPattern(fmt));
        String lastDay = lastday.format(DateTimeFormatter.ofPattern(fmt));
        return Lists.newArrayList(firstDay, lastDay);
    }

    /**
     * 获取上一个月的第一天和最后一天
     * @return
     */
    public static List<String> getLastMonthFirstAndEnd() {
        LocalDateTime date = LocalDateTime.now().minusMonths(1);
        LocalDateTime firstday = date.with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime lastday = date.with(TemporalAdjusters.lastDayOfMonth());

        String firstDay = firstday.format(DateTimeFormatter.ofPattern(fmt));
        String lastDay = lastday.format(DateTimeFormatter.ofPattern(fmt));
        return Lists.newArrayList(firstDay, lastDay);
    }

    /**
     * 获取某一天的前后日期
     * @param day
     * @param number
     * @return
     */
    public static String getDayByDay(String day, int number) {
        SimpleDateFormat sdf = new SimpleDateFormat(fmt);
        // 将字符串的日期转为Date类型，ParsePosition(0)表示从第一个字符开始解析
        Date date = sdf.parse(day, new ParsePosition(0));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // add方法中的第二个参数n中，正数表示该日期后n天，负数表示该日期的前n天
        calendar.add(Calendar.DATE, number);
        Date changeDate = calendar.getTime();
        String changeDay = sdf.format(changeDate);
        return changeDay;
    }

    /**
     * 计算从当前时间到午夜0点的秒数
     */
    public static long getSecondsUntilMidnight() {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取午夜0点的时间
        LocalDateTime midnight = LocalDateTime.of(today, LocalTime.MAX);
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 计算时间差，并转换为秒
        return ChronoUnit.SECONDS.between(now,midnight);
    }

}
