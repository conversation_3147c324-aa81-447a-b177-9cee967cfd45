package com.wbgame.utils.compare;

import com.google.common.collect.Lists;
import com.wbgame.utils.BlankUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/28
 * @description 通用的同比环比工具
 **/
public class CommonCompareUtils {

    private static final String PERCENT_SUFFIX = "%";
    private static final String FIELD_SUFFIX = "_rate";
    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    //分割号
    private static final String SEPARATOR = "/";

    /**
     * 提交两个相同类型的实体类列表，根据提供的key，对baseList包含的指定的字段求同比或者环比
     * 注：本方法的key肯定会包含日期，日期的不放在keyset中，而是使用两个 function 参数进行计算
     * @param baseList 需要求同比或者环比的实体类列表
     * @param compareList 用作求同比或者环比的实体类列表
     * @param keySet 标记某个实体的唯一标识，尽量多的字段，保证不会重复，否则会计算错误
     * @param valueSet 需要计算同比或者环比的字段
     * @param dateProcess 计算当前日期的方法，用作唯一标识的计算方法
     * @param dateKeyProcess 计算 同比/环比的日期的方法， 比如计算周同比，那就需要指定获取7天前的日期的方法
     * @param <T> 实体类的类型
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    public static <T> void compare(List<T> baseList, List<T> compareList,
                                   List<String> keySet, List<String> valueSet,
                                   Function<T, String> dateProcess,
                                   Function<T, String> dateKeyProcess)
            throws NoSuchFieldException, IllegalAccessException {
        if (CollectionUtils.isEmpty(baseList)) {
            return;
        }

        Class<?> clazz = baseList.get(0).getClass();
        if (CollectionUtils.isEmpty(compareList)) {
            for (T base : baseList) {
                for (String value : valueSet) {
                    Field rateField = clazz.getDeclaredField(value + FIELD_SUFFIX);
                    rateField.setAccessible(true);
                    Object rateFieldValue = rateField.get(base);
                    rateFieldValue = rateFieldValue == null ? "0.00%" : rateFieldValue + SEPARATOR + "0.00%";
                    rateField.set(base, rateFieldValue);
                }
            }
            return;
        }

        // 把用于对比的数据转化为以指定的key的map
        HashMap<String, T> compareMap = new HashMap<>();
        for (T compare : compareList) {
            String key = keyExtract(keySet, clazz, compare, dateProcess);
            compareMap.put(key, compare);
        }

        for (T base : baseList) {
            String key = keyExtract(keySet, clazz, base, dateKeyProcess);
            T compare = compareMap.get(key);
            // 按照指定的value字段，计算所有对应字段的同比\环比
            for (String value : valueSet) {
                Field field = clazz.getDeclaredField(value);
                field.setAccessible(true);
                Field rateField = clazz.getDeclaredField(value+FIELD_SUFFIX);
                rateField.setAccessible(true);
                Object rateFieldValue = rateField.get(base);
                if (compare == null) {
                    rateFieldValue = rateFieldValue == null ? "0.00%" : rateFieldValue + SEPARATOR + "0.00%";
                    rateField.set(base, rateFieldValue);
                    continue;
                }
                Object o1 = field.get(base);
                Object o2 = field.get(compare);
                Class<?> fieldType = field.getType();

                if (o1 == null || o2 == null) {
                    rateFieldValue = rateFieldValue == null ? "0.00%" : rateFieldValue + SEPARATOR + "0.00%";
                    rateField.set(base, rateFieldValue);
                    continue;
                }

                BigDecimal baseB = null;
                BigDecimal baseC = null;
                if (fieldType == String.class) {
                    baseB = new BigDecimal((String) o1);
                    baseC = new BigDecimal((String) o2);
                } else if (fieldType == int.class || fieldType == Integer.class) {
                    baseB = new BigDecimal((Integer) o1);
                    baseC = new BigDecimal((Integer) o2);
                } else if (fieldType == long.class || fieldType == Long.class) {
                    baseB = new BigDecimal((Long) o1);
                    baseC = new BigDecimal((Long) o2);
                } else if (fieldType == double.class || fieldType == Double.class) {
                    baseB = BigDecimal.valueOf((Double) o1);
                    baseC = BigDecimal.valueOf((Double) o2);
                } else {
                    rateFieldValue = rateFieldValue == null ? "0.00%" : rateFieldValue + SEPARATOR + "0.00%";
                    rateField.set(base, rateFieldValue);
                    continue;
                }
                if (baseC.doubleValue() == 0 && baseC.intValue() == 0) {
                    rateFieldValue = rateFieldValue == null ? "0.00%" : rateFieldValue + SEPARATOR + "0.00%";
                    rateField.set(base, rateFieldValue);
                    continue;
                }
                BigDecimal rate = baseB.subtract(baseC)
                        .divide(baseC, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100))
                        .setScale(2, RoundingMode.HALF_UP);
                rateFieldValue = rateFieldValue == null ? rate + PERCENT_SUFFIX : rateFieldValue + SEPARATOR + rate + PERCENT_SUFFIX;
                rateField.set(base, rateFieldValue);
            }
        }
    }

    /**
     * 提交两个相同类型的实体类列表，根据提供的key，对baseList包含的指定的字段求同比或者环比
     * 注：本方法的key肯定会包含日期，日期的不放在keyset中，而是使用两个 function 参数进行计算
     * @param baseList 需要求同比或者环比的实体类列表
     * @param compareList 用作求同比或者环比的实体类列表
     * @param keySet 标记某个实体的唯一标识，尽量多的字段，保证不会重复，否则会计算错误
     * @param valueSet 需要计算同比或者环比的字段
     * @param dateProcess 计算当前日期的方法，用作唯一标识的计算方法 默认日期格式 yyyy-MM-dd
     * @param lastDay 需要对比的数据是几天前的数据，单位 天
     * @param <T> 实体类的类型
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    public static <T> void compare(List<T> baseList, List<T> compareList,
                                   List<String> keySet, List<String> valueSet,
                                   Function<T, String> dateProcess, int lastDay) throws NoSuchFieldException, IllegalAccessException {
        compare(baseList, compareList, keySet, valueSet, dateProcess,
                t -> DateTime.parse(dateProcess.apply(t),
                        DateTimeFormat.forPattern(DEFAULT_DATE_FORMAT)).minusDays(lastDay).toString(DEFAULT_DATE_FORMAT));
    }

    /**
     * 提交两个相同类型的实体类列表，根据提供的key，对baseList包含的指定的字段求同比或者环比
     * 注：本方法的key肯定会包含日期，日期的不放在keyset中，而是使用两个 function 参数进行计算
     * @param baseList 需要求同比或者环比的实体类列表
     * @param compareList 用作求同比或者环比的实体类列表
     * @param keySet 标记某个实体的唯一标识，尽量多的字段，保证不会重复，否则会计算错误 用逗号拼接的字符串
     * @param valueSet 需要计算同比或者环比的字段 用逗号拼接的字符串
     * @param dateProcess 计算当前日期的方法，用作唯一标识的计算方法 默认日期格式 yyyy-MM-dd
     * @param lastDay 需要对比的数据是几天前的数据，单位 天
     * @param <T> 实体类的类型
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    public static <T> void compare(List<T> baseList, List<T> compareList,
                                   String keySet, String valueSet,
                                   Function<T, String> dateProcess, int lastDay) throws NoSuchFieldException, IllegalAccessException {
        ArrayList<String> keyList = Lists.newArrayList(keySet.split(","));
        if (BlankUtils.isBlank(keySet)) {
            keyList = Lists.newArrayList();
        }
        compare(baseList,
                compareList,
                keyList,
                Lists.newArrayList(valueSet.split(",")),
                dateProcess,
                t -> DateTime.parse(dateProcess.apply(t),
                        DateTimeFormat.forPattern(DEFAULT_DATE_FORMAT)).minusDays(lastDay).toString(DEFAULT_DATE_FORMAT));
    }

    private static <T> String keyExtract(List<String> keySet, Class<?> clazz, T base, Function<T, String> dateKeyProcess) throws NoSuchFieldException, IllegalAccessException {
        StringJoiner sj = new StringJoiner("#");
        sj.add(dateKeyProcess.apply(base));
        for (String key : keySet) {
            Field field = clazz.getDeclaredField(key);
            field.setAccessible(true);
            String fieldValue = field.get(base).toString();
            sj.add(fieldValue);
        }
        return sj.toString();
    }


    /**
     * 提交两个相同类型的实体类列表，根据提供的key，对baseList包含的指定的字段求同比或者环比
     * 注：本方法的key肯定会包含日期，日期的不放在keyset中，而是使用两个 function 参数进行计算
     * @param baseList 需要求同比或者环比的实体类列表
     * @param <T> 实体类的类型
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    public static <T> void ratioCompare(List<T> baseList, Map<String,String> valuesMap)
            throws NoSuchFieldException, IllegalAccessException {
        if (CollectionUtils.isEmpty(baseList)) {
            return;
        }
        Class<?> clazz = baseList.get(0).getClass();
        for (T base : baseList) {
            for (Map.Entry<String, String> entry : valuesMap.entrySet()) {
                String key = entry.getKey();
                String[] compareFields = entry.getValue().split(",");
                Field field = clazz.getDeclaredField(key);
                field.setAccessible(true);
                Object o1 = field.get(base);
                //人均pv，占比计算公式：该单元格/总人均pv
                for (String compareField : compareFields) {
                    Field calField = clazz.getDeclaredField(compareField);
                    calField.setAccessible(true);
                    Object o2 = calField.get(base);
                    Field rateField = clazz.getDeclaredField(compareField + FIELD_SUFFIX);
                    rateField.setAccessible(true);
                    Object rateFieldValue = rateField.get(base);
                    if (o1 == null || o2 == null) {
                        rateFieldValue = rateFieldValue == null ? "0.00%" : rateFieldValue + SEPARATOR + "0.00%";
                        rateField.set(base, rateFieldValue);
                        continue;
                    }
                    Class<?> fieldType = field.getType();
                    BigDecimal baseB = null;
                    BigDecimal baseC = null;
                    if (fieldType == String.class) {
                        baseB = new BigDecimal((String) o1);
                        baseC = new BigDecimal((String) o2);
                    } else if (fieldType == int.class || fieldType == Integer.class) {
                        baseB = new BigDecimal((Integer) o1);
                        baseC = new BigDecimal((Integer) o2);
                    } else if (fieldType == long.class || fieldType == Long.class) {
                        baseB = new BigDecimal((Long) o1);
                        baseC = new BigDecimal((Long) o2);
                    } else if (fieldType == double.class || fieldType == Double.class) {
                        baseB = BigDecimal.valueOf((Double) o1);
                        baseC = BigDecimal.valueOf((Double) o2);
                    } else {
                        rateFieldValue = rateFieldValue == null ? "0.00%" : rateFieldValue + SEPARATOR + "0.00%";
                        rateField.set(base, rateFieldValue);
                        continue;
                    }
                    if (baseB.doubleValue() == 0 && baseB.intValue() == 0) {
                        rateFieldValue = rateFieldValue == null ? "0.00%" : rateFieldValue + SEPARATOR + "0.00%";
                        rateField.set(base, rateFieldValue);
                        continue;
                    }
                    BigDecimal rate = baseC.divide(baseB, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                    rateFieldValue = rateFieldValue == null ? rate + PERCENT_SUFFIX : rateFieldValue + SEPARATOR + rate + PERCENT_SUFFIX;
                    rateField.set(base, rateFieldValue);
                }
            }
        }
    }


}
