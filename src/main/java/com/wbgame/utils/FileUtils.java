package com.wbgame.utils;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

import javax.imageio.ImageIO;

public class FileUtils {

	/**
	 * 通过包url获取该包大小 单位MB
	 * @param url1
	 * @return
	 * @throws IOException
	 */
	public static String getFileLength(String url1) throws IOException {
	 	int length = 0;
	 	String size = "";
        URL url;
        try {
            url = new URL(url1);
            HttpURLConnection urlcon = (HttpURLConnection) url.openConnection();
            //根据响应获取文件大小
            length = urlcon.getContentLength();
            BigDecimal fileSize = new BigDecimal(length);
            size = fileSize.divide(new BigDecimal(1048576), 2, RoundingMode.HALF_UP).toString();
            urlcon.disconnect();
        } catch (MalformedURLException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return size;
    }
	
	static byte[] bytes;

	public static void main(String[] args) throws Exception {
		byte[] data = getImageFromNetByUrl("http://a.vigame.cn/push_img/01.png");
		BufferedImage bi = ImageIO.read( new ByteArrayInputStream(data));

		int width = bi.getWidth();

		int height = bi.getHeight();
		System.out.println(width);
		System.out.println(height);

	}

	/**
	 * 通过图片url 转换 byte[]
	 * @param strUrl
	 * @return
	 */
	public static byte[] getImageFromNetByUrl(String strUrl) {
	    try {
	        URL url = new URL(strUrl);
	        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
	        conn.setRequestMethod("GET");
	        conn.setConnectTimeout(5 * 1000);
	        InputStream inStream = conn.getInputStream();// 通过输入流获取图片数据
	        byte[] btImg = readInputStream(inStream);// 得到图片的二进制数据
	        return btImg;
	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	    return null;
	}
	
	
	public static byte[] readInputStream(InputStream inStream) throws Exception {
	    ByteArrayOutputStream outStream = new ByteArrayOutputStream();
	    byte[] buffer = new byte[10240];
	    int len = 0;
	    while ((len = inStream.read(buffer)) != -1) {
	        outStream.write(buffer, 0, len);
	    }
	    inStream.close();
	    return outStream.toByteArray();
	
	}
	
	/**
	 * url zhuan 文件
	 * @param url
	 * @return
	 * @throws Exception
	 */
	public static File getFile(String url) throws Exception {
        //对本地文件命名
        String fileName = url.substring(url.lastIndexOf("."),url.length());
        File file = null;
 
        URL urlfile;
        InputStream inStream = null;
        OutputStream os = null;
        try {
            file = File.createTempFile("net_url", fileName);
            //下载
            urlfile = new URL(url);
            inStream = urlfile.openStream();
            os = new FileOutputStream(file);
 
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = inStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != os) {
                    os.close();
                }
                if (null != inStream) {
                    inStream.close();
                }
 
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
 
        return file;
    }
	
	
	public static byte[] getBytesByFile(File file) {
        try {
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            byte[] data = bos.toByteArray();
            bos.close();
            return data;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


	
}
