package com.wbgame.service.advert;

import com.wbgame.pojo.advert.RiskAnalysisActiveDataVo;
import com.wbgame.pojo.advert.RiskAnalysisRatioDataVo;
import com.wbgame.pojo.advert.RiskAnalysisRegDataVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname RiskAnalysisService
 * @Description TODO
 * @Date 2022/2/28 16:37
 */
public interface RiskAnalysisService {

    /**
     * 新增用户风险类型分析报表数据查询
     * @param vo
     * @return
     */
    List<RiskAnalysisRegDataVo> getRegRiskAnalysis(Map map);

    /**
     * 新增用户风险类型分析报表数据汇总查询
     * @param vo
     * @return
     */
    RiskAnalysisRegDataVo getRegRiskAnalysisSum(Map map);

    /**
     * 活跃用户风险类型分析报表数据查询
     * @param vo
     * @return
     */
    List<RiskAnalysisActiveDataVo> getActiveRiskAnalysis(Map map);

    /**
     * 活跃用户风险类型分析报表数据汇总查询
     * @param vo
     * @return
     */
    RiskAnalysisActiveDataVo getActiveRiskAnalysisSum(Map map);

    /**
     * 风险用户占比分析数据查询
     * @param map
     * @return
     */
    List<RiskAnalysisRatioDataVo> getRatioRiskAnalysis(Map map);

    /**
     * 风险用户占比分析数据汇总查询
     * @param map
     * @return
     */
    RiskAnalysisRatioDataVo getRatioRiskAnalysisSum(Map map);


}
