package com.wbgame.service.advert.impl;

import com.wbgame.mapper.adb.BigDataMapper;
import com.wbgame.mapper.master.AdvertMapper;
import com.wbgame.pojo.advert.MicGameIncomeVo;
import com.wbgame.service.advert.MicGameIncomeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname MicGameIncomeServiceImpl
 * @Description TODO
 * @Date 2022/4/11 19:09
 */
@Service
public class MicGameIncomeServiceImpl implements MicGameIncomeService {

    private final static Logger logger = LoggerFactory.getLogger(MicGameIncomeServiceImpl.class);

    @Autowired
    BigDataMapper bigDataMapper;

    @Autowired
    AdvertMapper advertMapper;

    @Override
    public int syncMicGameIncomeData(String tdate) {
        int succ = 0;
        //拉取大数据新增活跃 ads_wechat_user_cnt_daily
        List<MicGameIncomeVo> regDauDataList = getMicGameRegAndDauData(tdate);
        try {
            logger.info("syncMicGameIncomeData regDauDataList:"+regDauDataList.size());
            if (regDauDataList.size()>0){
                int succ1 = saveMicGameRegAndDauData(regDauDataList);
            }

            //拉取投放 国内投放细分数据查询 dn_report_spend_china_summary 返点消耗
            List<MicGameIncomeVo> rebateCostDataList = getMicGameRebateCostData(tdate);
            logger.info("syncMicGameIncomeData rebateCostDataList:"+rebateCostDataList.size());
            if (rebateCostDataList.size() > 0) {
                int succ2 = updateMicGameRebateCostData(rebateCostDataList);
            }
            //付费数据 新版支付信息查询 支付金额 支付人数 wb_pay_info
            List<MicGameIncomeVo> payDataList = getMicGamePayData(tdate);
            logger.info("syncMicGameIncomeData payDataList:"+payDataList.size());
            if (payDataList.size() > 0) {
                int succ3 = updateMicGamePayData(payDataList);
            }
            //广告收入 变现平台明细表 广告收入 dn_cha_cash_total revenue
            List<MicGameIncomeVo> adIncomeDataList = getMicGameAdIncomeData(tdate);
            logger.info("syncMicGameIncomeData adIncomeDataList:"+adIncomeDataList.size());
            if (adIncomeDataList.size() > 0) {
                int succ4 = updateMicGameAdIncomeData(adIncomeDataList);
            }
            succ++;
        } catch (Exception e) {
            logger.error("syncMicGameIncomeData error:",e);
        }
        return succ;
    }

    @Override
    public List<MicGameIncomeVo> getMicGameRegAndDauData(String tdate) {
        return bigDataMapper.getMicGameRegAndDauData(tdate);
    }

    @Override
    public List<MicGameIncomeVo> getMicGameRebateCostData(String tdate) {
        return advertMapper.getMicGameRebateCostData(tdate);
    }

    @Override
    public List<MicGameIncomeVo> getMicGamePayData(String tdate) {
        return advertMapper.getMicGamePayData(tdate);
    }

    @Override
    public List<MicGameIncomeVo> getMicGameAdIncomeData(String tdate) {
        return advertMapper.getMicGameAdIncomeData(tdate);
    }

    @Override
    public int saveMicGameRegAndDauData(List<MicGameIncomeVo> list) {
        return advertMapper.saveMicGameRegAndDauData(list);
    }

    @Override
    public int updateMicGameRebateCostData(List<MicGameIncomeVo> list) {
        return advertMapper.updateMicGameRebateCostData(list);
    }

    @Override
    public int updateMicGamePayData(List<MicGameIncomeVo> list) {
        return advertMapper.updateMicGamePayData(list);
    }

    @Override
    public int updateMicGameAdIncomeData(List<MicGameIncomeVo> list) {
        return advertMapper.updateMicGameAdIncomeData(list);
    }

    @Override
    public List<MicGameIncomeVo> getMicGameIncomeDataList(Map<String, String> map) {
        return advertMapper.getMicGameIncomeDataList(map);
    }

    @Override
    public MicGameIncomeVo getMicGameIncomeDataListSum(Map<String, String> map) {
        return advertMapper.getMicGameIncomeDataListSum(map);
    }

    @Override
    public int updateMicGameIncomeAdRevenue(List<MicGameIncomeVo> list) {
        return advertMapper.updateMicGameIncomeAdRevenue(list);
    }

	@Override
	public List<MicGameIncomeVo> getPaysMicGameIncomeDataList(Map<String, String> map) {
		return advertMapper.getPaysMicGameIncomeDataList(map);
	}
	@Override
	public MicGameIncomeVo getPaysMicGameIncomeDataListSum(Map<String, String> map) {
		return advertMapper.getPaysMicGameIncomeDataListSum(map);
	}
}
