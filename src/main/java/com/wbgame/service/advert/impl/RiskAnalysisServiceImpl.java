package com.wbgame.service.advert.impl;

import com.wbgame.mapper.adb.RiskAnalysisMapper;
import com.wbgame.pojo.advert.RiskAnalysisActiveDataVo;
import com.wbgame.pojo.advert.RiskAnalysisRatioDataVo;
import com.wbgame.pojo.advert.RiskAnalysisRegDataVo;
import com.wbgame.service.advert.RiskAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname RiskAnalysisServiceImpl
 * @Description TODO
 * @Date 2022/2/28 17:33
 */
@Service
public class RiskAnalysisServiceImpl implements RiskAnalysisService {

    @Autowired
    RiskAnalysisMapper riskAnalysisMapper;

    @Override
    public List<RiskAnalysisRegDataVo> getRegRiskAnalysis(Map map) {
        return riskAnalysisMapper.getRegRiskAnalysis(map);
    }

    @Override
    public RiskAnalysisRegDataVo getRegRiskAnalysisSum(Map map) {
        return riskAnalysisMapper.getRegRiskAnalysisSum(map);
    }

    @Override
    public List<RiskAnalysisActiveDataVo> getActiveRiskAnalysis(Map map) {
        return riskAnalysisMapper.getActiveRiskAnalysis(map);
    }

    @Override
    public RiskAnalysisActiveDataVo getActiveRiskAnalysisSum(Map map) {
        return riskAnalysisMapper.getActiveRiskAnalysisSum(map);
    }

    @Override
    public List<RiskAnalysisRatioDataVo> getRatioRiskAnalysis(Map map) {
        return riskAnalysisMapper.getRatioRiskAnalysis(map);
    }

    @Override
    public RiskAnalysisRatioDataVo getRatioRiskAnalysisSum(Map map) {
        return riskAnalysisMapper.getRatioRiskAnalysisSum(map);
    }
}
