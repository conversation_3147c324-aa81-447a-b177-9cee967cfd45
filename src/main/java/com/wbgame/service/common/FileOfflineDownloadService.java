package com.wbgame.service.common;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.common.param.CreateOfflineFileParam;
import com.wbgame.pojo.common.vo.OfflineFileTaskVo;
import com.wbgame.utils.PageResult;

public interface FileOfflineDownloadService {
	
	public int saveOfflineFileTask(CreateOfflineFileParam param);
	
	public Result<PageResult<OfflineFileTaskVo>> offlineFileTaskList(OfflineFileTaskVo param);
	
	int delete (OfflineFileTaskVo param);
}
