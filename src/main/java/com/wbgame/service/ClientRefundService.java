package com.wbgame.service;

import com.wbgame.pojo.clientRefund.ClientRefundVo;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2023/2/15
 * @class ClientConfigService
 */
public interface ClientRefundService {
    List<ClientRefundVo> selectClientRefund(Date start_date, Date end_date, String[] appid, String[] chaid, String[] paytype, String order_str);

    HashMap<String, Integer> selectClientRefundTotal(Date start_date, Date end_date, String[] appid, String[] chaid, String[] paytype);

    int insertClientRefund(Date tdate, String appid, String chaid, String paytype, Integer pay_revenue, Integer refund_revenue, Integer paynum, String cuser);

    int deleteClientRefund(int id);

    int updateClientRefund(int id, Integer pay_revenue, Integer refund_revenue, Integer paynum, String euser);
}
