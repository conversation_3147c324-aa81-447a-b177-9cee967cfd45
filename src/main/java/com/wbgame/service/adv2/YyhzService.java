package com.wbgame.service.adv2;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.wbgame.pojo.NpPostVo;

public interface YyhzService {
	
	
	// 通用执行语句和查询语句
	public int execSql(String sql); // 直接执行DML sql语句
	public List<String> queryListString(String sql);
	public List<Map<String, Object>> queryListMap(String sql);
	public List<Map<String, String>> queryListMapOne(String sql);
	public List<Map<String, Object>> queryListMapTwo(String sql, Object obj);
	public Map<String, Map<String, Object>> queryListMapOfKey(String sql);
	public <T> List<T> queryListBean(String sql, Class<T> clazz);
	public int execSqlHandle(String sql, Object obj);
	public List<NpPostVo> queryNpPost(String sql);
	public int batchExecSql(Map<String, Object> paramMap);
	public int batchExecSqlTwo(Map<String, Object> paramMap);
	
	// 变现-新用户广告展示pv
	public int updateDnAdshowAdduserPv(Map<String, Object> paramMap);
	
	/** 变现-新用户广告展示 */
	public List<Map<String,Object>> selectAdshowAdduser(Map<String, String> paramMap);
	/** 变现-新用户ltv递进 */
	public List<Map<String,Object>> selectSumLtvAdduser(Map<String, String> paramMap);
	public Map<String,Object> selectSumLtvAdduserSum(Map<String, String> paramMap);
	/** 变现-新用户ecpm分析 */
	public List<Map<String,Object>> selectDnEcpmAdduser(Map<String, String> paramMap);
	public Map<String,Object> selectDnEcpmAdduserSum(Map<String, String> paramMap);
	
	/** 变现-活跃用户广告展示 */
	public List<Map<String,Object>> selectAdshowActuser(Map<String, String> paramMap);
	/** 变现-活跃用户价值分析 */
	public List<Map<String,Object>> selectDnRevenueActuser(Map<String, String> paramMap);
	public Map<String,Object> selectDnRevenueActuserSum(Map<String, String> paramMap);
	/** 变现-活跃用户ecpm分析 */
	public List<Map<String,Object>> selectDnEcpmActuser(Map<String, String> paramMap);
	public Map<String,Object> selectDnEcpmActuserSum(Map<String, String> paramMap);
	
	
	/** 应用分渠道收支数据汇总 */
	public List<Map<String,Object>> selectAppChaRevenueTotal(Map<String, String> paramMap);
	public Map<String,Object> selectAppChaRevenueTotalSum(Map<String, String> paramMap);
	/** 游戏分渠道收支数据汇总 */
	public List<Map<String,Object>> selectGameChaRevenueTotal(Map<String, String> paramMap);
	public Map<String,Object> selectGameChaRevenueTotalSum(Map<String, String> paramMap);
	
	
	/** 变现-数据gap汇总分析 */
	public List<Map<String,Object>> selectAgentShowGap(Map<String, String> paramMap);
	
	/** SDK版本监控 */
	public List<Map<String,Object>> selectSdkRelationReport(Map<String, String> paramMap);
	/** 异常数据监控 */
	public List<Map<String,Object>> selectExtendClickWarn(Map<String, String> paramMap);
	
	
	/** 变现-新用户广告展示-校准后 */
	public List<Map<String,Object>> selectAdshowAdduserTwo(Map<String, String> paramMap);
	/** 变现-新用户ltv递进-校准后 */
	public List<Map<String,Object>> selectSumLtvAdduserTwo(Map<String, String> paramMap);
	public Map<String,Object> selectSumLtvAdduserSumTwo(Map<String, String> paramMap);
	/** 变现-新用户ecpm分析-校准后 */
	public List<Map<String,Object>> selectDnEcpmAdduserTwo(Map<String, String> paramMap);
	public Map<String,Object> selectDnEcpmAdduserSumTwo(Map<String, String> paramMap);
	
	/** 变现-活跃用户广告展示-校准后 */
	public List<Map<String,Object>> selectAdshowActuserTwo(Map<String, String> paramMap);
	/** 变现-活跃用户价值分析-校准后 */
	public List<Map<String,Object>> selectDnRevenueActuserTwo(Map<String, String> paramMap);
	public Map<String,Object> selectDnRevenueActuserSumTwo(Map<String, String> paramMap);
	/** 变现-活跃用户ecpm分析-校准后 */
	public List<Map<String,Object>> selectDnEcpmActuserTwo(Map<String, String> paramMap);
	public Map<String,Object> selectDnEcpmActuserSumTwo(Map<String, String> paramMap);
	
}
