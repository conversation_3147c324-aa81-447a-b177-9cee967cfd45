package com.wbgame.service.adv2;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/10/17 17:45
 */
public interface AdsRevenueAnalyzeService {


    String selectClickActuser(Map<String, String> paramMap);

    void exportAdshowActuser(HttpServletResponse response, String titleValues, String fileName, Map<String, String> paramMap);

    String selectAdRegClickUser(Map<String, String> paramMap);

    void exportAdRegClickUser(HttpServletResponse response, String value, String report, Map<String, String> paramMap);
}
