package com.wbgame.service.adv2.module;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.adv2.CSJAdcodeVo;
import com.wbgame.pojo.adv2.YLHAdcodeVo;
import com.wbgame.pojo.adv2.adcodeModule.CSJAdcodeModuleVo;
import com.wbgame.pojo.adv2.adcodeModule.CommonAdcodeModuleVo;
import com.wbgame.pojo.adv2.adcodeModule.YLHAdcodeModuleVo;
import com.wbgame.service.adv2.adcode.YLHAdcodeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.wbgame.controller.adv2.AdCodeAppConfigController.OPPO_PLATFORM;
import static com.wbgame.controller.adv2.AdCodeAppConfigController.YLH_PLATFORM;

/**
 * <AUTHOR>
 * @date 2023/11/10
 * @description csj 模板service
 **/
@Service
public class YlhModuleService extends BaseModuleService {

    @Resource
    private com.wbgame.service.adv2.adcode.YLHAdcodeService YLHAdcodeService;
    @Override
    public void createModule(Map<String, Object> map) {
        YLHAdcodeModuleVo vo = new YLHAdcodeModuleVo();
        try {
            CommonAdcodeModuleVo.mapToObj(map, vo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        advCommonAdCodeMapper.insertYLHModule(vo);
    }

    @Override
    public List<Map<String, Object>> findModule() {
        return advCommonAdCodeMapper.selectYLHModule(null);
    }

    @Override
    public void modifyModule(Map<String, Object> map) {
        YLHAdcodeModuleVo vo = new YLHAdcodeModuleVo();
        try {
            CommonAdcodeModuleVo.mapToObj(map, vo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        advCommonAdCodeMapper.updateYLHModule(vo);
    }

    @Override
    public int deleteModule(int id) {
        return advCommonAdCodeMapper.deleteYLHModule(id);
    }

    @Override
    public List<Map<String, Object>> selectModuleById(List<Integer> id) {
        return advCommonAdCodeMapper.selectYLHModule(id);
    }

    @Override
    public List<?> buildVo(JSONObject vo, String appid, String channel, String appName, String adExtensionName,String groupName) {
        YLHAdcodeModuleVo ylhAdcodeModuleVo = new YLHAdcodeModuleVo();
        List<JSONObject> resList = new ArrayList<>();
        try {
            CommonAdcodeModuleVo.mapToObj(vo, ylhAdcodeModuleVo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        List<YLHAdcodeVo> ylhAdcodeVos = ylhAdcodeModuleVo.transferToAdcodeVo(appName, appid, channel, adExtensionName,groupName);
        return ylhAdcodeVos;
    }

    @Override
    public List<JSONObject> handleAdcode(JSONObject vo, String userName, HttpServletRequest request, String appid, String channel, String appName, String adExtensionName) {
        YLHAdcodeModuleVo ylhAdcodeModuleVo = new YLHAdcodeModuleVo();
        List<JSONObject> resList = new ArrayList<>();
        try {
            CommonAdcodeModuleVo.mapToObj(vo, ylhAdcodeModuleVo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        List<YLHAdcodeVo> ylhAdcodeVos = ylhAdcodeModuleVo.transferToAdcodeVo(appName, appid, channel, adExtensionName,null);
        String moduleName = ylhAdcodeModuleVo.getModule_name();
        ylhAdcodeVos.forEach(x -> {
            Object res = YLHAdcodeService.createAdcode(x, userName);
            JSONObject json = JSONObject.parseObject((String) res);
            if (json.getIntValue("ret") == 0) {
                json.put("error", YLH_PLATFORM + moduleName + x.getAdExtensionName());
                resList.add(json);
            }
        });
        return resList;
    }

    @Override
    public List<JSONObject> handleAdcode(String userName, HttpServletRequest request, List<?> vos) {
        List<JSONObject> resList = new ArrayList<>();
        List<YLHAdcodeVo> ylhAdcodeVos = (List<YLHAdcodeVo>) vos;
        ylhAdcodeVos.forEach(x -> {
            Object res = YLHAdcodeService.createAdcode(x, userName);
            JSONObject json = JSONObject.parseObject((String) res);
            if (json.getIntValue("ret") == 0) {
                json.put("error", YLH_PLATFORM + x.getPlacement_name());
                resList.add(json);
            }
        });
        return resList;
    }

    @Override
    public JSONObject createPrepareDetect(List<?> vos) {
        List<YLHAdcodeVo> ylhAdVos = (List<YLHAdcodeVo>) vos;
        ArrayList<AdsidField> adsidFields = new ArrayList<>();
        for (YLHAdcodeVo vo : ylhAdVos) {
            AdsidField adsidField = new AdsidField(YLH_PLATFORM, vo.getSdk_ad_type(), vo.getAppid(), vo.getAdExtensionName(), vo.getPlacement_name());
            adsidFields.add(adsidField);
        }
        return commonPrepareDetect(adsidFields);
    }
}
