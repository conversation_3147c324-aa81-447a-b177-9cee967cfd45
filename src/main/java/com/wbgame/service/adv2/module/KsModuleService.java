package com.wbgame.service.adv2.module;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.controller.adv2.KSAdCodeController;
import com.wbgame.pojo.adv2.CSJAdcodeVo;
import com.wbgame.pojo.adv2.KSAdcodeVo;
import com.wbgame.pojo.adv2.YLHAdcodeVo;
import com.wbgame.pojo.adv2.adcodeModule.CSJAdcodeModuleVo;
import com.wbgame.pojo.adv2.adcodeModule.CommonAdcodeModuleVo;
import com.wbgame.pojo.adv2.adcodeModule.KSAdcodeModuleVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.wbgame.controller.adv2.AdCodeAppConfigController.KUAISHOU_PLATFORM;
import static com.wbgame.controller.adv2.AdCodeAppConfigController.OPPO_PLATFORM;

/**
 * <AUTHOR>
 * @date 2023/11/10
 * @description csj 模板service
 **/
@Service
public class KsModuleService extends BaseModuleService {

    @Resource
    private KSAdCodeController ksAdCodeController;
    @Override
    public void createModule(Map<String, Object> map) {
        KSAdcodeModuleVo vo = new KSAdcodeModuleVo();
        try {
            CommonAdcodeModuleVo.mapToObj(map, vo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        advCommonAdCodeMapper.insertKSModule(vo);
    }

    @Override
    public List<Map<String, Object>> findModule() {
        return advCommonAdCodeMapper.selectKSModule(null);
    }

    @Override
    public void modifyModule(Map<String, Object> map) {
        KSAdcodeModuleVo vo = new KSAdcodeModuleVo();
        try {
            CommonAdcodeModuleVo.mapToObj(map, vo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        advCommonAdCodeMapper.updateKSModule(vo);
    }

    @Override
    public int deleteModule(int id) {
        return advCommonAdCodeMapper.deleteKSModule(id);
    }

    @Override
    public List<Map<String, Object>> selectModuleById(List<Integer> ids) {
        return advCommonAdCodeMapper.selectKSModule(ids);
    }

    @Override
    public List<?> buildVo(JSONObject vo, String appid, String channel, String appName, String adExtensionName,String groupName) {
        KSAdcodeModuleVo ksAdcodeModuleVo = new KSAdcodeModuleVo();
        ArrayList<JSONObject> resList = new ArrayList<>();
        try {
            CommonAdcodeModuleVo.mapToObj(vo, ksAdcodeModuleVo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        List<KSAdcodeVo> ksAdcodeVos = ksAdcodeModuleVo.transferToAdcodeVo(appName, appid, channel, adExtensionName,groupName);
        return ksAdcodeVos;
    }

    @Override
    public List<JSONObject> handleAdcode(JSONObject vo, String userName, HttpServletRequest request, String appid, String channel, String appName, String adExtensionName) {
        KSAdcodeModuleVo ksAdcodeModuleVo = new KSAdcodeModuleVo();
        ArrayList<JSONObject> resList = new ArrayList<>();
        try {
            CommonAdcodeModuleVo.mapToObj(vo, ksAdcodeModuleVo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        List<KSAdcodeVo> ksAdcodeVos = ksAdcodeModuleVo.transferToAdcodeVo(appName, appid, channel, adExtensionName,null);
        String moduleName = ksAdcodeModuleVo.getModule_name();
        ksAdcodeVos.forEach(x -> {
            Object res = ksAdCodeController.commonHandleAdcode(x, userName);
            JSONObject json = (JSONObject) res;
            if (json.getIntValue("ret") == 0) {
                json.put("error", KUAISHOU_PLATFORM + moduleName + x.getAdExtensionName());
                resList.add(json);
            }
        });
        return resList;
    }

    @Override
    public List<JSONObject> handleAdcode(String userName, HttpServletRequest request, List<?> vos) {
        ArrayList<JSONObject> resList = new ArrayList<>();
        List<KSAdcodeVo> ksAdcodeVos = (List<KSAdcodeVo>) vos;

        ksAdcodeVos.forEach(x -> {
            Object res = ksAdCodeController.commonHandleAdcode(x, userName);
            JSONObject json = (JSONObject) res;
            if (json.getIntValue("ret") == 0) {
                json.put("error", KUAISHOU_PLATFORM + x.getAdExtensionName());
                resList.add(json);
            }
        });
        return resList;
    }

    @Override
    public JSONObject createPrepareDetect(List<?> vos) {
        List<KSAdcodeVo> ksAdVos = (List<KSAdcodeVo>) vos;
        ArrayList<AdsidField> adsidFields = new ArrayList<>();
        for (KSAdcodeVo vo : ksAdVos) {
            AdsidField adsidField = new AdsidField(KUAISHOU_PLATFORM, vo.getSdk_ad_type(), vo.getAppid(), vo.getAdExtensionName(), vo.getName());
            adsidFields.add(adsidField);
        }
        return commonPrepareDetect(adsidFields);
    }
}
