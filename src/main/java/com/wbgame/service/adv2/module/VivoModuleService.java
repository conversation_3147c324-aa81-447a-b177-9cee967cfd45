package com.wbgame.service.adv2.module;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.adv2.VivoAdVo;
import com.wbgame.pojo.adv2.adcodeModule.CommonAdcodeModuleVo;
import com.wbgame.pojo.adv2.adcodeModule.VivoAdcodeModuleVo;
import com.wbgame.service.adv2.adcode.VivoAdCodeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.wbgame.controller.adv2.AdCodeAppConfigController.VIVO_PLATFORM;

/**
 * <AUTHOR>
 * @date 2024/04/10
 * @description vivo 模板service
 **/
@Service
public class VivoModuleService extends BaseModuleService {

    @Resource
    private VivoAdCodeService vivoAdCodeService;
    @Override
    public void createModule(Map<String, Object> map) {
        VivoAdcodeModuleVo vo = new VivoAdcodeModuleVo();
        try {
            CommonAdcodeModuleVo.mapToObj(map, vo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        advCommonAdCodeMapper.insertVivoModule(vo);
    }

    @Override
    public List<Map<String, Object>> findModule() {
        return advCommonAdCodeMapper.selectVivoModule(null);
    }

    @Override
    public void modifyModule(Map<String, Object> map) {
        VivoAdcodeModuleVo vo = new VivoAdcodeModuleVo();
        try {
            CommonAdcodeModuleVo.mapToObj(map, vo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        advCommonAdCodeMapper.updateVivoModule(vo);
    }

    @Override
    public int deleteModule(int id) {
        return advCommonAdCodeMapper.deleteVivoModule(id);
    }

    @Override
    public List<Map<String, Object>> selectModuleById(List<Integer> id) {
        return advCommonAdCodeMapper.selectVivoModule(id);
    }

    @Override
    public List<?> buildVo(JSONObject vo, String appid, String channel, String appName, String adExtensionName,String groupName) {
        VivoAdcodeModuleVo VivoAdcodeModuleVo = new VivoAdcodeModuleVo();
        try {
            CommonAdcodeModuleVo.mapToObj(vo, VivoAdcodeModuleVo);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        List<VivoAdVo> VivoAdVos = VivoAdcodeModuleVo.transferToAdcodeVo(appName, appid, channel, adExtensionName,groupName);
        return VivoAdVos;
    }

    @Override
    public JSONObject createPrepareDetect(List<?> vos) {
        List<VivoAdVo> VivoAdVos = (List<VivoAdVo>) vos;
        ArrayList<AdsidField> adsidFields = new ArrayList<>();
        for (VivoAdVo vo : VivoAdVos) {
            AdsidField adsidField = new AdsidField(VIVO_PLATFORM, vo.getSdk_ad_type(), vo.getAppid(), vo.getAdExtensionName(), vo.getPosName());
            adsidFields.add(adsidField);
        }
        return commonPrepareDetect(adsidFields);
    }

    @Override
    public List<JSONObject> handleAdcode(String userName, HttpServletRequest request, List<?> vos) {
        List<VivoAdVo> VivoAdVos = (List<VivoAdVo>) vos;
        List<JSONObject> resList = new ArrayList<>();
//        String moduleName = VivoAdcodeModuleVo.getModule_name();
        VivoAdVos.forEach(x -> {
            try {
                Thread.sleep(1000);
            }catch (Exception e) {

            }
            x.setCreateUser(userName);
            x.setModifyUser( userName);
            Object res = vivoAdCodeService.createAdCode(x);
            JSONObject json = JSONObject.parseObject((String) res);
            if (json.getIntValue("ret") == 0) {
                json.put("error", VIVO_PLATFORM + x.getPosName());
                resList.add(json);
            }
        });
        return resList;
    }
}
