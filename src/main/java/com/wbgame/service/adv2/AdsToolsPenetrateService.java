package com.wbgame.service.adv2;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.adv2.bigdata.AdsInsideToolsPenetrateDataVo;
import com.wbgame.pojo.adv2.bigdata.AdsToolsPenetrateDataDTO;
import com.wbgame.pojo.adv2.bigdata.AdsOutsideToolsPenetrateDataVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 工具产品功能渗透报表业务层
 * @Date 2024/12/24 14:26
 */
public interface AdsToolsPenetrateService {

    /**
     * 查询版本和品牌数据
     *
     * @param tableName 表名
     * @return 查询结果
     */
    Result<Map<String, List<String>>> queryVersions(String tableName);


    /**
     * 工具海外产品功能渗透报表查询
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    Result<List<AdsOutsideToolsPenetrateDataVo>> queryOutsideList(AdsToolsPenetrateDataDTO dto);


    /**
     * 工具国内产品功能渗透报表-分页查询接口
     *
     * @param dto 查询条件参数
     * @return 查询结果
     */
    Result<List<AdsInsideToolsPenetrateDataVo>> queryInsideList(AdsToolsPenetrateDataDTO dto);
}
