package com.wbgame.service.adv2.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.adb.bigdata.DnwxChangeMapper;
import com.wbgame.service.AdService;
import com.wbgame.service.adv2.AdsRevenueAnalyzeService;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.StringUtils;
import com.wbgame.utils.UserPermissionsUtils;
import org.apache.commons.collections.MapUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/10/17 17:47
 */
@Service
public class AdsRevenueAnalyzeServiceImpl implements AdsRevenueAnalyzeService {

    @Autowired
    private DnwxChangeMapper dnwxChangeMapper;
    @Autowired
    private AdService adService;
    @Autowired
    private UserPermissionsUtils userPermissionsUtils;


    @Override
    public String selectClickActuser(Map<String, String> paramMap) {
        JSONObject result = new JSONObject();
        try {
            //用户权限添加
            String queryAppids = userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), paramMap.get("app_category"), paramMap.get("appid"));
            paramMap.put("appid",queryAppids);
            // 赋值应用名称
            Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
            PageHelper.startPage(paramMap);
            List<Map<String, Object>> resultList = dnwxChangeMapper.selectClickActuser(paramMap);
            PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(resultList);
            for (Map<String, Object> act : resultList) {
                act.put("add_rate", act.get("add_rate") + "%");
                act.put("seep_rate", act.get("seep_rate") + "%");
                act.put("add_seep_rate", act.get("add_seep_rate") + "%");
                act.put("global_seep_rate", act.get("global_seep_rate") + "%");
                act.put("add_global_seep_rate", act.get("add_global_seep_rate") + "%");
                Map<String, Object> app = appMap.get(act.get("appid") + "");
                if (MapUtils.isNotEmpty(app)) {
                    act.put("appname", app.get("app_name") + "-" + app.get("mapkey"));
                }
                //根据日期获取对应的星期，日期格式：2024-10-14
                String tdate = act.get("tdate") + "";
                if (!StringUtils.isEmpty(tdate)) {
                    String week = DateUtil.dateToWeek(tdate);
                    act.put("tdate", tdate + "(" + week + ")");
                }
                for (int i = 0; i < 15; i++) {
                    act.put("pv" + i, act.get("pv" + i) + "%");
                }
            }
            result.put("ret", 1);
            result.put("data", resultList);
            result.put("totalCount", pageInfo.getTotal());
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }


    @Override
    public void exportAdshowActuser(HttpServletResponse response, String titleValues, String report, Map<String, String> paramMap) {
        try {
            //用户权限添加
            String queryAppids = userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), paramMap.get("app_category"), paramMap.get("appid"));
            paramMap.put("appid",queryAppids);
            List<Map<String, Object>> contentList = dnwxChangeMapper.selectClickActuser(paramMap);
            // 赋值应用名称
            Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
            for (Map<String, Object> act : contentList) {
                //根据日期获取对应的星期，日期格式：2024-10-14
                String tdate = act.get("tdate") + "";
                if (!StringUtils.isEmpty(tdate)) {
                    String week = DateUtil.dateToWeek(tdate);
                    act.put("tdate", tdate + "(" + week + ")");
                }
                act.put("tdate", act.get("tdate") + "");
                Map<String, Object> app = appMap.get(act.get("appid") + "");
                if (MapUtils.isNotEmpty(app)) {
                    act.put("appname", app.get("app_name") + "-" + app.get("mapkey"));
                }
                act.put("add_rate", act.get("add_rate") + "%");
                act.put("seep_rate", act.get("seep_rate") + "%");
                act.put("add_seep_rate", act.get("add_seep_rate") + "%");
                act.put("global_seep_rate", act.get("global_seep_rate") + "%");
                act.put("add_global_seep_rate", act.get("add_global_seep_rate") + "%");
                for (int i = 0; i < 15; i++) {
                    act.put("pv" + i, act.get("pv" + i) + "%");
                }
            }
            Map<String, String> head = new LinkedHashMap<>();
            String[] split = titleValues.split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
            String fileName = report + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
            ExportExcelUtil.exportXLSX(response, contentList, head, fileName);
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
    }


    @Override
    public String selectAdRegClickUser(Map<String, String> paramMap) {
        JSONObject result = new JSONObject();
        try {
            //用户权限添加
            String queryAppids = userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), paramMap.get("app_category"), paramMap.get("appid"));
            paramMap.put("appid",queryAppids);
            PageHelper.startPage(paramMap);
            List<Map<String, Object>> resultList = dnwxChangeMapper.selectAdRegClickUser(paramMap);
            PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(resultList);
            // 赋值应用名称
            Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
            resultList.forEach(act -> {
                Map<String, Object> app = appMap.get(act.get("appid") + "");
                if (app != null)
                    act.put("appname", app.get("app_name"));
                if (act.get("adpos_type") != null) {
                    act.put("adpos_type", converAdType(act.get("adpos_type").toString()));
                }
                //根据日期获取对应的星期，日期格式：2024-10-14
                String tdate = act.get("tdate") + "";
                if (!StringUtils.isEmpty(tdate)) {
                    String week = DateUtil.dateToWeek(tdate);
                    act.put("tdate", tdate + "(" + week + ")");
                }
            });
            result.put("ret", 1);
            result.put("data", resultList);
            result.put("totalCount", pageInfo.getTotal());
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    /**
     * 获取广告类型标识对应的中文名称
     *
     * @param adType
     * @return String typeName
     */
    public static String converAdType(String adType) {
        String name = adType;
        if ("video".equals(adType)) {
            name = "视频";
        } else if ("plaque".equals(adType)) {
            name = "插屏";
        } else if ("splash".equals(adType)) {
            name = "开屏";
        } else if ("banner".equals(adType)) {
            name = "banner";
        } else if ("msg".equals(adType)) {
            name = "信息流";
        } else if ("icon".equals(adType)) {
            name = "icon";
        }
        return name;
    }


    @Override
    public void exportAdRegClickUser(HttpServletResponse response, String value, String report, Map<String, String> paramMap) {
        //用户权限添加
        String queryAppids = userPermissionsUtils.filterAppidByPermission(LOGIN_USER.get(), paramMap.get("app_category"), paramMap.get("appid"));
        paramMap.put("appid",queryAppids);
        List<Map<String, Object>> contentList = dnwxChangeMapper.selectAdRegClickUser(paramMap);
        // 赋值应用名称
        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
        contentList.forEach(act -> {
            Map<String, Object> app = appMap.get(act.get("appid") + "");
            if (app != null)
                act.put("appname", app.get("app_name"));

            if (act.get("adpos_type") != null) {
                act.put("adpos_type", converAdType(act.get("adpos_type").toString()));
            }
            //根据日期获取对应的星期，日期格式：2024-10-14
            String tdate = act.get("tdate") + "";
            if (!StringUtils.isEmpty(tdate)) {
                String week = DateUtil.dateToWeek(tdate);
                act.put("tdate", tdate + "(" + week + ")");
            }
        });
        Map<String, String> headerMap = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                headerMap.put(s[0], s[1]);
            }
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = report + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX(response, contentList, headerMap, fileName);
    }

}
