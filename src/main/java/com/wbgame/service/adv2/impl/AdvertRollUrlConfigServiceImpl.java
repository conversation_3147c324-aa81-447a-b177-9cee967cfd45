package com.wbgame.service.adv2.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.redpack.AdvertRollUrlConfigMapper;
import com.wbgame.pojo.redpack.AdvertRollUrlConfig;
import com.wbgame.pojo.redpack.AdvertRollUrlConfigVO;
import com.wbgame.service.adv2.IAdvertRollUrlConfigService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2023/01/16 016
 * @class: AdvertRollUrlConfigServiceImpl
 * @description:
 */
@Service
public class AdvertRollUrlConfigServiceImpl implements IAdvertRollUrlConfigService {

    @Autowired
    private AdvertRollUrlConfigMapper rollUrlConfigMapper;

    @Override
    public Result<Integer> deleteAdvertRollUrlConfig(List<Integer> appidList) {

        return ObjectUtils.isEmpty(appidList) ? ResultUtils.failure("参数错误")
                : ResultUtils.success(rollUrlConfigMapper.deleteAdvertRollUrlConfig(appidList));
    }

    @Override
    public Result<Integer> insertAdvertRollUrlConfig(AdvertRollUrlConfig record) {

        if (rollUrlConfigMapper.selectAppIdExist(record.getAppid()) != null) {

            return ResultUtils.failure("该产品已配置");
        }

        rollUrlConfigMapper.insertAdvertRollUrlConfig(record);
        return ResultUtils.success();
    }

    @Override
    public Result<PageResult<AdvertRollUrlConfigVO>> selectAdvertRollUrlConfig(AdvertRollUrlConfig example) {

        PageHelper.startPage(example.getStart(), example.getLimit());

        List<AdvertRollUrlConfigVO> list = rollUrlConfigMapper.selectAdvertRollUrlConfig(example);
        return ResultUtils.success(PageResult.page(list));
    }

    @Override
    public Result<Integer> updateAdvertRollUrlConfig(AdvertRollUrlConfig record) {

        rollUrlConfigMapper.updateAdvertRollUrlConfig(record);
        return ResultUtils.success();
    }
}
