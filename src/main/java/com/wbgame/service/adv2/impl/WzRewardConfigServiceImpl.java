package com.wbgame.service.adv2.impl;

import com.wbgame.mapper.slave2.wz.WzRewardConfigMapper;
import com.wbgame.pojo.adv2.wz.HomeWzLimitConfig;
import com.wbgame.pojo.adv2.wz.HomeWzRewardConfig;
import com.wbgame.pojo.adv2.wz.HomeWzTiwaiConfig;
import com.wbgame.pojo.adv2.wz.HomeWzWithdrawalSetting;
import com.wbgame.service.adv2.WzRewardConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class WzRewardConfigServiceImpl implements WzRewardConfigService {
	@Autowired
	private WzRewardConfigMapper wzRewardConfigMapper;


	@Override
	public List<HomeWzRewardConfig> queryAll(HomeWzRewardConfig homeWzRewardConfig) {
		return wzRewardConfigMapper.queryAll(homeWzRewardConfig);
	}

	@Override
	public HomeWzRewardConfig queryById(HomeWzRewardConfig reward) {
		return wzRewardConfigMapper.queryById(reward);
	}

	@Override
	public int insert(HomeWzRewardConfig homeWzRewardConfig) {
		return wzRewardConfigMapper.insert(homeWzRewardConfig);
	}

	@Override
	public int update(HomeWzRewardConfig homeWzRewardConfig) {
		return wzRewardConfigMapper.update(homeWzRewardConfig);
	}

	@Override
	public int delete(HomeWzRewardConfig homeWzRewardConfig) {
		return wzRewardConfigMapper.delete(homeWzRewardConfig);
	}

	@Override
	public List<HomeWzWithdrawalSetting> queryAllWzWithdrawal(HomeWzWithdrawalSetting config) {
		return wzRewardConfigMapper.queryAllWzWithdrawal(config);
	}

	@Override
	public HomeWzWithdrawalSetting queryAllByIdWzWithdrawal(int rewardId) {
		return wzRewardConfigMapper.queryAllByIdWzWithdrawal(rewardId);
	}

	@Override
	public int insertWzWithdrawal(HomeWzWithdrawalSetting config) {
		return wzRewardConfigMapper.insertWzWithdrawal(config);
	}

	@Override
	public int updateWzWithdrawal(HomeWzWithdrawalSetting config) {
		return wzRewardConfigMapper.updateWzWithdrawal(config);
	}

	@Override
	public int deleteWzWithdrawal(HomeWzWithdrawalSetting config) {
		return wzRewardConfigMapper.deleteWzWithdrawal(config);
	}

	@Override
	public List<HomeWzLimitConfig> queryAllWzLimitConfig(HomeWzLimitConfig config) {
		return wzRewardConfigMapper.queryAllWzLimitConfig(config);
	}
	@Override
	public HomeWzLimitConfig queryByIdWzLimitConfig(HomeWzLimitConfig config) {
		return wzRewardConfigMapper.queryByIdWzLimitConfig(config);
	}

	@Override
	public int insertWzLimitConfig(HomeWzLimitConfig config) {
		return wzRewardConfigMapper.insertWzLimitConfig(config);
	}

	@Override
	public int updateWzLimitConfig(HomeWzLimitConfig config) {
		return wzRewardConfigMapper.updateWzLimitConfig(config);
	}

	@Override
	public int deleteWzLimitConfig(HomeWzLimitConfig config) {
		return wzRewardConfigMapper.deleteWzLimitConfig(config);
	}

	@Override
	public List<HomeWzTiwaiConfig> queryAllWzTiwaiConfig(HomeWzTiwaiConfig config) {
		return wzRewardConfigMapper.queryAllWzTiwaiConfig(config);
	}
	@Override
	public int insertWzTiwaiConfig(HomeWzTiwaiConfig config) {
		return wzRewardConfigMapper.insertWzTiwaiConfig(config);
	}
	@Override
	public int updateWzTiwaiConfig(HomeWzTiwaiConfig config) {
		return wzRewardConfigMapper.updateWzTiwaiConfig(config);
	}
	@Override
	public int deleteWzTiwaiConfig(HomeWzTiwaiConfig config) {
		return wzRewardConfigMapper.deleteWzTiwaiConfig(config);
	}

	@Override
	public List<Map<String, String>> getDescList() {
		String query = "select `key`,val from home_wz_tiwai_desc";
		return wzRewardConfigMapper.queryListMapOne(query);
	}

}