package com.wbgame.service.adv2.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.ProductDurationOverseaMapper;
import com.wbgame.pojo.adv2.bigdata.ProductDurationOverseaDTO;
import com.wbgame.pojo.adv2.bigdata.ProductDurationOverseaVo;
import com.wbgame.service.AdService;
import com.wbgame.service.adv2.ProductDurationOverseaService;
import com.wbgame.utils.DataTransUtils;
import com.wbgame.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 工具品牌型号系统留存:国内，海外
 * @Date 2025/01/07 14:26
 */
@Service
public class ProductDurationOverseaServiceImpl implements ProductDurationOverseaService {


    @Autowired
    private ProductDurationOverseaMapper productDurationOverseaMapper;
    @Autowired
    private AdService adService;

    /**
     * 海外工具品牌型号系统留存-分页查询接口
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @Override
    public Result<List<ProductDurationOverseaVo>> queryOutsideList(ProductDurationOverseaDTO dto) {
        List<ProductDurationOverseaVo> resultList;
        long totalSize;
        if (dto.isPageFlag()) {
            //分页页码校验
            dto.checkPageParams();
            //分页查询操作
            PageHelper.startPage(dto.getStart(), dto.getLimit());
            //获取当前操作人权限下的appid
            List<ProductDurationOverseaVo> productDurationList = productDurationOverseaMapper.queryList(dto);
            PageInfo<ProductDurationOverseaVo> pageInfo = new PageInfo<>(productDurationList);
            //结果集封装
            resultList = pageInfo.getList();
            totalSize = pageInfo.getTotal();
        } else {
            //不分页查询操作
            resultList = productDurationOverseaMapper.queryList(dto);
            totalSize = resultList.size();
        }
        //汇总数据
        ProductDurationOverseaVo total = productDurationOverseaMapper.queryTotal(dto);
        //添加百分号
        DataTransUtils.addDataPercentage(total,"user_0_30s,user_30s_1m,user_1_2m,user_2_3m,user_3_5m,user_5_8m,user_8_10m,user_10m_plus");
        //查询应用信息
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);
        for (ProductDurationOverseaVo dataVo : resultList) {
            //不存在时间维度，赋值起始结束时间
            if (StringUtils.isEmpty(dataVo.getTdate())) {
                dataVo.setTdate(dto.getStart_date() + "至" + dto.getEnd_date());
            }
            //封装app_name
            if (appMap.containsKey(dataVo.getAppid())) {
                Map<String, Object> appInfoMap = appMap.get(dataVo.getAppid());
                dataVo.setAppname(appInfoMap.containsKey("app_name") ? appInfoMap.get("app_name").toString() : null);
            }
            //添加百分号
            DataTransUtils.addDataPercentage(dataVo,"user_0_30s,user_30s_1m,user_1_2m,user_2_3m,user_3_5m,user_5_8m,user_8_10m,user_10m_plus");
        }
        //查询结果返回
        return ResultUtils.success(Constants.OK, resultList, total, totalSize);
    }
}
