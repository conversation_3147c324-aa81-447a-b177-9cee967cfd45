package com.wbgame.service.adv2.impl;

import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.adv2.AdvCommonAdCodeMapper;
import com.wbgame.pojo.adv2.*;
import com.wbgame.service.adv2.AdCodeConfigService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.wbgame.common.constants.AdcodeCommonConstants.TOBID_PREFIX;

/**
 * <AUTHOR>
 * @Classname AdverAppConfigServiceImpl
 * @Description TODO
 * @Date 2021/11/25 14:57
 */
@Service
public class AdCodeConfigServiceImpl implements AdCodeConfigService {

    @Autowired
    Adv2Mapper adv2Mapper;

    @Resource
    private AdvCommonAdCodeMapper advCommonAdCodeMapper;

    /**
     * 0529 拉取账户的时候区分channel
     * 如果channel = 'csj' 那需要用csj或者 zhubao 去查找相应的账户
     * */
    @Override
    public List<AdCodeAccountVo> getAdCodeAccountList(AdCodeAccountVo vo) {
        if (BlankUtils.isNotBlank(vo.getTappid()) && !"null".equals(vo.getTappid())) {
            return advCommonAdCodeMapper.getAdCodeAccountListByTappid(vo);
        } else {
            if (BlankUtils.isNotBlank(vo.getChannel()) && "csj".equals(vo.getChannel())) {
                return advCommonAdCodeMapper.getCSJAdCodeAccountList(vo);
            }
            return advCommonAdCodeMapper.getAdCodeAccountList(vo);
        }
    }

    @Override
    public AdCodeAccountVo getAdCodeAccountByMix(AdCodeAccountVo vo) {
        return advCommonAdCodeMapper.getAdCodeAccountByMix(vo);
    }

    @Override
    public ExtendAdsidVo getDnAdSid(String adsid) {
        return adv2Mapper.getDnAdSid(adsid);
    }

    @Override
    public int addAdCodeAccount(AdCodeAccountVo vo) {
        return advCommonAdCodeMapper.addAdCodeAccount(vo);
    }
    @Override
    public int batchAddAdCodeAccount(List<AdCodeAccountVo> list) {
        return advCommonAdCodeMapper.batchAddAdCodeAccount(list);
    }

    @Override
    public int delAdCodeAccount(AdCodeAccountVo vo) {
        return advCommonAdCodeMapper.delAdCodeAccount(vo);
    }

    @Override
    public int updateAdCodeAccount(AdCodeAccountVo vo) {
        return advCommonAdCodeMapper.updateAdCodeAccount(vo);
    }

    @Override
    public List<CSJAdcodeVo> getHeadlineAdCodeList(CSJAdcodeVo vo) {
        return advCommonAdCodeMapper.getHeadlineAdCodeList(vo);
    }

    @Override
    public int saveHeadlineAdCode(CSJAdcodeVo vo) {
        return advCommonAdCodeMapper.saveHeadlineAdCode(vo);
    }

    @Override
    public List<KSAdcodeVo> getKSAdCodeList(KSAdcodeVo vo) {
        return advCommonAdCodeMapper.getKSAdCodeList(vo);
    }

    @Override
    public int saveKSAdCode(KSAdcodeVo vo) {
        return advCommonAdCodeMapper.saveKSAdCode(vo);
    }

    @Override
    public List<YLHAdcodeVo> getYLHAdCodeList(YLHAdcodeVo vo) {
        return advCommonAdCodeMapper.getYLHAdCodeList(vo);
    }

    @Override
    public int saveYLHAdcode(YLHAdcodeVo vo) {
        return advCommonAdCodeMapper.saveYLHAdCode(vo);
    }

    @Override
    public int updateKSCpm(KSAdcodeVo vo) {
        return advCommonAdCodeMapper.updateKSCpm(vo);
    }

    @Override
    public int updateYLHCpm(YLHAdcodeVo vo) {
        return advCommonAdCodeMapper.updateYLHCpm(vo);
    }

    @Override
    public int updateCSJCpm(CSJAdcodeVo vo) {
        return advCommonAdCodeMapper.updateCSJCpm(vo);
    }

    @Override
    public int saveYLHMedium(YLHMediumVo vo) {
        return advCommonAdCodeMapper.saveYLHMedium(vo);
    }

    @Override
    public int batchSaveYLHMedium(List<YLHMediumVo> list) {
        return advCommonAdCodeMapper.batchSaveYLHMedium(list);
    }

    @Override
    public int updateYLHMediumBaseInfo(YLHMediumVo vo) {
        return advCommonAdCodeMapper.updateYLHMediumBaseInfo(vo);
    }

    @Override
    public List<YLHMediumVo> getYLHMediumList(Map<String, String> map) {
        return advCommonAdCodeMapper.getYLHMediumList(map);
    }

    @Override
    public int saveMobvistaAdCode(MobvistaAdVo vo) {
        return advCommonAdCodeMapper.saveMobvistaAdCode(vo);
    }

    @Override
    public List<MobvistaAdVo> getMobvistaAdCode(MobvistaAdVo vo) {
        return advCommonAdCodeMapper.getMobvistaAdCodeList(vo);
    }

    @Override
    public List<String> getTappidList(String platformName, String channel) {
        // 参数校验
        if (BlankUtils.isBlank(platformName) || BlankUtils.isBlank(channel)) {
            throw new IllegalArgumentException("platformName or channel is blank");
        }

        return advCommonAdCodeMapper.selectTappidList(platformName, channel);
    }

    @Override
    public int saveMobvistaAdcodeToDnExtendAdconfig(MobvistaAdVo vo) {
        ExtendAdconfigVo config = new ExtendAdconfigVo();
        // 子渠道为 apple 则不填入
        config.setCha_id("apple".equals(vo.getChannel())? "" : vo.getChannel());

        config.setAppid(vo.getAppid());
        config.setIs_newuser("all");
        config.setUser_group("all");
        config.setAdpos_type(vo.getOpen_type());
        config.setStrategy(vo.getStrategy());

        String agent = "Mobvista";
        String sdkAdType = vo.getSdk_ad_type();
        String appid = vo.getAppid();
        String adExtentionName = vo.getAdExtensionName();
        String adsid = agent+"_"+sdkAdType+"_"+appid+"_"+adExtentionName;

        config.setAdsid(adsid);
        config.setStatu("1");
        //策略带rate 默认为0 其他默认值为5
        if (BlankUtils.checkBlank(vo.getEcpm_price())){
            config.setEcpm(new BigDecimal("0"));
        }else {
            if (vo.getStrategy().contains("rate")){
                config.setEcpm(new BigDecimal("0"));
            }else {
                config.setEcpm(new BigDecimal(vo.getEcpm_price()));
            }
        }
        //2022-09-01 开屏、banner、msg的无价格广告，在广告配置页面生成的预估ecpm默认为0；
        // 插屏、视频的无价格广告，在广告配置页面生成的预估ecpm默认为3  mobvista指定改为5
        String[] ecpm0 = {"splash","banner","msg"};
        if (Arrays.asList(ecpm0).contains(config.getAdpos_type())){
            if (BlankUtils.checkBlank(vo.getEcpm_price())){
                config.setEcpm(new BigDecimal("0"));
            }
        }

        String[] ecpm3 = {"plaque","video"};
        if (Arrays.asList(ecpm3).contains(config.getAdpos_type())){
            if (BlankUtils.checkBlank(vo.getEcpm_price())){
                config.setEcpm(new BigDecimal("5"));
            }
        }

        config.setPriority(0);
        config.setRate(100);
        config.setCuser(vo.getCreateUser());

        if (!BlankUtils.checkBlank(vo.getChannel())&&!"csj".equals(vo.getChannel())) {
            return adv2Mapper.insertDnExtendAdconfig(config);
        } else {
            return adv2Mapper.insertDnExtendAdconfigCSJ(config);
        }
    }

    @Override
    public List<CashCommonApiAccountVo> selectAccountListByPlatform(String platform) {
        return adv2Mapper.selectAccountListByPlatform(platform);
    }

    @Override
    public CashCommonApiAccountVo selectSecretByAccount(String accountName) {
        return adv2Mapper.selectSecretByAccount(accountName);
    }

    @Override
    public Map<String, Map<String, Object>> getAdvPlatformAppInfoList(String platform) {
        return advCommonAdCodeMapper.getAdvPlatformAppInfoList(platform);
    }

}
