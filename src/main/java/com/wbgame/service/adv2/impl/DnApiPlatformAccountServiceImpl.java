package com.wbgame.service.adv2.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adv2.DnApiPlatformAccountMapper;
import com.wbgame.pojo.DnApiPlatformAccount;
import com.wbgame.service.adv2.DnApiPlatformAccountService;
import com.wbgame.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * <AUTHOR>
 * @Description oppo和vivo的广告位账号配置业务层
 * @Date 2025/5/7 11:07
 */
@Service
public class DnApiPlatformAccountServiceImpl implements DnApiPlatformAccountService {

    @Autowired
    private DnApiPlatformAccountMapper dnApiPlatformAccountMapper;

    /**
     * 查询oppo，vivo账号配置表数据
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    @Override
    public Result<List<DnApiPlatformAccount>> queryList(DnApiPlatformAccount dto) {
        dto.checkPageParams();
        PageHelper.startPage(dto.getStart(), dto.getLimit());
        List<DnApiPlatformAccount> resultList = dnApiPlatformAccountMapper.queryList(dto);
        PageInfo<DnApiPlatformAccount> pageInfo = new PageInfo<>(resultList);
        return ResultUtils.success(Constants.OK, pageInfo.getList(), null, pageInfo.getTotal());
    }


    /**
     * 新增数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> insert(DnApiPlatformAccount dto) {
        String username = LOGIN_USER_NAME.get();
        dto.setCreateUser(username);
        dnApiPlatformAccountMapper.insert(dto);
        return ResultUtils.success();
    }


    /**
     * 更新oppo，vivo账号配置表数据
     *
     * @param dto 更新数据
     * @return 更新结果
     */
    @Override
    public Result<String> update(DnApiPlatformAccount dto) {
        //参数校验
        if (StringUtils.isEmpty(dto.getId())) {
            return ResultUtils.failure(Constants.ParamError);
        }
        String username = LOGIN_USER_NAME.get();
        dto.setModifyUser(username);
        //更新数据操作
        dnApiPlatformAccountMapper.updateById(dto);
        return ResultUtils.success();
    }

    /**
     * 根据id删除oppo，vivo账号配置数据
     *
     * @param id 数据唯一标识
     * @return 删除结果
     */
    @Override
    public Result<String> deleteById(String id) {
        if (StringUtils.isEmpty(id)) {
            return ResultUtils.failure(Constants.isNotNull);
        }
        dnApiPlatformAccountMapper.deleteById(id);
        return ResultUtils.success();
    }
}
