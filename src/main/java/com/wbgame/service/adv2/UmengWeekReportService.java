package com.wbgame.service.adv2;

import com.wbgame.pojo.advert.UmChWeekReportVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname UmengWeekReportService
 * @Description TODO
 * @Date 2021/8/23 14:23
 */
public interface UmengWeekReportService {

    /**
     * 获取渠道产品周数据对比明细-新增、活跃、收入、广告arpu、人均启动次数、留存1日、留存7日、日均在线时长
     * @param map
     * @return
     */
    List<UmChWeekReportVo> getUmengWeekReportBaseDataList(Map map);

    /**
     * 获取渠道产品周数据对比明细-消耗、买量新增、买量收入
     * @param map
     * @return
     */
    List<UmChWeekReportVo> getUmengWeekReportRevenueDataList(Map map);


}
