package com.wbgame.service.adv2.tobid;

/**
 * <AUTHOR>
 * @date 2023/10/10
 * @description
 **/

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import org.apache.commons.codec.binary.Hex;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

public class ToBidRequestV2 {

    /**
     * 变现账户名
     */
    private static final String USER_NAME = "<EMAIL>";
    /**
     * Sigmob Secret Key，从平台账户管理->Keys功能页获取。<br/>
     * 注意，这里要使用Sigmob Secret Key，而不是ToBid Secret Key
     */
    private static final String SECRET_KEY = "8a6d6b88b016f092321195d54ba97296";

    private static final String HEADER_NAME_SIGNATURE = "X-Open-Signature";

    /**
     * 固定的rule定义，用于创建默认的分组
     */
    public static final Rule rule = new Rule();

    static {
        rule.type = 7;
        rule.rule = 1;
        rule.content = Lists.newArrayList(2,3,4,5,100);
    }

    /**
     * 签名使用 SHA-1(160bit)算法生成<br/>
     *
     * @param plainText
     * @return
     * @throws NoSuchAlgorithmException
     */
    private static String sha1(String plainText) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("SHA");
        byte[] cipherBytes = messageDigest.digest(plainText.getBytes());
        String cipherStr = Hex.encodeHexString(cipherBytes);
        return cipherStr;
    }

    public static String doRequest(String url, Map<String, Object> requestMap)
            throws IOException, URISyntaxException, NoSuchAlgorithmException {

        // 接口接收post方式的json数据，而不是form表单。所以需要把请求参数转换成json对象
        ObjectMapper om = new ObjectMapper();
        String json = om.writeValueAsString(requestMap);

        // 计算签名.签名由body加Sigmob Secret Key组成
        String plainTxt = json+SECRET_KEY;
        String signStr = sha1(plainTxt);

        StringEntity stringEntity = new StringEntity(json, ContentType.APPLICATION_JSON);

        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(stringEntity);

        // 这里只能是 application/json，请勿添加其他信息，例如：application/json;charset=UTF-8
        httpPost.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        // 增加签名http header
        httpPost.setHeader(HEADER_NAME_SIGNATURE, signStr);
        CloseableHttpClient httpclient = HttpClients.createDefault();

        return httpclient.execute(httpPost, response -> {
//            System.out.println(response.getCode() + " " + response.getReasonPhrase());
            return EntityUtils.toString(response.getEntity());
        });
    }

    /**
     * v2版本的通用请求，主要用于含有复杂构造体的请求
     * @param requestMap 请求参数
     * @param url 请求地址
     * @return 返回字符串
     */
    public static String commonRequest(Map<String, Object> requestMap, String url) throws IOException, URISyntaxException, NoSuchAlgorithmException {

        // 除了业务需要的请求参数外，需要追加时间戳_t参数以及_user参数用于签名验证
        // 服务端会校验时间戳参数 timestamp，请求时间在±5秒内有效
        requestMap.put("_user", USER_NAME);
        requestMap.put("_t", System.currentTimeMillis());

        String s = doRequest(url, requestMap);
        return s;
    }

    public static void main(String[] args) throws IOException, URISyntaxException, NoSuchAlgorithmException {

//        Asserts.check(!USER_NAME.isBlank(), "请填写变现账号");
//        Asserts.check(!SECRET_Key.isBlank(), "请填写Sigmob Secret Key");

//        SourceAuthContent sac = new SourceAuthContent();
//        sac.setPlacementId("f1d71c82b98");
//
//        Map<String, Object> requestMap = new HashMap<String,Object>();
//        requestMap.put("name", "test11");
//        requestMap.put("pub_code", "3962545663756436");
//        requestMap.put("media_network_id", "8239");
//        requestMap.put("header_bidding_switch", 1);
//        requestMap.put("bidding_method", 1);
//        requestMap.put("source_auth_content", sac);
//
//        // 除了业务需要的请求参数外，需要追加时间戳_t参数以及_user参数用于签名验证
//        // 服务端会校验时间戳参数 timestamp，请求时间在±5秒内有效
//        requestMap.put("_user", USER_NAME);
//        requestMap.put("_t", System.currentTimeMillis());
//
//        //创建广告源
//        String createGroupUrl = "https://mmapi.sigmob.com/srv/open/api/union_mediation/ad_source/create";
//        String adSourceUrlList = "https://mmapi.sigmob.com/srv/open/api/union_mediation/ad_source/list";
//        try {
//            String x = doRequest(createGroupUrl, requestMap);
//            System.out.println(x);
//        } catch (NoSuchAlgorithmException | IOException | URISyntaxException e) {
//            e.printStackTrace();
//        }

        String url = "https://mmapi.sigmob.com/srv/open/api/union_mediation/group/create";
        HashMap<String, Object> params = new HashMap<>();
        params.put("rules", Lists.newArrayList(rule));
        params.put("name", "测试组");
        params.put("pub_code","3962545663756436");
        commonRequest(params, url);

    }

    public static class SourceAuthContent {
        public String placementId;
    }

    public static class CsjAppAuthContent {
        public String appId;
    }

    public static class AppAuthContent {
        public String appId;
    }

    public static class CsjSourceAuthContent {
        public String placementId;
        public String templateType; // 广告渲染模式。 注意：横幅广告不支持该字段；插屏广告仅全屏视频形式支持
        public String splashType; // 是否是开屏点睛广告。	0：不是 1：是
        public String subType; // 插屏广告广告类型。	0：插屏广告 1：全屏视频 2：新插屏
        public String ratio; // 插屏广告广告位尺寸比例。传入宽高比，例如3:2 仅subType为0的时候支持该字段	1:1，2:3，3:2
        public String imgType;  //	原生广告素材尺寸。 仅原生自渲染广告支持	0：690*388px 1：228*150px
        public String adSize; // 横幅广告广告位尺寸比例	600x90 600x300 600x400 600x500 600x260 600x150 640x100 690x388
    }

    public static class KsAppAuthContent {
        public String appId;
        public String appName;
    }

    public static class KsSourceAuthContent {
        public String placementId;
        public String playDirection; // 广告播放方向。 注意：仅激励视频和插屏广告支持；插屏仅全屏广告形式支持该字段	0：竖屏 1：横屏
        public String autoPlayMuted; // 视频是否静音播放。 注意：开屏广告不支持	0：否 1：是
        public String splashType; // 是否是开屏V+广告。 仅开屏支持	0：否 1：是
        public String subType; // 插屏广告类型。	0：插屏广告 1：全屏广告
        public String templateType;  //	广告渲染模式。	0：模板渲染 1：自渲染
    }

    public static class YlhAppAuthContent {
        public String appId;
    }

    public static class YlhSourceAuthContent {
        public String placementId;
    }



    public static class Rule {
        private int type;
        private int rule;
        private List<Integer> content;

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getRule() {
            return rule;
        }

        public void setRule(int rule) {
            this.rule = rule;
        }

        public List<Integer> getContent() {
            return content;
        }

        public void setContent(List<Integer> content) {
            this.content = content;
        }
    }
}
