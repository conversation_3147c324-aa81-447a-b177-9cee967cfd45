package com.wbgame.service.adv2.adcode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.constants.PlatformName;
import com.wbgame.mapper.master.AdCodeConfigMapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.yyhzCashReport.CashReportMapper;
import com.wbgame.pojo.adv2.OnlineAdsidDto;
import com.wbgame.pojo.adv2.reportEntity.AdConfigEntity;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.FeishuUtils;
import com.wbgame.utils.HttpClientUtils;
import com.wbgame.utils.jettison.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/29
 * @description
 **/
@Service
@Slf4j
public class VivoAdCodeStatusService {


    private static final String ADPOS_LIST_URL = "https://adnet.vivo.com.cn/api/position/search";
    @Autowired
    private AdMapper adMapper;
    @Resource
    private AdCodeConfigMapper adCodeConfigMapper;
    @Resource
    private CashReportMapper cashReportMapper;
    /* VIVO广告位状态异常告警群 */
    public final static String VIVO_CHANNEL_CHAT_ID = "oc_166b567a169ef0168a16e518a1545054";
    
    /**
     * 获取VIVO广告位状态
     */
    public void getAdSidStatus() {
    	String sql = "select account,tttoken,ttappid,ttparam,cname company from app_channel_config where channel='vivo'  and modify_time is not null";
        List<Map<String, String>> accountList = adMapper.queryListMapOne(sql);
        if(null==accountList||accountList.size()==0){
        	log.info("vivo getAdSidStatus fail,accountList is null");
        	return;
        }
        List<AdConfigEntity> adConfigList = cashReportMapper.getAdConfigByPlatform(PlatformName.VIVO);
        List<Map<String, Object>> appList = cashReportMapper.selectAppidByPkg();
        Map<Object, Object> appCollect = appList.stream().collect(
                Collectors.toMap(en -> en.get("packagename"), e ->  e.get("appid"), (x,y) -> y));
        Map<String, AdConfigEntity> collect = adConfigList.stream().collect(
                Collectors.toMap(en -> en.getCode() + "_" + en.getDn_appid(), e -> e, (x,y) -> y));
        ArrayList<OnlineAdsidDto> onlineAdsidDtos = new ArrayList<>();
        for (Map<String, String> act : accountList) {
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Cookie", act.get("tttoken"));
            String maccount=act.get("account");
        	String company=act.get("company");
            int totalPage = 1;
            for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                long millis = DateTime.now().getMillis();
                String url = ADPOS_LIST_URL+"?pageIndex="+pageNum+"&pageSize=20&order=desc&status=-1&orderBy=createDate&timestamp="+millis;
                try {
                    String httpGet = HttpClientUtils.getInstance().httpGet(url,headMap,true);
                    if(StringUtil.is_nullString(httpGet)){
                    	StringBuilder message = new StringBuilder();
                    	message.append("广告位状态爬取失败,媒体：VIVO,")
                        .append("报错账户：").append(maccount+"-"+company).append(",")
                        .append("媒体响应结果为空");
                    	//发送飞书邮件至孙文风
                    	HttpClientUtils.getInstance().httpGet("https://edc.vigame.cn:6115/fs/sendMsg?robot=robot1&uname=sunwf&msg="+message.toString());	
                    	log.info("getAdSidStatus fail,api result is null,"+",account:"+maccount+",company:"+company);
                    	continue;
                    }
                    if( !"1".equals(JSONObject.parseObject(httpGet).getString("code"))){
                    	String platformMsg=JSONObject.parseObject(httpGet).getJSONObject("errorCodeMsg").getString("errorMsg");
                    	StringBuilder message = new StringBuilder();
                    	message.append("广告位状态爬取失败,媒体：VIVO,")
                        .append("报错账户：").append(maccount+"-"+company).append(",")
                        .append("媒体报错返回："+platformMsg);
                    	HttpClientUtils.getInstance().httpGet("https://edc.vigame.cn:6115/fs/sendMsg?robot=robot1&uname=sunwf&msg="+message.toString());	
                    	log.info("vivo getAdSidStatus fail,api result:"+httpGet+",account:"+maccount+",company:"+company+",url=="+url);
                    	continue;
                    }
                    JSONObject data = JSONObject.parseObject(httpGet).getJSONObject("data");
                    totalPage = ((int) Math.ceil(data.getDoubleValue("totalCount")/20d));

                    JSONArray msgList = data.getJSONArray("positions");
                    if(null == msgList || msgList.isEmpty()){
                    	log.info("vivo getAdSidStatus fail,positions is null"+maccount+company+" url=="+url);
                    	continue;
                    }
                    for (int i = 0; i < msgList.size(); i++) {
                        JSONObject item = msgList.getJSONObject(i);
                        int status=item.getIntValue("status");
                        String positionName = item.getString("positionName");
                        String packageName = item.getString("packageName");
                        String uuid = item.getString("uuid");
                        OnlineAdsidDto onlineAdsidDto = new OnlineAdsidDto();
                        onlineAdsidDto.setPlatform(PlatformName.VIVO);
                        String appId=(String) appCollect.get(packageName);
                        onlineAdsidDto.setAppid(appId);
                        String code = uuid + "_" + appId;
                        onlineAdsidDto.setPlacement_id(uuid);
                        onlineAdsidDto.setPlacement_name(positionName);
                        onlineAdsidDto.setStatu(status);
                        AdConfigEntity config = collect.get(code);
                        if (config != null) {
                        	onlineAdsidDto.setApp_id(config.getAppid());
                            onlineAdsidDto.setAdsid(config.getAd_sid());
                        }
                        onlineAdsidDtos.add(onlineAdsidDto);
                        //判断当前广告位状态是否冻结状态 如是冻结状态并且当前库内的状态为开启状态的话则发出告警
                        Integer statu=cashReportMapper.getAdCodeStatus(uuid);
                        if(2==status&&null!=statu&&1==statu){
                    		 String temp = (
                                     "\n" +
                                         "平台：%s\n" +
                                         "主体账户：%s\n" +
                                         "告警内容：\n%s");
                            String format = String.format(temp, "vivo", maccount+"-"+company, "广告位："+uuid+" 状态由正常变更为冻结");
                    		FeishuUtils.sendMsgToGroupRobot(VIVO_CHANNEL_CHAT_ID, format, "zhenghy,hehp");
                        }
                        //判断当前广告位状态是否和库内的一致，不一致则记录状态更新时间
                        if(null!=statu&&status!=statu){
                        	onlineAdsidDto.setStatus_update_time(DateUtil.getDateTime());
                        }
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                    log.error("vivo getAdSidStatus error,account:"+maccount+",msg:{}",e);
                    StringBuilder message = new StringBuilder();
                    message.append("报错任务：").append("getAdSidStatus vivo-广告位状态爬虫").append("\n")
                            .append("报错账户：").append(maccount+"-"+company).append("\n")
                            .append("报错返回："+e.getMessage()).append("\n");
                }
            }
      }
        List<String> cleanAppids = cashReportMapper.selectCleanAppids();
        List<OnlineAdsidDto> ret = onlineAdsidDtos.stream()
                .filter(dto -> BlankUtils.isNotBlank(dto.getAdsid()))
                .filter(dto -> !cleanAppids.contains(dto.getAppid()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ret)) {
            adCodeConfigMapper.deleteAdsidDtos(PlatformName.VIVO);
            adCodeConfigMapper.insertAdsidDtos(ret);
        }

    }
    public void  sendFailMsg(String msg) {
   	 	Map<String,String> sendMap = new HashMap<>();
   	 	sendMap.put("msg",msg);
        sendMap.put("uname","sunwf");
        sendMap.put("robot","robot3");
        HttpClientUtils.getInstance().httpPost("https://edc.vigame.cn:6115/fs/sendMsg",sendMap);
	}
}
