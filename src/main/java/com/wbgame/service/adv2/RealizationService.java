package com.wbgame.service.adv2;

import com.wbgame.pojo.adv2.AdStyleDataVo;
import com.wbgame.pojo.adv2.ApiPullConfigVo;
import com.wbgame.pojo.adv2.ExtendAdinfoVo;

import java.util.List;
import java.util.Map;

public interface RealizationService {

    /**
     * 产品展示屏蔽配置（新）查询
     * @return
     */
    List<ExtendAdinfoVo> selectNewExtendAdinfoVo(Map<String,String> param);

    int insertNewExtendAdinfoVo(ExtendAdinfoVo record);

    int updateNewExtendAdinfoVo(ExtendAdinfoVo record);

    int deleteNewExtendAdinfoVo(ExtendAdinfoVo record);

    Long countNewExtendAdinfoVo(ExtendAdinfoVo record);

    /**
     * api数据拉取查询
     * @param param
     * @return
     */
    List<ApiPullConfigVo> selectApiPullConfigs(ApiPullConfigVo param);

    int addApiPullConfigs(ApiPullConfigVo param);

    int updateApiPullConfigs(ApiPullConfigVo param);

    int deleteApiPullConfigs(ApiPullConfigVo param);

    /**
     * 广告样式数据
     * @param paramMap
     * @return
     */
    List<AdStyleDataVo> getAdStyleDataList(Map<String,String> paramMap);

    /**
     * 广告样式数据-汇总数据
     * @param paramMap
     * @return
     */
    AdStyleDataVo getAdStyleDataListSum(Map<String,String> paramMap);

    /**
     * 查询同维度的广告展示屏蔽设置数据
     * @param record 查询条件
     * @return 查询符合条件的个数
     */
    Long countNewExtendAdinfoConfig(ExtendAdinfoVo record);
}
