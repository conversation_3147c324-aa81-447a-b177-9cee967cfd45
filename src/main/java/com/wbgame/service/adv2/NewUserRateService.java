package com.wbgame.service.adv2;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.adv2.bigdata.NewUserRateDTO;
import com.wbgame.pojo.adv2.bigdata.NewUserRateVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 网赚报表-新用户通过率及PV数据 业务层
 * @Date 2025/02/06 11:48
 */
public interface NewUserRateService {


    /**
     * 网赚报表-新用户通过率及PV数据 查询
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    Result<List<NewUserRateVo>> queryList(NewUserRateDTO dto);
}
