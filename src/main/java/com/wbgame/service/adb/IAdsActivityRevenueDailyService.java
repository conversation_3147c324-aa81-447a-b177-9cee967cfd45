package com.wbgame.service.adb;


import com.wbgame.common.response.Result;
import com.wbgame.pojo.operate.AdsActivityRevenueDailyDTO;
import com.wbgame.pojo.operate.AdsActivityRevenueDailyVO;
import com.wbgame.utils.PageResult;

import java.util.List;
import java.util.Map;

public interface IAdsActivityRevenueDailyService {

    Result<PageResult<AdsActivityRevenueDailyVO>> selectByExample(AdsActivityRevenueDailyDTO example);

    List<AdsActivityRevenueDailyVO> export(AdsActivityRevenueDailyDTO dto);

    List<AdsActivityRevenueDailyVO> getAcivityPayTotalChart(AdsActivityRevenueDailyDTO dto);

    List<AdsActivityRevenueDailyVO> getOldPayTotalChart(AdsActivityRevenueDailyDTO dto);

    String view(Map<String, String> param);
}