package com.wbgame.service.adb;

import java.util.List;
import java.util.Map;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.jettison.vo.DyxyxInfoFlowReportVo;
import com.wbgame.pojo.operate.ActivePaidUserRetentionVo;
import com.wbgame.pojo.operate.ActiveUserOnlineDurationVo;
import com.wbgame.pojo.operate.UserTagRVo;
import com.wbgame.pojo.operate.WxGameOnlineTimeVo;
import com.wbgame.pojo.param.ActivePaidUserRetentioParam;
import com.wbgame.pojo.param.CommonPageParam;
import com.wbgame.pojo.param.UserTagRParam;
import com.wbgame.pojo.param.WxGameOnlineTimeParam;
import com.wbgame.utils.PageResult;


/**
 * @author: xugx
 * @createDate: 2023/03/23 
 * @class: ActivePaidUserRetentionService
 * @description:
 */
public interface ActivePaidUserRetentionService {


    /**
     * 活跃度用户留存
     */
    Result<PageResult<ActivePaidUserRetentionVo>> list(ActivePaidUserRetentioParam dto);
    
    Result<Map<String,List<ActivePaidUserRetentionVo>>> lineChart (ActivePaidUserRetentioParam dto);
    
    List<ActivePaidUserRetentionVo> export (ActivePaidUserRetentioParam dto);
    
    /**
     * 活跃度用户在线时长
     */
    Result<PageResult<ActiveUserOnlineDurationVo>> OnlineDurationList(ActivePaidUserRetentioParam dto);
    
    List<ActiveUserOnlineDurationVo> OnlineDurationExport (ActivePaidUserRetentioParam dto);
    
    List<ActiveUserOnlineDurationVo> OnlineDurationChart (ActivePaidUserRetentioParam dto);
    
    /**
     * 小游戏设备在线时长和启动次数
     */
    Result<PageResult<WxGameOnlineTimeVo>> wxGameOnlineTimeList(WxGameOnlineTimeParam dto);
    
    List<WxGameOnlineTimeVo> wxGameOnlineTimeExport (WxGameOnlineTimeParam dto);
    
    List<WxGameOnlineTimeVo> wxGameOnlineTimeChart (WxGameOnlineTimeParam dto);
    
    
    /**
     * 用户分R分析
     */
    Result<PageResult<UserTagRVo>> userTagRList(UserTagRParam dto);
    
    List<UserTagRVo> userTagRExport (UserTagRParam dto);
    
    List<UserTagRVo> userTagRChart (UserTagRParam dto);
    
    /**
     * 抖小信息流投放数据报表
     */
    
    Result<PageResult<DyxyxInfoFlowReportVo>> dyXyxInfoFlowList(CommonPageParam dto); 
    
    List<DyxyxInfoFlowReportVo> dyXyxInfoFlowExport(CommonPageParam dto); 

}
