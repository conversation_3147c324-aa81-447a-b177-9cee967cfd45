package com.wbgame.service.adb;

import com.wbgame.common.GeneralReportParam;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.operate.AdsBackflowUserRetentionAnalysisDailyVO;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/8/29
 * @class: IAdsBackflowUserRetentionAnalysisDailyService
 * @description:
 */
public interface IAdsBackflowUserRetentionAnalysisDailyService {

    Result<PageResult<AdsBackflowUserRetentionAnalysisDailyVO>> selectAddedActivityMiniGames(GeneralReportParam generalReportParam);
    List<AdsBackflowUserRetentionAnalysisDailyVO> export(GeneralReportParam generalReportParam);


    List<AdsBackflowUserRetentionAnalysisDailyVO> selectAdsBackflowUserRetentionAnalysisDailyMap(GeneralReportParam generalReportParam);


}
