package com.wbgame.service.adb;

import java.util.List;
import java.util.Map;

import com.wbgame.pojo.NpPostVo;
import com.wbgame.pojo.adv2.DnAdsidCashTotalVo;
import com.wbgame.pojo.adv2.ExtendAdsidVo;

public interface MarketingService {
	
	
	// 通用执行语句和查询语句
	public int execSql(String sql); // 直接执行DML sql语句
	public List<String> queryListString(String sql);
	public List<Map<String, Object>> queryListMap(String sql);
	public List<Map<String, String>> queryListMapOne(String sql);
	public List<Map<String, Object>> queryListMapTwo(String sql, Object obj);
	public Map<String, Map<String, Object>> queryListMapOfKey(String sql);
	public <T> List<T> queryListBean(String sql, Class<T> clazz);
	public int execSqlHandle(String sql, Object obj);
	public List<NpPostVo> queryNpPost(String sql);
	public int batchExecSql(Map<String, Object> paramMap);
	public int batchExecSqlTwo(Map<String, Object> paramMap);
	
	
}
