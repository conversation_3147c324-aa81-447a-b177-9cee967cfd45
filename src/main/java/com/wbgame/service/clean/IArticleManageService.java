package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.ArticleManageDTO;
import com.wbgame.pojo.clean.ArticleManageVO;
import com.wbgame.utils.PageResult;

/**
 * @author: zhangY
 * @createDate: 2022/10/24
 * @class: IArticleManageService
 * @description:
 */
public interface IArticleManageService {

    Result<Integer> updateByIsDeleted(ArticleManageDTO dto);

    Result<Long> insertArticleManage(ArticleManageDTO record);

    Result<PageResult<ArticleManageVO>> selectArticleManage(ArticleManageDTO articleManage);

    Result<Integer> updateArticleManage(ArticleManageDTO record);
}
