package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.SuperWallpaperTitleConfigMapper;
import com.wbgame.pojo.clean.img.SuperWallpaperTitleConfig;
import com.wbgame.service.clean.ISuperWallpaperTitleConfigService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2023/01/09 009
 * @class: SuperWallpaperTitleConfigServiceImpl
 * @description:
 */
@Service
public class SuperWallpaperTitleConfigServiceImpl implements ISuperWallpaperTitleConfigService {

    @Autowired
    private SuperWallpaperTitleConfigMapper titleConfigMapper;

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> deleteSuperWallpaperTitleConfig(List<Integer> typeIdList) {

        return ObjectUtils.isEmpty(typeIdList) ? ResultUtils.failure("参数错误")
                : ResultUtils.success(titleConfigMapper.deleteSuperWallpaperTitleConfig(typeIdList));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> insertSuperWallpaperTitleConfig(SuperWallpaperTitleConfig record) {

        if (titleConfigMapper.selectByType(record.getType()) != null) {

            return ResultUtils.failure("类别名已存在");
        }
        titleConfigMapper.insertSuperWallpaperTitleConfig(record);
        return ResultUtils.success();
    }

    @Override
    public Result<PageResult<SuperWallpaperTitleConfig>> selectSuperWallpaperTitleConfig(SuperWallpaperTitleConfig example) {

        PageHelper.startPage(example.getStart(), example.getLimit());

        List<SuperWallpaperTitleConfig> list = titleConfigMapper.selectSuperWallpaperTitleConfig(example);
        return ResultUtils.success(PageResult.page(list));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateSuperWallpaperTitleConfig(SuperWallpaperTitleConfig record) {

        Integer getId = titleConfigMapper.selectByType(record.getType());
        if (getId != null && !getId.equals(record.getTypeId())) {

            return ResultUtils.failure("类别名已存在");
        }
        int i = titleConfigMapper.updateSuperWallpaperTitleConfig(record);
        return ResultUtils.success(i);
    }
}
