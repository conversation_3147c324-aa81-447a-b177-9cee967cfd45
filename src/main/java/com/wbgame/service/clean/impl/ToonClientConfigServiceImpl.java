package com.wbgame.service.clean.impl;

import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.ToonClientConfigMapper;
import com.wbgame.mapper.clean.master.ToonModelMapper;
import com.wbgame.pojo.clean.toonstory.ToonClientConfig;
import com.wbgame.pojo.clean.toonstory.ToonClientConfigVO;
import com.wbgame.service.clean.IToonClientConfigService;
import com.wbgame.utils.tool.ToolCacheUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2022/10/18
 * @class: ToonClientConfigServiceImpl
 * @description:
 */
@Service
public class ToonClientConfigServiceImpl implements IToonClientConfigService {

    @Autowired
    private ToonClientConfigMapper toonClientConfigMapper;

    @Autowired
    private ToonModelMapper toonModelMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${spring.profiles.active}")
    private String active;


    @Override
    public Result<List<ToonClientConfigVO>> selectToonClientConfig() {

        return ResultUtils.success(toonClientConfigMapper.selectToonClientConfig());
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateToonClientConfig(ToonClientConfig toonClientConfig) {

        if (toonModelMapper.selectIdById(toonClientConfig.getModelId()) == null) {

            return ResultUtils.failure("模板不存在!");
        }

        toonClientConfig.setModifyTime(System.currentTimeMillis());
        int i = toonClientConfigMapper.updateToonClientConfig(toonClientConfig);

        // 更新缓存
        Map<String, Object> param = new LinkedHashMap<>();
        param.put(ToolCacheUtils.REDIS_KEY, Collections.singletonList("toon:config"));

        try {
            ToolCacheUtils.deleteCache(active, restTemplate, param);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.failure("刷新缓存异常");
        }
        return ResultUtils.success(i);
    }
}
