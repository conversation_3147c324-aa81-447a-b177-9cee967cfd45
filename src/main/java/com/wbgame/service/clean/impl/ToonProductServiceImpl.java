package com.wbgame.service.clean.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.ToonProductMapper;
import com.wbgame.pojo.clean.toonstory.ToonProduct;
import com.wbgame.pojo.clean.toonstory.ToonProductDTO;
import com.wbgame.pojo.clean.toonstory.ToonProductVO;
import com.wbgame.service.clean.IToonProductService;
import com.wbgame.utils.PageResult;
import com.wbgame.utils.tool.ToolCacheUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2022/10/17
 * @class: ToonProductServiceImpl
 * @description:
 */
@Service
public class ToonProductServiceImpl implements IToonProductService {

    private ToonProductMapper productMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${spring.profiles.active}")
    private String active;



    @Autowired
    public void setProductMapper(ToonProductMapper productMapper) {
        this.productMapper = productMapper;
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Long> insertToonProduct(ToonProductDTO record) {


        if (productMapper.selectByProductName(record.getProductName()) != null) {

            return ResultUtils.failure("商品名已被使用!");
        }

        ToonProduct toonProduct = new ToonProduct();
        BeanUtils.copyProperties(record, toonProduct);
        long timeMillis = System.currentTimeMillis();
        toonProduct.setCreateTime(timeMillis);
        toonProduct.setModifyTime(timeMillis);
        productMapper.insertToonProduct(toonProduct);


        List<ToonProductVO> voList = productMapper.selectCache(record.getCha());
        // 更新缓存
        Map<String, Object> param = new LinkedHashMap<>();
        param.put("key", "toon:product:cha:" + record.getCha());
        param.put("value", JSON.toJSONString(voList));

        try {
            ToolCacheUtils.flushCache(active, ToolCacheUtils.OTHER, restTemplate, param);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.failure("刷新缓存异常");
        }
        return ResultUtils.success(toonProduct.getId());
    }

    @Override
    public Result<PageResult<ToonProductVO>> selectToonProduct(ToonProductDTO dto) {

        PageHelper.startPage(dto.getStart(), dto.getLimit());
        List<ToonProductVO> list = productMapper.selectToonProduct(dto);
        return ResultUtils.success(PageResult.page(new PageInfo<>(list)));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateToonProduct(ToonProductDTO record) {

        Long getId = productMapper.selectByProductName(record.getProductName());
        if (getId != null && !getId.equals(record.getId())) {

            return ResultUtils.failure("商品名已被使用!");
        }
        ToonProduct toonProduct = new ToonProduct();
        BeanUtils.copyProperties(record, toonProduct);

        toonProduct.setModifyTime(System.currentTimeMillis());
        productMapper.updateToonProduct(toonProduct);

        // 更新缓存
        if (flushCache()) return ResultUtils.failure("刷新缓存异常");
        return ResultUtils.success();
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateProductSort(String userName, List<Long> idList) {

        List<ToonProduct> productList = new LinkedList<>();
        // 修改序号
        long timeMillis = System.currentTimeMillis();
        for (int i = 0; i < idList.size(); i++) {

            ToonProduct  toonProduct = new ToonProduct();
            toonProduct.setId(idList.get(i));
            toonProduct.setSort(i);
            toonProduct.setModifyUser(userName);
            toonProduct.setModifyTime(timeMillis);
            productList.add(toonProduct);
        }

        Integer integer = productMapper.updateProductSort(productList);
        if (flushCache()) return ResultUtils.failure("刷新缓存异常");

        return ResultUtils.success();
    }

    /**
     * 刷新缓存
     * @return true 缓存刷新异常
     */
    private boolean flushCache() {
        // 更新缓存
        Map<String, Object> param = new LinkedHashMap<>();
        List<String> list = new LinkedList<>();
        list.add("toon:product:cha:google");
        list.add("toon:product:cha:apple");
        param.put(ToolCacheUtils.REDIS_KEY, list);

        try {
            ToolCacheUtils.deleteCache(active, restTemplate, param);
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }
        return false;
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateStatus(ToonProductDTO dto) {

        ToonProduct  toonProduct = new ToonProduct();
        BeanUtils.copyProperties(dto, toonProduct);
        toonProduct.setModifyTime(System.currentTimeMillis());
        productMapper.updateStatus(toonProduct);

        // 更新缓存
        if (flushCache()) return ResultUtils.failure("刷新缓存异常");
        return ResultUtils.success();
    }

    @Override
    public Result<List<ToonProductVO>> selectToonProductByCha(String cha) {
        return ResultUtils.success(productMapper.selectToonProductByCha(cha));
    }
}
