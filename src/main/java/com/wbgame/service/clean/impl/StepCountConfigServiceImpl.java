package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.StepCountConfigMapper;
import com.wbgame.pojo.clean.stepcount.StepCountConfig;
import com.wbgame.pojo.clean.stepcount.StepCountConfigVO;
import com.wbgame.service.clean.IStepCountConfigService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/11/03 003
 * @class: StepCountConfigServiceImpl
 * @description:
 */
@Service
public class StepCountConfigServiceImpl implements IStepCountConfigService {

    @Autowired
    private StepCountConfigMapper countConfigMapper;


    @Override
    public Result<Integer> deleteStepCountConfig(List<Long> idList) {

        return ObjectUtils.isEmpty(idList) ? ResultUtils.failure(Constants.ParamError)
                : ResultUtils.success(countConfigMapper.deleteStepCountConfig(idList));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Long> insertStepCountConfig(StepCountConfig record) {

        if (countConfigMapper.selectStepCountExits(record) != null) {

            return ResultUtils.failure("配置已存在！");
        }
        long timeMillis = System.currentTimeMillis();
        record.setCreateTime(timeMillis);
        record.setModifyTime(timeMillis);
        countConfigMapper.insertStepCountConfig(record);
        return ResultUtils.success(record.getId());
    }

    @Override
    public Result<PageResult<StepCountConfigVO>> selectStepCountConfig(StepCountConfig dto) {

        PageHelper.startPage(dto.getStart(), dto.getLimit());
        List<StepCountConfigVO> list = countConfigMapper.selectStepCountConfig(dto);
        return ResultUtils.success(PageResult.page(list));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateStepCountConfig(StepCountConfig record) {

        return ResultUtils.success(countConfigMapper.updateStepCountConfig(record));
    }
}
