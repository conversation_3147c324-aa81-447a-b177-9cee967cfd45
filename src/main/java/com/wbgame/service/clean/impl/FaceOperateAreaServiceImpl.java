package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.FaceOperateAreaMapper;
import com.wbgame.mapper.clean.master.FaceOpreateSortMapper;
import com.wbgame.pojo.clean.face.FaceOperateArea;
import com.wbgame.pojo.clean.face.FaceOpreateSort;
import com.wbgame.pojo.clean.face.FaceProductConfig;
import com.wbgame.pojo.clean.face.FaceProductLinArea;
import com.wbgame.service.clean.IFaceOperateAreaService;
import com.wbgame.utils.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: zhangY
 * @createDate: 2022/12/01 001
 * @class: FaceOperateAreaServiceImpl
 * @description:
 */
@Service
public class FaceOperateAreaServiceImpl implements IFaceOperateAreaService {

    @Autowired
    private FaceOperateAreaMapper operateAreaMapper;

    @Autowired
    private FaceOpreateSortMapper sortMapper;

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> deleteFaceOperateArea(List<Integer> idList) {

        if (ObjectUtils.isEmpty(idList)) {

            return ResultUtils.failure(Constants.ParamError);
        }

//        sortMapper.deleteFaceOpreateSort(idList);
        return ResultUtils.success(operateAreaMapper.deleteFaceOperateArea(idList));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> insertFaceOperateArea(FaceOperateArea record) {

        // 运营地区名校验重复性
        if (operateAreaMapper.selectIdByOperateAreaName(record.getOperateAreaName()) != null) {

            return ResultUtils.failure("运营地区名重复");
        }

        // 地区是否已被选择
        List<FaceOperateArea> faceOperateAreas = operateAreaMapper.selectArea();

        String repeatRegion;
        if (!StringUtils.isBlank(repeatRegion = whetherTheRegionIsSelected(true, record, faceOperateAreas))) {

            return ResultUtils.failure("重复地区【" + repeatRegion + "】");
        }

        operateAreaMapper.insertFaceOperateArea(record);
        return ResultUtils.success();
    }

    @Override
    public Result<PageResult<FaceOperateArea>> selectFaceOperateArea(FaceOperateArea example) {

        PageHelper.startPage(example.getStart(), example.getLimit());
        List<FaceOperateArea> list = operateAreaMapper.selectFaceOperateArea(example);

        return ResultUtils.success(PageResult.page(list));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateFaceOperateArea(FaceOperateArea record) {


        Integer getId = operateAreaMapper.selectIdByOperateAreaName(record.getOperateAreaName());
        // 运营地区名校验重复性
        if (getId != null && !getId.equals(record.getId())) {

            return ResultUtils.failure("运营地区名重复");
        }

        // 地区是否已被选择
        List<FaceOperateArea> faceOperateAreas = operateAreaMapper.selectArea();

        String repeatRegion;
        if (!StringUtils.isBlank(repeatRegion = whetherTheRegionIsSelected(false, record, faceOperateAreas))) {

            return ResultUtils.failure("重复地区【" + repeatRegion + "】");
        }

        operateAreaMapper.updateFaceOperateArea(record);
        return ResultUtils.success();
    }

    /**
     * 校验地区是否被选择
     *
     * @param flag            TRUE 新增 FALSE 修改
     * @param faceOperateArea 需要添加的地区详情
     * @param operateAreaList 已被选择的地区
     * @return
     */
    public String whetherTheRegionIsSelected(boolean flag, FaceOperateArea faceOperateArea, List<FaceOperateArea> operateAreaList) {

        String area = faceOperateArea.getOperateArea();
        Integer id = faceOperateArea.getId();

        // 重复城市字符串
        StringBuilder repeatCity = new StringBuilder();

        if (ObjectUtils.isEmpty(faceOperateArea) || StringUtils.isBlank(area) || ObjectUtils.isEmpty(operateAreaList)) {

            return repeatCity.toString();
        }

        List<String> getAreaList = operateAreaList.stream()
                .filter(operateArea -> {

                    // 当修改时 过滤自己的数据
                    if (!flag && operateArea.getId().equals(id)) {
                        return false;
                    }
                    return true;
                })
                .map(FaceOperateArea::getOperateArea)
                .collect(Collectors.toList());

        for (String areaSimple : area.split(",")) {

            for (String getArea : getAreaList) {

                if (getArea.contains(areaSimple)) {


                    repeatCity.append(areaSimple).append(" ");
                }
            }
        }

        return repeatCity.toString();
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateSort(List<FaceOpreateSort> sortList) {

        List<Integer> productIdList = sortList.stream().map(FaceOpreateSort::getProductId).distinct().collect(Collectors.toList());

        if (productIdList.size() != sortList.size()) {

            return ResultUtils.failure("不能选择多个商品修改");
        }
        for (int i = 0; i < sortList.size(); i++) {

            FaceOpreateSort sort = sortList.get(i);
            sort.setSortId(i);
        }
        sortMapper.updateFaceOpreateSort(sortList);
        return ResultUtils.success();
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> insertFaceOpreateSort(FaceProductLinArea productLinArea, String userName) {

        Integer productId = productLinArea.getProductId();
        List<FaceOpreateSort> sortList = productLinArea.getSortList();
        List<Integer> productIdList = null;

        if (!ObjectUtils.isEmpty(sortList)
                &&
                (productIdList = sortList
                .stream()
                .map(FaceOpreateSort::getProductId)
                .distinct()
                .collect(Collectors.toList())).size() > 1) {

            // 判断商品详情集合中商品id是否全部相同

            return ResultUtils.failure("参数错误");
        }

        // 删除 原有关联地区数据
        sortMapper.deleteFaceOpreateSort(productId);

        // 添加数据
        if (!ObjectUtils.isEmpty(sortList)) {

            sortMapper.insertFaceOpreateSort(sortList);
        }

        // 修改原商品配置表编辑时间
        sortMapper.updateProductConfigModifyTime(productId, userName);

        return ResultUtils.success();
    }

    @Override
    public Result<Map<String, List<FaceProductConfig>>> getAreaListByCha() {

        List<FaceProductConfig> getList = sortMapper.getAreaListByCha();

        Map<String, List<FaceProductConfig>> chaGroupMap = getList
                .stream()
                .collect(Collectors.groupingBy(FaceProductConfig::getCha));

        return ResultUtils.success(chaGroupMap);
    }

    @Override
    public Result<List<FaceProductConfig>> selectConfigByArea(FaceProductConfig area) {

        return ResultUtils.success(sortMapper.selectConfigByArea(area));
    }

    @Override
    public List<FaceOpreateSort> selectOpreateSortByProductId(Integer productId) {
        return sortMapper.selectOpreateSortByProductId(productId);
    }

    @Override
    public Result<List<FaceProductConfig>> selectProductByChannelDownArea(String channel, String area) {

        return ResultUtils.success(sortMapper.selectProductByChannelDownArea(channel, area));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> batchAddProductToSort(List<FaceOpreateSort> sortList) {

        if (ObjectUtils.isEmpty(sortList)) {

            return ResultUtils.failure("参数错误");
        }

        sortMapper.insertFaceOpreateSort(sortList);
        return ResultUtils.success();
    }
}
