package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.BargainUserInfoMapper;
import com.wbgame.pojo.clean.chop.BargainUserInfo;
import com.wbgame.service.clean.IBargainUserInfoService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/11/22 022
 * @class: BargainUserInfoServiceImpl
 * @description:
 */
@Service
public class BargainUserInfoServiceImpl implements IBargainUserInfoService {

    @Autowired
    private BargainUserInfoMapper userInfoMapper;

    @Override
    public Result<PageResult<BargainUserInfo>> selectUserInfo(BargainUserInfo userInfo) {

        PageHelper.startPage(userInfo.getStart(), userInfo.getLimit());
        List<BargainUserInfo> list = userInfoMapper.selectUserInfo(userInfo);

        return ResultUtils.success(PageResult.page(list));
    }
}
