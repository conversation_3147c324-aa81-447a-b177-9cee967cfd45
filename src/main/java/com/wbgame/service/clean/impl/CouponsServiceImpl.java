package com.wbgame.service.clean.impl;

import com.wbgame.mapper.clean.master.CouponsMapper;
import com.wbgame.pojo.clean.coupons.*;
import com.wbgame.service.clean.CouponsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname CouponsServiceImpl
 * @Description TODO
 * @Date 2021/10/11 14:18
 */
@Service
public class CouponsServiceImpl implements CouponsService {

    @Autowired
    CouponsMapper couponsMapper;

    @Override
    public List<CouponsWithdrawVo> getCouponsValidateWithdrawList(CouponsWithdrawVo vo) {
        return couponsMapper.getCouponsValidateWithdrawList(vo);
    }

    @Override
    public List<CouponsWithdrawVo> getCouponsWithdrawList(CouponsWithdrawVo vo) {
        return couponsMapper.getCouponsWithdrawList(vo);
    }

    @Override
    public List<CouponsOrderVo> getCouponsOrderList(CouponsOrderVo vo) {
        return couponsMapper.getCouponsOrderList(vo);
    }

    @Override
    public List<CouponsTbOrderVo> getCouponsTbOrderList(CouponsTbOrderVo vo) {
        return couponsMapper.getCouponsTbOrderList(vo);
    }

    @Override
    public List<CouponsMtOrderVo> getCouponsMtOrderList(CouponsMtOrderVo vo) {
        return couponsMapper.getCouponsMtOrderList(vo);
    }

    @Override
    public List<CouponsRatioConfigVo> getCouponsRatioConfigList(CouponsRatioConfigVo vo) {
        return couponsMapper.getCouponsRatioConfigList(vo);
    }

    @Override
    public int addCouponsRatio(CouponsRatioConfigVo vo) {
        return couponsMapper.addCouponsRatio(vo);
    }

    @Override
    public int updateCouponsRatio(CouponsRatioConfigVo vo) {
        return couponsMapper.updateCouponsRatio(vo);
    }

    @Override
    public int delCouponsRatio(CouponsRatioConfigVo vo) {
        return couponsMapper.delCouponsRatio(vo);
    }


}
