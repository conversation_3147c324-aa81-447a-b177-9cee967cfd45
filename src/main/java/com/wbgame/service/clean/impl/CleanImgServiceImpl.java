package com.wbgame.service.clean.impl;

import com.wbgame.mapper.clean.master.CleanImgMapper;
import com.wbgame.pojo.clean.img.ImgConfigVo;
import com.wbgame.pojo.clean.img.ImgRandNumVo;
import com.wbgame.pojo.clean.img.ImgTypeVo;
import com.wbgame.service.clean.CleanImgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname CleanImgServiceImpl
 * @Description TODO
 * @Date 2021/3/23 11:38
 */
@Service
public class CleanImgServiceImpl implements CleanImgService {

    @Autowired
    CleanImgMapper cleanImgMapper;


    @Override
    public List<ImgTypeVo> selectImgTypeList(ImgTypeVo typeVo) {
        return cleanImgMapper.selectImgTypeList(typeVo);
    }

    @Override
    public List<ImgTypeVo> selectImgTypeByType(Map map) {
        return cleanImgMapper.selectImgTypeByType(map);
    }

    @Override
    public List<ImgTypeVo> selectImgTypeByTypeSort(Map type) {
        return cleanImgMapper.selectImgTypeByTypeSort(type);
    }

    @Override
    public ImgTypeVo selectImgTypeById(ImgTypeVo typeVo) {
        return cleanImgMapper.selectImgTypeById(typeVo);
    }

    @Override
    public int updateImgType(ImgTypeVo typeVo) {
        return cleanImgMapper.updateImgType(typeVo);
    }

    @Override
    public int deleteImgType(ImgTypeVo typeVo) {
        return cleanImgMapper.deleteImgType(typeVo);
    }

    @Override
    public int insertImgType(ImgTypeVo typeVo) {
        return cleanImgMapper.insertImgType(typeVo);
    }

    @Override
    public List<ImgConfigVo> selectImgConfigList(ImgConfigVo img) {
        return cleanImgMapper.selectImgConfigList(img);
    }

    @Override
    public int updateImgConfigStatus(ImgConfigVo img) {
        return cleanImgMapper.updateImgConfigStatus(img);
    }

    @Override
    public int updateImgConfigStatusBatch(ImgConfigVo img) {
        return cleanImgMapper.updateImgConfigStatusBatch(img);
    }

    @Override
    public int updateImgConfigTypeSortBatch(ImgConfigVo img) {
        return cleanImgMapper.updateImgConfigTypeSortBatch(img);
    }

    @Override
    public int deleteImgConfig(ImgConfigVo img) {
        return cleanImgMapper.deleteImgConfig(img);
    }

    @Override
    public int insertImgConfig(ImgConfigVo img) {
        return cleanImgMapper.insertImgConfig(img);
    }

    @Override
    public int updateImgConfig(ImgConfigVo img) {
        return cleanImgMapper.updateImgConfig(img);
    }

    @Override
    public int copyImgConfigBatch(List<ImgConfigVo> list) {
        return cleanImgMapper.copyImgConfigBatch(list);
    }

    @Override
    public int deleteImgConfigByTypeSort(String typeSort) {
        return cleanImgMapper.deleteImgConfigByTypeSort(typeSort);
    }

    @Override
    public int updateImgConfigByTypeChange(ImgTypeVo typeVo) {
        return cleanImgMapper.updateImgConfigByTypeChange(typeVo);
    }

    @Override
    public ImgRandNumVo selectImgRandNum() {
        return cleanImgMapper.selectImgRandNum();
    }

    @Override
    public int updaImgRandNum(ImgRandNumVo vo) {
        return cleanImgMapper.updateImgRandNum(vo);
    }


}
