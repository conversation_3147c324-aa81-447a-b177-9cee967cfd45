package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.clean.master.WifiProductConfigMapper;
import com.wbgame.pojo.clean.WifiProductConfig;
import com.wbgame.pojo.clean.WifiProductConfigDTO;
import com.wbgame.pojo.clean.WifiProductConfigVO;
import com.wbgame.service.clean.IWifiProductConfigService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedList;
import java.util.List;

/**
 * @authoer: zhangY
 * @createDate: 2022/6/20 11:35
 * @class: WifiProductConfigServiceImpl
 * @description:
 */
@Service
public class WifiProductConfigServiceImpl implements IWifiProductConfigService {

    @Autowired
    private WifiProductConfigMapper productConfigMapper;

    @Override
    @Transactional
    public String deleteById(Integer id) {
        return ReturnJson.success(productConfigMapper.deleteById(id));
    }

    @Override
    @Transactional
    public String insertProductConfig(WifiProductConfigDTO productConfig) {

        // 判断商品名称是否存在
        if (productConfigMapper.getIdByProductName(productConfig.getProductName()) != null) {
            return ReturnJson.toErrorJson("商品名存在");
        }

        WifiProductConfig config = new WifiProductConfig();
        BeanUtils.copyProperties(productConfig, config);
        productConfigMapper.insertProductConfig(config);
        return ReturnJson.success();
    }

    @Override
    @Transactional
    public String updateProductConfigById(WifiProductConfigDTO productConfig) {

        Integer id = productConfigMapper.getIdByProductName(productConfig.getProductName());
        if (id != null && !id.equals(productConfig.getId())) {

            return ReturnJson.toErrorJson("商品名称重复");
        }

        WifiProductConfig config = new WifiProductConfig();
        BeanUtils.copyProperties(productConfig, config);
        productConfigMapper.updateProductConfigById(config);
        return ReturnJson.success();
    }

    @Override
    public String selectProductConfigAllList(WifiProductConfigDTO productConfigDTO) {

        PageHelper.startPage(productConfigDTO.getStart(), productConfigDTO.getLimit());
        List<WifiProductConfigVO> list = productConfigMapper.selectProductConfigAllList(productConfigDTO);
        PageInfo<WifiProductConfigVO> pageInfo = new PageInfo<>(list);
        return ReturnJson.success(PageResult.page(pageInfo));
    }

    @Override
    @Transactional
    public String produceSort(String ids, String cha) {


        String regex = "^(\\d,{0,1})*\\d$";
        if (!ids.matches(regex)) {
            return ReturnJson.error(Constants.ParamError);
        }
        String[] idArr = ids.split(",");
        // 根据渠道id 统计 传入 id 有效个数
        int count = productConfigMapper.countProduceByIdsAndCha(idArr, cha);
        if (idArr.length != count) {

            return ReturnJson.error(Constants.ParamError);
        }

        List<WifiProductConfig> list = new LinkedList<>();
        int length = idArr.length;
        for (int i = 0; i < length; i++) {

            WifiProductConfig wifiProductConfig = new WifiProductConfig();
            wifiProductConfig.setId(Integer.parseInt(idArr[i]));
            // 按照ids顺序降序，
//            wifiProductConfig.setSort(length -1 - i);
            // 按照ids顺序升序
            wifiProductConfig.setSort(i);
            list.add(wifiProductConfig);
        }
        productConfigMapper.batchUpdateSort(list);

        return ReturnJson.success();
    }

    @Override
    public String commoditySortingQuery(String cha) {

        return ReturnJson.success(productConfigMapper.commoditySortingQuery(cha));
    }
}
