package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.CoinsGroupConfigMapper;
import com.wbgame.pojo.clean.CoinsGroupConfig;
import com.wbgame.pojo.clean.CoinsGroupConfigDTO;
import com.wbgame.pojo.clean.CoinsGroupConfigVO;
import com.wbgame.service.clean.ICoinsGroupConfigService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/9/20
 * @class: CoinsGroupConfigServiceImpl
 * @description:
 */
@Service
public class CoinsGroupConfigServiceImpl implements ICoinsGroupConfigService {

    private CoinsGroupConfigMapper coinsGroupConfigMapper;

    @Autowired
    public void setCoinsGroupConfigMapper(CoinsGroupConfigMapper coinsGroupConfigMapper) {
        this.coinsGroupConfigMapper = coinsGroupConfigMapper;
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> deleteByIdList(List<Integer> idList) {

        return ObjectUtils.isEmpty(idList) ? ResultUtils.success()
                : ResultUtils.success(coinsGroupConfigMapper.deleteByIdList(idList));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> insertCoinsGroupConfig(CoinsGroupConfigDTO record) {

        if (coinsGroupConfigMapper.selectGroup(record.getGroup()) != null) {

            return ResultUtils.failure("分组名已存在!");
        }

        CoinsGroupConfig coinsGroupConfig = new CoinsGroupConfig();
        BeanUtils.copyProperties(record, coinsGroupConfig);
        return ResultUtils.success(coinsGroupConfigMapper.insertCoinsGroupConfig(coinsGroupConfig));
    }

    @Override
    public Result<PageResult<CoinsGroupConfigVO>> selectCoinsGroupConfig(CoinsGroupConfigDTO dto) {

        PageHelper.startPage(dto.getStart(), dto.getLimit());
        List<CoinsGroupConfigVO> list = coinsGroupConfigMapper.selectCoinsGroupConfig(dto);

        return ResultUtils.success(PageResult.page(new PageInfo<>(list)));
    }

    @Override
    @Transactional(transactionManager = "cleanTransactionManager")
    public Result<Integer> updateCoinsGroupConfig(CoinsGroupConfigDTO record) {

        CoinsGroupConfig getCoinsGroup = coinsGroupConfigMapper.selectGroupToId(record.getGroup());
        if (getCoinsGroup != null && !getCoinsGroup.getId().equals(record.getId())) {

            return ResultUtils.failure("分组名已存在!");
        }

        CoinsGroupConfig coinsGroupConfig = new CoinsGroupConfig();
        BeanUtils.copyProperties(record, coinsGroupConfig);
        return ResultUtils.success(coinsGroupConfigMapper.updateCoinsGroupConfig(coinsGroupConfig));
    }
}
