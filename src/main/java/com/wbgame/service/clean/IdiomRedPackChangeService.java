package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.IdiomRedpackChangeDTO;
import com.wbgame.pojo.clean.IdiomRedpackChangeVO;
import com.wbgame.utils.PageResult;

/**
 * @author: zhangY
 * @createDate: 2022/9/22
 * @class: IdiomRedpackChangeService
 * @description:
 */
public interface IdiomRedPackChangeService {

    Result<PageResult<IdiomRedpackChangeVO>> selectIdiomRedpackChange(IdiomRedpackChangeDTO dto);
}
