package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.toonstory.ToonProductDTO;
import com.wbgame.pojo.clean.toonstory.ToonProductVO;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/10/17
 * @class: IToonProductService
 * @description:
 */
public interface IToonProductService {

    Result<Long> insertToonProduct(ToonProductDTO record);

    Result<PageResult<ToonProductVO>> selectToonProduct(ToonProductDTO dto);

    Result<Integer> updateToonProduct(ToonProductDTO record);

    /**
     * 修改排序
     */
    Result<Integer> updateProductSort(String userName, List<Long> idList);

    Result<Integer> updateStatus(ToonProductDTO dto);

    /**
     * 商品排序：根据渠道查询
     * @param cha
     * @return
     */
    Result<List<ToonProductVO>> selectToonProductByCha(String cha);

}
