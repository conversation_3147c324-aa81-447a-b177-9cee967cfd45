package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.chop.BargainLevelConfig;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/11/15 015
 * @class: IBargainLevelConfigService
 * @description:
 */
public interface IBargainLevelConfigService {

    Result<Integer> deleteLevelConfig(List<Integer> levelIdList);

    Result<Integer> insertLevelConfig(BargainLevelConfig record);

    Result<PageResult<BargainLevelConfig>> selectLevelConfig(BargainLevelConfig levelConfig);

    Result<Integer> updateLevelConfig(BargainLevelConfig record);
}
