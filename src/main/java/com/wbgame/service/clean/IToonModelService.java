package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.toonstory.*;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/10/13
 * @class: IToonModel
 * @description:
 */
public interface IToonModelService {

//    Result<Integer> deleteToonModelByIdList(List<Long> idList);

    Result<Long> insertToonModel(ToonModelDTO dto);

    Result<PageResult<ToonModelVO>> selectToonModel(ToonModelDTO dto);

    Result<Integer> updateToonModel(ToonModelDTO dto);

    /**
     * 修改地区分类
     */
    Result<Integer> updateClassIdOrAreaId(ToonModelRegionRelDTO relDTO);

    /**
     * 修改状态为上架或下架
     */
    Result<Integer> updateStatus(ToonModelDTO dto);


    /**
     * ****************模板排序****************
     */
    List<ToonModelRegionRelVO> selectToonModelByClass(Long classId);
    List<ToonModelRegionRelVO> selectClassByModelId(Long modelId);

    /**
     * 修改排序
     */
    Result<Integer> updateRegionSort(List<Long> relIdList, String userName);

    /**
     * 获取所有模板 (模板下拉数据)
     */
    List<ToonModelVO> selectAllModel();

    /**
     * 置顶
     */
    Result<Integer> updateModelLinkTop(Long id, String userName);

    /**
     * 根据关联表id移除模板和分类关联数据
     */
    Result<Integer> deleteModelRegionById(Long id);

    Result<Integer> updateToonModelTempType(ToonModelDTO dto);

    List<ToonModelRegionRelVO> selectModelByArea(Long areaId);
}
