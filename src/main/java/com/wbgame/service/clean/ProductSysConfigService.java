package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.request.ProductSysConfigRequestParam;
import com.wbgame.pojo.clean.request.ProductSysConfigSaveRequestParam;
import com.wbgame.pojo.clean.response.ProductSysConfigResponseParam;
import com.wbgame.utils.PageResult;

import java.util.List;



/**
 * @author: Xugx
 * @createDate: 2024/3/11
 * @class: ApiPacketParaConfigService
 * @description:
 */
public interface ProductSysConfigService {

    Result<Integer> delete(List<Integer> mchid);

    Result<Integer> add(ProductSysConfigSaveRequestParam param);

    Result<PageResult<ProductSysConfigResponseParam>> page(ProductSysConfigRequestParam param);

    Result<Integer> update(ProductSysConfigSaveRequestParam param);
    
}
