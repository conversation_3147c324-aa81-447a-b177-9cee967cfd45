package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.operate.WindControlLevelDTO;
import com.wbgame.pojo.operate.WindControlLevelVO;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/9/22
 * @class: IWindControlLevelService
 * @description:
 */
public interface IWindControlLevelService {

    Result<Integer> deleteByIdList(List<Integer> idList);


    Result<Integer> insertWindControlLevel(String appid, String prjid, Integer version, String userName);



    Result<PageResult<WindControlLevelVO>> selectWindControlLevel(WindControlLevelDTO dto);


    Result<Integer> updateWindControlLevel(WindControlLevelDTO dto);
    Result<Integer> copyWindControlLevel(WindControlLevelDTO dto);
}
