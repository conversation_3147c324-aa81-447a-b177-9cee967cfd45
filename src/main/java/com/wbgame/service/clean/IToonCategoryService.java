package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.toonstory.ToonCategoryDTO;
import com.wbgame.pojo.clean.toonstory.ToonCategoryVO;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/10/11
 * @class: IToonCategoryService
 * @description:
 */
public interface IToonCategoryService {

    Result<Long> insertToonCategory(ToonCategoryDTO dto);


    Result<Integer> updateToonCategory(ToonCategoryDTO dto);

    Result<PageResult<ToonCategoryVO>> selectToonCategory(ToonCategoryDTO dto);

    /**
     * 根据地区ID获取分类数据
     */
    Result<List<ToonCategoryVO>> selectToonCategoryByArea(Long areaId);

    /**
     * 修改分类序号
     */
    Result<Integer> updateSort(List<Long> idList, Long areaId, String userName);

    /**
     * 根据所有地区ID对应的分类数据
     */
    Result<List<List<ToonCategoryVO>>> selectAllToonCategoryByArea();
}
