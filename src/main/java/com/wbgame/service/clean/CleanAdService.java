package com.wbgame.service.clean;

import com.wbgame.pojo.clean.NpPostVo;

import java.util.List;
import java.util.Map;

public interface CleanAdService {
	
	
	// 通用执行语句和查询语句
	public int execSql(String sql); // 直接执行DML sql语句
	public List<String> queryListString(String sql);
	public List<Map<String, Object>> queryListMap(String sql);
	public List<Map<String, Object>> queryListMapTwo(String sql, Object obj);
	public Map<String, Map<String, Object>> queryListMapOfKey(String sql);
	public <T> List<T> queryListBean(String sql, Class<T> clazz);
	public int execSqlHandle(String sql, Object obj);
	public List<NpPostVo> queryNpPost(String sql);
	public int batchExecSql(Map<String, Object> paramMap);
	
}
