package com.wbgame.service.clean;


import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.AppcfgBlackLog;
import com.wbgame.utils.PageResult;

import java.util.List;

public interface IAppcfgBlackLogService {

    Result<PageResult<AppcfgBlackLog>> selectByExample(AppcfgBlackLog example);

    /**
     * 根据ip删除safe_mark_config 对应markId的配置
     * @param ipList
     * @return
     */
    Result<Integer> deleteIp(List<String> ipList);

}