package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.aipaint.FacePlusAiModelDTO;
import com.wbgame.pojo.clean.aipaint.FacePlusAiModelRegionDTO;
import com.wbgame.pojo.clean.aipaint.FacePlusAiModelRegionVO;
import com.wbgame.pojo.clean.aipaint.FacePlusAiModelVO;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/10/13
 * @class: IToonModel
 * @description:
 */
public interface IFacePlusAiModelService {

    Result<Long> insertModel(FacePlusAiModelDTO dto);

    Result<PageResult<FacePlusAiModelVO>> selectModel(FacePlusAiModelDTO dto);

    Result<Integer> updateModel(FacePlusAiModelDTO dto);

    /**
     * 修改地区分类
     */
    Result<Integer> updateClassIdOrAreaId(FacePlusAiModelRegionDTO relDTO);

    /**
     * 修改状态为上架或下架
     */
    Result<Integer> updateStatus(FacePlusAiModelDTO dto);


    /**
     * ****************模板排序****************
     */
    List<FacePlusAiModelRegionVO> selectModelByArea(Long classId);
    List<FacePlusAiModelRegionVO> selectAreaByModelId(Long modelId);

    /**
     * 修改排序
     */
    Result<Integer> updateRegionSort(List<Long> relIdList, String userName);

    /**
     * 获取所有模板 (模板下拉数据)
     */
//    List<FacePlusAiModelVO> selectAllModel();

    /**
     * 置顶
     */
    Result<Integer> updateModelLinkTop(Long id, String userName);

    /**
     * 根据关联表id移除模板和分类关联数据
     */
    Result<Integer> deleteModelRegionById(Long id);

    Result<Integer> updateModelTempType(FacePlusAiModelDTO dto);
}
