package com.wbgame.service.finance.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.master.ActivityInfoMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.AppInfo;
import com.wbgame.pojo.operate.ActivityInfo;
import com.wbgame.pojo.operate.AdsActivityRevenueDailyVO;
import com.wbgame.service.finance.IActivityInfoService;
import com.wbgame.utils.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: zhangY
 * @createDate: 2023/02/21 021
 * @class: ActivityInfoServcieImpl
 * @description:
 */
@Service("activityInfoService")
public class ActivityInfoServiceImpl implements IActivityInfoService {

    @Resource
    private ActivityInfoMapper activityInfoMapper;

    @Resource
    private YyhzMapper yyhzMapper;

    @Override
    @Transactional(transactionManager = "masterTransactionManager")
    public Result<Integer> deleteActivity(List<Integer> idList) {

        return ObjectUtils.isEmpty(idList) ? ResultUtils.failure("参数错误")
                : ResultUtils.success(activityInfoMapper.deleteActivity(idList));
    }

    @Override
    @Transactional(transactionManager = "masterTransactionManager")
    public Result<Integer> insertActivity(ActivityInfo record) {


        if (activityInfoMapper.selectAppIdLinActivityId(record) != null) {

            return ResultUtils.failure("配置已存在");
        }

        activityInfoMapper.insertActivity(record);

        return ResultUtils.success();
    }

    @Override
    public Result<PageResult<ActivityInfo>> selectByExample(ActivityInfo example) {

        PageHelper.startPage(example.getStart(), example.getLimit());

        List<ActivityInfo> list = activityInfoMapper.selectByExample(example);

        Map<Integer, AppInfo> appMap = yyhzMapper.selectAllAppInfoList();
        for (ActivityInfo vo : list) {

            AppInfo appInfo = appMap.get(vo.getAppId());
            if (appInfo != null) {

                vo.setAppName(appInfo.getAppName());
            }
        }
        return ResultUtils.success(PageResult.page(list));
    }

    @Override
    @Transactional(transactionManager = "masterTransactionManager")
    public Result<Integer> updateActivity(ActivityInfo record) {

        Integer getId = activityInfoMapper.selectAppIdLinActivityId(record);
        if (getId != null && !getId.equals(record.getId())) {

            return ResultUtils.failure("配置已存在");
        }
        activityInfoMapper.updateActivity(record);
        return ResultUtils.success();
    }

    @Override
    public List<ActivityInfo> selectAllList(ActivityInfo activityInfo) {

        return activityInfoMapper.selectByExample(activityInfo);
    }

    @Override
    @Transactional(transactionManager = "masterTransactionManager")
    public Result<Integer> batchImport(List<String[]> dataArrList, String userName) {

        //List<String[]> addArrList = new ArrayList<>();
        //
        //pre : for (int i = 0; i < dataArrList.size(); i++) {
        //
        //    String[] preArr = dataArrList.get(i);
        //
        //     for (int j = i + 1; j < dataArrList.size(); j++) {
        //
        //        String[] currArr = dataArrList.get(j);
        //
        //        if (preArr[0].equals(currArr[0]) && preArr[1].equals(currArr[1])) {
        //
        //           break pre;
        //        }
        //    }
        //
        //    addArrList.add(preArr);
        //}


        if (ObjectUtils.isEmpty(dataArrList)) {

            return ResultUtils.failure("上传数据为空");
        }

        List<ActivityInfo> activityInfoList = selectAllList(null);

        // 将appid和活动id拼接 appIdactivityId
        Map<String, List<ActivityInfo>> activityMap = activityInfoList
                .stream()
                .collect(Collectors.groupingBy(vo ->

                        (vo.getAppId() + vo.getActivityId()).intern()
                ));

        dataArrList.removeIf(dataArr -> activityMap.get(dataArr[0] + dataArr[1]) != null);

        List<ActivityInfo> addInfoList = dataArrList.stream().map(arr -> {

            ActivityInfo activityInfo = new ActivityInfo();
            activityInfo.setAppId(Integer.valueOf(arr[0]));
            activityInfo.setActivityId(arr[1]);
            activityInfo.setActivityName(arr[2]);
            activityInfo.setCreateUser(userName);
            return activityInfo;
        }).distinct().collect(Collectors.toList());

        if (!addInfoList.isEmpty()) {

            activityInfoMapper.batchImport(addInfoList);
        }

        return ResultUtils.success();
    }
}
