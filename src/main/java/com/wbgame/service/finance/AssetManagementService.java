package com.wbgame.service.finance;

import com.wbgame.pojo.finance.AssetMaintenance;
import com.wbgame.pojo.finance.AssetsInfo;
import com.wbgame.pojo.finance.AssetsInfoLog;
import com.wbgame.pojo.finance.AssetsInfoTable;

import java.util.List;
import java.util.Map;

/**
 * 资产管理
 *
 * <AUTHOR>
 * @date 2020/10/16
 */
public interface AssetManagementService {
    List<AssetsInfoTable> selectAssertsTable();

    List<AssetsInfoTable> selectAssertsDepTable();

    List<AssetsInfoTable> selectAssertsDateTable();

    List<AssetsInfoLog> selectAssertsLog(Map<String, Object> map);

    List<AssetsInfo> selectAsserts(Map<String, Object> map);

    int addAsserts(Map<String, Object> map);

    void insertAssertsLog(AssetsInfoLog assertsLogVo);

    int updateAsserts(Map<String, Object> map);

    int handerAsserts(Map<String, Object> map);

    void batchAddAsserts(List<AssetsInfo> list);

    void batchAssertsLog(List<AssetsInfoLog> listlog);

    List<AssetMaintenance> selectAssetMaintenance(AssetMaintenance assetMaintenance);

    int insertAssetMaintenance(AssetMaintenance assetMaintenance);

    int updateAssetMaintenance(AssetMaintenance assetMaintenance);

    int deleteAssetMaintenance(AssetMaintenance assetMaintenance);

    Long existAssertsByAssetsid(String assetsid);

    void deleteAssert(String id);

    void batchupdateAsserts(Map<String, Object> param);
}
