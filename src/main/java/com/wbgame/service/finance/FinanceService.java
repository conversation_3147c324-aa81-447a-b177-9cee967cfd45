package com.wbgame.service.finance;

import com.wbgame.pojo.PredictIncomeOverview;
import com.wbgame.pojo.finance.FinanceCpsCost;
import com.wbgame.pojo.finance.*;
import com.wbgame.pojo.jettison.report.InfoResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 财务管理
 *
 * <AUTHOR>
 * @date 2020/10/16
 */
public interface FinanceService {

    int updateFinanceAppidMoney(FinanceAppidMoney financeAppidMoney);

    int syncFinanceAppid(FinanceAppidMoney financeAppidMoney);

    int insertFinanceAppidMoney(List<FinanceAppidMoney> list);

    int deleteFinanceAppidMoney(FinanceAppidMoney financeAppidMoney);

    List<FinanceAppidMoney> selectFinanceAppidMoney(Map<String, Object> map);

    int deleteFinanceCpsCost(FinanceCpsCost financeCpsCost);

    int insertFinanceCpsCost(List<FinanceCpsCost> list);

    int updateFinanceCpsCost(FinanceCpsCost financeCpsCost);

    List<FinanceCpsCost> selectFinanceCpsCost(Map<String, Object> map);

    int deleteFinanceDevCost(FinanceDevCost financeDevCost);

    int insertFinanceDevCost(List<FinanceDevCost> list);

    int updateFinanceDevCost(FinanceDevCost financeDevCost);

    List<FinanceDevCost> selectFinanceDevCost(Map<String, Object> map);

    int insertFinanceOtherCost(List<FinanceOtherCost> list);

    int deleteFinancePlfIncome(FinancePlfIncome financePlfIncome);

    int insertFinancePlfIncome(List<FinancePlfIncome> list);

    int updateFinancePlfIncome(FinancePlfIncome financePlfIncome);

    List<FinancePlfIncome> selectFinancePlfIncome(Map<String, Object> map);

    int deleteDnConfigFinance(DnConfigFinance dnConfigFinance);

    int insertDnConfigFinance(DnConfigFinance dnConfigFinance);

    int updateDnConfigFinance(DnConfigFinance dnConfigFinance);

    List<DnConfigFinance> selectDnConfigFinance(DnConfigFinance dnConfigFinance);

    int deleteFinancePreIncome(FinancePreIncome financePreIncome);

    int insertFinancePreIncome(List<FinancePreIncome> list);

    int updateFinancePreIncome(FinancePreIncome financePreIncome);

    List<FinancePreIncome> selectFinancePreIncome(Map<String, Object> map);

    int deleteFinanceSltIncome(FinanceSltIncome financeSltIncome);

    int insertFinanceSltIncome(List<FinanceSltIncome> list);

    int updateFinanceSltIncome(FinanceSltIncome financeSltIncome);

    List<FinanceSltIncome> selectFinanceSltIncome(Map<String, Object> map);

    List<Map<String, Object>> selectFinancePreIncomeNew(Map<String, Object> map);

    List<Map<String, Object>> countFinancePreIncomeNew(Map<String, Object> map);

    List<FinancePreIncomeSum> selectFinancePreIncomeSum(Map<String, Object> map);

    List<Map<String, Object>> countFinancePreIncomeSum(Map<String, Object> map);

    List<Map<String, Object>> exportFinancePreIncomeSum(Map<String, Object> map);

    List<FinancePreIncomeSum> selectSyncFinancePreIncomeSum1(Map<String, Object> map);

    List<FinancePreIncomeSum> selectSyncFinancePreIncomeSum2(Map<String, Object> map);

    int insertFinancePreIncomeSum(List<FinancePreIncomeSum> list);

    List<Map<String, Object>> selectAllAgent();

    Long existfinanceAccountConfig(FinanceAccountConfig app);

    void insertExamineFinanceAppidMoney(String year, String month,String table);

    void deleteExamineFinanceAppidMoney(String year, String month,String table);

    boolean selectExamineFinanceAppidMoney(String year, String month,String table);

    void syncAccountAppid();

    List<FinancePreIncomeSum> selectSyncFinancePreIncomeSum3(Map<String, Object> map);

    void batchInsertFinancePreIncomeSum(List<FinancePreIncomeSum> list);

    List<PredictIncomeOverview> selectPredictIncomeOverview(Map param);

    int updatePredictIncomeOverview(PredictIncomeOverview predictIncomeOverview);

    void batchInsertPredictIncomeOverview(List<PredictIncomeOverview> list);

    List<Map<String, Object>> exportNameRelation();

    List<PredictIncomeOverview> selectSyncProIncomeOverview(Map<String, Object> map);

    PredictIncomeOverview countPredictIncomeOverview(Map param);

    List<FinanceAppidMoneyImport> selectFinanceAppidMoneyImport(Map param);

    FinanceAppidMoneyImport selectSumFinanceAppidMoneyImport(Map param);

    void batchInsertFinanceAppidMoneyImport(List<FinanceAppidMoneyImport> list);

    /** 2023.6.25 以下五个方法涉及到finance_baidu_account_config表，迁移至adv2，暂不移除，确认上线没问题后再移除 */
    List<Map<String, Object>> selectBaiduAccountConfigs(Map param);

    void insertBaiduAccountConfig(Map param);

    void batchInsertBaiduAccountConfigImport(List<Map<String, Object>> list);

    void updateBaiduAccountConfig(Map param);

    void deleteBaiduAccountConfig(Map param);

    List<Map<String, Object>> selectFinancePreIncomeDetail(Map param);

    Map<String, Object> countFinancePreIncomeDetail(Map param);

    void synFinancePreIncomeDetail(Map<String, Object> param);

    int updateDnReportFinance(DnReportFinance1 dnReportFinance);

    int insertDnReportFinance(List<DnReportFinance1> list);

//    List<FinancePreIncomeSum> selectSyncFinancePreIncomeSum4(Map<String, Object> map);

    void examineFinacePreIncomeSummary(Map<String, Object> param);

    FinancePreIncomeSum selectFinancePreIncomeSumById(Long id);

    void deleteFinancePreIncomeSumNotExamine(Map<String, Object> map);

    FinancePreIncomeSum selectFinancePreIncomeSumOne(FinancePreIncomeSum financePreIncomeSum);

    InfoResult getFinanceSpendReport(FinanceSpendReportParam param);

    void getFinanceSpendReportExport(FinanceSpendReportParam param, HttpServletResponse response);

    InfoResult getFinanceOtherCostList(FinanceOtherCost param);

    void financeOtherCostExport(FinanceOtherCost param, HttpServletResponse response);

    List<FinanceOtherCost> getWithoutAppReport(FinanceOtherCost financeOtherCost);

    List<FinanceOtherCost> getWithoutMediaReport(FinanceOtherCost financeOtherCost);

    List<FinanceOtherCost> getSameReport(FinanceOtherCost financeOtherCost);

    List<FinanceOhayooVo> selectFinanceOhayooConfig(FinanceOhayooVo param);

    void addFinanceOhayooConfig(FinanceOhayooVo param);

    void updateFinanceOhayooConfig(FinanceOhayooVo param);

    void deleteFinanceOhayooConfig(FinanceOhayooVo param);

    FinanceOhayooVo selectFinanceOhayooConfigById(String appid);
}
