package com.wbgame.service.haiwai;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.operate.GoogleKeyConfig;
import com.wbgame.pojo.operate.GoogleKeyConfigDTO;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Description google支付秘钥配置 业务接口层
 * @Date 2024/8/29 16:21
 */
public interface GoogleKeyConfigService {
    /**
     * 根据 appid 删除 google支付秘钥配置 数据
     *
     * @param appidList appid集合
     * @return 删除结果
     */
    Result<Integer> deleteByAppid(List<Integer> appidList);

    /**
     * 新增google支付秘钥配置 数据
     *
     * @param dto 新增的数据dto
     * @return 新增结果
     */
    Result<Integer> insertGoogleKey(GoogleKeyConfigDTO dto);


    /**
     * 修改 google支付秘钥配置 数据
     *
     * @param dto
     * @return 修改结果
     */
    Result<Integer> updateGoogleKey(GoogleKeyConfigDTO dto);

    /**
     * 查询 google支付秘钥配置 数据
     *
     * @param dto
     * @return 查询结果
     */
    Result<PageResult<GoogleKeyConfig>> selectByCondition(GoogleKeyConfigDTO dto);

    /**
     * google支付秘钥配置 刷新缓存接口
     * @return 处理结果
     */
    Result<String> refreshCache();

}
