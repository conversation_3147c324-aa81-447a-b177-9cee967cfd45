package com.wbgame.service.yxc.impl;

import com.alibaba.fastjson.JSONArray;
import com.wbgame.mapper.yxcgame.YxcAdMapper;
import com.wbgame.service.yxc.YxcAdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("yxcAdService")
public class YxcAdServiceImpl implements YxcAdService {

	@Autowired
	private YxcAdMapper yxcAdMapper;
	
	
	@Override
	public int execSql(String sql) {
		return yxcAdMapper.execSql(sql);
	}
	@Override
	public List<String> queryListString(String sql) {
		return yxcAdMapper.queryListString(sql);
	}
	@Override
	public List<Map<String, Object>> queryListMap(String sql) {
		return yxcAdMapper.queryListMap(sql);
	}
	@Override
	public Map<String, Map<String, Object>> queryListMapOfKey(String sql) {
		return yxcAdMapper.queryListMapOfKey(sql);
	}
	@Override
	public <T> List<T> queryListBean(String sql, Class<T> clazz) {
		List<Map<String, Object>> listBean = yxcAdMapper.queryListMap(sql);
		return JSONArray.parseArray(JSONArray.toJSONString(listBean), clazz);
	}
	@Override
	public int execSqlHandle(String sql, Object obj) {
		return yxcAdMapper.execSqlHandle(sql, obj);
	}

	@Override
	public List<Map<String, Object>> queryListMapTwo(String sql, Object obj) {
		return yxcAdMapper.queryListMapTwo(sql, obj);
	}
	@Override
	public int batchExecSql(Map<String, Object> paramMap) {
		return yxcAdMapper.batchExecSql(paramMap);
	}
	
	
	@Override
	public List<Map<String, Object>> selectGameEventInfo(Map<String, Object> paramMap) {
		return yxcAdMapper.selectGameEventInfo(paramMap);
	}

}
