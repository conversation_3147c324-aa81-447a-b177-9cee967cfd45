package com.wbgame.service;

import com.wbgame.pojo.PermissionApprovalResponseVO;

/**
 * 权限申请审批服务接口
 * <AUTHOR>
 */
public interface PermissionApprovalService {

    /**
     * 提交权限申请并获取审批人信息
     * @param request 权限申请请求
     * @return 权限申请响应，包含审批人信息
     */
    PermissionApprovalResponseVO submitPermissionApplication(PermissionApprovalResponseVO request);

    /**
     * 创建权限申请记录
     * @param request 权限申请请求
     * @param approvers 审批人信息
     * @return 申请记录ID
     */
    Integer createPermissionApplicationRecord(PermissionApprovalResponseVO request, PermissionApprovalResponseVO approvers);
}
