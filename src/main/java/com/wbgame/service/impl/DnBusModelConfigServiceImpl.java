package com.wbgame.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.CleanYdMapper;
import com.wbgame.mapper.master.DnBusModelConfigMapper;
import com.wbgame.pojo.DnBusModelConfig;
import com.wbgame.pojo.DnBusModelConfigDTO;
import com.wbgame.pojo.DnBusModelConfigVO;
import com.wbgame.service.IDnBusModelConfigService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/8/8 11:45
 * @class: DnBusModelConfigServiceImpl
 * @description:
 */
@Service
public class DnBusModelConfigServiceImpl implements IDnBusModelConfigService {

    @Autowired
    private DnBusModelConfigMapper dnBusModelConfigMapper;

    @Autowired
    private CleanYdMapper cleanYdMapper;

    @Override
    @Transactional(transactionManager = "masterTransactionManager")
    public Result<Integer> deleteDnBusModelConfigById(List<Integer> id) {

        return ObjectUtils.isEmpty(id) ? ResultUtils.failure(Constants.ParamError) :
                ResultUtils.success(dnBusModelConfigMapper.deleteDnBusModelConfigById(id));
    }

    @Override
    @Transactional(transactionManager = "masterTransactionManager")
    public Result<Integer> insertDnBusModelConfig(DnBusModelConfigDTO record) {

        if (!dnBusModelConfigMapper.modelConfigExist(record.getBusModel()).isEmpty()) {

            return ResultUtils.failure("该配置已经存在!");
        }

        DnBusModelConfig dnBusModelConfig = new DnBusModelConfig();
        BeanUtils.copyProperties(record, dnBusModelConfig);
        int i = dnBusModelConfigMapper.insertDnBusModelConfig(dnBusModelConfig);

        return i > 0 ? ResultUtils.success(dnBusModelConfig.getId()) : ResultUtils.failure();
    }

    @Override
    public Result<PageResult<DnBusModelConfigVO>> selectDnBusModelConfigByCondition(Integer start, Integer limit, DnBusModelConfigDTO dnBusModelConfigDTO) {

        PageHelper.startPage(start, limit);
        List<DnBusModelConfigVO> list = dnBusModelConfigMapper.selectDnBusModelConfigByCondition(dnBusModelConfigDTO);
        return ResultUtils.success(PageResult.page(new PageInfo<>(list)));
    }

    @Override
    @Transactional(transactionManager = "masterTransactionManager")
    public Result<Integer> updateDnBusModelConfigById(DnBusModelConfigDTO record) {

        List<Integer> idList = dnBusModelConfigMapper.modelConfigExist(record.getBusModel());
        if (idList.size() > 0 && !idList.get(0).equals(record.getId())) {

            return ResultUtils.failure("改配置已存在!");
        }
        DnBusModelConfig dnBusModelConfig = new DnBusModelConfig();
        BeanUtils.copyProperties(record, dnBusModelConfig);
        int i = dnBusModelConfigMapper.updateDnBusModelConfigById(dnBusModelConfig);
        return i > 0 ? ResultUtils.success(i) : ResultUtils.failure("该配置不存在");
    }

    @Override
    public Result<List<DnBusModelConfigVO>> getModelList() {
        return ResultUtils.success(dnBusModelConfigMapper.getModelList());
    }

    @Override
    public List<DnBusModelConfigVO> export(DnBusModelConfigDTO dnBusModelConfigDTO) {
        return dnBusModelConfigMapper.export(dnBusModelConfigDTO);
    }
}
