package com.wbgame.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.wbgame.pojo.ProductPriceConfigVo;
import com.wbgame.pojo.ProductRevenueVo;
import com.wbgame.mapper.slave2.MobileIntegralConfigMapper;
import com.wbgame.service.MobileIntegralConfigService;

import java.util.List;
import java.util.Map;

@Service
public class MobileIntegralConfigServiceImpl implements  MobileIntegralConfigService {

    @Resource
    private MobileIntegralConfigMapper mobileIntegralConfigMapper;

    @Override
    public int insert(ProductPriceConfigVo record) {
        return mobileIntegralConfigMapper.insert(record);
    }


    @Override
    public int updateByPrimaryKey(ProductPriceConfigVo record) {
        return mobileIntegralConfigMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<ProductPriceConfigVo> selectByAll(ProductPriceConfigVo record) {
        return mobileIntegralConfigMapper.selectByAll(record);
    }


	@Override
	public int deleteByPrimaryKey(ProductPriceConfigVo record) {
		 return mobileIntegralConfigMapper.deleteByPrimaryKey(record);
	}


	@Override
	public int insertProductRevenue() {
		return mobileIntegralConfigMapper.insertProductRevenue();
	}


	@Override
	public List<ProductRevenueVo> selectRevenueList(Map<String,Object> map) {
		return mobileIntegralConfigMapper.selectRevenueList(map);
	}

}


