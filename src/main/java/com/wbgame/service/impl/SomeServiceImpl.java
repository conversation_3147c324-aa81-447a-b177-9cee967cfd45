package com.wbgame.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ocean.rawsdk.ApiExecutor;
import com.alibaba.ocean.rawsdk.client.exception.OceanException;
import com.umeng.uapp.param.UmengUappEventCreateParam;
import com.umeng.uapp.param.UmengUappEventCreateResult;
import com.wbgame.mapper.adb.ADBUmengMapper;
import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.SomeMapper;
import com.wbgame.pojo.*;
import com.wbgame.pojo.adv2.AdCodeAccountVo;
import com.wbgame.pojo.custom.AdTotalHourTwoVo;
import com.wbgame.pojo.wbsys.WxActionRecord;
import com.wbgame.service.SomeService;
import com.wbgame.servlet.qihoo.StringUtils;
import com.wbgame.task.UsertagTask;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;

import com.wbgame.utils.MailToolTwo;
import org.apache.commons.collections.map.HashedMap;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.jsoup.select.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.mail.Address;
import javax.mail.internet.InternetAddress;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service("someService")
public class SomeServiceImpl implements SomeService {

    private static final Logger logger = LoggerFactory.getLogger(SomeServiceImpl.class);

    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    @Autowired
    SomeMapper someMapper;
    @Autowired
    AdMapper adMapper;
    @Autowired
    DnwxBiMapper dnwxBiMapper;
    @Resource
    private ADBUmengMapper adbUmengMapper;

    private static final String[] FORBIDEN_LIST = {
            "B_popup_page_trigger",
            "B_ta_fail",
            "B_popup_function_electricity",
            "B_popup_home_sense_perform_show",
            "B_battery_change_scene_fail",
            "B_popup_home_sense_trigger",
            "B_home_scene_fail",
            "B_lock_screen_onaction_screenoff",
            "B_popup_home_sense_content_attach",
            "B_popup_home_sense_created",
            "B_power_charge_scene_fail"
    };
    private static final String[] FORBIDEN_MATCH_LIST = {
            "default_load",
    };

    @Override
    public RespUserInfo selectUserInfoByAttr(Map<String, Object> map) {
        return someMapper.selectUserInfoByAttr(map);
    }

    @Override
    public List<RespUserInfo> selectUserInfoAllByAttr(Map<String, Object> map) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public int insertUserInfo(Map<String, Object> map) {
        return someMapper.insertUserInfo(map);
    }

    @Override
    public List<ConfigVo> selectGiftConfig() {
        return someMapper.selectGiftConfig();
    }


    @Override
    public RespUserInfo queryUserByAttr(RequestVo rv, String type) {
        ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
        RespUserInfo ru = null;

        if (!BlankUtils.checkBlank(rv.getId()))
            ru = (RespUserInfo) opsForValue.get(CommonUtil.REDIS_STRING_SOME_USERINFO + rv.getId());
        if (ru == null) { // 缓存中没有用户
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("tableName", "some_user_info_" + rv.getAppid());
            if (type.equals("id")) {
                map.put("id", rv.getId());
            } else if (type.equals("wx")) {
                map.put("wx", rv.getWxid());
            } else if (type.equals("imei")) {
                map.put("imei", rv.getImei());
            }
            ru = someMapper.selectUserInfoByAttr(map);
        }
        if (ru != null) {
            // 更新登录时间，缓存存入新时间、返回不变
            String before_time = ru.getLogin_time();
            ru.setLogin_time(String.valueOf(DateTime.now().getMillis() / 1000));

            opsForValue.set(CommonUtil.REDIS_STRING_SOME_USERINFO + rv.getId(), ru);
            opsForValue.set(CommonUtil.REDIS_STRING_SOME_WXIDTOID + ru.getWxid(), ru.getId());
            opsForValue.set(CommonUtil.REDIS_STRING_SOME_IMEITOID + ru.getImei(), ru.getId());
            ru.setLogin_time(before_time);
        }
        return ru;
    }

    @Override
    public List<DrawConfigVo> drawConfig() {
        return someMapper.selectDrawConfig();
    }

    @Override
    public String invalidateDrawConfig() {
        return "ok";
    }

    @Override
    public List<ShortUrlVo> selectShortUrl() {
        return someMapper.selectShortUrl();
    }

    @Override
    public String invalidateShortUrl() {
        return "ok";
    }

    @Override
    public List<ShortUrlVo> selectShortUrlAll() {
        return someMapper.selectShortUrlAll();
    }

    @Override
    public int insertShortUrl(ShortUrlVo shortUrlVo) {
        return someMapper.insertShortUrl(shortUrlVo);
    }

    @Override
    public List<ApkAppInfoVo> selectAppInfo() {
        return someMapper.selectAppInfo();
    }

    @Override
    public List<ApkAppInfoVo> selectAppInfoData(String date) {
        return someMapper.selectAppInfoData(date);
    }

    @Override
    public List<ApkUserTotalVo> selectApkUserTotal(String appid) {
        return someMapper.selectApkUserTotal(appid);
    }

    @Override
    public List<ApkUserTotalVo> selectApkUserTotalTwo(Map<String, Object> paramMap) {
        return someMapper.selectApkUserTotalTwo(paramMap);
    }

    @Override
    public List<ApkUserTotalVo> selectApkUserTotalThree(Map<String, Object> paramMap) {
        return someMapper.selectApkUserTotalThree(paramMap);
    }

    @Override
    public List<ApkUserTotalVo> selectApkUserTotalTwoV2(Map<String, Object> paramMap) {
        return someMapper.selectApkUserTotalTwoV2(paramMap);
    }

    @Override
    public ApkUserTotalVo selectApkUserTotalThreeV2(Map<String, Object> paramMap) {
        return someMapper.selectApkUserTotalThreeV2(paramMap);
    }

    @Override
    public ApkUserTotalVo selectTodayApkUserTotalThreeV2(Map<String, Object> paramMap) {
        return someMapper.selectTodayApkUserTotalThreeV2(paramMap);
    }

    @Override
    public int insertDauUser(List<ApkUserTotalVo> list) {
        return someMapper.insertDauUser(list);
    }

    @Override
    public int insertDauUserDetail(List<ApkUserTotalVo> list) {
        return someMapper.insertDauUserDetail(list);
    }

    @Override
    public List<ApkSelectNewVo> selectApkUserNew(Map<String, Object> map) {
        return someMapper.selectApkUserNew(map);
    }

    @Override
    public List<ApkSelectNewVo> selectApkUserNewGroup(Map<String, Object> map) {
        return someMapper.selectApkUserNewGroup(map);
    }

    @Override
    public List<ApkSelectNewVo> selectApkUserDau(Map<String, Object> map) {
        return someMapper.selectApkUserDau(map);
    }

    @Override
    public ApkSelectNewVo selectApkUserDauSum(Map<String, Object> map) {
        return someMapper.selectApkUserDauSum(map);
    }

    @Override
    public List<ApkSelectNewVo> selectApkUserDauGroup(Map<String, Object> map) {
        return someMapper.selectApkUserDauGroup(map);
    }

    @Override
    public ApkSelectNewVo selectApkUserDauGroupSum(Map<String, Object> map) {
        return someMapper.selectApkUserDauGroupSum(map);
    }

    @Override
    public List<ApkKeepUserVo> selectApkUserKeep(Map<String, Object> map) {
        return someMapper.selectApkUserKeep(map);
    }

    @Override
    public List<ApkUserTotalVo> selectApkUserDetail(Map<String, Object> map) {
        return someMapper.selectApkUserDetail(map);
    }

    @Override
    public ApkUserTrendVo selectApkUserTrend(Map<String, Object> map) {
        return someMapper.selectApkUserTrend(map);
    }

    @Override
    public List<ApkUserTotalVo> selectApkUserTrendLine(Map<String, Object> map) {
        return someMapper.selectApkUserTrendLine(map);
    }

    @Override
    public List<ApkUserTotalVo> selectApkUserNewTrend(Map<String, Object> map) {
        return someMapper.selectApkUserNewTrend(map);
    }

    @Override
    public List<ApkUserVerVo> selectApkUserChannel(Map<String, Object> map) {
        return someMapper.selectApkUserChannel(map);
    }

    @Override
    public List<ApkUserVerVo> selectApkUserVer(Map<String, Object> map) {
        return someMapper.selectApkUserVer(map);
    }

    @Override
    public int insertKeepNum(List<ApkUserKeepVo> list) {
        return someMapper.insertKeepNum(list);
    }

    @Override
    public int updateKeep7Num(List<ApkUserKeepVo> list) {
        return someMapper.updateKeep7Num(list);
    }

    @Override
    public int updateKeep30Num(List<ApkUserKeepVo> list) {
        return someMapper.updateKeep30Num(list);
    }

    @Override
    public int insertChannelFeeAdv(List<AdvChaFeeVo> list) {
        return someMapper.insertChannelFeeAdv(list);
    }

    @Override
    public int insertChannelFee(List<AdvFeeVo> list) {
        return someMapper.insertChannelFee(list);
    }

    @Override
    public List<TouSuVo> selectFeedBackConfig(Map<Object, Object> map) {
        return someMapper.selectFeedBackConfig(map);
    }

    @Override
    public int insertFeedBackConfig(TouSuVo touSuVo) {
        return someMapper.insertFeedBackConfig(touSuVo);
    }

    @Override
    public int updateFeedBackConfig(TouSuVo touSuVo) {
        return someMapper.updateFeedBackConfig(touSuVo);
    }

    @Override
    public int removeFeedBackConfig(String id) {
        return someMapper.removeFeedBackConfig(id);
    }

    @Override
    public List<ApkButtonVo> selectButtonInfo(Map<Object, Object> map) {
        return someMapper.selectButtonInfo(map);
    }

    @Override
    public int updateButtonInfo(ApkButtonVo apkButtonVo) {
        return someMapper.updateButtonInfo(apkButtonVo);
    }

    @Override
    public int insertButtonInfo(ApkButtonVo apkButtonVo) {
        return someMapper.insertButtonInfo(apkButtonVo);
    }

    @Override
    public int insertUserTalkBack(UserTalkBack userTalkBack) {
        return someMapper.insertUserTalkBack(userTalkBack);
    }

    @Override
    public ApkSystemConfigVo selectSystemConfig() {
        return someMapper.selectSystemConfig();
    }

    @Override
    public int updateSystemConfig(ApkSystemConfigVo apkSystemConfigVo) {
        return someMapper.updateSystemConfig(apkSystemConfigVo);
    }

    @Override
    public List<ProjectVo> selectDhmInfo(Map<Object, Object> map) {
        return someMapper.selectDhmInfo(map);
    }

    @Override
    public int insertDhmInfo(Map<Object, Object> map) {
        return someMapper.insertDhmInfo(map);
    }

    @Override
    public int insertDhmInfoList(List<Map<String, Object>> list) {
        return someMapper.insertDhmInfoList(list);
    }

    @Override
    public List<NpPacUpdateVo> selectAppUpdateInfo(Map<Object, Object> map) {
        return someMapper.selectAppUpdateInfo(map);
    }

    @Override
    public int insertAppUpdateInfo(NpPacUpdateVo npPacUpdateVo) {
        return someMapper.insertAppUpdateInfo(npPacUpdateVo);
    }

    @Override
    public int updateAppUpdateInfo(NpPacUpdateVo npPacUpdateVo) {
        return someMapper.updateAppUpdateInfo(npPacUpdateVo);
    }

    @Override
    public int deleteAppUpdateInfo(Map<Object, Object> map) {
        return someMapper.deleteAppUpdateInfo(map);
    }

    @Override
    public List<AppInfoVo> selectAppListInfo(Map<Object, Object> map) {
        return someMapper.selectAppListInfo(map);
    }

    @Override
    public int insertAppListInfo(Map<Object, Object> map) {
        return someMapper.insertAppListInfo(map);
    }

    @Override
    public int updateAppListInfo(Map<Object, Object> map) {
        return someMapper.updateAppListInfo(map);
    }

    @Override
    public int updateMarketListFind(String appid, String find_vals) {
        String[] split = find_vals.split(",");
        for (String val : split) {
            String sql = "UPDATE `market_sys_find` set app_list_find=CONCAT(app_list_find,'," + appid + "') where find_val = '" + val + "' AND app_list_find != '' AND app_list_find not like '%" + appid + "%'";
            adMapper.execSql(sql);
        }

        return 1;
    }

    @Override
    public int deleteAppListInfo(String ids) {
        return someMapper.deleteAppListInfo(ids);
    }

    @Override
    public List<SelectStortVo> selectChannelInfo() {
        return someMapper.selectChannelInfo();
    }

    @Override
    public List<UengIncomeShowVo> selectUmengEnter(Map<Object, Object> map) {
        return someMapper.selectUmengEnter(map);
    }

    @Override
    public int insertUmengEnter(UengIncomeShowVo uengIncomeShowVo) {
        return someMapper.insertUmengEnter(uengIncomeShowVo);
    }

    @Override
    public int updateUmengEnter(UengIncomeShowVo uengIncomeShowVo) {
        return someMapper.updateUmengEnter(uengIncomeShowVo);
    }

    @Override
    public int deleteUmengEnter(UengIncomeShowVo uengIncomeShowVo) {
        return someMapper.deleteUmengEnter(uengIncomeShowVo);
    }

    @Override
    public List<UmengTreeListVo> selectUmengList() {
        return someMapper.selectUmengList();
    }

    @Override
    public List<UmengTreeListVo> selectUmengListTwo() {
        return someMapper.selectUmengListTwo();
    }

    @Override
    public List<UmengChannelTotalVo> selectUmengChannelTotal(Map<Object, Object> map) {
        return someMapper.selectUmengChannelTotal(map);
    }

    @Override
    public List<UmengChannelTotalVo> selectUmengChannelTotalGroup(Map<Object, Object> map) {
        return someMapper.selectUmengChannelTotalGroup(map);
    }
    @Override
    public List<UmengChannelTotalVo> selectUmengChannelTotalGroup2(Map<Object, Object> map) {
        return someMapper.selectUmengChannelTotalGroup2(map);
    }
    
    @Override
    public List<UmengChannelTotalVo> selectUmengChannelReport(Map<Object, Object> map) {
        return someMapper.selectUmengChannelReport(map);
    }

    @Override
    public List<QPayVo> selectQpayInfo(Map<Object, Object> map) {
        return someMapper.selectQpayInfo(map);
    }

    @Override
    public int insertQpayInfo(QPayVo qPayVo) {
        return someMapper.insertQpayInfo(qPayVo);
    }

    @Override
    public int updateQpayInfo(QPayVo qPayVo) {
        return someMapper.updateQpayInfo(qPayVo);
    }

    @Override
    public int deleteQpayInfo(String id) {
        return someMapper.deleteQpayInfo(id);
    }

    @Override
    public List<QpayRuleVo> selectQpayRuleInfo(Map<Object, Object> map) {
        return someMapper.selectQpayRuleInfo(map);
    }

    @Override
    public int insertQpayRuleInfo(QpayRuleVo qpayRuleVo) {
        return someMapper.insertQpayRuleInfo(qpayRuleVo);
    }

    @Override
    public int updateQpayRuleInfo(QpayRuleVo qpayRuleVo) {
        return someMapper.updateQpayRuleInfo(qpayRuleVo);
    }

    @Override
    public int deleteQpayRuleInfo(String id) {
        return someMapper.deleteQpayRuleInfo(id);
    }

    @Override
    public List<NpPostVo> selectFilterDeviceBlack(Map<Object, Object> map) {
        return someMapper.selectFilterDeviceBlack(map);
    }

    @Override
    public List<NpPostVo> selectFilterDeviceWhite(Map<Object, Object> map) {
        return someMapper.selectFilterDeviceWhite(map);
    }

    @Override
    public List<NpPostVo> selectFilterDeviceIp(Map<Object, Object> map) {
        return someMapper.selectFilterDeviceIp(map);
    }

    @Override
    public int insertFilterDeviceBlack(Map<Object, Object> map) {
        return someMapper.insertFilterDeviceBlack(map);
    }

    @Override
    public int insertFilterDeviceWhite(Map<Object, Object> map) {
        return someMapper.insertFilterDeviceWhite(map);
    }

    @Override
    public int insertFilterDeviceIp(Map<Object, Object> map) {
        return someMapper.insertFilterDeviceIp(map);
    }

    @Override
    public int deleteFilterDeviceBlack(Map<Object, Object> map) {
        return someMapper.deleteFilterDeviceBlack(map);
    }

    @Override
    public int deleteFilterDeviceWhite(Map<Object, Object> map) {
        return someMapper.deleteFilterDeviceWhite(map);
    }

    @Override
    public int deleteFilterDeviceIp(Map<Object, Object> map) {
        return someMapper.deleteFilterDeviceIp(map);
    }

    @Override
    public List<AdPrjVo> selectApiAdConfig(Map<Object, Object> map) {
        return someMapper.selectApiAdConfig(map);
    }

    @Override
    public int insertApiAdConfig(AdPrjVo adPrjVo) {
        return someMapper.insertApiAdConfig(adPrjVo);
    }

    @Override
    public int updateApiAdConfig(AdPrjVo adPrjVo) {
        return someMapper.updateApiAdConfig(adPrjVo);
    }

    @Override
    public int deleteApiAdConfig(Map<Object, Object> map) {
        return someMapper.deleteApiAdConfig(map);
    }

    @Override
    public int openApiAdConfig(Map<Object, Object> map) {
        return someMapper.openApiAdConfig(map);
    }

    @Override
    public List<ExtendVo> selectNewAdConfig(Map<Object, Object> map) {
        return someMapper.selectNewAdConfig(map);
    }

    @Override
    public int insertNewAdConfig(ExtendVo extendVo) {
        return someMapper.insertNewAdConfig(extendVo);
    }

    @Override
    public int updateNewAdConfig(ExtendVo extendVo) {
        return someMapper.updateNewAdConfig(extendVo);
    }

    @Override
    public int deleteNewAdConfig(String ids) {
        return someMapper.deleteNewAdConfig(ids);
    }

    @Override
    public int batchInsertNewAdConfig(List<ExtendVo> list) {
        return someMapper.batchInsertNewAdConfig(list);
    }

    @Override
    public List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfig(Map<Object, Object> map) {
        return someMapper.selectNewAdOpenConfig(map);
    }

    @Override
    public List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfigForSqlTest(Map<String, String> paramMap) {

        String sqlStr = "select t.appid,t.limit_num,t.seqid, t.prjmid,t.name,t.type,t.rate,t.ad_sid_str,t.agentpecent,t.round,t.statu,t.delaytime,t.activ_cityid,t.activ_telecom,t.activ_statu,date_format(t.createdate,'%Y-%m-%d %H:%i:%S') createdate,t.delaydays as limit_date,t.delaysecond as limit_second,t.showmodel  from TEST_EXTEND_ADCONFIG t where 1=1";

        String appid = paramMap.get("appid");
        String ad_sid = paramMap.get("ad_sid");
        String prjid = paramMap.get("prjid");
        String status = paramMap.get("status");
        String testids = paramMap.get("testids");
        String orgid = paramMap.get("orgid");
        String sc_name = paramMap.get("sc_name");

        if (appid != null && !"".equals(appid)) {
            sqlStr = sqlStr + " and t.appid = '" + appid + "'";
        }
        if (ad_sid != null && !"".equals(ad_sid)) {
            sqlStr = sqlStr + " and t.ad_sid_str like '%" + ad_sid + "%'";
        }
        if (prjid != null && !"".equals(prjid)) {
            sqlStr = sqlStr + " and t.prjmid like '%" + prjid + "%'";
        }
        if (!StringUtils.isEmpty(testids)) {//2019.5.6 yangk 修改测试id范围
            sqlStr = sqlStr + " and (t.prjmid in (" + testids + ") or t.prjmid like '3333%')";
        }//2019.5.6 end
        if (status != null && !"".equals(status)) {
            sqlStr = sqlStr + " and t.statu = '" + status + "'";
        }
        if (sc_name != null && !"".equals(sc_name)) {
            sqlStr = sqlStr + " and t.name = '" + sc_name + "'";
        }

        if (!StringUtils.isEmpty(orgid)
                && (orgid.contains("business") || orgid.contains("busines_s_assistant"))) {
            sqlStr = sqlStr + "and (t.ad_sid_str like 'opponative%'" +
                    " or t.ad_sid_str like 'oppo%'"
                    + " or t.ad_sid_str like 'vivonative%'"
                    + " or t.ad_sid_str like 'miad%'"
                    + " or t.ad_sid_str like 'ad4399%'"
                    + " or t.ad_sid_str like 'ledouad%'"
                    + " or t.ad_sid_str like 'lenovo%'"
                    + " or t.ad_sid_str like 'minative%'"
                    + " or t.ad_sid_str like 'vivo%')";
        }
        sqlStr = sqlStr + " order by createdate desc ";

        return someMapper.selectNewAdOpenConfigForSqlTest(sqlStr);
    }

    @Override
    public List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfigForSql(Map<String, String> paramMap) {

        String sqlStr = "select t.limit_num,t.seqid, t.prjmid,t.name,t.type,t.rate,t.ad_sid_str,t.agentpecent,t.round,t.statu,t.delaytime,t.activ_cityid,t.activ_telecom,t.activ_statu,date_format(t.createdate,'%Y-%m-%d %H:%i:%S') createdate,t.delaydays as limit_date,t.delaysecond as limit_second,t.showmodel  from EXTEND_ADCONFIG t where 1=1";

        String ad_sid = paramMap.get("ad_sid");
        String prjid = paramMap.get("prjid");
        String status = paramMap.get("status");
        String testids = paramMap.get("testids");
        String orgid = paramMap.get("orgid");
        String sc_name = paramMap.get("sc_name");
        String prjids = paramMap.get("prjids");

        if (ad_sid != null && !"".equals(ad_sid)) {
            sqlStr = sqlStr + " and t.ad_sid_str like '%" + ad_sid + "%'";
        }
        if (prjid != null && !"".equals(prjid)) {
            sqlStr = sqlStr + " and t.prjmid like '%" + prjid + "%'";
        }
        if (!StringUtils.isEmpty(testids)) {//2019.5.6 yangk 修改测试id范围
            sqlStr = sqlStr + " and (t.prjmid in (" + testids + ") or t.prjmid like '3333%')";
        }//2019.5.6 end
        if (status != null && !"".equals(status)) {
            sqlStr = sqlStr + " and t.statu = '" + status + "'";
        }
        if (sc_name != null && !"".equals(sc_name)) {
            sqlStr = sqlStr + " and t.name = '" + sc_name + "'";
        }
        if (prjids != null && !"".equals(prjids)) {
            sqlStr = sqlStr + " and (t.prjmid in (" + prjids + "))";
        }
        if (!StringUtils.isEmpty(orgid)
                && (orgid.contains("business") || orgid.contains("busines_s_assistant"))) {
            sqlStr = sqlStr + "and (t.ad_sid_str like 'opponative%'" +
                    " or t.ad_sid_str like 'oppo%'"
                    + " or t.ad_sid_str like 'vivonative%'"
                    + " or t.ad_sid_str like 'miad%'"
                    + " or t.ad_sid_str like 'ad4399%'"
                    + " or t.ad_sid_str like 'ledouad%'"
                    + " or t.ad_sid_str like 'lenovo%'"
                    + " or t.ad_sid_str like 'minative%'"
                    + " or t.ad_sid_str like 'vivo%')";
        }
        sqlStr = sqlStr + " order by createdate desc ";

        return someMapper.selectNewAdOpenConfigForSql(sqlStr);
    }

    @Override
    public int insertNewAdOpenConfig(com.wbgame.pojo.push.ConfigVo configVo) {
        return someMapper.insertNewAdOpenConfig(configVo);
    }

    @Override
    public int batchupdateNewAdOpenConfig(List<com.wbgame.pojo.push.ConfigVo> list) {
        return someMapper.batchupdateNewAdOpenConfig(list);
    }

    @Override
    public int updateNewAdOpenConfig(com.wbgame.pojo.push.ConfigVo configVo) {
        return someMapper.updateNewAdOpenConfig(configVo);
    }

    @Override
    public int deleteNewAdOpenConfig(Map<Object, Object> map) {
        return someMapper.deleteNewAdOpenConfig(map);
    }

    @Override
    public int openNewAdOpenConfig(Map<Object, Object> map) {
        return someMapper.openNewAdOpenConfig(map);
    }

    @Override
    public List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfigByPrj(String id) {
        return someMapper.selectNewAdOpenConfigByPrj(id);
    }

    @Override
    public int batchInsertNewAdOpenConfig(List<com.wbgame.pojo.push.ConfigVo> list) {
        return someMapper.batchInsertNewAdOpenConfig(list);
    }

	/*@Override
	public List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfigInfo(Map<Object, Object> map) {
		return someMapper.selectNewAdOpenConfigInfo(map);
	}*/

    @Override
    public List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfigInfoById(List<String> list) {
        return someMapper.selectNewAdOpenConfigInfoById(list);
    }

    @Override
    public List<UmengTotalVo> selectProduct(Map<Object, Object> map) {
        return someMapper.selectProduct(map);
    }

    @Override
    public List<ProdutChannelDataVo> selectProductData(Map<Object, Object> map) {
        return someMapper.selectProductData(map);
    }

    @Override
    public int updateUmengProduct(Map<Object, Object> map) {
        return someMapper.updateUmengProduct(map);
    }

    @Override
    public List<ExtendVo> selectNewAdConfigBySid(String str) {
        return someMapper.selectNewAdConfigBySid(str);
    }

    @Override
    public List<NpActiveFree> selectAdTableConfig(Map<Object, Object> map) {
        return someMapper.selectAdTableConfig(map);
    }

    @Override
    public int updateAdTableConfig(NpActiveFree activeFree) {
        return someMapper.updateAdTableConfig(activeFree);
    }

    @Override
    public int insertAdTableConfig(NpActiveFree activeFree) {
        return someMapper.insertAdTableConfig(activeFree);
    }

    @Override
    public List<UmengTotalVo> selectIncomeData(Map<Object, Object> map) {
        return someMapper.selectIncomeData(map);
    }

    @Override
    public List<NpPostVo> selectPageAutoConfig(Map<Object, Object> map) {
        return someMapper.selectPageAutoConfig(map);
    }

    @Override
    public int insertPageAuto(Map<Object, Object> map) {
        return someMapper.insertPageAuto(map);
    }

    @Override
    public List<ClientPostParamVo> queryClientPost(ClientPostParamVo conditionVo) {
        String beginTime = convertDateTime(conditionVo.getBeginTime());
        String endTime = convertDateTime(conditionVo.getEndTime());
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyyMMdd");
        DateTime begin = DateTime.parse(beginTime, format);
        DateTime end = DateTime.parse(endTime, format);

        StringBuffer sql = new StringBuffer("select a.lsn,");
        sql.append("a.imei,");
        sql.append("a.productid,");
        sql.append("a.projectid,");
        sql.append("a.smscenter,");
        sql.append("a.version,");
        sql.append("a.release_verno,");
        sql.append("a.screnres,");
        sql.append("a.wifissid,");

        sql.append("a.oaid,");
        sql.append("a.macaddr,");
        sql.append("a.cityid,");
        sql.append("a.net,");
        sql.append("a.gametimes,");
        sql.append("a.mobilemodel,");
        sql.append("a.mmid,");
        if (Integer.valueOf(beginTime) - Integer.valueOf("20200801") > 0) {
            sql.append("a.opencount,");
        }
        sql.append("a.createtime from (");

        while (begin.compareTo(end) <= 0) {
            sql.append(converSqlUpQuery(conditionVo, begin.toString("yyyyMMdd"), null, conditionVo.getBeginTime()));
            begin = begin.plusDays(1);
            if (begin.compareTo(end) <= 0) {
                sql.append(" union ");
            }
        }

        sql.append(") a order by "+conditionVo.getOrder());
        //sql.append(" order by a.createtime desc");
        List<ClientPostParamVo> resList = someMapper.queryClientPost(sql.toString());

        return resList;
    }
    
    @Override
    public List<ClientPostParamVo> queryClientPostTwo(ClientPostParamVo conditionVo) {
    	String beginTime = convertDateTime(conditionVo.getBeginTime());
        String endTime = convertDateTime(conditionVo.getEndTime());
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyyMMdd");
        DateTime begin = DateTime.parse(beginTime, format);
        DateTime end = DateTime.parse(endTime, format);

        StringBuffer sql = new StringBuffer("select a.lsn,");
        sql.append("a.imei,");
        sql.append("a.productid,");
        sql.append("a.projectid,");
        sql.append("a.smscenter,");
        sql.append("a.version,");
        sql.append("a.release_verno,");
        sql.append("a.screnres,");
        sql.append("a.wifissid,");

        sql.append("a.oaid,");
        sql.append("a.macaddr,");
        sql.append("a.cityid,");
        sql.append("a.net,");
        sql.append("a.gametimes,");
        sql.append("a.mobilemodel,");
        sql.append("a.mmid,");
        sql.append("a.cname,");
        if (Integer.valueOf(beginTime) - Integer.valueOf("20200801") > 0) {
            sql.append("a.opencount,");
        }
        sql.append("a.createtime from (");

        while (begin.compareTo(end) <= 0) {
            sql.append(converSqlUpQueryTwo(conditionVo, begin.toString("yyyyMMdd"), null, conditionVo.getBeginTime()));
            begin = begin.plusDays(1);
            if (begin.compareTo(end) <= 0) {
                sql.append(" union ");
            }
        }

        sql.append(") a order by "+conditionVo.getOrder());
        List<ClientPostParamVo> resList = dnwxBiMapper.queryClientPost(sql.toString());

        return resList;
    }

    @Override
    public List<NpPostVo> selectNpMap(Map<String, String> map) {
        return someMapper.selectNpMap(map);
    }

    @Override
    public List<UserNewCountVo> selectUserNewCount(Map<String, String> map) {
        String today = DateTime.now().toString("yyyyMMdd");
        String sql = "select tt.* from (" +
                "SELECT aa.*,bb.p_priName FROM user_newcount_total aa left join zjh_project_info bb " +
                "on aa.projectid = bb.p_pri and aa.mmid = bb.p_mmid " +
                "where aa.tdate = '" + today + "' and aa.mmid != '0' " +
                "union " +
                "SELECT aa.*,bb.p_priName FROM user_newcount_total aa left join zjh_project_info bb " +
                "on aa.projectid = bb.p_pri " +
                "where aa.tdate = '" + today + "' and aa.mmid = '0' " +
                "GROUP BY aa.projectid) tt where 1=1";

        String projectid = map.get("projectid");
        String mmid = map.get("mmid");
        StringBuffer sb = new StringBuffer();
        if (projectid != null && projectid.length() > 0) {
            sb.append(" and tt.projectid = '" + projectid + "'");
        }
        if (mmid != null && mmid.length() > 0) {
            sb.append(" and tt.mmid = '" + mmid + "'");
        }

        sql = sql + sb.toString();
        sql = sql + " order by tt.new_count desc";

        List<UserNewCountVo> query = someMapper.selectUserNewCount(sql);
        //List<UserNewCountVo> query = this.query(sql, UserNewCountVo.class, null);
        return query;
    }

    @Override
    public int insertApkGameTime(List<ApkGameTimeVo> list) {
        return someMapper.insertApkGameTime(list);
    }

    @Override
    public List<ApkGameTimeVo> selectApkGameTime(Map<Object, Object> map) {
        String projectid = (String) map.get("projectid");
        String productid = (String) map.get("productid");
        String begin = (String) map.get("begin");
        String end = (String) map.get("end");
        if (projectid != null && projectid.length() > 0) {
            String sql = "select blockgame,productid,projectid,nsingle,osingle,oday,nday\n" +
                    "from apk_gametime where productid = \"" + productid + "\" and projectid = \"" + projectid + "\"\n" +
                    "and DATE_FORMAT(STR_TO_DATE(createtime,'%Y%m%d'),'%Y-%m-%d') >= \"" + begin + "\"\n" +
                    "and DATE_FORMAT(STR_TO_DATE(createtime,'%Y%m%d'),'%Y-%m-%d') <= \"" + end + "\"";
            List<ApkGameTimeVo> list = someMapper.selectApkGameTime(sql);
            return list;
        } else {
            String sql = "select blockgame,productid,SUM(nsingle) as nsingle,SUM(osingle) as osingle,SUM(nday) as nday,SUM(oday) as oday \n" +
                    "from apk_gametime where productid = \"" + productid + "\" \n" +
                    "and  DATE_FORMAT(STR_TO_DATE(createtime,'%Y%m%d'),'%Y-%m-%d') >= \"" + begin + "\"\n" +
                    "and DATE_FORMAT(STR_TO_DATE(createtime,'%Y%m%d'),'%Y-%m-%d') <= \"" + end + "\"\n" +
                    "GROUP BY blockgame";
            List<ApkGameTimeVo> list = someMapper.selectApkGameTime(sql);
            return list;
        }
    }

    @Override
    public Map<String, Object> getChannelsAndVersion(String appid) {
        Map<String, Object> map = new HashedMap();
        List<String> versions = new ArrayList<>();
        List<String> channels = new ArrayList<>();
        List<Map<String,Object>> values = adbUmengMapper.getChannelsAndVersions(appid);
        if (values != null && values.size() > 0) {
            for (Map<String,Object> m : values) {
                if ("channel".equals(m.get("type")+"")) {
                    channels.add(m.get("val")+"");
                }else{
                    versions.add(m.get("val")+"");
                }
            }
        }
        map.put("channels", channels);
        map.put("versions", versions);
        return map;
    }

    @Override
    public List<Map<String, Object>> selectApkUserDetailV2(Map<String, Object> map) {
        return someMapper.selectApkUserDetailV2(map);
    }

    @Override
    public List<AppCategory> getAppCategorys() {
        return someMapper.getAppCategorys();
    }
    @Override
    public Map<String, Map<String, Object>> getTwoAppCategorys() {
    	String query = "select CONCAT(id,'') as mapkey,id,`name` FROM two_app_category ";
    	Map<String, Map<String, Object>> map = adMapper.queryListMapOfKey(query);
    	return map;
    }

    @Override
    public void deleteDnGroupApp() {
        someMapper.deleteDnGroupApp();
    }

    @Override
    public void batchInsertDnGroupApp(List<Map<String, Object>> list) {
        someMapper.batchInsertDnGroupApp(list);
    }

    @Override
    public Long countUmengKey(String umeng_key) {
        return someMapper.countUmengKey(umeng_key);
    }

    @Override
    public AppInfoVo selectAppInfoId(String hiddenId) {
        return someMapper.selectAppInfoId(hiddenId);
    }

    @Override
    public List<AdTotalHourTwoVo> selectAdTotalHour(AdTotalHourTwoVo adTotalHourVo) {
        String today = DateTime.now().toString("yyyy-MM-dd");
        String yesteday = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        String sql = "select * from ad_total_hour where adtype = " + adTotalHourVo.getAdtype() + " and tdate BETWEEN '" + yesteday + "' AND '" + today + "'";

        List<AdTotalHourTwoVo> query = someMapper.selectAdTotalHour(sql);
        return query;
    }

    @Override
    public List<Map<String, Object>> selectNpMap(String sql) {
        return someMapper.selectNpMaps(sql);
    }

    @Override
    public List<SysPathVo> selectPathInfo() {
        return someMapper.selectPathInfo();
    }

    @Override
    public String invalidatePathInfo() {
        return "ok";
    }

    @Override
    public int insertLogInfo(WxActionRecord wxActionRecord) {
        return someMapper.insertLogInfo(wxActionRecord);
    }

    @Override
    public List<PkLogVo> SelectPkLog(String datestr) {
        String sql = "select UPPER(gamename) as gamename,SUM(gamenum) as gamenum,SUM(gameroom) as gameroom,gameip,date_format(datestr,'%H') as datestr from pk_game_log " +
                " where date_format(datestr,'%Y-%m-%d') = '" + datestr + " ' GROUP BY UPPER(gamename),date_format(datestr,'%H')";
        return someMapper.selectPkLog(sql);
    }

    @Override
    public List<ProdutChannelDataVo> selectNewUserByPid(Map<String, Object> map) {
        return someMapper.selectNewUserByPid(map);
    }

    @Override
    public List<ProdutChannelDataVo> selectDauUserByPid(Map<String, Object> map) {
        return someMapper.selectDauUserByPid(map);
    }

    @Override
    public List<ProdutChannelDataVo> selectStartCountByPid(Map<String, Object> map) {
        return someMapper.selectStartCountByPid(map);
    }

    @Override
    public List<AssetsInfo> selectAsserts(Map<String, Object> map) {
        return someMapper.selectAsserts(map);
    }

    @Override
    public int addAsserts(Map<String, Object> map) {
        return someMapper.addAsserts(map);
    }

    @Override
    public int updateAsserts(Map<String, Object> map) {
        return someMapper.updateAsserts(map);
    }

    @Override
    public int handerAsserts(Map<String, Object> map) {
        return someMapper.handerAsserts(map);
    }

    @Override
    public int batchAddAsserts(List<AssetsInfo> list) {
        return someMapper.batchAddAsserts(list);
    }

    @Override
    public List<AssetsInfoLog> selectAssertsLog(Map<String, Object> map) {
        return someMapper.selectAssertsLog(map);
    }

    @Override
    public int insertAssertsLog(AssetsInfoLog assetsInfoLog) {
        return someMapper.insertAssertsLog(assetsInfoLog);
    }

    @Override
    public int batchAssertsLog(List<AssetsInfoLog> list) {
        return someMapper.batchAssertsLog(list);
    }

    @Override
    public List<ApkUserVerVo> selectApkTotalChannel(Map<String, Object> map) {
        return someMapper.selectApkTotalChannel(map);
    }

    @Override
    public List<ApkUserVerVo> selectApkTotalVer(Map<String, Object> map) {
        return someMapper.selectApkTotalVer(map);
    }

    @Override
    public List<AssetsInfoTable> selectAssertsTable() {
        return someMapper.selectAssertsTable();
    }

    @Override
    public List<AssetsInfoTable> selectAssertsDepTable() {
        return someMapper.selectAssertsDepTable();
    }

    @Override
    public List<AssetsInfoTable> selectAssertsDateTable() {
        return someMapper.selectAssertsDateTable();
    }

    @Override
    public int insertLoginTotal(String table, List<LoginTotalVo> list) {
        return someMapper.insertLoginTotal(table, list);
    }

    @Override
    public List<LoginTotalVo> selectLoginTable(Map<Object, Object> map) {
        return someMapper.selectLoginTable(map);
    }

    @Override
    public List<LoginTotalVo> selectLoginDetail(Map<Object, Object> map) {
        return someMapper.selectLoginDetail(map);
    }


    public static String convertDateTime(String oldDate) {
        return oldDate.substring(0, 10).replace("-", "");
    }

    private String converSqlUpQuery(ClientPostParamVo conditionVo, String conTime, Boolean flag, String endTime) {
		/*String conTime = "";
		String convertBegin = "";
		String convertEnd = "";

		if(flag != null && flag)//有开始时间
		{
			conTime = convertDateTime(temTime);
			convertBegin = "'"+temTime+"','yyyy-mm-dd hh24:mi:ss'";
			convertEnd = "'"+conTime +" 23:59:59','yyyymmdd hh24:mi:ss'";
		}else if(flag != null && !flag)//使用结束时间
		{
			conTime = convertDateTime(temTime);
			convertBegin = "'"+conTime +" 00:00:00','yyyymmdd hh24:mi:ss'";
			convertEnd = "'"+temTime+"','yyyy-mm-dd hh24:mi:ss'";
		}
		else
		{
			conTime = temTime;
			convertBegin = "'"+temTime +" 00:00:00','yyyymmdd hh24:mi:ss'";
			convertEnd = "'"+temTime +" 23:59:59','yyyymmdd hh24:mi:ss'";
		}*/

        StringBuffer sql = new StringBuffer("select  a.lsn,");
        sql.append("a.imei,");
        sql.append("a.productid,");
        sql.append("a.projectid,");
        sql.append("a.smscenter,");
        sql.append("a.version,");
        sql.append("a.release_verno,");
        sql.append("a.screnres,");
        sql.append("from_base64(a.wifissid) wifissid,");

        sql.append("a.oaid,");
        sql.append("a.macaddr,");
        sql.append("a.cityid,");
        sql.append("a.net,");
        sql.append("a.gametimes,");
        sql.append("a.mobilemodel,");
        sql.append("a.mmid,");
        if (Integer.valueOf(conTime) - Integer.valueOf("20200801") > 0) {
            sql.append("a.opencount,");
        }
        sql.append("a.createtime from np_post_log_" + conTime + " a");
        sql.append(" where 1 = 1");


        if (conditionVo.getProjectid() != null && !"".equals(conditionVo.getProjectid())) {
            sql.append(" and a.projectid = " + conditionVo.getProjectid() + "");
        }
        if (conditionVo.getProductid() != null && !"".equals(conditionVo.getProductid())) {
            sql.append(" and a.productid = " + conditionVo.getProductid() + "");
        }
        if (conditionVo.getImei() != null && !"".equals(conditionVo.getImei())) {
            sql.append(" and a.imei = '" + conditionVo.getImei() + "'");
        }
        if (conditionVo.getLsn() != null && !"".equals(conditionVo.getLsn())) {
            sql.append(" and a.lsn = " + conditionVo.getLsn() + "");
        }
        if (conditionVo.getCha_id() != null && !"".equals(conditionVo.getCha_id())) {
            sql.append(" and a.mmid = " + conditionVo.getCha_id() + "");
        }
        return sql.toString();
    }
    
    private String converSqlUpQueryTwo(ClientPostParamVo conditionVo, String conTime, Boolean flag, String endTime) {

        StringBuffer sql = new StringBuffer("select  a.lsn,");
        sql.append("a.imei,");
        sql.append("a.productid,");
        sql.append("a.projectid,");
        sql.append("a.smscenter,");
        sql.append("a.version,");
        sql.append("a.release_verno,");
        sql.append("a.screnres,");

        sql.append("a.oaid,");
        sql.append("a.macaddr,");
        sql.append("a.cityid,");
        sql.append("a.net,");
        sql.append("a.gametimes,");
        sql.append("a.mobilemodel,");
        sql.append("a.mmid,");
        
        sql.append("a.wifissid,");
        sql.append("a.cname,");
        if (Integer.valueOf(conTime) - Integer.valueOf("20200801") > 0) {
            sql.append("a.opencount,");
        }
        sql.append("a.createtime from dnwx_wjy.np_post_log_" + conTime + " a");
        sql.append(" where 1 = 1");


        if (conditionVo.getProjectid() != null && !"".equals(conditionVo.getProjectid())) {
            sql.append(" and a.projectid = " + conditionVo.getProjectid() + "");
        }
        if (conditionVo.getProductid() != null && !"".equals(conditionVo.getProductid())) {
            sql.append(" and a.productid = " + conditionVo.getProductid() + "");
        }
        if (conditionVo.getImei() != null && !"".equals(conditionVo.getImei())) {
            sql.append(" and a.imei = '" + conditionVo.getImei() + "'");
        }
        if (conditionVo.getLsn() != null && !"".equals(conditionVo.getLsn())) {
            sql.append(" and a.lsn = " + conditionVo.getLsn() + "");
        }
        if (conditionVo.getCha_id() != null && !"".equals(conditionVo.getCha_id())) {
            sql.append(" and a.mmid = " + conditionVo.getCha_id() + "");
        }
        return sql.toString();
    }

    @Override
    public List<com.wbgame.pojo.push.ConfigVo> selectNewAdNameOpenConfig(String seqids) {
        return someMapper.selectNewAdNameOpenConfig(seqids);
    }

    @Override
    public int insertSDKConfigInfo(SDKConfigInfo record) {
        return someMapper.insertSDKConfigInfo(record);
    }

    @Override
    public List<SDKConfigInfo> selectSDKConfigInfo(SDKConfigInfo record) {
        return someMapper.selectSDKConfigInfo(record);
    }

    @Override
    public int updateSDKConfigInfo(SDKConfigInfo record) {
        return someMapper.updateSDKConfigInfo(record);
    }

    @Override
    public List<UmengChannelConfig> selectUmengChannelConfig(String id) {
        return someMapper.selectUmengChannelConfig(id);
    }

    @Override
    public int insertUmengChannelConfig(UmengChannelConfig umengChannelConfig) {
        return someMapper.insertUmengChannelConfig(umengChannelConfig);
    }

    @Override
    public int updateUmengChannelConfig(UmengChannelConfig umengChannelConfig) {
        return someMapper.updateUmengChannelConfig(umengChannelConfig);
    }

    @Override
    public int deleteUmengChannelConfig(UmengChannelConfig umengChannelConfig) {
        return someMapper.deleteUmengChannelConfig(umengChannelConfig);
    }

    @Override
    public List<UmengChannelTotalVo> selectUmengChangeInfo(
            Map<String, Object> map) {
        return someMapper.selectUmengChangeInfo(map);
    }

    @Transactional(value = "masterTransactionManager")
    @Override
    public int insertDnChannelCost(List<DnChannelCost> list) {
        // 检验已审核通过的日期数据，已审核过则不允许覆盖
        if (list != null && list.size() > 0) {
            String sql = "select ischeck from umeng_channel_check where tdate = '" + list.get(0).getTdate() + "'";
            List<String> check = adMapper.queryListString(sql);
            if (check != null && check.size() > 0 && "1".equals(check.get(0))) {
                return -100;
            }
        }

        int recode = someMapper.insertDnChannelCost(list);

        // 合作方收支数据入库
        List<String> asList = Arrays.asList("37862", "37930", "37892", "37944");
        List<DnChannelCost> collect = list.stream()
                .filter(act -> !asList.contains(act.getAppid()))
                .collect(Collectors.toList());
        someMapper.insertDnChannelCostTwo(collect);

        return recode;
    }

    @Override
    public List<DnChannelCost> selectDnChannelCost(Map<String, Object> map) {
        return someMapper.selectDnChannelCost(map);
    }

    @Override
    public List<DnChannelTotal> selectDnChannelTotal(Map<String, Object> map) {
        return someMapper.selectDnChannelTotal(map);
    }

    @Override
    public int updateMarketSysFind(MarketSysFind marketSysFind) {
        return someMapper.updateMarketSysFind(marketSysFind);
    }

    @Override
    public List<MarketSysFind> selectMarketSysFind(MarketSysFind marketSysFind) {
        return someMapper.selectMarketSysFind(marketSysFind);
    }

    @Override
    public int deleteMarketSysFind(MarketSysFind marketSysFind) {
        return someMapper.deleteMarketSysFind(marketSysFind);
    }

    @Override
    public int insertMarketSysFind(MarketSysFind marketSysFind) {
        return someMapper.insertMarketSysFind(marketSysFind);
    }

    @Override
    public String[] selectfindVal(String type) {
        return someMapper.selectfindVal(type);
    }

    @Override
    public int batchCopyMarketSysFindByPageUrl(List<MarketSysFind> list) {
        return someMapper.batchCopyMarketSysFindByPageUrl(list);
    }

    @Override
    public int batchEditMarketSysFindByAppIdOrPid(MarketSysFind vo) {
        return someMapper.batchEditMarketSysFindByAppIdOrPid(vo);
    }

    @Transactional
    @Override
    public int updateDnChannelTotalAll(DnChannelTotal dnChannelTotal) {
        someMapper.updateDnChannelTotalOne(dnChannelTotal);
        someMapper.updateDnChannelTotalTwo(dnChannelTotal);
        someMapper.updateDnChannelTotalThree(dnChannelTotal);
        return 1;
    }

    @Override
    public int deleteDnChannelTotalAll(DnChannelTotal dct) {
        // 删除产品收支汇总表 和 成本数据表记录
        someMapper.deleteDnChannelTotalOne(dct);
        someMapper.deleteDnChannelTotalTwo(dct);
        return 1;
    }

    @Override
    public List<Map<String, Object>> selectDnChannelTotalNew(Map<String, Object> map) {
        return someMapper.selectDnChannelTotalNew(map);
    }

    @Override
    public Map<String, Object> selectDnChannelTotalNewSum(Map<String, Object> map) {
        return someMapper.selectDnChannelTotalNewSum(map);
    }

    @Override
    public List<Map<String, Object>> selectDnChannelTotal2021(Map<String, Object> map) {
        return someMapper.selectDnChannelTotal2021(map);
    }

    @Override
    public Map<String, Object> selectDnChannelTotal2021Sum(Map<String, Object> map) {
        return someMapper.selectDnChannelTotal2021Sum(map);
    }

    @Override
    public int importAppCategoryExcel(Map param) {
        return someMapper.importAppCategoryExcel(param);
    }

    @Override
    public UmengChannelTotalVo countUmengChannelTotal(Map<Object, Object> parmMap) {
        return someMapper.countUmengChannelTotal(parmMap);
    }

    @Override
    public List<Map<String, Object>> exportAppListInfo(Map<Object, Object> map) {
        return someMapper.exportAppListInfo(map);
    }

    @Override
    public int updeteFilterDeviceWhite(Map<Object, Object> map) {
        return someMapper.updeteFilterDeviceWhite(map);
    }

    @Override
    public List<Map<String, Object>> selectShielduser(Map<String, String> paramMap) {
        return someMapper.selectShielduser(paramMap);
    }

    @Override
    public List<Map<String, Object>> selectAppByCategory(String category) {
        return someMapper.selectAppByCategory(category);
    }

    @Override
    public List<Map<String, Object>> selectAppByCategorys(Map<String, String> paramMap) {
        return someMapper.selectAppByCategorys(paramMap);
    }

    @Override
    public List<Map<String, Object>> selectAppCategoryByRelation(String category) {
        List<Map<String, Object>> list = someMapper.selectAppCategoryByRelation(category);
        /* 将相同app_category_id的two_app_category_id作为List集合字段保存在对应的app_category_id对象下 */
        List<Map<String, Object>> collect = new ArrayList<>();
        list.stream().collect(Collectors.groupingBy(m -> m.get("app_category_id")+"#"+m.get("app_category_name"))).forEach((key, val) -> {

            Map<String, Object> info = new HashMap<>();
            List<Object> valList = val.stream().filter(act -> act.get("two_app_category_id") != null).map(m -> {
                m.remove("app_category_id");
                m.remove("app_category_name");
                return m;
            }).collect(Collectors.toList());
            info.put("app_category_id", key.split("#")[0]);
            info.put("app_category_name", key.split("#")[1]);
            info.put("two_app_category", valList);
            collect.add(info);
        });
        //collect按照app_category_id排序
        collect.sort((m1, m2) -> {
            return Integer.valueOf(m1.get("app_category_id")+"").compareTo(Integer.valueOf(m2.get("app_category_id")+""));
        });
        return collect;
    }

    @Override
    public void deleteDnGroup() {
        someMapper.deleteDnGroup();
    }

    @Override
    public void batchInsertDnGroup(List<Map<String, Object>> list) {
        someMapper.batchInsertDnGroup(list);
    }

    @Async("aaaScheduler")
    @Override
    public void asyncCreateUmengEvent(List<String> configList, String operater, String email,String umeng_account) {
        logger.info("asyncCreateUmengEvent start");
        try {
            Address[] addresses = new Address[]{
                    new InternetAddress(email),
                    new InternetAddress("<EMAIL>"),
                    new InternetAddress("<EMAIL>")
            };
            Map<String,String> paramMap = new HashMap<>();
            if (!"all".equals(umeng_account)){
                paramMap.put("umeng_account",umeng_account);
            }
            List<AppInfoVo> appInfoList = selectAppListInfo(new HashMap<>());
            int i = 0;
            Map<String, String> retMap = new TreeMap<>();

            Pattern first = Pattern.compile("[a-zA-Z0-9_,\\-()=+!*;@#:%\\[\\]‘\\${}^|~\\n\\r\\t ]{1,255}");
            Pattern second = Pattern.compile("[a-zA-Z0-9_\u4e00-\u9fa5,\\-()=+!*;@#:%\\[\\]‘\\${}^|~\\n\\r\\t ]{1,255}");

            for (String s : configList) {
                i++;
                String[] config = s.split(",");
                if (config.length < 3) {
                    retMap.put("第" + i + "行配置,", "不符合要求");
                    continue;
                }

                String eventName = config[0].trim();
                String eventDisplayName = config[1].trim();
                String eventType = config[2].trim();

                //禁止操作事件
                if (Arrays.asList(FORBIDEN_LIST).contains(eventName)) {
                    continue;
                }
                for (String str : FORBIDEN_MATCH_LIST) {
                    if (eventName.contains(str)) {
                        continue;
                    }
                }

                if (BlankUtils.checkBlank(eventName) || BlankUtils.checkBlank(eventDisplayName) || BlankUtils.checkBlank(eventType)) {
                    retMap.put("第" + i + "行配置,", "不符合要求");
                    continue;
                }
                if (!first.matcher(eventName).matches() || !second.matcher(eventDisplayName).matches() || !org.apache.commons.lang.StringUtils.isNumeric(eventType)) {
                    retMap.put("第" + i + "行配置,", "不符合要求");
                    continue;
                }

                for (AppInfoVo appInfo : appInfoList) {
                    if (!BlankUtils.checkBlank(appInfo.getUmeng_account()) && !BlankUtils.checkBlank(appInfo.getUmeng_key())) {
                        Boolean eventTypeFlag = "1".equals(eventType);
                        UmengUappEventCreateParam param = new UmengUappEventCreateParam();
                        param.setAppkey(appInfo.getUmeng_key());
                        param.setEventName(eventName);
                        try {
                            param.setEventDisplayName(URLEncoder.encode(eventDisplayName, "UTF-8"));
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                        param.setEventType(eventTypeFlag);

                        // 获取友盟账户的执行器
                        ApiExecutor apiExecutor = UsertagTask.getUmengAccountExecutor(appInfo.getUmeng_account());
                        if (apiExecutor == null) {
                            retMap.put("第" + i + "行配置," + "产品:" + appInfo.getId() + "-" + appInfo.getApp_name(), ",UemngAccount+UmengKey没有配置对");
                        } else {

                            String result = umengUappEventCreate(apiExecutor, param);
                            if (!BlankUtils.checkBlank(result)) {
                                JSONObject retJson = JSONObject.parseObject(result);
                                if (!"0".equals(retJson.getString("status"))) {
                                    retMap.put("第" + i + "行配置," + "产品:" + appInfo.getId() + "-" + appInfo.getApp_name(), "请求友盟接口没有生效");
                                }
                            } else {
                                retMap.put("第" + i + "行配置," + "产品:" + appInfo.getId() + "-" + appInfo.getApp_name(), "请求友盟接口没有生效");
                            }
                        }
                    }
                }
            }
            StringBuffer sb = new StringBuffer();
            sb.append("<table>");
            sb.append("<tr><td><span style='color:red'>操作人:" + operater + ",邮箱:" + email + "</span></td></tr>");
            if (retMap.size()>0){
                for (Map.Entry<String, String> map : retMap.entrySet()) {
                    sb.append("<tr><td><span>" + map.getKey() + map.getValue() + "</span></td></tr>");
                }
            }else {
                sb.append("<tr><td><span>" +"创建成功</span></td></tr>");
            }

            sb.append("</table>");
            String mailState = MailToolTwo.sendMails(sb.toString(), addresses);
            logger.info("asyncCreateUmengEvent end,mailState:" + mailState);
        } catch (Exception e) {
            logger.error("asyncCreateUmengEvent error", e);
        }

    }

    @Override
    public List<Map<String,Object>> getDepartments(Map<String,Object> param) {
        return someMapper.getDepartments(param);
    }

    @Override
    public List<UmengChannelTotalVo> selectUmengChannelTotalNew(Map<Object, Object> map) {
        return someMapper.selectUmengChannelTotalNew(map);
    }

    @Override
    public List<UmengChannelTotalVo> selectUmengChannelReportNew(Map<Object, Object> map) {
        return someMapper.selectUmengChannelReportNew(map);
    }

    @Override
    public UmengChannelTotalVo countUmengChannelTotalNew(Map<Object, Object> parmMap) {
        return someMapper.countUmengChannelTotalNew(parmMap);
    }

    public String umengUappEventCreate(ApiExecutor apiExecutor, UmengUappEventCreateParam param) {
        String retString = "";
        try {
            apiExecutor.setServerHost("gateway.open.umeng.com");
            UmengUappEventCreateResult result = apiExecutor.execute(param);
            logger.info("umengUappEventCreate umeng_key:"+param.getAppkey()+",result:"+JSON.toJSONString(result));
            retString = JSONObject.toJSONString(result);
        } catch (OceanException e) {
            logger.error("umengUappEventCreate:::::errorCode=" + e.getErrorCode() + ", errorMessage=" + e.getErrorMessage() + ",appkey=" + param.getAppkey());
        }
        return retString;
    }

    @Override
    public List<String> getChannelPlatforms() {
        return someMapper.getChannelPlatforms();
    }

    @Override
    public void updateXyxId(String appid, String xyx_id, String find_vals) {
        someMapper.updateXyxId(appid,xyx_id,find_vals);
    }
}