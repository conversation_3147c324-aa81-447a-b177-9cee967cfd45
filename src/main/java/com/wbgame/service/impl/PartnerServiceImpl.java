package com.wbgame.service.impl;

import com.wbgame.mapper.haiwaiad.HaiwaiCfgMapper;
import com.wbgame.mapper.master.PartnerOverseasMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.wbgame.utils.BlankUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.mapper.master.PartnerMapper;
import com.wbgame.mapper.master.PartnerOverseasMapper;
import com.wbgame.mapper.master.SomeMapper;
import com.wbgame.pojo.DnChannelInfo;
import com.wbgame.pojo.adv2.DnChaRevenueTotal;
import com.wbgame.pojo.adv2.DnPartnerRateVo;
import com.wbgame.service.PartnerService;

@Service("partnerService")
public class PartnerServiceImpl implements PartnerService {

	Logger logger = LoggerFactory.getLogger(PartnerServiceImpl.class);
	
	@Autowired
	private PartnerMapper partnerMapper;
	@Autowired
	private SomeMapper someMapper;
	@Autowired
	private DnwxBiMapper dnwxBiMapper;

	@Autowired
	private PartnerOverseasMapper partnerOverseasMapper;
	@Autowired
	private HaiwaiCfgMapper haiwaiCfgMapper;

	
	@Override
	public <T> List<T> queryListBean(String sql, Class<T> clazz) {
		List<Map<String, String>> listBean = partnerMapper.queryListMapOne(sql);
		return JSONArray.parseArray(JSONArray.toJSONString(listBean), clazz);
	}
	
	@Override
	public List<Map<String, Object>> selectPartnerInvestInfoNew(Map<String, String> paramMap) {
		return partnerMapper.selectPartnerInvestInfoNew(paramMap);
	}
	@Override
	public Map<String, Object> selectPartnerInvestInfoNewSum(Map<String, String> paramMap) {
		return partnerMapper.selectPartnerInvestInfoNewSum(paramMap);
	}
	
	@Override
	public List<Map<String, Object>> selectPartnerRevenueInfoNew(Map<String, String> paramMap) {
		return partnerMapper.selectPartnerRevenueInfoNew(paramMap);
	}
	@Override
	public Map<String, Object> selectPartnerRevenueInfoNewSum(Map<String, String> paramMap) {
		return partnerMapper.selectPartnerRevenueInfoNewSum(paramMap);
	}

	@Override
	public List<Map<String, Object>> selectPartnerAppRevenueNew(Map<String, String> paramMap) {
		return partnerMapper.selectPartnerAppRevenueNew(paramMap);
	}
	@Override
	public Map<String, Object> selectPartnerAppRevenueNewSum(Map<String, String> paramMap) {
		return partnerMapper.selectPartnerAppRevenueNewSum(paramMap);
	}


	@Override
	public boolean syncPartnerInvestInfoNew(String tdate) {
		try {
			String del = "delete from partner_invest_info where tdate='"+tdate+"' ";
			partnerMapper.execSql(del);
			
			partnerMapper.insertPartnerInvestNew(tdate);


			/** 单独苹果ADS广告的投放金额 */
			String adsQuery = String.format("SELECT tdate,appid,'apple' cha_media,ROUND(SUM(spend),2) rebate_consume FROM dnwx_bi.ads_apple_ads_game_ios where tdate BETWEEN '%s' and '%s' GROUP BY tdate,appid", tdate, tdate);
			List<Map<String, String>> mapList = dnwxBiMapper.queryListMapOne(adsQuery);
			if(mapList != null && !mapList.isEmpty()){
				List<DnChaRevenueTotal> adsList = JSONArray.parseArray(JSON.toJSONString(mapList), DnChaRevenueTotal.class);
				partnerMapper.insertPartnerInvestNewOfCps(adsList);
			}

			/** 单独更新cps合作渠道的投放金额 */
			String sql = "select '"+tdate+"' tdate,dnappid appid,cha_id,agent,cha_type_name,cha_media,IFNULL(TRUNCATE(sum(aa.revenue),2),0) ad_revenue "+
					"from dn_cha_cash_total aa where date = '"+tdate+"' and app_id != '0' and dnappid != '0' and ad_sid != '' and cha_id is not null "+
					"and cha_id in (SELECT cha_id FROM dn_channel_info WHERE cha_type=8 and cha_id != 'ylyq') "+
					"GROUP BY dnappid,cha_media ";
			List<DnChaRevenueTotal> reList = queryListBean(sql, DnChaRevenueTotal.class);
			
			// 获取cps合作渠道对应的分成比例值
			Map<String, DnChannelInfo> ratioMap = someMapper.selectDnChannelRatMap();
			
			List<DnChaRevenueTotal> collect = reList.stream().map(dnc -> {
				// 判断子渠道标识，计算投放金额
				DnChannelInfo chaRatio = ratioMap.get(dnc.getCha_id());
				if(chaRatio != null){
					BigDecimal rot = new BigDecimal(chaRatio.getChaRatio());
					dnc.setRebate_consume(new BigDecimal(dnc.getAd_revenue()).multiply(rot).toString());
					return dnc;
				}
				
				return null;
			}).filter(Objects::nonNull).collect(Collectors.toList());
			
			if(collect != null && !collect.isEmpty()){
				partnerMapper.insertPartnerInvestNewOfCps(collect);
			}
			
			/** 计算合作方支出比例配置 */
			String query = "select * from partner_invest_rate";
			List<DnPartnerRateVo> rateList = queryListBean(query, DnPartnerRateVo.class);
			for (DnPartnerRateVo rate : rateList) {
				
				String update = "update partner_invest_info set invest_amount=TRUNCATE(invest_amount*#{obj.rate},2) where tdate='"+tdate+"' "+ 
						"and appid in (select id from app_info where app_category=#{obj.app_category}) "+
						"and cha_media=#{obj.cha_media} ";
				partnerMapper.execSqlHandle(update, rate);
			}
			
			
			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}


	@Override
	public boolean syncPartnerRevenueInfoNew(String tdate) {
		try {
			String del = "delete from partner_revenue_info where tdate='"+tdate+"' ";
			partnerMapper.execSql(del);
			
			partnerMapper.insertPartnerRevenueNew(tdate);
			
			/** 计算合作方收入比例配置 */
			String query = "select * from partner_revenue_rate";
			List<DnPartnerRateVo> rateList = queryListBean(query, DnPartnerRateVo.class);
			for (DnPartnerRateVo rate : rateList) {
				
				String update = "update partner_revenue_info set revenue=TRUNCATE(revenue*#{obj.rate},2) where tdate='"+tdate+"' "+ 
						"and appid in (select id from app_info where app_category=#{obj.app_category}) "+
						"and agent=#{obj.agent} ";
				partnerMapper.execSqlHandle(update, rate);
			}
			
			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	
	@Override
	public boolean syncPartnerAppBillingNew(String day) {

		/** 同步付费收入金额和付费退款金额  */
		try {
			logger.info("执行syncAppRevenueNewBilling-refund 开始...");
			String sql = "delete from wb_pay_refund_total where tdate = '"+day+"' ";
			partnerMapper.execSql(sql);

			Map<String, String> paramMap = new HashMap<String, String>();
			paramMap.put("tdate", day);
			partnerMapper.insertPartnerAppPayRefund(paramMap);
			partnerMapper.updatePartnerAppPayRefundRate(paramMap);

			logger.info("执行syncAppRevenueNewBilling-refund 完成...");
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncAppRevenueNewBilling-refund 异常...");
			return false;
		}


    	/** 拉取付费收入，其中需要排除bus_category=2小游戏类型 以及 处理苹果支付类型应用*0.7  */
    	try {
    		logger.info("执行syncAppRevenueNewBilling 开始...");
        	
        	partnerMapper.insertPartnerAppRevenueNewBilling(day);
        	
        	logger.info("执行syncAppRevenueNewBilling 完成...");
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncAppRevenueNewBilling 异常...");
			return false;
		}
    	
    	/** 小游戏类型产品数据单独拉取  */
    	try {
    		logger.info("执行syncAppRevenueNewBilling-xyx 开始...");

			String query = "select id from app_info where bus_category=2";
			List<String> appList = partnerMapper.queryListString(query);
			String apps = String.join(",", appList);

			/* 小游戏新增活跃数据 */
    		String sql = "select tdate,appid,sum(reg_user_cnt) addnum,sum(active_user_cnt) actnum,'0' refund_revenue,'0' pay_revenue,'0' paynum from dnwx_bi.ads_wechat_user_cnt_daily "+
    				"where tdate = '"+day+"' and appid in ("+apps+") group by tdate,appid";
    		List<Map<String, String>> list = dnwxBiMapper.queryListMapOne(sql);

			/* 小游戏微信支付费率 */
    		String sql1 = "select '"+day+"' as tdate,appid,CONCAT('"+day+"',appid) mapkey,IFNULL(paynum,0) paynum,"+
    							"IFNULL(TRUNCATE(SUM(CASE WHEN rate is not NULL THEN pay_revenue*rate/100 WHEN paytype='腾讯米大师支付' THEN pay_revenue*0.57 WHEN paytype='微信支付' THEN pay_revenue*0.98 WHEN paytype='聚合支付' THEN pay_revenue*0.98 ELSE pay_revenue END)/100,2),0) pay_revenue, "+
    							"IFNULL(TRUNCATE(SUM(CASE WHEN rate is not NULL THEN refund_revenue*rate/100 WHEN paytype='腾讯米大师支付' THEN refund_revenue*0.57 WHEN paytype='微信支付' THEN refund_revenue*0.98 WHEN paytype='聚合支付' THEN refund_revenue*0.98 ELSE refund_revenue END)/100,2),0) refund_revenue "+
					"from wb_pay_refund_total where tdate='"+day+"'  "+
						"and appid in (select id from app_info where app_category=17) group by tdate,appid ";
			Map<String, Map<String, Object>> payMap = partnerMapper.queryListMapOfKey(sql1);

			/* 微信-IAA支付费率 */
    		String sql2 = "select '"+day+"' as tdate,appid,CONCAT('"+day+"',appid) mapkey,IFNULL(paynum,0) paynum,"+
    							"IFNULL(TRUNCATE(SUM(CASE WHEN rate is not NULL THEN pay_revenue*rate/100 WHEN paytype='腾讯米大师支付' THEN pay_revenue*0.57 WHEN paytype='微信支付' THEN pay_revenue*0.98 ELSE pay_revenue END)/100,2),0) pay_revenue, "+
    							"IFNULL(TRUNCATE(SUM(CASE WHEN rate is not NULL THEN refund_revenue*rate/100 WHEN paytype='腾讯米大师支付' THEN refund_revenue*0.57 WHEN paytype='微信支付' THEN refund_revenue*0.98 ELSE refund_revenue END)/100,2),0) refund_revenue "+
					"from wb_pay_refund_total where tdate='"+day+"'  "+
						"and appid in (select id from app_info where app_category=39) group by tdate,appid ";
			Map<String, Map<String, Object>> iaapayMap = partnerMapper.queryListMapOfKey(sql2);

			/* 抖音小游戏字节支付费率 */
    		String sql3 = "select '"+day+"' as tdate,appid,CONCAT('"+day+"',appid) mapkey,IFNULL(paynum,0) paynum,"+
    							"IFNULL(TRUNCATE(SUM(CASE WHEN rate is not NULL THEN pay_revenue*rate/100 WHEN paytype='字节支付' and chaid != 'dyly' THEN pay_revenue*0.9 ELSE pay_revenue END)/100,2),0) pay_revenue, "+
    							"IFNULL(TRUNCATE(SUM(CASE WHEN rate is not NULL THEN refund_revenue*rate/100 WHEN paytype='字节支付' and chaid != 'dyly' THEN refund_revenue*0.9 ELSE refund_revenue END)/100,2),0) refund_revenue "+
					"from wb_pay_refund_total where tdate='"+day+"'  "+
						"and appid in (select id from app_info where app_category=43) group by tdate,appid ";
			Map<String, Map<String, Object>> ttpayMap = partnerMapper.queryListMapOfKey(sql3);


			/* qq小游戏qq支付费率 */
    		String sql4 = "select '"+day+"' as tdate,appid,CONCAT('"+day+"',appid) mapkey,IFNULL(paynum,0) paynum,"+
    							"IFNULL(TRUNCATE(SUM(CASE WHEN rate is not NULL THEN pay_revenue*rate/100 WHEN paytype='qq支付' THEN pay_revenue*0.6 ELSE pay_revenue END)/100,2),0) pay_revenue, "+
    							"IFNULL(TRUNCATE(SUM(CASE WHEN rate is not NULL THEN refund_revenue*rate/100 WHEN paytype='qq支付' THEN refund_revenue*0.6 ELSE refund_revenue END)/100,2),0) refund_revenue "+
					"from wb_pay_refund_total where tdate='"+day+"'  "+
						"and appid in (select id from app_info where app_category=44) group by tdate,appid ";
			Map<String, Map<String, Object>> qqpayMap = partnerMapper.queryListMapOfKey(sql4);

			/* 华为小游戏华为支付费率 */
    		String sql5 = "select '"+day+"' as tdate,appid,CONCAT('"+day+"',appid) mapkey,IFNULL(paynum,0) paynum,"+
    							"IFNULL(TRUNCATE(SUM(CASE WHEN rate is not NULL THEN pay_revenue*rate/100 WHEN paytype='华为支付' THEN pay_revenue*0.6 ELSE pay_revenue END)/100,2),0) pay_revenue, "+
    							"IFNULL(TRUNCATE(SUM(CASE WHEN rate is not NULL THEN refund_revenue*rate/100 WHEN paytype='华为支付' THEN refund_revenue*0.6 ELSE refund_revenue END)/100,2),0) refund_revenue "+
					"from wb_pay_refund_total where tdate='"+day+"'  "+
						"and appid in (select id from app_info where app_category=45) group by tdate,appid ";
			Map<String, Map<String, Object>> hwpayMap = partnerMapper.queryListMapOfKey(sql5);


			// 按照日期+产品ID匹配赋值付费金额
			for (Map<String, String> act : list) {
				Map<String, Object> act1 = payMap.get(act.get("tdate")+act.get("appid"));
				if(act1 != null){
					act.put("pay_revenue", act1.get("pay_revenue")+"");
					act.put("refund_revenue", act1.get("refund_revenue")+"");
					act.put("paynum", act1.get("paynum")+"");
					act.put("app_category", "17");
				}

				Map<String, Object> act2 = iaapayMap.get(act.get("tdate")+act.get("appid"));
				if(act2 != null){
					act.put("pay_revenue", act2.get("pay_revenue")+"");
					act.put("refund_revenue", act2.get("refund_revenue")+"");
					act.put("paynum", act2.get("paynum")+"");
					act.put("app_category", "39");
				}

				Map<String, Object> act3 = ttpayMap.get(act.get("tdate")+act.get("appid"));
				if(act3 != null){
					act.put("pay_revenue", act3.get("pay_revenue")+"");
					act.put("refund_revenue", act3.get("refund_revenue")+"");
					act.put("paynum", act3.get("paynum")+"");
					act.put("app_category", "43");
				}

				Map<String, Object> act4 = qqpayMap.get(act.get("tdate")+act.get("appid"));
				if(act4 != null){
					act.put("pay_revenue", act4.get("pay_revenue")+"");
					act.put("refund_revenue", act4.get("refund_revenue")+"");
					act.put("paynum", act4.get("paynum")+"");
					act.put("app_category", "44");
				}

				Map<String, Object> act5 = hwpayMap.get(act.get("tdate")+act.get("appid"));
				if(act5 != null){
					act.put("pay_revenue", act5.get("pay_revenue")+"");
					act.put("refund_revenue", act5.get("refund_revenue")+"");
					act.put("paynum", act5.get("paynum")+"");
					act.put("app_category", "45");
				}
			}
			
    		if(list != null && !list.isEmpty()){
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("sql1", "REPLACE INTO partner_app_revenue_total(tdate,appid,actnum,addnum,pay_revenue,refund_revenue,paynum,app_category,ischeck) values ");
				paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.actnum},#{li.addnum},#{li.pay_revenue},#{li.refund_revenue},#{li.paynum},#{li.app_category},'0') ");
				paramMap.put("sql3", " ");
				paramMap.put("list", list);
				partnerMapper.batchExecSql(paramMap);
    		}
        	
        	logger.info("执行syncAppRevenueNewBilling-xyx 完成...");
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncAppRevenueNewBilling-xyx 异常...");
			return false;
		}
    	
		return true;
	}
	
	@Transactional(value = "masterTransactionManager")
	@Override
	public boolean checkPartnerAppCostTotal(Map<String, String> paramMap) {
		
		String tdate = paramMap.get("sdate");
		/** 审核收支稽核查询页面时，同时审核明细的两个页面 */
		String sql = "update partner_app_revenue_total set ischeck='1' where tdate='"+tdate+"' ";
		partnerMapper.execSql(sql);
		
		String sql2 = "update partner_invest_info set ischeck='1' where tdate='"+tdate+"' ";
		partnerMapper.execSql(sql2);
        
        String sql3 = "update partner_revenue_info set ischeck='1' where tdate='"+tdate+"' ";
        partnerMapper.execSql(sql3);
		
        partnerMapper.insertPartnerAppCostTotal(paramMap);
		return true;
	}
	
	@Transactional(value = "masterTransactionManager")
	@Override
	public boolean updatePartnerAppCostBilling(Map<String, String> paramMap) {
		
		/** 收支稽核查询调整付费收入，同时更新审核前审核后的两个页面 */
		String sql = "update partner_app_revenue_total set pay_revenue=#{obj.pay_revenue},refund_revenue=#{obj.refund_revenue} where tdate=#{obj.tdate} and appid=#{obj.appid} ";
		partnerMapper.execSqlHandle(sql, paramMap);
		
		String sql2 = "update partner_channel_cost set billing_income=#{obj.pay_revenue},refund_revenue=#{obj.refund_revenue} where tdate=#{obj.tdate} and appid=#{obj.appid} ";
		partnerMapper.execSqlHandle(sql2, paramMap);
		
		return true;
	}
	



	@Override
	public boolean distributeReduceAmount(Map<String, String> paramMap) {
		try {
			// 查询该月该产品的每日付费收入数据
			String sql = "SELECT tdate, appid, pay_revenue FROM partner_app_revenue_total WHERE DATE_FORMAT(tdate,'%Y-%m') = #{obj.month} AND appid = #{obj.appid} AND pay_revenue > 0 ";
			List<Map<String, Object>> dailyRevenues = partnerMapper.queryListMapTwo(sql, paramMap);

			if (dailyRevenues.isEmpty()) {
				return false;
			}

			// 计算总付费收入
			BigDecimal totalRevenue = BigDecimal.ZERO;
			for (Map<String, Object> day : dailyRevenues) {
				BigDecimal dayRevenue = new BigDecimal(day.get("pay_revenue").toString());
				totalRevenue = totalRevenue.add(dayRevenue);
			}

			BigDecimal totalReduceAmount = new BigDecimal(paramMap.get("reduce_amount"));
			Random random = new Random();

			// 先按比例计算每天的基础核减金额，并添加随机浮动
			List<Map<String, Object>> updateList = new ArrayList<>();
			BigDecimal allocatedAmount = BigDecimal.ZERO;

			// 先计算除最后一天外的所有天数的核减金额（带随机浮动）
			for (int i = 0; i < dailyRevenues.size() - 1; i++) {
				Map<String, Object> day = dailyRevenues.get(i);
				String tdate = day.get("tdate").toString();
				String appid = day.get("appid").toString();
				BigDecimal dayRevenue = new BigDecimal(day.get("pay_revenue").toString());

				// 计算当天应分配的核减金额比例
				BigDecimal ratio = dayRevenue.divide(totalRevenue, 10, RoundingMode.HALF_UP);
				BigDecimal baseReduceAmount = totalReduceAmount.multiply(ratio).setScale(2, RoundingMode.HALF_UP);

				// 在基础金额上随机上下浮动（±10%）
				double fluctuation = 0.9 + random.nextDouble() * 0.2; // 随机浮动因子：0.9到1.1之间
				BigDecimal dayReduceAmount = baseReduceAmount.multiply(new BigDecimal(fluctuation)).setScale(2, RoundingMode.HALF_UP);

				// 确保单日核减金额不超过当日收入
				if (dayReduceAmount.compareTo(dayRevenue) > 0) {
					dayReduceAmount = dayRevenue;
				}

				allocatedAmount = allocatedAmount.add(dayReduceAmount);

				// 计算核减后的付费收入
				BigDecimal reducedPayRevenue = dayRevenue.subtract(dayReduceAmount).setScale(2, RoundingMode.HALF_UP);

				Map<String, Object> updateMap = new HashMap<>();
				updateMap.put("tdate", tdate);
				updateMap.put("appid", appid);
				updateMap.put("reduce_amount", dayReduceAmount);
				updateMap.put("reduce_pay_revenue", reducedPayRevenue);
				updateList.add(updateMap);
			}

			// 最后一天用于调整，确保总金额准确
			Map<String, Object> lastDay = dailyRevenues.get(dailyRevenues.size() - 1);
			String lastTdate = lastDay.get("tdate").toString();
			String appid = lastDay.get("appid").toString();
			BigDecimal lastDayRevenue = new BigDecimal(lastDay.get("pay_revenue").toString());

			// 计算最后一天应分配的核减金额（确保总和等于传入的reduce_amount）
			BigDecimal lastDayReduceAmount = totalReduceAmount.subtract(allocatedAmount).setScale(2, RoundingMode.HALF_UP);;

			// 确保最后一天的核减金额不超过当天收入且不为负
			if (lastDayReduceAmount.compareTo(lastDayRevenue) > 0) {
				lastDayReduceAmount = lastDayRevenue;
			}
			if (lastDayReduceAmount.compareTo(BigDecimal.ZERO) < 0) {
				lastDayReduceAmount = BigDecimal.ZERO;
			}

			// 计算核减后的付费收入
			BigDecimal lastDayReducedPayRevenue = lastDayRevenue.subtract(lastDayReduceAmount).setScale(2, RoundingMode.HALF_UP);;

			Map<String, Object> lastUpdateMap = new HashMap<>();
			lastUpdateMap.put("tdate", lastTdate);
			lastUpdateMap.put("appid", appid);
			lastUpdateMap.put("reduce_amount", lastDayReduceAmount);
			lastUpdateMap.put("reduce_pay_revenue", lastDayReducedPayRevenue);
			updateList.add(lastUpdateMap);

			logger.info("分摊核减金额结果: " + JSON.toJSONString(updateList));
			updateList.forEach(act -> System.out.println(act.get("reduce_amount")+"\t"+act.get("reduce_pay_revenue")));

			// 批量插入或更新核减表
			if (!updateList.isEmpty()) {
				Map<String, Object> batchParam = new HashMap<>();
				batchParam.put("sql1", "INSERT INTO partner_pay_reduce (tdate, appid, reduce_amount, reduce_pay_revenue, ischeck, createtime) VALUES ");
				batchParam.put("sql2", "(#{li.tdate}, #{li.appid}, #{li.reduce_amount}, #{li.reduce_pay_revenue}, '0', now())");
				batchParam.put("sql3", " ON DUPLICATE KEY UPDATE reduce_amount = VALUES(reduce_amount), reduce_pay_revenue = VALUES(reduce_pay_revenue), ischeck = '0'");
				batchParam.put("list", updateList);
				partnerMapper.batchExecSql(batchParam);
			}
			
			return true;
		} catch (Exception e) {
			logger.error("分摊核减金额失败", e);
			return false;
		}
	}

	@Override
	public boolean updateReduceAmount(Map<String, String> paramMap) {
		try {
			// 只更新核减后付费金额字段
			String insertSql = "INSERT INTO partner_pay_reduce (tdate, appid, reduce_amount, reduce_pay_revenue, ischeck, createtime, cuser) VALUES "+
					"(#{obj.tdate}, #{obj.appid}, '0', #{obj.reduce_pay_revenue}, '2', now(), #{obj.cuser})"+
					" ON DUPLICATE KEY UPDATE reduce_pay_revenue=VALUES(reduce_pay_revenue), ischeck='2', createtime=now(), cuser=VALUES(cuser) ";
//			String updateSql = "UPDATE partner_pay_reduce SET createtime=now(),cuser=#{obj.cuser},ischeck='2',reduce_pay_revenue=#{obj.reduce_pay_revenue} " +
//					"WHERE tdate = #{obj.tdate} AND appid = #{obj.appid}";
			partnerMapper.execSqlHandle(insertSql, paramMap);

			return true;
		} catch (Exception e) {
			logger.error("修改核减金额失败", e);
			return false;
		}
	}

	@Transactional(value = "masterTransactionManager")
	@Override
	public boolean syncReducedPayToAudit(Map<String, String> paramMap) {
		try {
			// 查询核减后的付费收入
			String querySql = "SELECT tdate, appid, reduce_pay_revenue FROM partner_pay_reduce WHERE DATE_FORMAT(tdate,'%Y-%m')=#{obj.month} AND appid=#{obj.appid} ";
			List<Map<String, Object>> results = partnerMapper.queryListMapTwo(querySql, paramMap);

			// 更新稽核表中的付费收入
			String updateSql = "UPDATE partner_app_revenue_total SET pay_revenue = #{li.reduce_pay_revenue} WHERE tdate = #{li.tdate} AND appid = #{li.appid}";
			Map<String, Object> info = new HashMap();
			info.put("sql1", updateSql);
			info.put("list", results);
			partnerMapper.batchExecSqlTwo(info);

			// 更新合作方结果表中的付费收入
			String updateSql2 = "UPDATE partner_channel_cost SET billing_income = #{li.reduce_pay_revenue} WHERE tdate = #{li.tdate} AND appid = #{li.appid}";
			Map<String, Object> info2 = new HashMap();
			info2.put("sql1", updateSql2);
			info2.put("list", results);
			partnerMapper.batchExecSqlTwo(info2);

			// 更新核减表的审核状态
			String updateReduceSql = "UPDATE partner_pay_reduce SET ischeck = '1' WHERE DATE_FORMAT(tdate,'%Y-%m')=#{obj.month} AND appid=#{obj.appid} ";
			partnerMapper.execSqlHandle(updateReduceSql, paramMap);

			return true;
		} catch (Exception e) {
			logger.error("同步核减后付费收入到稽核失败", e);
			return false;
		}
	}



	@Override
	public List<Map<String, Object>> selectPartnerAppRevenueWithReduce(Map<String, String> paramMap) {
		try {
			return partnerMapper.selectPartnerAppRevenueWithReduce(paramMap);
		} catch (Exception e) {
			logger.error("查询合作产品收入与核减信息失败", e);
			return new ArrayList<>();
		}
	}

	@Override
	public Map<String, Object> selectPartnerAppRevenueWithReduceSum(Map<String, String> paramMap) {
		try {
			return partnerMapper.selectPartnerAppRevenueWithReduceSum(paramMap);
		} catch (Exception e) {
			logger.error("查询合作产品收入与核减信息汇总失败", e);
			return new HashMap<>();
		}
	}

    // ===============================================================
    // Overseas Methods (海外版方法) - Implementation
    // ===============================================================

    @Override
    public List<Map<String, Object>> selectPartnerInvestInfoNewOverseas(Map<String, String> paramMap) {
        return partnerMapper.selectPartnerInvestInfoNew(paramMap);
    }

    @Override
    public Map<String, Object> selectPartnerInvestInfoNewSumOverseas(Map<String, String> paramMap) {
        return partnerMapper.selectPartnerInvestInfoNewSum(paramMap);
    }

    @Override
    public List<Map<String, Object>> selectPartnerRevenueInfoNewOverseas(Map<String, String> paramMap) {
        return partnerMapper.selectPartnerRevenueInfoNew(paramMap);
    }

    @Override
    public Map<String, Object> selectPartnerRevenueInfoNewSumOverseas(Map<String, String> paramMap) {
        return partnerMapper.selectPartnerRevenueInfoNewSum(paramMap);
    }


    @Override
    public List<Map<String, Object>> selectPartnerAppRevenueNewOverseas(Map<String, String> paramMap) {
        return partnerMapper.selectPartnerAppRevenueNew(paramMap);
    }

    @Override
    public Map<String, Object> selectPartnerAppRevenueNewSumOverseas(Map<String, String> paramMap) {
        return partnerMapper.selectPartnerAppRevenueNewSum(paramMap);
    }

    public Map<String, Map<String, Object>> selectDimCountryMap() {
		String query = "SELECT country_code mapkey, country_name FROM dim_country ";
        return partnerMapper.queryListMapOfKey(query);
    }

	@Override
	public boolean syncPartnerInvestInfoNewOverseas(String tdate, Map<String, String> contextParams) {
		try {
			String del = "delete from partner_invest_info_overseas where tdate='"+tdate+"' ";
			partnerOverseasMapper.execSql(del);

			List<Map<String, String>> list =
					haiwaiCfgMapper.queryListMapOne(String.format("SELECT `day` tdate,app appid,media cha_media,`country` country_code, IFNULL(TRUNCATE(SUM(rebateSpend), 2),0) invest_amount, '0' as 'ischeck' FROM dnwx_bi.dn_report_spend_oversea WHERE `day` = '%s' and LENGTH(app) = 5 GROUP BY app,media,country", tdate));
			partnerOverseasMapper.insertPartnerInvestNewOfList(list);


			/** 计算合作方支出比例配置 */
//			String query = "select * from partner_invest_rate";
//			List<DnPartnerRateVo> rateList = queryListBean(query, DnPartnerRateVo.class);
//			for (DnPartnerRateVo rate : rateList) {
//
//				String update = "update partner_invest_info_overseas set invest_amount=TRUNCATE(invest_amount*#{obj.rate},2) where tdate='"+tdate+"' "+
//						"and appid in (select id from app_info where app_category=#{obj.app_category}) "+
//						"and cha_media=#{obj.cha_media} ";
//				partnerMapper.execSqlHandle(update, rate);
//			}


			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}


	@Override
	public boolean syncPartnerRevenueInfoNewOverseas(String tdate, Map<String, String> contextParams) {
		try {
			String del = "delete from partner_revenue_info_overseas where tdate='"+tdate+"' ";
			partnerOverseasMapper.execSql(del);

			List<Map<String, String>> list =
					haiwaiCfgMapper.queryListMapOne(String.format("SELECT date tdate,dnappid appid,agent,`country` country_code, IFNULL(TRUNCATE(sum(t1.dollar_revenue),2),0) revenue, IFNULL(TRUNCATE(sum(t1.pv),0),0) pv, '0' as 'ischeck' FROM dnwx_bi.dn_cha_cash_total t1 WHERE date='%s' and app_id != '0' and dnappid in (select id from dnwx_cfg.app_info where app_category in (15,16)) GROUP BY date,dnappid,agent,country ", tdate));

			partnerOverseasMapper.insertPartnerRevenueNewOfList(list);

			/** 计算合作方收入比例配置 */
//			String query = "select * from partner_revenue_rate";
//			List<DnPartnerRateVo> rateList = queryListBean(query, DnPartnerRateVo.class);
//			for (DnPartnerRateVo rate : rateList) {
//
//				String update = "update partner_revenue_info_overseas set revenue=TRUNCATE(revenue*#{obj.rate},2) where tdate='"+tdate+"' "+
//						"and appid in (select id from app_info where app_category=#{obj.app_category}) "+
//						"and agent=#{obj.agent} ";
//				partnerMapper.execSqlHandle(update, rate);
//			}

			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	@Override
	public boolean syncPartnerAppBillingNewOverseas(String tdate, Map<String, String> contextParams) {

		/** 拉取付费收入，其中需要排除bus_category=2小游戏类型 以及 处理苹果支付类型应用*0.7  */
		try {
			logger.info("执行syncAppRevenueNewBillingOverseas 开始...");

			List<Map<String, String>> list = haiwaiCfgMapper.selectPartnerAppRevenueNewBilling(tdate);
			partnerOverseasMapper.insertPartnerAppRevenueNewBillingOfList(list);

			logger.info("执行syncAppRevenueNewBillingOverseas 完成...");
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("执行syncAppRevenueNewBillingOverseas 异常...");
		}
		return false;
	}

	@Transactional(value = "masterTransactionManager")
	@Override
	public boolean checkPartnerAppCostTotalOverseas(Map<String, String> paramMap) {

		String tdate = paramMap.get("sdate");
		/** 审核收支稽核查询页面时，同时审核明细的两个页面 */
		String sql = "update partner_app_revenue_total_overseas set ischeck='1' where tdate='"+tdate+"' ";
		partnerMapper.execSql(sql);

		String sql2 = "update partner_invest_info_overseas set ischeck='1' where tdate='"+tdate+"' ";
		partnerMapper.execSql(sql2);

		String sql3 = "update partner_revenue_info_overseas set ischeck='1' where tdate='"+tdate+"' ";
		partnerMapper.execSql(sql3);

		// 写合作方表
//		partnerMapper.insertPartnerAppCostTotal(paramMap);
		return true;
	}
	@Transactional(value = "masterTransactionManager")
	@Override
	public boolean updatePartnerAppCostBillingOverseas(Map<String, String> paramMap) {

		/** 收支稽核查询调整付费收入，同时更新审核前审核后的两个页面 */
		String sql = "update partner_app_revenue_total_overseas set pay_revenue=#{obj.pay_revenue},refund_revenue=#{obj.refund_revenue} where tdate=#{obj.tdate} and appid=#{obj.appid} ";
		partnerMapper.execSqlHandle(sql, paramMap);

		// 写合作方表
//		String sql2 = "update partner_channel_cost set billing_income=#{obj.pay_revenue},refund_revenue=#{obj.refund_revenue} where tdate=#{obj.tdate} and appid=#{obj.appid} ";
//		partnerMapper.execSqlHandle(sql2, paramMap);

		return true;
	}

}