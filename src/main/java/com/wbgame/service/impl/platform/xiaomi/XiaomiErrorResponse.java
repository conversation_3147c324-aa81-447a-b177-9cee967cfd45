package com.wbgame.service.impl.platform.xiaomi;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/30
 * @description
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class XiaomiErrorResponse {
    private int code;
    private ErrorDataWrapper data;
    private String message;
}

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
class ErrorDataWrapper {
    private int page;
    private int pageSize;
    private int total;
    private List<XiaomiError> dataList;
}

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
class XiaomiError {
    private String publisherId;
    private String errorCode;
    private String errorCodeMessage;
    private int requestFailNum;
    private int originRequestNum;
    private String requestFailRatio;
    private Object statisticList; // 可以根据实际需要调整类型
    private String suggestion;
}