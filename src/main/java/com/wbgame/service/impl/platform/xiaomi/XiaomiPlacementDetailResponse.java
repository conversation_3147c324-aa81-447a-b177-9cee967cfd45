package com.wbgame.service.impl.platform.xiaomi;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/30
 * @description
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class XiaomiPlacementDetailResponse {
    private int code;
    private PlacementDataWrapper data;
    private String message;
}

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
class PlacementDataWrapper {
    private int page;
    private int pageSize;
    private int total;
    private List<XiaomiPlacementDetailDetail> dataList;
}

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
class XiaomiPlacementDetailDetail {
    private String placementId;
    private String placementName;
    private int styleId;
    private String styleName;
    private int originRequestNum;
    private int effectRequestNum;
    private int fillingNum;
    private String fillingRate;
    private String gain;
    private String ecpm;
}