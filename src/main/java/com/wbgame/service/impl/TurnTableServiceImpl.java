package com.wbgame.service.impl;

import com.wbgame.mapper.master.TurnTableMapper;
import com.wbgame.pojo.nnjy.TurnTableVO;
import com.wbgame.service.TurnTableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TurnTableServiceImpl implements TurnTableService {

    @Autowired
    TurnTableMapper turnTableMapper;

    @Override
    @Transactional
    public int insertTurnTable(List<TurnTableVO> turnTableNumList, List<TurnTableVO> turnTableTypeList, List<TurnTableVO> turnTablePrizeList) {
        //写入玩转盘人数
        if (turnTableNumList.size() > 0) {
            Map<String, Object> map1 = new HashMap<String, Object>();
            map1.put("list", turnTableNumList);
            turnTableMapper.insertTurnTableNum(map1);
        }

        if (turnTableTypeList.size() > 0) {
            //写入各类转盘人数
            Map<String, Object> map2 = new HashMap<String, Object>();
            map2.put("list", turnTableTypeList);
            turnTableMapper.insertTurnTableTypeNum(map2);
        }

        if (turnTablePrizeList.size() > 0) {
            //写入各类中奖人数
            Map<String, Object> map3 = new HashMap<String, Object>();
            map3.put("list", turnTablePrizeList);
            turnTableMapper.insertTurnTablePrizeNum(map3);
        }
        return 0;
    }

    @Override
    public List<TurnTableVO> getTurnTableList(Map<String, Object> map) {
        return turnTableMapper.getTurnTableList(map);
    }
}
