package com.wbgame.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.mapper.master.LeyuanMapper;
import com.wbgame.service.LeyuanService;
import com.wbgame.utils.BlankUtils;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class LeyuanServiceImpl implements LeyuanService {


    Logger logger = LoggerFactory.getLogger(LeyuanService.class);

    private static final String login_url = "https://developer.233xyx.com/apiserv/developer/cp/login/login";

    private static final String data_url = "https://developer.233xyx.com/developer/ad/bill/data/board/query";

    @Resource
    private LeyuanMapper leyuanMapper;

    @Override
    public void synLeyuanData(String start, String end) throws Exception{
        logger.info("-----------------开始同步233乐园应用数据---------------------------");
        JSONObject login = new JSONObject();
        login.put("secret", "ZG53eDE2MDI=");
        login.put("phone", "13822170555");
        logger.info("开始登录...");
        String r = send(login_url, login, "utf-8", null);
        String token = "";
        try {
            JSONObject jsonObject = JSONObject.parseObject(r);
            if ("200".equals(jsonObject.getString("code"))) {
                token = jsonObject.getJSONObject("data").getString("token");
            }
            logger.info("登录结果:" + jsonObject.getString("message"));
        } catch (Exception e) {
            logger.info("登录结果:异常:" + e.getMessage());
            e.printStackTrace();
        }
        if (!BlankUtils.checkBlank(token)) {
            logger.info("开始抓取数据...");
            //数据汇集
            List<JSONObject> data = new ArrayList<>();
            //参数
            JSONObject param = new JSONObject();
            param.put("appKeys", new ArrayList<>());
            param.put("posList", new ArrayList<>());
            param.put("groupDimension", "GAME_ID");
            param.put("startDate", DateTime.parse(start).getMillis());
            param.put("endDate", DateTime.parse(end).plusDays(1).minusSeconds(1).getMillis());
            param.put("pageSize",1000000);
            param.put("pageNum",1);
            try {
                String d = send(data_url, param, "utf-8", token);
                JSONObject result = JSONObject.parseObject(d);
                if ("200".equals(result.getString("code"))) {
                    JSONArray jsonArray = result.getJSONObject("data").getJSONObject("tableData").getJSONArray("dataList");
                    if (jsonArray != null) {
                        data = jsonArray.toJavaList(JSONObject.class);
                    }
                }
                logger.info("抓取数据量:"+data.size());
                if (data.size() > 0) {
                    logger.info("开始入库...");
                    for (JSONObject j : data) {
                        j.put("date",dateConvertion(j.getString("date")));
                        j.put("channel","233");
                        j.put("add_num",0);
                        j.put("act_num",0);
                    }
                    leyuanMapper.batchInsertLeyuanList(data);
                    logger.info("入库完成,同步完成");
                }
            } catch (Exception e) {
                logger.info("异常中断爬取数据...");
                e.printStackTrace();
            }
        }
    }

    /**
     *@Description:日期转换，将接口返回的20180524转为2018-05-24
     *@param str 传递的日期字符串
     */
    private static String dateConvertion(String str){
        Date parse = null;
        String dateString = "";
        try {
            parse = new SimpleDateFormat("yyyyMMdd").parse(str);
            dateString = new SimpleDateFormat("yyyy-MM-dd").format(parse);
        } catch (Exception e) {
            dateString=null;
        }

        return dateString;
    }

    /**
     * 发送post请求
     *
     * @param url        路径
     * @param jsonObject 参数(json类型)
     * @param encoding   编码格式
     * @return
     * @throws ParseException
     * @throws IOException
     */
    public static String send(String url, JSONObject jsonObject, String encoding, String token) throws ParseException, IOException {
        String body = "";
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        //装填参数
        StringEntity s = new StringEntity(jsonObject.toString(), "utf-8");
        s.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE,
                "application/json"));
        //设置参数到请求对象中
        httpPost.setEntity(s);
        //设置header信息
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        if (!BlankUtils.checkBlank(token)) {
            httpPost.setHeader("233auth", token);
        }
        //执行请求操作，并拿到结果（同步阻塞）
        CloseableHttpResponse response = client.execute(httpPost);
        //获取结果实体
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            //按指定编码转换结果实体为String类型
            body = EntityUtils.toString(entity, encoding);
        }
        EntityUtils.consume(entity);
        //释放链接
        response.close();
        return body;
    }

}
