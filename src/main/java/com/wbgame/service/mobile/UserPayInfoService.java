package com.wbgame.service.mobile;

import com.wbgame.pojo.game.*;
import com.wbgame.pojo.game.pay.PayActivityVo;
import com.wbgame.pojo.game.report.PayAdValueReportVo;
import com.wbgame.pojo.mobile.UserPayInfoVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname UserPayInfoService
 * @Description TODO
 * @Date 2022/3/8 18:16
 */
public interface UserPayInfoService {

    List<UserPayInfoVo> getUserPayInfoList(Map<String,String> map);

    UserPayInfoVo getUserPayInfoSum(Map<String,String> map);

    List<PayGapVo> getPayGapList(Map<String,String> map);

    PayGapVo getPayGapSum(Map<String,String> map);

    List<PayCashAnalysisVo> getPayCashList(Map<String, String> map);

    PayCashAnalysisVo getPayCashSum(Map<String,String> map);

    List<PayCashAnalysisVo> getPayCashTotalList(Map<String,String> map);

    List<TimingTrendVo> getTimingTrendList(Map<String,String> map);

    TimingTrendVo getTimingTrendSum(Map<String,String> map);

    List<Map<String, Object>> getTimingDaysList(Map<String,String> map);

    List<PayAdValueVo> getPayAdValueList(Map<String,String> map);

    PayAdValueReportVo getPayAdValueDayReportList(Map<String,String> map);

    PayAdValueVo getPayAdValueSum(Map<String,String> map);

    List<PayAdValueVo> getPayAdValueTotalList(Map<String,String> map);

    PayAdValueReportVo getPayAdValueTotalReportList(Map<String,String> map);

    PayAdValueVo getPayAdValueTotalSum(Map<String,String> map);

    List<PayNotArrivedOrderVo> getNotArrivedOrderList(Map<String,String> map);

    PayNotArrivedOrderVo getNotArrivedOrderSum(Map<String,String> map);

    List<PayActivityVo> getPayActivityList(Map<String,String> map);

    PayActivityVo getPayActivitySum(Map<String,String> map);

    void updateOrderMark(String orderid, String login_name);
}
