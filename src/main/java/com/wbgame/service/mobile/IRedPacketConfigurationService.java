package com.wbgame.service.mobile;

import com.wbgame.pojo.mobile.*;

import java.util.List;

/**
 * @authoer: zhangY
 * @createDate: 2022/6/13 10:54
 * @class: IRedPacketConfigurationService
 * @description:
 */
public interface IRedPacketConfigurationService {

    /**
     * 内购支付监控配置
     */
    String insertMachMonitorConfig(WxMachMonitorConfigDTO machDTO);
    String updateMachMonitorConfig(WxMachMonitorConfigDTO machDTO);
    String deleteMachMonitorConfig(List<WxMachMonitorConfigDTO> machDTOList);
    String selectMachMonitorConfig(WxMachMonitorConfigQuery query);

    /**
     *通用提现ip白名单配置
     */
    String insertHbWithdrawIpConfig(HbWithdrawIpConfigDTO hbWithdrawIpConfigDTO);
    String deleteHbWithdrawIpConfig(List<Integer> idList);
    String updateHbWithdrawIpConfig(HbWithdrawIpConfigDTO hbWithdrawIpConfigDTO);
    String selectWithdrawConfig(HbWithdrawIpConfigQuery query);
}
