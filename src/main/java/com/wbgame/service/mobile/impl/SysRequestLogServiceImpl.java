package com.wbgame.service.mobile.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.master.YdMapper;
import com.wbgame.pojo.mobile.SysRequestLogDTO;
import com.wbgame.service.mobile.SysRequestLogService;
import com.wbgame.utils.DataTransUtils;
import com.wbgame.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 系统页面访问情况业务实现层
 * @Date 2024/12/10 15:13
 */
@Service
public class SysRequestLogServiceImpl implements SysRequestLogService {


    @Autowired
    private YdMapper ydMapper;

    /**
     * 查询系统页面访问情况
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    @Override
    public Result<List<SysRequestLogDTO>> queryList(SysRequestLogDTO dto) {
        //参数校验
        dto.checkPageParams();
        dto.setPage_index(DataTransUtils.transToSql(dto.getPage_index()));
        String key = dto.getKey();
        if (!StringUtils.isEmpty(key)) {
            String[] split = key.split("&");
            dto.setIndex(split[0]);
            dto.setSymbol(split[1]);
            dto.setNumber(split[2]);
        }
        //数据分页查询
        PageHelper.startPage(dto.getStart(), dto.getLimit());
        List<SysRequestLogDTO> sysRequestLogList = ydMapper.querySysRequestLogList(dto);
        PageInfo<SysRequestLogDTO> pageInfo = new PageInfo<>(sysRequestLogList);
        List<SysRequestLogDTO> resultList = pageInfo.getList();
        //封装时间
        String tdate = dto.getStart_date() + "至" + dto.getEnd_date();
        resultList.forEach(data -> data.setTdate(tdate));
        return ResultUtils.success(Constants.OK, resultList, null, pageInfo.getTotal());
    }


}
