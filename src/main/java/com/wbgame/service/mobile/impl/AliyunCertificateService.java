package com.wbgame.service.mobile.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.cas20200407.models.*;
import com.aliyun.tea.TeaException;
import com.wbgame.common.ApiException;
import com.wbgame.pojo.mobile.CertificateConfig;
import com.wbgame.utils.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/5/28 14:59
 */
//@Service
public class AliyunCertificateService {

    private static final String ACCESS_KEY_ID = "LTAI5tBok41ELTNMdhi6XCD7";
    private static final String ACCESS_KEY_SECRET = "******************************";


    private static final ConcurrentHashMap<String, String> CERTIFICATE_API_CLIENT_MAP = new ConcurrentHashMap<>(16);





    /**
     * <b>description</b> :
     * <p>使用凭据初始化账号Client</p>
     *
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.cas20200407.Client createClient() throws Exception {
        // 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.credentials.Client credential = new com.aliyun.credentials.Client();
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setCredential(credential);
        // Endpoint 请参考 https://api.aliyun.com/product/cas
        config.endpoint = "cas.aliyuncs.com";
        config.accessKeyId = ACCESS_KEY_ID;
        config.accessKeySecret = ACCESS_KEY_SECRET;
        return new com.aliyun.cas20200407.Client(config);
    }

    /**
     * <b>description</b> :
     * <p>使用凭据初始化账号Client</p>
     *
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.cas20200407.Client createClient(CertificateConfig params) throws Exception {
        // 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.credentials.Client credential = new com.aliyun.credentials.Client();
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setCredential(credential);
        // Endpoint 请参考 https://api.aliyun.com/product/cas
        config.endpoint = "cas.aliyuncs.com";
        config.accessKeyId = params.getAccess_key_id();
        config.accessKeySecret = params.getAccess_key_secret();
        return new com.aliyun.cas20200407.Client(config);
    }

    /**
     * 上传阿里云证书接口
     *
     * @param domain 域名
     * @param cert   证书
     * @param key    私钥
     * @return 上传结果
     * @throws Exception
     */
    public static UploadUserCertificateResponseBody uploadUserCertificate(CertificateConfig config,String domain, String cert, String key) throws Exception {
        com.aliyun.cas20200407.Client client = createClient(config);
        com.aliyun.cas20200407.models.UploadUserCertificateRequest uploadUserCertificateRequest = new com.aliyun.cas20200407.models.UploadUserCertificateRequest();
        //请求参数封装
        String replace = domain.replace("*.", "");
        String certificateName = replace + "_" + DateUtil.getDate(new Date()) + "_" + System.currentTimeMillis();
        uploadUserCertificateRequest.setName(certificateName).setCert(cert).setKey(key);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            UploadUserCertificateResponse response = client.uploadUserCertificateWithOptions(uploadUserCertificateRequest, runtime);
            System.out.println("response = " + JSON.toJSONString(response));
            return response.getBody();
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new ApiException(error.getMessage() + "," + error.getData().get("Recommend"));
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new Exception(error.getMessage());
        }
    }

    /**
     * 获取所有部署任务列表
     *
     * @return
     */
    public static List<ListDeploymentJobResponseBody.ListDeploymentJobResponseBodyData> listDeploymentJobs(String status) throws Exception {
        com.aliyun.cas20200407.Client client = createClient();
        com.aliyun.cas20200407.models.ListDeploymentJobRequest listDeploymentJobRequest = new com.aliyun.cas20200407.models.ListDeploymentJobRequest();
        listDeploymentJobRequest.setStatus(status);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        List<ListDeploymentJobResponseBody.ListDeploymentJobResponseBodyData> resultList = new ArrayList<>();
        try {
            int pageSize = 50;
            int total = 50;
            for (int page = 1; (page - 1) * pageSize < total; page++) {
                listDeploymentJobRequest.setCurrentPage(page);
                listDeploymentJobRequest.setShowSize(pageSize);
                // 复制代码运行请自行打印 API 的返回值
                ListDeploymentJobResponse listDeploymentJobResponse = client.listDeploymentJobWithOptions(listDeploymentJobRequest, runtime);
                ListDeploymentJobResponseBody body = listDeploymentJobResponse.getBody();
                total = body.total.intValue();
                resultList.addAll(body.getData());
            }
            return resultList;
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new ApiException(error.getMessage() + "," + error.getData().get("Recommend"));
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new Exception(error.getMessage());
        }
    }


    /**
     * 更新部署任务
     *
     * @throws Exception
     */
    public static UpdateDeploymentJobResponseBody updateDeploymentJob(Long jobId, String cerIds) throws Exception {
        com.aliyun.cas20200407.Client client = createClient();
        com.aliyun.cas20200407.models.UpdateDeploymentJobRequest updateDeploymentJobRequest = new com.aliyun.cas20200407.models.UpdateDeploymentJobRequest()
                .setJobId(jobId)
                .setCertIds(cerIds);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            UpdateDeploymentJobResponse updateDeploymentJobResponse = client.updateDeploymentJobWithOptions(updateDeploymentJobRequest, runtime);
            return updateDeploymentJobResponse.getBody();
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new ApiException(error.getMessage() + "," + error.getData().get("Recommend"));
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new Exception(error.getMessage());
        }
    }


    /**
     * 创建部署任务
     *
     * @param name        部署任务名称
     * @param jobType     任务类型:user：云产品部署任务（不含云服务器）,cloud：多云部署任务
     * @param certIds     证书 ID，多个证书 ID 用半角逗号（,）分隔
     * @param resourceIds 云产品资源 ID，多个资源 ID 用半角逗号（,）分隔
     * @param contactIds  联系人 ID。多个联系人 ID 用半角逗号（,）分隔
     * @return 部署任务 ID。
     * @throws Exception
     */
    public static Long CreateDeploymentJob(String name, String jobType, String certIds, String resourceIds, String contactIds) throws Exception {

        com.aliyun.cas20200407.Client client = createClient();
        com.aliyun.cas20200407.models.CreateDeploymentJobRequest createDeploymentJobRequest = new com.aliyun.cas20200407.models.CreateDeploymentJobRequest()
                .setName(name)
                .setJobType(jobType)
                .setCertIds(certIds)
                .setResourceIds(resourceIds)
                .setContactIds(contactIds);
        //.setScheduleTime(System.currentTimeMillis() + 1000 * 10);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            CreateDeploymentJobResponse deploymentJobWithOptions = client.createDeploymentJobWithOptions(createDeploymentJobRequest, runtime);
            return deploymentJobWithOptions.getBody().getJobId();
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new ApiException(error.getMessage() + "," + error.getData().get("Recommend"));
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new Exception(error.getMessage());
        }
    }

    /**
     * 获取云厂商及对应云产品的资源列表
     *
     * @param cloudName    云厂商：aliyun：阿里云，Tencent：腾讯云
     * @param cloudProduct 云产品：ALB：应用负载均衡
     * @param keyword      根据云资源绑定域名或实例 ID 关键字过滤
     * @return
     * @throws Exception
     */
    public static List<ListCloudResourcesResponseBody.ListCloudResourcesResponseBodyData> ListCloudResources(String cloudName, String cloudProduct, String keyword) throws Exception {
        com.aliyun.cas20200407.Client client = createClient();
        com.aliyun.cas20200407.models.ListCloudResourcesRequest listCloudResourcesRequest = new com.aliyun.cas20200407.models.ListCloudResourcesRequest()
                .setCloudName(cloudName)
                //.setCloudName("aliyun")
                .setCloudProduct(cloudProduct)
                //.setCloudProduct("ALB")
                .setKeyword(keyword);
        //.setKeyword("zjh178.com");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            List<ListCloudResourcesResponseBody.ListCloudResourcesResponseBodyData> resultList = new ArrayList<>();
            int pageSize = 50;
            int total = 50;
            for (int page = 1; (page - 1) * pageSize < total; page++) {
                // 复制代码运行请自行打印 API 的返回值
                listCloudResourcesRequest.setCurrentPage(page);
                listCloudResourcesRequest.setShowSize(pageSize);
                ListCloudResourcesResponse resourcesResponse = client.listCloudResourcesWithOptions(listCloudResourcesRequest, runtime);
                ListCloudResourcesResponseBody body = resourcesResponse.getBody();
                total = body.getTotal().intValue();
                resultList.addAll(body.getData());
            }
            return resultList;
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new ApiException(error.getMessage() + "," + error.getData().get("Recommend"));
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new Exception(error.getMessage());
        }
    }

    /**
     * 获取联系人列表
     *
     * @param keyword 搜索关键词。例如姓名、邮箱和电话中的关键字
     * @return 联系人列表
     * @throws Exception
     */
    public static List<ListContactResponseBody.ListContactResponseBodyContactList> ListContact(String keyword) throws Exception {
        com.aliyun.cas20200407.Client client = createClient();
        com.aliyun.cas20200407.models.ListContactRequest listContactRequest = new com.aliyun.cas20200407.models.ListContactRequest()
                .setKeyword(keyword);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            List<ListContactResponseBody.ListContactResponseBodyContactList> contactList = new ArrayList<>();
            int pageSize = 50;
            int total = 50;
            for (int page = 1; (page - 1) * pageSize < total; page++) {
                listContactRequest.setCurrentPage(page);
                listContactRequest.setShowSize(pageSize);
                // 复制代码运行请自行打印 API 的返回值
                ListContactResponse listContactResponse = client.listContactWithOptions(listContactRequest, runtime);
                ListContactResponseBody body = listContactResponse.getBody();
                total = body.getTotalCount().intValue();
                contactList.addAll(body.getContactList());
            }
            return contactList;
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new ApiException(error.getMessage() + "," + error.getData().get("Recommend"));
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new Exception(error.getMessage());
        }
    }

    public static void DeleteDeploymentJob(Long jobId) throws Exception {
        com.aliyun.cas20200407.Client client = createClient();
        com.aliyun.cas20200407.models.DeleteDeploymentJobRequest deleteDeploymentJobRequest = new com.aliyun.cas20200407.models.DeleteDeploymentJobRequest()
                .setJobId(jobId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            client.deleteDeploymentJobWithOptions(deleteDeploymentJobRequest, runtime);
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new ApiException(error.getMessage() + "," + error.getData().get("Recommend"));
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new Exception(error.getMessage());
        }
    }

    /**
     * 更新部署任务状态
     *
     * @param jobId  部署任务 ID
     * @param status 目标状态：scheduling：立即调度任务。
     * @throws Exception
     */
    public static void UpdateDeploymentJobStatus(Long jobId, String status) throws Exception {
        com.aliyun.cas20200407.Client client = createClient();
        com.aliyun.cas20200407.models.UpdateDeploymentJobStatusRequest updateDeploymentJobStatusRequest = new com.aliyun.cas20200407.models.UpdateDeploymentJobStatusRequest()
                .setJobId(jobId)
                .setStatus(status);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            UpdateDeploymentJobStatusResponse statusResponse = client.updateDeploymentJobStatusWithOptions(updateDeploymentJobStatusRequest, runtime);
            UpdateDeploymentJobStatusResponseBody body = statusResponse.getBody();
            System.out.println("body = " + body);
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new ApiException(error.getMessage() + "," + error.getData().get("Recommend"));
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw new Exception(error.getMessage());
        }
    }


}
