package com.wbgame.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.wbgame.pojo.*;
import com.wbgame.pojo.custom.AdTotalHourTwoVo;
import com.wbgame.pojo.wbsys.WxActionRecord;

public interface SomeService {
	RespUserInfo selectUserInfoByAttr(Map<String, Object> map);
	List<RespUserInfo> selectUserInfoAllByAttr(Map<String, Object> map);
	int insertUserInfo(Map<String, Object> map);
	
	RespUserInfo queryUserByAttr(RequestVo rv,String type);
	
	// 礼包配置
	@Cached(name="SomeService.selectGiftConfig", expire = 3600, cacheType = CacheType.LOCAL)
	List<ConfigVo> selectGiftConfig();

	//抽奖配置
	@Cached(name="SomeService.drawConfig", expire = 3600, cacheType = CacheType.LOCAL)
	List<DrawConfigVo> drawConfig();
	@CacheInvalidate(name="SomeService.drawConfig")
    String invalidateDrawConfig();

	@Cached(name="SomeService.selectShortUrl", expire = 3600, cacheType = CacheType.LOCAL)
	List<ShortUrlVo> selectShortUrl();
	@CacheInvalidate(name="SomeService.selectShortUrl")
    String invalidateShortUrl();

	List<ShortUrlVo> selectShortUrlAll();
	
	int insertShortUrl(ShortUrlVo shortUrlVo);

	List<ApkAppInfoVo> selectAppInfo();

	List<ApkAppInfoVo> selectAppInfoData(String date);

	List<ApkUserTotalVo> selectApkUserTotal(String appid);
	List<ApkUserTotalVo> selectApkUserTotalTwo(Map<String, Object> paramMap);
	List<ApkUserTotalVo> selectApkUserTotalThree(Map<String, Object> paramMap);

	/**
	 * 新版本apk统计接口
	 * @param paramMap
	 * @return
	 */
	List<ApkUserTotalVo> selectApkUserTotalTwoV2(Map<String, Object> paramMap);
	ApkUserTotalVo selectApkUserTotalThreeV2(Map<String, Object> paramMap);
	ApkUserTotalVo selectTodayApkUserTotalThreeV2(Map<String, Object> paramMap);

	int insertDauUser(List<ApkUserTotalVo> list);

	int insertDauUserDetail(List<ApkUserTotalVo> list);

	List<ApkSelectNewVo> selectApkUserNew(Map<String, Object> map);

	List<ApkSelectNewVo> selectApkUserNewGroup(Map<String,Object> map);

	List<ApkSelectNewVo> selectApkUserDau(Map<String,Object> map);
	ApkSelectNewVo selectApkUserDauSum(Map<String, Object> map);

	List<ApkSelectNewVo> selectApkUserDauGroup(Map<String,Object> map);
	ApkSelectNewVo selectApkUserDauGroupSum(Map<String,Object> map);

	List<ApkKeepUserVo> selectApkUserKeep(Map<String,Object> map);

	List<ApkUserTotalVo> selectApkUserDetail(Map<String,Object> map);

	ApkUserTrendVo selectApkUserTrend(Map<String,Object> map);

	List<ApkUserTotalVo> selectApkUserTrendLine(Map<String,Object> map);

	List<ApkUserTotalVo> selectApkUserNewTrend(Map<String,Object> map);

	List<ApkUserVerVo> selectApkUserChannel(Map<String,Object> map);

	List<ApkUserVerVo> selectApkUserVer(Map<String,Object> map);

	int insertKeepNum(List<ApkUserKeepVo> list);

	int updateKeep7Num(List<ApkUserKeepVo> list);

	int updateKeep30Num(List<ApkUserKeepVo> list);

	int insertChannelFeeAdv(List<AdvChaFeeVo> list);

	int insertChannelFee(List<AdvFeeVo> list);

	List<TouSuVo> selectFeedBackConfig(Map<Object,Object> map);

	int insertFeedBackConfig(TouSuVo touSuVo);

	int updateFeedBackConfig(TouSuVo touSuVo);

	int removeFeedBackConfig(String id);

	List<ApkButtonVo> selectButtonInfo(Map<Object,Object> map);

	int updateButtonInfo(ApkButtonVo apkButtonVo);

	int insertButtonInfo(ApkButtonVo apkButtonVo);
	
	int insertUserTalkBack(UserTalkBack userTalkBack);

	ApkSystemConfigVo selectSystemConfig();

	int updateSystemConfig(ApkSystemConfigVo apkSystemConfigVo);

	List<ProjectVo> selectDhmInfo(Map<Object,Object> map);

	int insertDhmInfo(Map<Object,Object> map);
	
	int insertDhmInfoList(List<Map<String, Object>> list);

	List<NpPacUpdateVo> selectAppUpdateInfo(Map<Object,Object> map);

	int insertAppUpdateInfo(NpPacUpdateVo npPacUpdateVo);

	int updateAppUpdateInfo(NpPacUpdateVo npPacUpdateVo);

	int deleteAppUpdateInfo(Map<Object,Object> map);

	List<AppInfoVo> selectAppListInfo(Map<Object,Object> map);

	int insertAppListInfo(Map<Object,Object> map);

	int updateAppListInfo(Map<Object,Object> map);

	int deleteAppListInfo(String ids);
	/**
	 * 更新负责人的产品收支权限配置
	 * @param appid 应用ID
	 * @param find_vals 负责人组
	 * @return
	 */
	int updateMarketListFind(String appid, String find_vals);

	List<SelectStortVo> selectChannelInfo();

	List<UengIncomeShowVo> selectUmengEnter(Map<Object,Object> map);

	int insertUmengEnter(UengIncomeShowVo uengIncomeShowVo);
	int updateUmengEnter(UengIncomeShowVo uengIncomeShowVo);
	int deleteUmengEnter(UengIncomeShowVo uengIncomeShowVo);

	List<UmengTreeListVo> selectUmengList();

	List<UmengTreeListVo> selectUmengListTwo();

	List<UmengChannelTotalVo> selectUmengChannelTotal(Map<Object,Object> map);
	List<UmengChannelTotalVo> selectUmengChannelTotalGroup(Map<Object,Object> map);
	/**
	 * 查询友盟留存等数据  底表为友盟api最新底表数据
	 * @param map 
	 * @return
	 */
	List<UmengChannelTotalVo> selectUmengChannelTotalGroup2(Map<Object,Object> map);

	List<UmengChannelTotalVo> selectUmengChannelReport(Map<Object,Object> map);

	List<QPayVo> selectQpayInfo(Map<Object,Object> map);

	int insertQpayInfo(QPayVo qPayVo);

	int updateQpayInfo(QPayVo qPayVo);

	int deleteQpayInfo(String id);

	List<QpayRuleVo> selectQpayRuleInfo(Map<Object,Object> map);

	int insertQpayRuleInfo(QpayRuleVo qpayRuleVo);

	int updateQpayRuleInfo(QpayRuleVo qpayRuleVo);

	int deleteQpayRuleInfo(String id);

	List<NpPostVo> selectFilterDeviceBlack(Map<Object,Object> map);

	List<NpPostVo> selectFilterDeviceWhite(Map<Object,Object> map);

	List<NpPostVo> selectFilterDeviceIp(Map<Object,Object> map);

	int insertFilterDeviceBlack(Map<Object,Object> map);

	int insertFilterDeviceWhite(Map<Object,Object> map);

	int insertFilterDeviceIp(Map<Object,Object> map);

	int deleteFilterDeviceBlack(Map<Object,Object> map);

	int deleteFilterDeviceWhite(Map<Object,Object> map);

	int deleteFilterDeviceIp(Map<Object,Object> map);
	
	int updeteFilterDeviceWhite(Map<Object, Object> map);

	List<AdPrjVo> selectApiAdConfig(Map<Object,Object> map);

	int insertApiAdConfig(AdPrjVo adPrjVo);

	int updateApiAdConfig(AdPrjVo adPrjVo);

	int deleteApiAdConfig(Map<Object,Object> map);

	int openApiAdConfig(Map<Object,Object> map);

	List<ExtendVo> selectNewAdConfig(Map<Object,Object> map);

	int insertNewAdConfig(ExtendVo extendVo);

	int updateNewAdConfig(ExtendVo extendVo);

	int deleteNewAdConfig(String ids);

	int batchInsertNewAdConfig(List<ExtendVo> list);

	List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfig(Map<Object,Object> map);

	List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfigForSqlTest(Map<String, String> paramMap);
	List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfigForSql(Map<String, String> paramMap);

	int insertNewAdOpenConfig(com.wbgame.pojo.push.ConfigVo configVo);

	int batchupdateNewAdOpenConfig(List<com.wbgame.pojo.push.ConfigVo> list);

	int updateNewAdOpenConfig(com.wbgame.pojo.push.ConfigVo configVo);

	int deleteNewAdOpenConfig(Map<Object,Object> map);

	int openNewAdOpenConfig(Map<Object,Object> map);

	List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfigByPrj(String id);

	int batchInsertNewAdOpenConfig(List<com.wbgame.pojo.push.ConfigVo> list);

	//List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfigInfo(Map<Object,Object> map);

	List<com.wbgame.pojo.push.ConfigVo> selectNewAdOpenConfigInfoById(List<String> list);

	List<UmengTotalVo> selectProduct(Map<Object,Object> map);

	List<ProdutChannelDataVo> selectProductData(Map<Object,Object> map);

	int updateUmengProduct(Map<Object,Object> map);

	List<ExtendVo> selectNewAdConfigBySid(String str);

	List<NpActiveFree> selectAdTableConfig(Map<Object,Object> map);

	int updateAdTableConfig(NpActiveFree activeFree);

	int insertAdTableConfig(NpActiveFree activeFree);

	List<UmengTotalVo> selectIncomeData(Map<Object,Object> map);

	List<NpPostVo> selectPageAutoConfig(Map<Object,Object> map);
	int insertPageAuto(Map<Object,Object> map);

	List<ClientPostParamVo> queryClientPost(ClientPostParamVo conditionVo);
	List<ClientPostParamVo> queryClientPostTwo(ClientPostParamVo conditionVo);

	List<NpPostVo> selectNpMap(Map<String,String> map);

	List<UserNewCountVo> selectUserNewCount(Map<String,String> map);

	int insertApkGameTime(List<ApkGameTimeVo> list);

	List<ApkGameTimeVo> selectApkGameTime(Map<Object,Object> map);

	List<AdTotalHourTwoVo> selectAdTotalHour(AdTotalHourTwoVo adTotalHourVo);

	List<Map<String,Object>> selectNpMap(String sql);

	@Cached(name="SomeService.selectPathInfo", expire = 3600, cacheType = CacheType.LOCAL)
	List<SysPathVo> selectPathInfo();

	@CacheInvalidate(name="SomeService.selectPathInfo")
	String invalidatePathInfo();

	int insertLogInfo(WxActionRecord wxActionRecord);

	List<PkLogVo> SelectPkLog(String datestr);

	List<ProdutChannelDataVo> selectNewUserByPid(Map<String,Object> map);

	List<ProdutChannelDataVo> selectDauUserByPid(Map<String,Object> map);

	List<ProdutChannelDataVo> selectStartCountByPid(Map<String,Object> map);

	List<AssetsInfo> selectAsserts(Map<String,Object> map);

	int addAsserts(Map<String,Object> map);

	int updateAsserts(Map<String,Object> map);

	int handerAsserts(Map<String,Object> map);

	int batchAddAsserts(List<AssetsInfo> list);

	List<AssetsInfoLog> selectAssertsLog(Map<String,Object> map);

	int insertAssertsLog(AssetsInfoLog assetsInfoLog);

	int batchAssertsLog(List<AssetsInfoLog> list);

	List<ApkUserVerVo> selectApkTotalChannel(Map<String,Object> map);

	List<ApkUserVerVo> selectApkTotalVer(Map<String,Object> map);

	List<AssetsInfoTable> selectAssertsTable();

	List<AssetsInfoTable> selectAssertsDepTable();

	List<AssetsInfoTable> selectAssertsDateTable();

	int insertLoginTotal(String table, List<LoginTotalVo> list);

	List<LoginTotalVo> selectLoginTable(Map<Object,Object> map);

	List<LoginTotalVo> selectLoginDetail(Map<Object,Object> map);
	
	List<com.wbgame.pojo.push.ConfigVo> selectNewAdNameOpenConfig(String seqids);
	
	int insertSDKConfigInfo(SDKConfigInfo record);

    List<SDKConfigInfo> selectSDKConfigInfo(SDKConfigInfo record);

    int updateSDKConfigInfo(SDKConfigInfo record);
    
    List<UmengChannelConfig> selectUmengChannelConfig(String id);
    int insertUmengChannelConfig(UmengChannelConfig umengChannelConfig);
    int updateUmengChannelConfig(UmengChannelConfig umengChannelConfig);
    int deleteUmengChannelConfig(UmengChannelConfig umengChannelConfig);

    //广告-友盟新增活跃表
    List<UmengChannelTotalVo> selectUmengChangeInfo(Map<String, Object> parmMap);
    
    //导入成本
    int insertDnChannelCost(List<DnChannelCost> list);
    //页面查询
    List<DnChannelCost> selectDnChannelCost(Map<String,Object> map) ;
    List<DnChannelTotal> selectDnChannelTotal(Map<String,Object> map);
    
    //国内广告-产品收支权限配置	
    int updateMarketSysFind(MarketSysFind marketSysFind);
    List<MarketSysFind>selectMarketSysFind(MarketSysFind marketSysFind);
    int deleteMarketSysFind(MarketSysFind marketSysFind);
    int insertMarketSysFind(MarketSysFind marketSysFind);
    String [] selectfindVal(String type);
	/** 批量*/
    int batchCopyMarketSysFindByPageUrl(List<MarketSysFind> list);
    int batchEditMarketSysFindByAppIdOrPid(MarketSysFind vo);
    
    /** 修改产品收支汇总表 和 成本数据表 收支和新增活跃 */
    int updateDnChannelTotalAll(DnChannelTotal dnChannelTotal);
    /** 删除产品收支汇总表 和 成本数据表 记录 */
    int deleteDnChannelTotalAll(DnChannelTotal dct);
    
    /** 游戏产品收支汇总表 */
    List<Map<String,Object>> selectDnChannelTotalNew(Map<String,Object> map);
    Map<String,Object> selectDnChannelTotalNewSum(Map<String,Object> map);
    
    /** 游戏产品收支汇总表2021 */
    List<Map<String,Object>> selectDnChannelTotal2021(Map<String,Object> map);
    Map<String,Object> selectDnChannelTotal2021Sum(Map<String,Object> map);


    int importAppCategoryExcel(Map param);

    // 友盟渠道信息查询汇总
	UmengChannelTotalVo countUmengChannelTotal(Map<Object, Object> parmMap);

    //导出应用管理
    List<Map<String, Object>> exportAppListInfo(Map<Object, Object> map);

	/**
	 * 得到安装渠道和当前版本号-下拉框数据源
	 *
	 */
	Map<String, Object> getChannelsAndVersion(String appid);

	/**
	 * 实时查询
	 * @param map
	 * @return
	 */
	List<Map<String, Object>> selectApkUserDetailV2(Map<String, Object> map);

	/**
	 * 所有产品分类
	 * @return
	 */
    List<AppCategory> getAppCategorys();
    /** 所有产品的二级分类 */
    Map<String, Map<String, Object>> getTwoAppCategorys();

    void deleteDnGroupApp();

	void batchInsertDnGroupApp(List<Map<String, Object>> list);

    Long countUmengKey(String umeng_key);

	AppInfoVo selectAppInfoId(String hiddenId);
	
	/** 屏蔽设备明细查询 */
	public List<Map<String,Object>> selectShielduser(Map<String, String> paramMap);

    List<Map<String,Object>> selectAppByCategory(String category);

    /** 支持多选的应用分类取应用*/
	List<Map<String,Object>> selectAppByCategorys(Map<String, String> paramMap);

	/** 查询应用分类的级联关系  */
	List<Map<String, Object>> selectAppCategoryByRelation(String category);

    /** 删除主库 dn_group数据*/
    void deleteDnGroup();

    /** 写入主库 dn_group*/
	void batchInsertDnGroup(List<Map<String, Object>> list);

	/** 异步执行请求友盟创建自定义事件接口*/
	void asyncCreateUmengEvent(List<String> configList,String operater,String email,String umeng_account);

	List<Map<String,Object>> getDepartments(Map<String,Object> param);

	/** 渠道产品查询明细-新*/
	List<UmengChannelTotalVo> selectUmengChannelTotalNew(Map<Object, Object> map);

	/** 渠道产品周数据对比透视*/
	List<UmengChannelTotalVo> selectUmengChannelReportNew(Map<Object,Object> map);

	/** 渠道产品查询明细-新汇总*/
	UmengChannelTotalVo countUmengChannelTotalNew(Map<Object, Object> parmMap);

	/** 获取渠道站内信平台 **/
	List<String> getChannelPlatforms();

	/** 更新小游戏原始id **/
    void updateXyxId(String appid, String xyx_id, String find_vals);
}
