package com.wbgame.service;

import com.wbgame.pojo.mobile.ColumnVo;
import com.wbgame.pojo.mobile.CustomReportParam;
import com.wbgame.pojo.mobile.TableVo;

import java.util.List;
import java.util.Map;

/**
 * @description: 自定义报表业务
 * @author: huangmb
 * @date: 2021/04/16
 **/
public interface CustomReportService {

    /**
     * 获取库所有表
     * @return
     */
    List<TableVo> getTables();

    /**
     * 获取表所有字段属性
     * @param table
     * @return
     */
    List<ColumnVo> getColumns(String table);

    /**
     * 获取表所有字段名
     * @param table
     * @return
     */
    List<String> getStrColumns(String table);

    /**
     * 查询
     * @param customReportParam
     * @return
     */
    List<Map<String, Object>> selectCustomReport(CustomReportParam customReportParam);
}
