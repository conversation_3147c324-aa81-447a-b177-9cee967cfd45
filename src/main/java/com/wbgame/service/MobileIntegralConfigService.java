package com.wbgame.service;

import com.wbgame.pojo.ProductPriceConfigVo;
import com.wbgame.pojo.ProductRevenueVo;

import java.util.List;
import java.util.Map;

public interface MobileIntegralConfigService {

    int insert(ProductPriceConfigVo record);

    int updateByPrimaryKey(ProductPriceConfigVo record);

    List<ProductPriceConfigVo> selectByAll(ProductPriceConfigVo record);

    int deleteByPrimaryKey(ProductPriceConfigVo record);
    
    int insertProductRevenue();
    
    List<ProductRevenueVo> selectRevenueList(Map<String,Object> map);

}


