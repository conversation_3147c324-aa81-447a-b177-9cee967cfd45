package com.wbgame.service.jettison;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.jettison.MaterialTagAppDto;
import com.wbgame.utils.PageResult;

import java.util.List;



/**
 * @author: Xugx
 * @createDate: 2023/3/29
 * @class: MaterialTagAppManageService
 * @description:
 */
public interface MaterialTagAppManageService {

    Result<Integer> delete(List<String> mchid);

    Result<Integer> add(MaterialTagAppDto record);

    Result<PageResult<MaterialTagAppDto>> list(MaterialTagAppDto record);

    Result<Integer> update(MaterialTagAppDto record);
    
    
   
}
