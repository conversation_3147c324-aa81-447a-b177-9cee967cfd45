package com.wbgame.service.jettison.impl;

import com.wbgame.mapper.adb.SpiderDataMapper;
import com.wbgame.pojo.jettison.report.SpiderDataVo;
import com.wbgame.pojo.jettison.report.SpiderTaskVo;
import com.wbgame.pojo.jettison.report.param.SpiderDataParam;
import com.wbgame.pojo.jettison.report.param.SpiderTaskParam;
import com.wbgame.service.jettison.SpiderDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SpiderDataServiceImpl implements SpiderDataService {

    @Resource
    private SpiderDataMapper spiderDataMapper;

    @Override
    public List<SpiderTaskVo> selectSpiderTaskList(SpiderTaskParam spiderTaskParam) {
        return spiderDataMapper.selectSpiderTaskList(spiderTaskParam);
    }

    @Override
    public List<SpiderDataVo> selectSpiderDataList(SpiderDataParam spiderDataParam) {
        return spiderDataMapper.selectSpiderDataList(spiderDataParam);
    }
}
