package com.wbgame.service.jettison;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.jettison.param.BatchUpdateTfDateParam;
import com.wbgame.pojo.jettison.param.VivoBatchQueryParam;
import com.wbgame.pojo.jettison.param.VivoBatchTemplateParam;
import com.wbgame.pojo.jettison.vo.VivoBatchTemplateVo;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/29 15:49
 */
public interface VivoBatchTemplateService {

    JSONObject getTags();

    Boolean create(VivoBatchTemplateParam param);

    Boolean update(VivoBatchTemplateParam param);

    Boolean delete(Long temp_id);

    List<VivoBatchTemplateVo> selectVivoBatchTemplateList(VivoBatchQueryParam param);

    VivoBatchTemplateParam selectVivoBatchTemplateDetail(Long temp_id);

    void batchUpdateTfDate(BatchUpdateTfDateParam param);
}
