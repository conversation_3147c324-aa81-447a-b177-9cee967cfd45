package com.wbgame.service.jettison;


import java.util.List;

import com.wbgame.pojo.jettison.param.ActiveUserIncomParam;
import com.wbgame.pojo.jettison.param.CreativeReportParam;
import com.wbgame.pojo.jettison.param.KeyBehaviorParam;
import com.wbgame.pojo.jettison.vo.ActiveUserIncomVo;
import com.wbgame.pojo.jettison.vo.KeyBehaviorVo;
import com.wbgame.pojo.jettison.vo.MediaCreativeVo;



public interface RealizeService {
	
	
	public List<ActiveUserIncomVo> activeUserIncomeReport(ActiveUserIncomParam vo)throws Exception;
	
	public List<ActiveUserIncomVo> activeUserIncomeReportTotal(ActiveUserIncomParam vo)throws Exception;
	
	public List<MediaCreativeVo> creativeReport(CreativeReportParam param)throws Exception;
	
	public MediaCreativeVo creativeReportTotal(CreativeReportParam param)throws Exception;
	
	public List<MediaCreativeVo> WxGameCreativeReport(CreativeReportParam param)throws Exception;
	
	public List<MediaCreativeVo> WxGameCreativeReportTotal(CreativeReportParam param)throws Exception;
	
	public List<KeyBehaviorVo> keyBehaviorReport(KeyBehaviorParam vo)throws Exception;
	
	public List<KeyBehaviorVo> keyBehaviorReportTotal(KeyBehaviorParam vo)throws Exception;
	
}
