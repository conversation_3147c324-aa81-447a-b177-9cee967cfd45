package com.wbgame.service;

import com.wbgame.pojo.GameOnlineConfig;

import java.util.List;

public interface GameOnlineConfigService {




    int insertSelective(GameOnlineConfig record);

    int updateByPrimaryKeySelective(GameOnlineConfig record);

    List<GameOnlineConfig> selectByAll(GameOnlineConfig record);

    int deleteByPrimaryKey(Integer appid, String channel, String version,String effect_type,String buy_id);

    GameOnlineConfig selectByPrimaryKey(Integer appid, String channel, String version,String effect_type,String buy_id);
}


