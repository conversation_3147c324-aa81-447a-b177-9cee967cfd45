package com.wbgame.service.product;

import java.util.List;
import java.util.Map;

import com.wbgame.pojo.NpPostVo;
import com.wbgame.pojo.product.DnwxX3dataVo;
import com.wbgame.pojo.product.DnwxX4dataVo;
import com.wbgame.pojo.product.ProductAssessVo;
import com.wbgame.pojo.product.ProductAuthorizeBookVo;

public interface ProductReportService {
	
	
	// 通用执行语句和查询语句
	public int execSql(String sql); // 直接执行DML sql语句
	public List<String> queryListString(String sql);
	public List<Map<String, Object>> queryListMap(String sql);
	public List<Map<String, String>> queryListMapOne(String sql);
	public List<Map<String, Object>> queryListMapTwo(String sql, Object obj);
	public Map<String, Map<String, Object>> queryListMapOfKey(String sql);
	public <T> List<T> queryListBean(String sql, Class<T> clazz);
	public int execSqlHandle(String sql, Object obj);
	public List<NpPostVo> queryNpPost(String sql);
	public int batchExecSql(Map<String, Object> paramMap);
	public int batchExecSqlTwo(Map<String, Object> paramMap);
	
	/** 产品发行评估 */
	public Map<String, ProductAssessVo> selectPushAssess(Map<String, String> paramMap);
	
	
	/** x3配置 */
	public List<Map<String, Object>> selectDnwxX3dataConfig(Map<String, String> paramMap);
	/** x4配置 */
	public List<Map<String, Object>> selectDnwxX4dataConfig(Map<String, String> paramMap);


	/** 产品授权自动化 */
	public List<Map<String, Object>> selectProductAuthorizeBook(Map<String, String> paramMap);
	/** 运营授权自动化 */
	public List<Map<String, Object>> selectProductAuthorizeBookTwo(Map<String, String> paramMap);

	/** 盖章申请记录-授权自动化 */
	public List<Map<String, Object>> selectProductAuthorizeBookAll(Map<String, String> paramMap);
	public int insertProductAuthorizeBookAll(ProductAuthorizeBookVo data);

	public List<ProductAuthorizeBookVo> selectProductAuthorizeBookAllById(String id);


	/** 游戏热修复配置 */
	public List<Map<String, Object>> selectDnGamePatchConfig(Map<String, String> paramMap);


	/** 游戏隐私政策sdk信息配置 */
	public List<Map<String, Object>> selectDnGamePolicyConfig(Map<String, String> paramMap);

}
