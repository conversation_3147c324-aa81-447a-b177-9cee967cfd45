package com.wbgame.service.game.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.bigdata.GameCommunityMapper;
import com.wbgame.mapper.master.game.GameDataMapper;
import com.wbgame.pojo.game.config.query.AICustomConfigRequestParam;
import com.wbgame.pojo.game.config.query.AliAccountConfigRequestParam;
import com.wbgame.pojo.game.config.query.AliAccountConfigSaveRequestParam;
import com.wbgame.pojo.game.config.response.AICustomConfigResponse;
import com.wbgame.pojo.game.config.response.AliAccountConfigResponse;
import com.wbgame.pojo.game.report.query.AICustomHandleParam;
import com.wbgame.pojo.game.report.query.AiCustomRequestParam;
import com.wbgame.pojo.game.report.response.AICustomResponse;
import com.wbgame.service.game.GameCommunityService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname GameCommunityServiceImpl
 * @Description TODO
 * @Date 2025/3/5 16:28
 */
@Slf4j
@Service
public class GameCommunityServiceImpl implements GameCommunityService {

    @Autowired
    private GameCommunityMapper gameCommunityMapper;

    @Autowired
    private GameDataMapper gameDataMapper;


    @Override
    public Result<List<AICustomResponse>> selectAiCustomList(AiCustomRequestParam param) {
        List<AICustomResponse> resultList;
        long totalSize;
        if (param.isPageFlag()) {
            //分页查询数据
            PageHelper.startPage(param.getStart(), param.getLimit());
            List<AICustomResponse> queryList = gameDataMapper.selectAICustomList(param);
            PageResult<AICustomResponse> pageResult = PageResult.page(queryList);
            resultList = pageResult.getList();
            totalSize = pageResult.getTotalSize();
        } else {
            //不分页查询数据
            resultList = gameDataMapper.selectAICustomList(param);
            totalSize = resultList.size();
        }
        //数据格式转换操作
        return ResultUtils.success(Constants.OK, resultList, null, totalSize);
    }

    @Override
    public List<AICustomResponse> selectAiCustomListNew(AiCustomRequestParam param) {
        List<AICustomResponse> queryList = gameDataMapper.selectAICustomListNew(param);
        return queryList;
    }

    @Override
    public void exportAICustomList(HttpServletResponse response,AiCustomRequestParam param) {
        Result<List<AICustomResponse>> queryResult = this.selectAiCustomList(param);
        //封装标题列
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = param.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        ExportExcelUtil.exportXLSX2(response, queryResult.getData(), head, param.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }

    @Override
    public List<AICustomConfigResponse> selectAICustomConfigList(AICustomConfigRequestParam param) {
        return gameDataMapper.selectAICustomConfigList(param);
    }

    @Override
    public Long insertAICustomConfig(AICustomConfigRequestParam param) {
        return gameDataMapper.insertAICustomConfig(param);
    }

    @Override
    public Long updateAICustomConfig(AICustomConfigRequestParam param) {
        return gameDataMapper.updateAICustomConfig(param);
    }

    @Override
    public void deleteAICustomConfig(Long id) {
        gameDataMapper.deleteAICustomConfig(id);
    }

    @Override
    public List<AICustomResponse> selectAICustomListOrigin(String start_date, String end_date) {
        return gameCommunityMapper.selectAICustomListOrigin(start_date,end_date);
    }

    @Override
    public int insertAICustomListBatch(List<AICustomResponse> list) {
        return gameDataMapper.insertAICustomListBatch(list);
    }

    @Override
    public void syncAICustomList(String start_date,String end_date) {
        if (StringUtils.isBlank(start_date)){
            DateTime now = DateTime.now();
            start_date = now.plusHours(-1).toString("yyyy-MM-dd HH:00:00");
            end_date = now.toString("yyyy-MM-dd HH:00:00");
        }

        log.info("syncAICustomList start ,start_date = {},end_date = {}",start_date,end_date);
        List<AICustomResponse> list = gameCommunityMapper.selectAICustomListOrigin(start_date,end_date);
        if (!list.isEmpty()){
            this.insertAICustomListBatch(list);
        }
        log.info("syncAICustomList end ,totalSize = {}",list.size());
    }

    @Override
    public void updateAICustomMessage(AICustomHandleParam param) {
        gameDataMapper.updateAICustomState(param);
    }

    @Override
    public Result<List<AliAccountConfigResponse>> selectAliAccountConfigListPage(AliAccountConfigRequestParam param) {
        List<AliAccountConfigResponse> resultList;
        long totalSize;
        if (param.isPageFlag()) {
            //分页查询数据
            PageHelper.startPage(param.getStart(), param.getLimit());
            List<AliAccountConfigResponse> queryList = gameDataMapper.selectAliAccountConfigListPage(param);
            PageResult<AliAccountConfigResponse> pageResult = PageResult.page(queryList);
            resultList = pageResult.getList();
            totalSize = pageResult.getTotalSize();
        } else {
            //不分页查询数据
            resultList = gameDataMapper.selectAliAccountConfigListPage(param);
            totalSize = resultList.size();
        }
        //数据格式转换操作
        return ResultUtils.success(Constants.OK, resultList, null, totalSize);
    }

    @Override
    public AliAccountConfigResponse getAliAccountConfig(String account) {
        return gameDataMapper.getAliAccountConfig(account);
    }

    @Override
    public Long insertAliAccountConfig(AliAccountConfigSaveRequestParam param) {
        return gameDataMapper.saveAliAccountConfig(param);
    }

    @Override
    public Long updateAliAccountConfig(AliAccountConfigSaveRequestParam param) {
        return gameDataMapper.updateAliAccountConfig(param);
    }

    @Override
    public void deleteAliAccountConfig(Long id) {
        gameDataMapper.deleteAliAccountConfig(id);
    }


}
