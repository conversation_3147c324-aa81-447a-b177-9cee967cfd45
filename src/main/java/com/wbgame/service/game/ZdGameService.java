package com.wbgame.service.game;

import com.wbgame.pojo.game.config.*;
import com.wbgame.pojo.game.report.GameUserInfoVo;
import com.wbgame.pojo.game.report.SubscribeSendLogVo;
import com.wbgame.pojo.game.report.query.SubscribeSendLogQueryVo;
import com.wbgame.pojo.game.userinfo.LoginVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname ZdGameService
 * @Description TODO
 * @Date 2022/5/7 16:59
 */
public interface ZdGameService {

    List<MailVo> getMailList(MailVo vo);

    MailVo getMailById(String id);

    int saveMail(MailVo vo);

    int updateMail(MailVo vo);

    int updateMailSendState(MailVo vo);

    int delMail(MailVo vo);

    List<LoginVo> getLoginLogList(Map<String,String> map);

    List<GameTableVo> getGameTableConfigList(Map<String,String> map);

    List<Map<String,Object>> getCityListMap();

    List<ForbiddenVo> getForbiddenList(Map<String,String> map);

    int saveForbidden(ForbiddenVo vo);

    int updateForbidden(ForbiddenVo vo);

    int delForbidden(ForbiddenVo vo);


    List<ABTestConfigVo> getABTestConfigList(ABTestConfigVo vo);

    int saveABTestConfig(ABTestConfigVo vo);

    int delABTestConfig(String id);

    int updateABTestConfig(ABTestConfigVo vo);

    /** 获取问题分类列表*/
    List<QATypeConfigVo> getQATypeConfigList(QATypeConfigVo vo);

    /** 新增客服反馈应用分类配置*/
    int saveQATypeConfig(QATypeConfigVo vo);

    /** 修改客服反馈应用分类配置*/
    int updateQATypeConfig(QATypeConfigVo vo);

    /** 删除客服反馈应用分类配置*/
    int delQATypeConfig(String pqaId);


    /** 获取客服反馈配置*/
    List<QAConfigVo> getQAConfigList(QAConfigVo vo);

    /** 保存客服反馈配置*/
    int saveQAConfig(QAConfigVo vo);

    /** 更新客服反馈配置*/
    int updateQAConfig(QAConfigVo vo);

    /** 删除客服反馈配置*/
    int delQAConfig(String qaId);

    /** 获取用户系统loginid*/
    List<GameUserInfoVo> getGameUserLoginId(GameUserInfoVo vo);

    /** 查询微信发送订阅消息操作结果*/
    List<SubscribeSendLogVo> getSubscribeHandleLogList(SubscribeSendLogQueryVo vo);

    /** 查询微信发送订阅消息操作结果汇总*/
    SubscribeSendLogVo getSubscribeHandleLogListSum(SubscribeSendLogQueryVo vo);

    /** 查询白名单配置*/
    List<WhiteListVo> getWhiteConfigList(WhiteListVo vo);

    /** 保存白名单配置*/
    int saveWhiteConfig(WhiteListVo vo);

    /** 修改白名单配置*/
    int updateWhiteConfig(WhiteListVo vo);

    /** 删除白名单配置*/
    int delWhiteConfig(WhiteListVo vo);

    /** 批量保存白名单*/
    int batchSaveWhiteConfig(List<WhiteListVo> list);

    /** 查询公告配置*/
    List<NoticeVo> getNoticeConfigList(NoticeVo vo);

    /** 新增公告配置*/
    int insertNoticeConfig(NoticeVo vo);

    /** 更新公告配置*/
    int updateNoticeConfig(NoticeVo vo);

    /** 删除公告配置*/
    int deleteNoticeConfig(NoticeVo vo);
}
