package com.wbgame.service;


import com.wbgame.common.response.Result;
import com.wbgame.pojo.operate.AdsUserGroupLevelSumDaily;
import com.wbgame.pojo.operate.AdsUserGroupLevelSumDailyVO;
import com.wbgame.utils.PageResult;

import java.util.List;
import java.util.Map;

public interface IAdsUserGroupLevelSumDailyService {


    Result<PageResult<AdsUserGroupLevelSumDailyVO>> selectByExample(AdsUserGroupLevelSumDaily example);

    List<AdsUserGroupLevelSumDailyVO> export(AdsUserGroupLevelSumDaily example);

    /**
     * 图
     * @param example
     * @return
     */
    Result<Map<String, Map<String, Object>>> selectByExampleMap(AdsUserGroupLevelSumDaily example);




   }