package com.wbgame.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.nnjy.TurnTableVO;
import com.wbgame.service.TurnTableService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.*;
import java.util.concurrent.TimeUnit;

@RestController
public class TurnTableController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private TurnTableService turnTableService;

    @CrossOrigin
    @RequestMapping(value = "/getTurnTableList", method = {RequestMethod.GET, RequestMethod.POST})
    public JSONObject getTurnTableList(CurrUserVo cu, HttpServletRequest request) {

        String appid = BlankUtils.checkNull(request, "appid");
        String pid = BlankUtils.checkNull(request, "pid");
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String beginDt = BlankUtils.checkNull(request, "beginDt");
        String endDt = BlankUtils.checkNull(request, "endDt");

        JSONObject result = new JSONObject();

        // token验证
        if (BlankUtils.checkBlank(cu.getToken())) {
            result.put("msg", "token is error!");
            result.put("ret", 2);
            return result;
        }
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(cu.getToken());
        if (cuser == null) {
            result.put("msg", "token is error!");
            result.put("ret", 2);
            return result;
        } else {
            redisTemplate.opsForValue()
                    .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
        }
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        // 进行分页
        PageHelper.startPage(pageNo, pageSize);

        Map<String, Object> map = new HashMap<>();
        map.put("beginDt", beginDt);
        map.put("endDt", endDt);
        map.put("pid", pid);
        map.put("appid", appid);

        List<TurnTableVO> list = turnTableService.getTurnTableList(map);
        // 导出的excel数据存储
        redisTemplate.opsForValue().set("getTurnTableList-" + cu.getToken(),
                list, 20 * 60, TimeUnit.SECONDS);
        long size = ((Page) list).getTotal();

        result.put("data", list);
        result.put("ret", 1);
        result.put("totalCount", size);
        return result;
    }

    @CrossOrigin
    @RequestMapping(value = "/getTurnTableList/export", method = RequestMethod.POST)
    public @ResponseBody
    void exportTurnTableList(HttpServletRequest request, HttpServletResponse response) {
        String appid = BlankUtils.checkNull(request, "appid");
        String pid = BlankUtils.checkNull(request, "pid");
        String beginDt = BlankUtils.checkNull(request, "beginDt");
        String endDt = BlankUtils.checkNull(request, "endDt");

        Map<String, Object> map = new HashMap<>();
        map.put("beginDt", beginDt);
        map.put("endDt", endDt);
        map.put("pid", pid);
        map.put("appid", appid);

        List<TurnTableVO> list = turnTableService.getTurnTableList(map);

        Map<String, String> inMap = new LinkedHashMap<>();
        inMap.put("strDt", "yyyy-MM-dd");

        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;
        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("mmdate", "日期");
        headerMap.put("turnNum", "参与人数");
        headerMap.put("type1", "转盘类型1次数");
        headerMap.put("type2", "转盘类型2次数");
        headerMap.put("type3", "转盘类型3次数");
        headerMap.put("type4", "转盘类型4次数");
        headerMap.put("type5", "转盘类型5次数");

        headerMap.put("prize1", "转盘类型1大奖次数");
        headerMap.put("prize2", "转盘类型2大奖次数");
        headerMap.put("prize3", "转盘类型3大奖次数");
        headerMap.put("prize4", "转盘类型4大奖次数");
        headerMap.put("prize5", "转盘类型5大奖次数");
        for (TurnTableVO tt : list) {
            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("mmdate", tt.getMmdate());
            contentMap.put("turnNum", tt.getTurnNum());
            contentMap.put("type1", tt.getType1());
            contentMap.put("type2", tt.getType2());
            contentMap.put("type3", tt.getType3());
            contentMap.put("type4", tt.getType4());
            contentMap.put("type5", tt.getType5());
            contentMap.put("prize1", tt.getPrize1());
            contentMap.put("prize2", tt.getPrize2());
            contentMap.put("prize3", tt.getPrize3());
            contentMap.put("prize4", tt.getPrize4());
            contentMap.put("prize5", tt.getPrize5());
            contentList.add(contentMap);
        }

        String fileName = "大转盘查询_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
    }

}
