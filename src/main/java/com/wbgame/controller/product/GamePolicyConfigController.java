package com.wbgame.controller.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.product.DnGamePolicyConfigVo;
import com.wbgame.pojo.product.DnwxX4dataVo;
import com.wbgame.service.product.ProductReportService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 游戏隐私政策sdk信息配置
 * @author: caow
 * @date: 2024/09/05
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/product")
public class GamePolicyConfigController {

    @Autowired
    private ProductReportService productReportService;
    @Autowired
    private YyhzMapper yyhzMapper;


    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/gamePolicy/list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(DnwxX4dataVo data,HttpServletRequest request,HttpServletResponse response) throws IOException {
        String[] args = {"partner_sdk","company","ctype","statu","cuser","euser"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        try {
            PageHelper.startPage(paramMap);
            List<Map<String, Object>> list = productReportService.selectDnGamePolicyConfig(paramMap);

            return ReturnJson.successPage(list);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }


    /**
     * 操作
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/gamePolicy/handle", method={RequestMethod.GET,RequestMethod.POST})
    public String handle(DnGamePolicyConfigVo data, HttpServletRequest request, HttpServletResponse response) {

        // token验证
        String token = request.getParameter("token");
        Object object = redisTemplate.opsForValue().get(token);
        JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));
        
        int result = 0;
        try {
            data.setCuser(json.getString("login_name"));
            data.setEuser(json.getString("login_name"));
            
            if ("add".equals(request.getParameter("handle"))) {
                result = yyhzMapper.insertGamePolicyConfig(data);
                return "{\"ret\":1,\"msg\":\"操作成功!\",\"add_id\":"+data.getId()+"}";

            } else if ("edit".equals(request.getParameter("handle"))) {
                result = yyhzMapper.updateGamePolicyConfig(data);

            } else if ("del".equals(request.getParameter("handle"))) {
                result = yyhzMapper.deleteGamePolicyConfig(data);
            }

            if (result > 0)
                return ReturnJson.success();
            else
                return ReturnJson.toErrorJson("无效操作！");

        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }

}
