package com.wbgame.controller.adv2;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.adv2.AdvAdcodeXiaomiInfo;
import com.wbgame.pojo.adv2.XiaomiAdDTO;
import com.wbgame.service.adv2.adcode.XiaomiAdCodeService;
import io.swagger.annotations.Api;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * <AUTHOR>
 * @date 2024/11/08
 * @description xiaomi创建广告位
 **/
@RequestMapping("/adv2/xiaomiAdCode")
@CrossOrigin
@RestController
@Api(tags = "xiaomi 创建广告位")
public class XiaomiAdCodeController {
    public static final String LOCK_MI_FILE_BATCH_IMPORT = "lock_xiaomi_file_batch_import";
    @Resource
    private XiaomiAdCodeService xiaomiAdCodeService;
    @Resource
    private Redisson redisson;

    /**
     * 小米 创建广告位接口
     *
     * @param dto 创建广告位所需参数
     * @return 创建结果
     */
    @ControllerLoggingEnhancer
    @RequestMapping("/add")
    public Result<String> createAdCode(XiaomiAdDTO dto) {
        String loginUserName = LOGIN_USER_NAME.get();
        dto.setCreateUser(loginUserName);
        dto.setModifyUser(loginUserName);
        return xiaomiAdCodeService.createAdCode(dto);
    }

    /**
     * xiaomi 批量上传接口
     *
     * @return 导入结果
     */
    @ControllerLoggingEnhancer
    @RequestMapping("/import")
    public Result<String> importFile(@RequestParam(value = "fileName") MultipartFile file,Boolean isSyncAdpos) throws InterruptedException {
        RLock lock = redisson.getLock(LOCK_MI_FILE_BATCH_IMPORT);
        if (lock.tryLock(1L, TimeUnit.SECONDS)) {
            try {
                return xiaomiAdCodeService.batchImport(file,isSyncAdpos);
            } catch (Exception e) {
                e.printStackTrace();
                return ResultUtils.failure("上传文件失败,请联系管理员!错误信息:" + e.getMessage());
            } finally {
                lock.unlock();
            }
        } else {
            return ResultUtils.failure("有上传任务正在运行中，请稍后再试");
        }
    }


    @ControllerLoggingEnhancer
    @RequestMapping("/list")
    public Result<List<AdvAdcodeXiaomiInfo>> queryXiaomiList(AdvAdcodeXiaomiInfo dto, @RequestParam(value = "start") int start, @RequestParam(value = "limit") int limit) {
        //数据查询
        return xiaomiAdCodeService.queryXiaomiAdCode(dto, start, limit);

    }

}
