package com.wbgame.controller.adv2;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.adv2.wz.HomeWzLimitConfig;
import com.wbgame.pojo.adv2.wz.HomeWzRewardConfig;
import com.wbgame.pojo.adv2.wz.HomeWzTiwaiConfig;
import com.wbgame.pojo.adv2.wz.HomeWzWithdrawalSetting;
import com.wbgame.service.AdService;
import com.wbgame.service.adv2.WzRewardConfigService;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 网赚项目-数据配置项
 * @author: caow
 * @date: 2024/10/30
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/adv2")
public class WzRewardConfigController {

    @Autowired
    private WzRewardConfigService wzRewardConfigService;
    @Autowired
    private AdService adService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    /**
     * 奖励配置-查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/wzReward/list", method={RequestMethod.GET, RequestMethod.POST})
    public String wzRewardList(HomeWzRewardConfig data, Integer start,Integer limit, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            PageHelper.startPage(start, limit);
            List<HomeWzRewardConfig> list = wzRewardConfigService.queryAll(data);

            return ReturnJson.successPage(list);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }
    /**
     * 奖励配置-查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/wzReward/export", method={RequestMethod.GET, RequestMethod.POST})
    public void wzRewardExport(HomeWzRewardConfig data, HttpServletRequest request, HttpServletResponse response) throws IOException {

        List<HomeWzRewardConfig> list = wzRewardConfigService.queryAll(data);

        // 赋值应用名称
        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
        list.forEach(act -> {
            Map<String, Object> app = appMap.get(act.getAppid());
            if(app != null)
                act.setAppname(app.get("app_name")+"");
        });

        String value = request.getParameter("value");
        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        ExportExcelUtil.exportXLSX2(response,list,head,"奖励配置_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx");
    }


    /**
     * 奖励配置-操作
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/wzReward/handle", method={RequestMethod.GET,RequestMethod.POST})
    public String wzRewardhandle(HomeWzRewardConfig data, HttpServletRequest request, HttpServletResponse response) {

        // token验证
        String token = request.getParameter("token");
        if (token != null){
            Object object = redisTemplate.opsForValue().get(token);
            JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));
            data.setCuser(json.getString("login_name"));
            data.setEuser(json.getString("login_name"));
        }
        
        int result = 0;
        try {

            if ("add".equals(request.getParameter("handle"))) {
                HomeWzRewardConfig config = wzRewardConfigService.queryById(data);
                if(config != null){
                    return ReturnJson.toErrorJson("已有相同的产品+子渠道+奖励ID配置存在！");
                }

                result = wzRewardConfigService.insert(data);

            } else if ("edit".equals(request.getParameter("handle"))) {
                result = wzRewardConfigService.update(data);

            } else if ("del".equals(request.getParameter("handle"))) {
                result = wzRewardConfigService.delete(data);
            }

            if (result > 0)
                return ReturnJson.success();
            else
                return ReturnJson.toErrorJson("无效操作！");

        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }

    /**
     * 提现档位配置-查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/wzWithdrawal/list", method={RequestMethod.GET, RequestMethod.POST})
    public String wzWithdrawalList(HomeWzWithdrawalSetting data, Integer start,Integer limit, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            PageHelper.startPage(start, limit);
            List<HomeWzWithdrawalSetting> list = wzRewardConfigService.queryAllWzWithdrawal(data);

            return ReturnJson.successPage(list);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }


    /**
     * 提现档位配置-操作
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/wzWithdrawal/handle", method={RequestMethod.GET,RequestMethod.POST})
    public String wzWithdrawalhandle(HomeWzWithdrawalSetting data, HttpServletRequest request, HttpServletResponse response) {

        // token验证
        String token = request.getParameter("token");
        if (token != null){
            Object object = redisTemplate.opsForValue().get(token);
            JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));
            data.setCuser(json.getString("login_name"));
            data.setEuser(json.getString("login_name"));
        }

        int result = 0;
        try {
            if ("add".equals(request.getParameter("handle"))) {
                result = wzRewardConfigService.insertWzWithdrawal(data);
                return "{\"ret\":1,\"msg\":\"操作成功!\",\"add_id\":"+data.getWlevel_id()+"}";

            } else if ("edit".equals(request.getParameter("handle"))) {
                result = wzRewardConfigService.updateWzWithdrawal(data);

            } else if ("del".equals(request.getParameter("handle"))) {
                result = wzRewardConfigService.deleteWzWithdrawal(data);
            }

            if (result > 0)
                return ReturnJson.success();
            else
                return ReturnJson.toErrorJson("无效操作！");

        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }

    /**
     * 提现全局配置-查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/wzLimitConfig/list", method={RequestMethod.GET, RequestMethod.POST})
    public String wzLimitConfiglist(HomeWzLimitConfig data, Integer start, Integer limit, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            PageHelper.startPage(start, limit);
            List<HomeWzLimitConfig> list = wzRewardConfigService.queryAllWzLimitConfig(data);

            return ReturnJson.successPage(list);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }


    /**
     * 提现全局配置-操作
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/wzLimitConfig/handle", method={RequestMethod.GET,RequestMethod.POST})
    public String wzLimitConfighandle(HomeWzLimitConfig data, HttpServletRequest request, HttpServletResponse response) {

        // token验证
        String token = request.getParameter("token");
        if (token != null){
            Object object = redisTemplate.opsForValue().get(token);
            JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));
            data.setCuser(json.getString("login_name"));
            data.setEuser(json.getString("login_name"));
        }

        int result = 0;
        try {
            if ("add".equals(request.getParameter("handle"))) {
                HomeWzLimitConfig config = wzRewardConfigService.queryByIdWzLimitConfig(data);
                if(config != null){
                    return ReturnJson.toErrorJson("已有相同的产品+子渠道配置存在！");
                }

                result = wzRewardConfigService.insertWzLimitConfig(data);

            } else if ("edit".equals(request.getParameter("handle"))) {
                result = wzRewardConfigService.updateWzLimitConfig(data);

            } else if ("del".equals(request.getParameter("handle"))) {
                result = wzRewardConfigService.deleteWzLimitConfig(data);
            }

            if (result > 0)
                return ReturnJson.success();
            else
                return ReturnJson.toErrorJson("无效操作！");

        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }


    /**
     * 体外配置-查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/wzTiwaiConfig/list", method={RequestMethod.GET, RequestMethod.POST})
    public String wzTiwaiConfigList(HomeWzTiwaiConfig data, Integer start, Integer limit, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            PageHelper.startPage(start, limit);
            List<HomeWzTiwaiConfig> list = wzRewardConfigService.queryAllWzTiwaiConfig(data);

            return ReturnJson.successPage(list);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }

    /**
     * 体外配置-JSON解析列表查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/wzTiwaiConfig/getDescList", method={RequestMethod.GET, RequestMethod.POST})
    public String getDescList(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            List<Map<String, String>> list = wzRewardConfigService.getDescList();
            // 转存为Map格式
            Map<String, String> descMap = list.stream().collect(Collectors.toMap(act -> act.get("key").toString(), act -> act.get("val").toString()));

            return ReturnJson.success(descMap);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }


    /**
     * 体外配置-操作
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/wzTiwaiConfig/handle", method={RequestMethod.GET,RequestMethod.POST})
    public String wzTiwaiConfighandle(HomeWzTiwaiConfig data, HttpServletRequest request, HttpServletResponse response) {

        // token验证
        String token = request.getParameter("token");
        if (token != null){
            Object object = redisTemplate.opsForValue().get(token);
            JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));
            data.setCuser(json.getString("login_name"));
            data.setEuser(json.getString("login_name"));
        }

        int result = 0;
        try {
            if ("add".equals(request.getParameter("handle"))) {
                result = wzRewardConfigService.insertWzTiwaiConfig(data);
                return "{\"ret\":1,\"msg\":\"操作成功!\",\"add_id\":"+data.getId()+"}";

            } else if ("edit".equals(request.getParameter("handle"))) {
                result = wzRewardConfigService.updateWzTiwaiConfig(data);

            } else if ("del".equals(request.getParameter("handle"))) {
                result = wzRewardConfigService.deleteWzTiwaiConfig(data);
            }

            if (result > 0)
                return ReturnJson.success();
            else
                return ReturnJson.toErrorJson("无效操作！");

        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("错误信息: "+e.getMessage());
        }
    }


}
