package com.wbgame.controller.adv2;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.wbgame.annotation.ControllerEnhancer;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.adv2.SigmobAdVo;
import com.wbgame.service.adv2.adcode.SigmobAdCodeService;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.List;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * <AUTHOR>
 * @date 2023/5/19
 * @description
 **/
@RequestMapping("/adv2/sigmobAdCode")
@CrossOrigin
@RestController
public class SigmobAdCodeController {

    @Resource
    private SigmobAdCodeService sigmobAdCodeService;

    /**
     * sigmob创建广告位接口
     * @param request
     * @param vo
     * @return
     */
    @ControllerLoggingEnhancer
    @RequestMapping("handleAdCode")
    public Object handleAdCode(HttpServletRequest request, SigmobAdVo vo){
        String loginUserName = LOGIN_USER_NAME.get();

        vo.setCreateUser(loginUserName);
        vo.setModifyUser(loginUserName);

        return sigmobAdCodeService.createAdCode(vo);
    }

    @ControllerEnhancer
    @RequestMapping("list")
    public String list(SigmobAdVo vo, @RequestParam(value = "start") int start, @RequestParam(value = "limit") int limit) {
        PageInfo<SigmobAdVo> res = sigmobAdCodeService.findSigmobAdCode(vo, start, limit);

        List<SigmobAdVo> data = res.getList();
        long total = res.getTotal();

        Result<List<SigmobAdVo>> result = new Result<>();
        result.setData(data);
        result.setTotalCount(total);
        result.setRet(1);
        result.setMsg("success");
        return JSONObject.toJSONString(result);
    }
}
