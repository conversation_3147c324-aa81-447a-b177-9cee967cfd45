package com.wbgame.controller.adv2.bigdata;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.master.BigMapper;
import com.wbgame.service.AdService;
import com.wbgame.service.BigdataService;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.service.adb.UserReportService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;

/**
 * 大数据采集来源的用户报表相关
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/mobile/userReportTotal")
public class UserReportDataController {
	
	@Autowired
    private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private AdService adService;
	@Autowired
	private BigdataService bigdataService;
	@Autowired
	private UserReportService userReportService;
	
	
	/**
	 * 新增活跃汇总.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/list", method={RequestMethod.GET, RequestMethod.POST})
	public String list(HttpServletRequest request,HttpServletResponse response) throws IOException {
		
		
		String[] args = {"sdate","edate","appGroup","appid","channel","pid","userlabel","group","order"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
		
		JSONObject result = new JSONObject();
		try {
			PageHelper.startPage(paramMap);
			List<Map<String, Object>> list = userReportService.selectUserReportTotal(paramMap);
			long size = ((Page) list).getTotal();
			
			// 赋值项目ID对应的版本号ver
			Map<String, Map<String, Object>> pMap = adService.selectProjectidChannelMap();
			list.forEach(act -> {
				act.put("tdate", act.get("tdate")+"");
				
				Map<String, Object> act2 = pMap.get(act.get("pid")+"");
				if(act2 != null){
					act.put("ver", act2.get("ver")+"");
				}
			});
			
			// 同时查询子渠道和版本分组的报表数据
			paramMap.put("group", "pid");
			List<Map<String, Object>> verList = userReportService.selectUserReportTotal(paramMap);
			verList.forEach(act -> {
				Map<String, Object> act2 = pMap.get(act.get("pid")+"");
				if(act2 != null){
					act.put("ver", act2.get("ver")+"");
				}
			});
			
			paramMap.put("group", "channel");
			List<Map<String, Object>> chaList = userReportService.selectUserReportTotal(paramMap);
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("verList", verList);
			result.put("chaList", chaList);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	
	/**
	 * 新增活跃汇总.导出
	 * @param request
	 * @param response
	 * @return
	 */ 
	@RequestMapping(value = "/export", method={RequestMethod.GET, RequestMethod.POST})
	public void export(HttpServletRequest request,HttpServletResponse response) {
		
		String[] args = {"sdate","edate","appGroup","appid","channel","pid","userlabel","group","order"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
		
		List<Map<String, Object>> contentList = userReportService.selectUserReportTotal(paramMap);

		// 赋值项目ID对应的版本号ver
		Map<String, Map<String, Object>> pMap = adService.selectProjectidChannelMap();
		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			act.put("tdate", act.get("tdate")+"");
			
			Map<String, Object> act2 = pMap.get(act.get("pid")+"");
			if(act2 != null){
				act.put("ver", act2.get("ver")+"");
			}
			
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));
		});
		
		
		Map<String, String> headerMap = new LinkedHashMap<String, String>();

        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
		
		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		String export_file_name = request.getParameter("export_file_name");
		if (BlankUtils.checkBlank(export_file_name)){
			export_file_name = "新增活跃汇总-大数据";
		}
		String fileName = export_file_name +"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);
	}
	
}
