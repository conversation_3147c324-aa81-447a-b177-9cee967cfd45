package com.wbgame.controller.adv2.bigdata;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.Asserts;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.adv2.bigdata.AdsInsideToolsPenetrateDataVo;
import com.wbgame.pojo.adv2.bigdata.AdsToolsPenetrateDataDTO;
import com.wbgame.pojo.adv2.bigdata.AdsOutsideToolsPenetrateDataVo;
import com.wbgame.service.adv2.AdsToolsPenetrateService;
import com.wbgame.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 工具产品功能渗透报表:国内，海外
 * @Date 2024/12/25 14:19
 */
@Api(tags = "工具产品功能渗透报表")
@CrossOrigin
@ControllerLoggingEnhancer
@RestController
@RequestMapping(value = "/adb/toolsPenetrate")
public class AdsToolsPenetrateController {

    @Autowired
    private AdsToolsPenetrateService adsToolsPenetrateService;

    /**
     * 查询版本和品牌数据
     *
     * @return 查询结果
     */
    @GetMapping("/outside/queryVersions")
    public Result<Map<String, List<String>>> queryOutsideVersions() {
        String tableName = "ads_tools_agree_create_flow_daily";
        return adsToolsPenetrateService.queryVersions(tableName);
    }


    /**
     * 工具海外产品功能渗透报表-分页查询接口
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @PostMapping("/outside/list")
    public Result<List<AdsOutsideToolsPenetrateDataVo>> queryOutsideList(@RequestBody AdsToolsPenetrateDataDTO dto) {
        dto.setPageFlag(true);
        return adsToolsPenetrateService.queryOutsideList(dto);
    }

    /**
     * 工具海外产品功能渗透报表-导出接口
     *
     * @param dto 查询参数
     */
    @PostMapping("/outside/export")
    public void outsideExport(AdsToolsPenetrateDataDTO dto, HttpServletResponse response) {
        dto.setPageFlag(false);
        //不分页查询数据
        Result<List<AdsOutsideToolsPenetrateDataVo>> queryResult = adsToolsPenetrateService.queryOutsideList(dto);
        //封装标题列
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = dto.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        ExportExcelUtil.exportXLSX2(response, queryResult.getData(), head, dto.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }


    /**
     * 查询国内版本和品牌数据
     *
     * @return 查询结果
     */
    @GetMapping("/inside/queryVersions")
    public Result<Map<String, List<String>>> queryInsideVersions() {
        String tableName = "ads_tools_agree_create_flow_china_daily";
        return adsToolsPenetrateService.queryVersions(tableName);
    }


    /**
     * 工具国内产品功能渗透报表-分页查询接口
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @PostMapping("/inside/list")
    public Result<List<AdsInsideToolsPenetrateDataVo>> queryInsideList(@RequestBody AdsToolsPenetrateDataDTO dto) {
        dto.setPageFlag(true);
        return adsToolsPenetrateService.queryInsideList(dto);
    }

    /**
     * 工具国内产品功能渗透报表-导出接口
     *
     * @param dto 查询参数
     */
    @PostMapping("/inside/export")
    public void insideExport(AdsToolsPenetrateDataDTO dto, HttpServletResponse response) {
        dto.setPageFlag(false);
        //不分页查询数据
        Result<List<AdsInsideToolsPenetrateDataVo>> queryResult = adsToolsPenetrateService.queryInsideList(dto);
        //封装标题列
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = dto.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        ExportExcelUtil.exportXLSX2(response, queryResult.getData(), head, dto.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }

}
