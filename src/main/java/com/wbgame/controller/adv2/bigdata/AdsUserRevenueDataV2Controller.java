package com.wbgame.controller.adv2.bigdata;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.Asserts;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.adv2.bigdata.AdsUserRevenueDTO;
import com.wbgame.pojo.adv2.bigdata.AdsUserRevenueVo;
import com.wbgame.service.adv2.AdsUserRevenueDataV2Service;
import com.wbgame.utils.ExportExcelUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/06/04
 * @description 用户展示分布
 **/
@CrossOrigin
@RestController
@RequestMapping("/adb/app/v2")
@ControllerLoggingEnhancer
public class AdsUserRevenueDataV2Controller {

    @Autowired
    private AdsUserRevenueDataV2Service adsUserRevenueDataV2Service;

    /**
     * 变现-活跃-新增用户广告展示
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @ApiOperation(value = "变现-新增用户展示频次", notes = "变现-新增用户展示频次", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start", value = "页码", dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "条数", dataType = "String"),
            @ApiImplicitParam(name = "appid", value = "产品id", dataType = "String"),
            @ApiImplicitParam(name = "channel", value = "渠道", dataType = "String"),
            @ApiImplicitParam(name = "start_date", value = "起始日期", dataType = "String"),
            @ApiImplicitParam(name = "end_date", value = "结束日期", dataType = "String"),
            @ApiImplicitParam(name = "user_type", value = "用户类型(输入新增)", dataType = "String"),
            @ApiImplicitParam(name = "adpos_type", value = "广告位类型", dataType = "String"),
            @ApiImplicitParam(name = "isDouyin", value = "是否请求抖音数据", dataType = "int")
    })
    @PostMapping(value = "/selectAdshowActuser")
    public Object selectAdshowActuser(@RequestBody AdsUserRevenueDTO dto) {
        return adsUserRevenueDataV2Service.selectAdshowActuser(dto);
    }

    /**
     * 变现-活跃用户广告展示.导出
     *
     * @param dto      导出条件
     * @param response HttpServletResponse
     */
    @ApiOperation(value = "变现-新增用户展示频次.导出", notes = "变现-新增用户展示频次.导出", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appid", value = "产品id", dataType = "String"),
            @ApiImplicitParam(name = "channel", value = "渠道", dataType = "String"),
            @ApiImplicitParam(name = "start_date", value = "起始日期", dataType = "String"),
            @ApiImplicitParam(name = "end_date", value = "结束日期", dataType = "String"),
            @ApiImplicitParam(name = "user_type", value = "用户类型", defaultValue = "新增", dataType = "String"),
            @ApiImplicitParam(name = "adpos_type", value = "广告位类型", dataType = "String"),
            @ApiImplicitParam(name = "value", value = "自定义导出字段", dataType = "String"),
            @ApiImplicitParam(name = "report", value = "文件名", dataType = "String"),
            @ApiImplicitParam(name = "isDouyin", value = "是否请求抖音数据", dataType = "int")
    })
    @RequestMapping(value = "/exportAdshowActuser", method = {RequestMethod.GET, RequestMethod.POST})
    public void exportAdshowActuser(@RequestBody AdsUserRevenueDTO dto, HttpServletResponse response) {
        dto.setStart(1);
        dto.setLimit(999999);
        Result<List<AdsUserRevenueVo>> resultList = adsUserRevenueDataV2Service.selectAdshowActuser(dto);
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = dto.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = dto.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX2(response, resultList.getData(), head, fileName);
    }

}
