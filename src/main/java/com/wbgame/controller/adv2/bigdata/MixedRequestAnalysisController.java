package com.wbgame.controller.adv2.bigdata;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.wbgame.annotation.ControllerEnhancer;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.adv2.bigdata.MixedRequestAnalysisAggVo;
import com.wbgame.pojo.adv2.bigdata.MixedRequestAnalysisVo;
import com.wbgame.service.AdService;
import com.wbgame.service.adv2.bigdata.MixedRequestAnalysisService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 5.5
 * @description
 **/

@RestController
@CrossOrigin
@RequestMapping("/adb/mixedRequestAnalysis")
public class MixedRequestAnalysisController {

    @Resource
    private AdService adService;
    @Resource
    private MixedRequestAnalysisService mixedRequestAnalysisService;

    @ControllerEnhancer
    @RequestMapping(value = "/list", method={RequestMethod.POST})
    public String mixedRequestAnalysisList(MixedRequestAnalysisVo vo, int start, int limit) {

        PageInfo<MixedRequestAnalysisAggVo> detail
                = mixedRequestAnalysisService.findMixedRequestAnalysisDetail(vo, start, limit);
        List<MixedRequestAnalysisAggVo> data = detail.getList();
        long total = detail.getTotal();

        Result<List<MixedRequestAnalysisAggVo>> result = new Result<>();
        result.setData(data);
        result.setTotalCount(total);

        result.setTotal(getTotalCount(data));
        result.setRet(1);
        result.setMsg("success");
        return JSONObject.toJSONString(result);
    }

    /**
     * 新增活跃汇总.导出
     * @return
     */
    @ControllerEnhancer
    @RequestMapping(value = "/export", method={RequestMethod.POST})
    public void export(HttpServletResponse response, MixedRequestAnalysisVo vo,
                       String value, int start, int limit) {

        List<MixedRequestAnalysisAggVo> detail
                = mixedRequestAnalysisService.findMixedRequestAnalysisDetailNoPage(vo);

        List contentList = detail.stream()
                .map(v -> JSONObject.parseObject(JSONObject.toJSONString(v), Map.class)).collect(Collectors.toList());


        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        if (!BlankUtils.checkBlank(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        // 赋值项目ID对应的版本号ver
        Map<String, Map<String, Object>> pMap = adService.selectProjectidChannelMap();
        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
        ((List<Map<String, Object>>)contentList).forEach(act -> {
            act.put("tdate", act.get("tdate")+"");

            Map<String, Object> act2 = pMap.get(act.get("pid")+"");
            if(act2 != null){
                act.put("ver", act2.get("ver")+"");
            }

            Map<String, Object> app = appMap.get(act.get("appid")+"");
            if(app != null)
                act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));
        });

        String fileName = "混合请求分析_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
//        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, null, response);
        ExportExcelUtil.exportXLSX(response,contentList, headerMap, fileName);
    }

    private Map<String, Object> getTotalCount(List<MixedRequestAnalysisAggVo> list) {
        Map<String, Object> total = new HashMap<>();
        double total_ad_revenue = 0;
        double ad_revenue = 0;
        long active_user_cnt = 0;
        long reg_user_cnt = 0;

        float dau_arpu = 0;
        float add_arpu = 0;

        float avg_pv_splash = 0;
        float avg_pv_plaque = 0;
        float avg_pv_banner = 0;
        float avg_pv_video = 0;
        float avg_pv_msg = 0;

        float ecpm_splash = 0;
        float ecpm_plaque = 0;
        float ecpm_banner = 0;
        float ecpm_video = 0;
        float ecpm_msg = 0;

        float pv_splash = 0;
        float pv_plaque = 0;
        float pv_banner = 0;
        float pv_video = 0;
        float pv_msg = 0;

        float revenue_splash = 0;
        float revenue_plaque = 0;
        float revenue_banner = 0;
        float revenue_video = 0;
        float revenue_msg = 0;

        float arpu_splash = 0;
        float arpu_plaque = 0;
        float arpu_banner = 0;
        float arpu_video = 0;
        float arpu_msg = 0;

        for (MixedRequestAnalysisAggVo vo : list) {
            total_ad_revenue += vo.getTotal_ad_revenue();
            ad_revenue += vo.getAd_revenue();
            active_user_cnt += vo.getActive_user_cnt();
            reg_user_cnt += vo.getReg_user_cnt();

            dau_arpu += vo.getDau_arpu();
            add_arpu += vo.getAdd_arpu();

            avg_pv_splash += vo.getAvg_pv_splash();
            avg_pv_plaque += vo.getAvg_pv_plaque();
            avg_pv_banner += vo.getAvg_pv_banner();
            avg_pv_video += vo.getAvg_pv_video();
            avg_pv_msg += vo.getAvg_pv_msg();

            ecpm_splash += vo.getEcpm_splash();
            ecpm_plaque += vo.getEcpm_plaque();
            ecpm_banner += vo.getEcpm_banner();
            ecpm_video += vo.getEcpm_video();
            ecpm_msg += vo.getEcpm_msg();

            pv_splash += vo.getPv_splash();
            pv_plaque += vo.getPv_plaque();
            pv_banner += vo.getPv_banner();
            pv_video += vo.getPv_video();
            pv_msg += vo.getPv_msg();

            revenue_splash += vo.getRevenue_splash();
            revenue_plaque += vo.getRevenue_plaque();
            revenue_banner += vo.getRevenue_banner();
            revenue_video += vo.getRevenue_video();
            revenue_msg += vo.getRevenue_msg();

            arpu_splash += vo.getArpu_splash();
            arpu_plaque += vo.getArpu_plaque();
            arpu_banner += vo.getArpu_banner();
            arpu_video += vo.getArpu_video();
            arpu_msg += vo.getArpu_msg();
        }

        total.put("total_ad_revenue", (double)Math.round(total_ad_revenue * 100) / 100);
        total.put("ad_revenue", (double)Math.round(ad_revenue * 100) / 100);
        total.put("avg_pv_splash", (double)Math.round(avg_pv_splash / list.size() * 100) / 100);
        total.put("avg_pv_plaque", (double)Math.round(avg_pv_plaque / list.size() * 100) / 100);
        total.put("avg_pv_banner", (double)Math.round(avg_pv_banner / list.size() * 100) / 100);
        total.put("avg_pv_video", (double)Math.round(avg_pv_video / list.size() * 100) / 100);
        total.put("avg_pv_msg", (double)Math.round(avg_pv_msg / list.size() * 100) / 100);

        total.put("ecpm_splash", (double)Math.round(ecpm_splash * 100) / 100);
        total.put("ecpm_plaque", (double)Math.round(ecpm_plaque * 100) / 100);
        total.put("ecpm_banner", (double)Math.round(ecpm_banner  * 100) / 100);
        total.put("ecpm_video", (double)Math.round(ecpm_video * 100) / 100);
        total.put("ecpm_msg", (double)Math.round(ecpm_msg * 100) / 100);
        total.put("pv_splash", pv_splash);
        total.put("pv_plaque", pv_plaque);
        total.put("pv_banner", pv_banner);
        total.put("pv_video", pv_video);
        total.put("pv_msg", pv_msg);
        total.put("revenue_splash", (double)Math.round(revenue_splash * 100) / 100);
        total.put("revenue_plaque", (double)Math.round(revenue_plaque * 100) / 100);
        total.put("revenue_banner", (double)Math.round(revenue_banner * 100) / 100);
        total.put("revenue_video", (double)Math.round(revenue_video * 100) / 100);
        total.put("revenue_msg", (double)Math.round(revenue_msg * 100) / 100);
        double revenue_rate = ad_revenue / total_ad_revenue;
        total.put("revenue_rate", (double)Math.round(revenue_rate * 10000) / 100 + "%");
        total.put("active_user_cnt", active_user_cnt);
        total.put("reg_user_cnt", reg_user_cnt);
        total.put("dau_arpu", (double)Math.round(dau_arpu / list.size() * 1000) / 1000);
        total.put("add_arpu", (double)Math.round(add_arpu / list.size() * 1000) / 1000);
        total.put("add_rate", (double)Math.round((float) reg_user_cnt / (float) active_user_cnt * 10000) / 100 + "%");

        total.put("arpu_splash",(double) Math.round(arpu_splash / list.size() * 1000) / 1000);
        total.put("arpu_plaque",(double)Math.round(arpu_plaque / list.size() * 1000) / 1000);
        total.put("arpu_banner", (double)Math.round(arpu_banner / list.size() * 1000) / 1000);
        total.put("arpu_video", (double)Math.round(arpu_video / list.size() * 1000) / 1000);
        total.put("arpu_msg", (double)Math.round(arpu_msg / list.size() * 1000) / 1000);

        return total;
    }

}
