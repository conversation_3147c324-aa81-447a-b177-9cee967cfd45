package com.wbgame.controller.adv2.bigdata;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.aop.ApiIgp;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.advert.UmengAdIncomeReportVo;
import com.wbgame.pojo.param.ActivityTakeParam;
import com.wbgame.service.AdService;
import com.wbgame.service.BigdataService;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 大数据采集来源的变现收入汇总相关.adb统计
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/adb")
public class NewRevenueDataController {

	@Autowired
    private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private Adv2Service adv2Service;
	@Autowired
	private AdService adService;
	@Autowired
	private BigdataService bigdataService;
	@Autowired
	private DnwxBiMapper dnwxBiMapper;
	@Autowired
	private YyhzMapper yyhzMapper;


	/**
	 * 变现预估收入数据查询.v2
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectDnExIncomeDataTwo", method={RequestMethod.GET, RequestMethod.POST})
	public String selectDnExIncomeDataTwo(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String appid = request.getParameter("appid");
		String cha_id = request.getParameter("cha_id");
		String prjid = request.getParameter("prjid");
		String adsid = request.getParameter("adsid");
		String adpos_type = request.getParameter("adpos_type");
		String agent = request.getParameter("agent");
		String appGroup = request.getParameter("appGroup");
		String cha_type_name = request.getParameter("cha_type_name");
		String strategy = request.getParameter("strategy");
		String user_group = request.getParameter("user_group");
		String sdk_adtype = request.getParameter("sdk_adtype");
		String temp_id = request.getParameter("temp_id");
		String temp_name = request.getParameter("temp_name");
		String order_str = request.getParameter("order_str");

		String date_group = request.getParameter("date_group");
		String appid_group = request.getParameter("appid_group");
		String cha_id_group = request.getParameter("cha_id_group");
		String prjid_group = request.getParameter("prjid_group");
		String adsid_group = request.getParameter("adsid_group");
		String adpos_type_group = request.getParameter("adpos_type_group");
		String sdk_adtype_group = request.getParameter("sdk_adtype_group");
		String user_group_group = request.getParameter("user_group_group");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			/** 处理应用组数据 */
			paramMap.put("appGroup", appGroup);
			BlankUtils.setAppGroup(paramMap);

			paramMap.put("start_date", start_date);
			paramMap.put("end_date", end_date);
			paramMap.put("appid", appid);
			paramMap.put("cha_id", cha_id);
			paramMap.put("prjid", prjid);
			paramMap.put("adsid", adsid);
			paramMap.put("adpos_type", adpos_type);
			paramMap.put("agent", agent);
			paramMap.put("cha_type_name", cha_type_name);
			paramMap.put("strategy", strategy);
			paramMap.put("user_group", user_group);
			paramMap.put("sdk_adtype", sdk_adtype);
			paramMap.put("temp_id", temp_id);
			paramMap.put("temp_name", temp_name);
			paramMap.put("order_str", order_str);


			String group = "";
			if("1".equals(date_group)){
				group += ",tdate";
			}
			if("1".equals(appid_group)){
				group += ",appid";
			}
			if("1".equals(cha_id_group)){
				group += ",cha_id";
			}
			if("1".equals(prjid_group)){
				group += ",prjid";
			}
			if("1".equals(adsid_group)){
				group += ",adsid";
			}
			if("1".equals(adpos_type_group)){
				group += ",adpos_type";
			}
			if("1".equals(sdk_adtype_group)){
				group += ",sdk_adtype";
			}
			if("1".equals(user_group_group)){
				group += ",user_group";
			}

			paramMap.put("group", group.isEmpty()?"":group.substring(1));
			paramMap.put("tableName", "dnwx_bi.ads_realization_income_assess_daily");

			PageHelper.startPage(pageNo, pageSize);
			List<Map<String, Object>> list = dnwxBiMapper.selectDnExIncomeData(paramMap);
			long size = ((Page) list).getTotal();
			list.forEach(act -> {
				act.put("fill_rate", act.get("fill_rate")+"%");
				act.put("show_rate", act.get("show_rate")+"%");
				act.put("click_rate", act.get("click_rate")+"%");
			});

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
			result.put("total", dnwxBiMapper.selectDnExIncomeDataSum(paramMap));
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 变现预估收入数据导出.v2
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/bigdata/exportDnExIncomeDataTwo", method={RequestMethod.GET, RequestMethod.POST})
	public void exportDnExIncomeDataTwo(HttpServletRequest request,HttpServletResponse response,
										@ApiIgp({"value","version"}) ActivityTakeParam param) {

		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String appid = request.getParameter("appid");
		String cha_id = request.getParameter("cha_id");
		String prjid = request.getParameter("prjid");
		String adsid = request.getParameter("adsid");
		String adpos_type = request.getParameter("adpos_type");
		String agent = request.getParameter("agent");
		String appGroup = request.getParameter("appGroup");
		String cha_type_name = request.getParameter("cha_type_name");
		String strategy = request.getParameter("strategy");
		String user_group = request.getParameter("user_group");
		String sdk_adtype = request.getParameter("sdk_adtype");
		String temp_id = request.getParameter("temp_id");
		String temp_name = request.getParameter("temp_name");
		String order_str = request.getParameter("order_str");

		String date_group = request.getParameter("date_group");
		String appid_group = request.getParameter("appid_group");
		String cha_id_group = request.getParameter("cha_id_group");
		String prjid_group = request.getParameter("prjid_group");
		String adsid_group = request.getParameter("adsid_group");
		String adpos_type_group = request.getParameter("adpos_type_group");
		String sdk_adtype_group = request.getParameter("sdk_adtype_group");
		String user_group_group = request.getParameter("user_group_group");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return ;
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		/** 处理应用组数据 */
		paramMap.put("appGroup", appGroup);
		BlankUtils.setAppGroup(paramMap);

		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		paramMap.put("appid", appid);
		paramMap.put("cha_id", cha_id);
		paramMap.put("prjid", prjid);
		paramMap.put("adsid", adsid);
		paramMap.put("adpos_type", adpos_type);
		paramMap.put("agent", agent);
		paramMap.put("cha_type_name", cha_type_name);
		paramMap.put("strategy", strategy);
		paramMap.put("user_group", user_group);
		paramMap.put("sdk_adtype", sdk_adtype);
		paramMap.put("temp_id", temp_id);
		paramMap.put("temp_name", temp_name);
		paramMap.put("order_str", order_str);


		String group = "";
		if("1".equals(date_group)){
			group += ",tdate";
		}
		if("1".equals(appid_group)){
			group += ",appid";
		}
		if("1".equals(cha_id_group)){
			group += ",cha_id";
		}
		if("1".equals(prjid_group)){
			group += ",prjid";
		}
		if("1".equals(adsid_group)){
			group += ",adsid";
		}
		if("1".equals(adpos_type_group)){
			group += ",adpos_type";
		}
		if("1".equals(sdk_adtype_group)){
			group += ",sdk_adtype";
		}
		if("1".equals(user_group_group)){
			group += ",user_group";
		}

		paramMap.put("group", group.isEmpty()?"":group.substring(1));
		paramMap.put("tableName", "dnwx_bi.ads_realization_income_assess_daily");

		List<Map<String, Object>> contentList = dnwxBiMapper.selectDnExIncomeData(paramMap);
		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));

			act.put("fill_rate", act.get("fill_rate")+"%");
			act.put("show_rate", act.get("show_rate")+"%");
			act.put("click_rate", act.get("click_rate")+"%");
		});

		headerMap.put("date","日期");
		headerMap.put("appname","应用");
		headerMap.put("cha_id","子渠道");
		headerMap.put("prjid","项目id");
		headerMap.put("adsid","广告源");
		headerMap.put("adpos_type","广告类型");
		headerMap.put("sdk_adtype","sdk广告源类型");
		headerMap.put("user_group","人群");
		headerMap.put("agent","广告平台");
		headerMap.put("strategy","广告策略");
		headerMap.put("cha_type_name","投放类型");
		headerMap.put("ecpm","预估ecpm");
		headerMap.put("income","预估收益");
		headerMap.put("req_num","请求数");
		headerMap.put("fill_num","填充数");
		headerMap.put("fill_rate","填充率");
		headerMap.put("show_num","展示数");
		headerMap.put("show_rate","展示率");
		headerMap.put("click_num","点击数");
		headerMap.put("click_rate","点击率");
		headerMap.put("act_num","活跃数");
		headerMap.put("add_num","新增数");

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");

		String fileName = param.getReport()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		Map<String,String> head = new LinkedHashMap<>();
		try {
			String[] split = param.getValue().split(";");
			for (int i = 0;i<split.length;i++) {
				String[] s = split[i].split(",");
				head.put(s[0],s[1]);
			}
		}catch (Exception e) {
			Asserts.fail("自定义列导出异常");
		}

		ExportExcelUtil.exportXLSX(response,contentList,head,fileName);
	}

	/**
	 * 变现收入校准.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectExtendIncomeRevise", method={RequestMethod.GET, RequestMethod.POST})
	public String selectExtendIncomeRevise(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String cha_id = request.getParameter("cha_id");
		String prjid = request.getParameter("prjid");
		String adsid = request.getParameter("adsid");
		String cha_media = request.getParameter("cha_media");
		String agent = request.getParameter("agent");
		String cha_type_name = request.getParameter("cha_type_name");
		String adpos_type = request.getParameter("adpos_type");
		String user_group = request.getParameter("user_group");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		// group 默认设置
		if (BlankUtils.isBlank(group)) {
			group = "tdate,appid,cha_id,prjid,adsid,adpos_type,user_group";
		}

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
				sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			paramMap.put("sdate", sdate);
			paramMap.put("edate", edate);
			paramMap.put("appid", appid);
			paramMap.put("cha_id", cha_id);
			paramMap.put("prjid", prjid);
			paramMap.put("adsid", adsid);
			paramMap.put("adpos_type", adpos_type);
			paramMap.put("cha_media", cha_media);
			paramMap.put("agent", agent);
			paramMap.put("cha_type_name", cha_type_name);
			paramMap.put("user_group", user_group);
			paramMap.put("order_str", order_str);
			paramMap.put("group", group);

			/* 处理应用组数据 */
			paramMap.put("appGroup", appGroup);
			BlankUtils.setAppGroup(paramMap);

			paramMap.put("tableName", "dnwx_bi.ads_dn_extend_revise_income_daily");

			PageHelper.startPage(pageNo, pageSize);
			List<Map<String, Object>> list = dnwxBiMapper.selectExtendIncomeRevise(paramMap);
			long size = ((Page) list).getTotal();
			String finalGroup = group;
			String finalEdate = edate;
			String finalSdate = sdate;
			list.forEach(act -> {
				if (finalGroup.contains("tdate")) {
					act.put("tdate", act.get("tdate") + "");
				} else {
					act.put("tdate", finalSdate + "至" + finalEdate);
				}
			});

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
			result.put("total", dnwxBiMapper.selectExtendIncomeReviseSum(paramMap));
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 变现收入校准.导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/bigdata/exportExtendIncomeRevise", method={RequestMethod.GET, RequestMethod.POST})
	public void exportExtendIncomeRevise(HttpServletRequest request,HttpServletResponse response,
										 @ApiIgp({"value","version"}) ActivityTakeParam param) {

		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String cha_id = request.getParameter("cha_id");
		String prjid = request.getParameter("prjid");
		String adsid = request.getParameter("adsid");
		String cha_media = request.getParameter("cha_media");
		String agent = request.getParameter("agent");
		String cha_type_name = request.getParameter("cha_type_name");
		String adpos_type = request.getParameter("adpos_type");
		String user_group = request.getParameter("user_group");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");

		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return ;
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// group 默认设置
		if (BlankUtils.isBlank(group)) {
			group = "tdate,appid,cha_id,prjid,adsid,adpos_type,user_group";
		}

		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
			sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		paramMap.put("sdate", sdate);
		paramMap.put("edate", edate);
		paramMap.put("appid", appid);
		paramMap.put("cha_id", cha_id);
		paramMap.put("prjid", prjid);
		paramMap.put("adsid", adsid);
		paramMap.put("adpos_type", adpos_type);
		paramMap.put("cha_media", cha_media);
		paramMap.put("agent", agent);
		paramMap.put("cha_type_name", cha_type_name);
		paramMap.put("group", group);
		paramMap.put("user_group", user_group);
		paramMap.put("order_str", order_str);
		/** 处理应用组数据 */
		paramMap.put("appGroup", appGroup);
		BlankUtils.setAppGroup(paramMap);

		paramMap.put("tableName", "dnwx_bi.ads_dn_extend_revise_income_daily");

		List<Map<String, Object>> contentList = dnwxBiMapper.selectExtendIncomeRevise(paramMap);
		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		String finalGroup = group;
		String finalEdate = edate;
		String finalSdate = sdate;
		contentList.forEach(act -> {
//			act.put("tdate", act.get("tdate")+"");
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));

			if (finalGroup.contains("tdate")) {
				act.put("tdate", act.get("tdate") + "");
			} else {
				act.put("tdate", finalSdate + "至" + finalEdate);
			}
			act.put("ecpm", ((BigDecimal)act.get("ecpm")).doubleValue());
		});

		headerMap.put("tdate","日期");
		headerMap.put("appname","应用");
		headerMap.put("cha_id","子渠道");
		headerMap.put("prjid","项目id");
		headerMap.put("adsid","广告源");
		headerMap.put("adpos_type","广告类型");
		headerMap.put("sdk_adtype","sdk广告源类型");
		headerMap.put("user_group","人群");
		headerMap.put("ecpm","预估ecpm");
		headerMap.put("self_show","自统计展示");
		headerMap.put("gap_val","GAP值");
		headerMap.put("revise_show","校准展示");
		headerMap.put("revise_revenue","校准收入");
		headerMap.put("actnum","活跃数");
		headerMap.put("addnum","新增数");

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");

//		String fileName = "变现收入校准查询_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
		String fileName = param.getReport()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		Map<String,String> head = new LinkedHashMap<>();
		try {
			String[] split = param.getValue().split(";");
			for (int i = 0;i<split.length;i++) {
				String[] s = split[i].split(",");
				head.put(s[0],s[1]);
			}
		}catch (Exception e) {
			Asserts.fail("自定义列导出异常");
		}

		ExportExcelUtil.exportXLSX(response,contentList,head,fileName);
//		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
	}

	/**
	 * 汇总数据校准.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectAdtypeTotalRevise", method={RequestMethod.GET, RequestMethod.POST})
	public String selectAdtypeTotalRevise(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String cha_id = request.getParameter("cha_id");
		String cha_type_name = request.getParameter("cha_type_name");
		String cha_media = request.getParameter("cha_media");
		String user_group = request.getParameter("user_group");
		String dau = request.getParameter("dau");
		String order_str = request.getParameter("order_str");
		String ctype = request.getParameter("ctype");
		String group = request.getParameter("group");
		String temp_id = request.getParameter("temp_id");
		String temp_name = request.getParameter("temp_name");

		// 日期，应用，投放类型，投放媒体，子渠道 维度分组

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
				sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			paramMap.put("sdate", sdate);
			paramMap.put("edate", edate);
			paramMap.put("appid", appid);
			paramMap.put("cha_id", cha_id);
			paramMap.put("cha_type_name", cha_type_name);
			paramMap.put("cha_media", cha_media);
			paramMap.put("user_group", user_group);
			paramMap.put("dau", dau);
			paramMap.put("order_str", order_str);
			paramMap.put("temp_id",temp_id);
			paramMap.put("temp_name",temp_name);
			/** 处理应用组数据 */
			paramMap.put("appGroup", appGroup);
			BlankUtils.setAppGroup(paramMap);

			paramMap.put("group", group);


			List<Map<String, Object>> list = null;
			if("v3".equals(ctype)){
				// 汇总数据校准-新增arpu校准页面
				PageHelper.startPage(pageNo, pageSize);
				list = dnwxBiMapper.selectAdtypeTotalReviseV3(paramMap);

				result.put("total", dnwxBiMapper.selectAdtypeTotalReviseV3Sum(paramMap));

			}else{
				String selectNew = request.getParameter("selectNew");
				/* 汇总数据校准V2: 产品-子渠道维度汇总数据，广告类型取自客户端上报的open_type -郑生.20241017*/
				String NewV2 = request.getParameter("NewV2");
				if (BlankUtils.isNotBlank(selectNew) && "true".equalsIgnoreCase(selectNew)) {
					paramMap.put("tableName", "dnwx_bi.ads_dn_extend_revise_adtype_new_daily");
				}else if (BlankUtils.isNotBlank(NewV2) && "true".equalsIgnoreCase(NewV2)) {
					paramMap.put("tableName", "dnwx_bi.ads_dn_extend_revise_log_adtype_daily");
				} else {
					paramMap.put("tableName", "dnwx_bi.ads_dn_extend_revise_adtype_daily");
				}

				PageHelper.startPage(pageNo, pageSize);
				list = dnwxBiMapper.selectAdtypeTotalRevise(paramMap);
				result.put("total", dnwxBiMapper.selectAdtypeTotalReviseSum(paramMap));

				/** 增加环比数据的处理 */
				if(group.contains("tdate")){
					String before = DateTime.parse(sdate).minusDays(1).toString("yyyy-MM-dd");
					paramMap.put("sdate", before);
					List<Map<String, Object>> twoList = dnwxBiMapper.selectAdtypeTotalRevise(paramMap);
					Map<String, Map<String, Object>> twoMap = new HashMap<>();
					twoList.forEach(act2 -> {
						twoMap.put(act2.get("mapkey")+"", act2);
					});

					list.stream().forEach(act -> {
						//默认环比增长为0.00%
						act.put("addrate_match", "0.00%");
						act.put("dau_arpu_match", "0.00%");
						//					act.put("arpu_splash_match", "0.00%");
						//					act.put("arpu_plaque_match", "0.00%");
						//					act.put("arpu_banner_match", "0.00%");
						//					act.put("arpu_video_match", "0.00%");
						//					act.put("arpu_msg_match", "0.00%");

						String cur_date = (act.get("tdate")+"");
						String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
						String key = (act.get("mapkey")+"").replace(cur_date, yesterday);

						// 设置各类型的环比数值
						Map<String, Object> act2 = twoMap.get(key);
						if(act2 != null){
							String[] adtypes = {"addrate","dau_arpu"};
							//						String[] adtypes = {"addrate","dau_arpu","arpu_splash","arpu_plaque","arpu_banner","arpu_video","arpu_msg"};
							for (String adtype : adtypes) {
								if(act.get(adtype) != null && act2.get(adtype) != null && BlankUtils.isNumeric(act2.get(adtype)+"")){
									act.put(adtype+"_match", strToTwoNumber(act.get(adtype).toString(),act2.get(adtype).toString()));
								}
							}
						}
					});
				}
			}

			long size = ((Page) list).getTotal();


			// 赋值应用名称
			Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
			list.forEach(act -> {
				act.put("tdate", act.get("tdate")+"");
				if(act.get("addrate") != null){
					act.put("addrate", act.get("addrate")+"%");
				}

				Map<String, Object> app = appMap.get(act.get("appid")+"");
				if(app != null)
					act.put("appname", app.get("app_name"));

				//功能标识条件搜索需展示搜索的功能名称(单模块)
				if (!StringUtils.isEmpty(paramMap.get("temp_id"))) {
					try {
						String[] tempIds = act.get("temp_id").toString().split("\\|");
						String[] tempNames = act.get("temp_name").toString().split("\\|");
						for (int i = 0; i<tempIds.length;i++) {
							if (tempIds[i].contains(paramMap.get("temp_id")+"")) {
								act.put("temp_single_name",tempNames[i]);
							}
						}
					}catch (Exception e) {
						e.printStackTrace();
					}
				}

			});

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
    *
    * @param data1 当前日期数据
    * @param data2 前一天数据
    * @return
    */
   private String strToTwoNumber(String data1,String data2){
       String rate ="0.00%";
       try {
           BigDecimal brate = new BigDecimal(data1)
                   .subtract(new BigDecimal(data2))
                   .divide(new BigDecimal(data2),4,RoundingMode.HALF_UP)
                   .multiply(new BigDecimal("100"))
                   .setScale(2,RoundingMode.HALF_UP);
           rate = brate.toString()+"%";
       }catch (Exception e){
       }
       return rate;
   }

	/**
	 * 汇总数据校准-飞书消息使用.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectAdtypeTotalRevise/common", method={RequestMethod.GET, RequestMethod.POST})
	public String selectAdtypeTotalReviseCommon(HttpServletRequest request,HttpServletResponse response) throws IOException {

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String cha_id = request.getParameter("cha_id");
		String cha_type_name = request.getParameter("cha_type_name");
		String cha_media = request.getParameter("cha_media");
		String user_group = request.getParameter("user_group");
		String dau = request.getParameter("dau");
		String order_str = request.getParameter("order_str");
		String ctype = request.getParameter("ctype");
		String group = request.getParameter("group");
		String temp_id = request.getParameter("temp_id");
		String temp_name = request.getParameter("temp_name");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
				sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			paramMap.put("sdate", sdate);
			paramMap.put("edate", edate);
			paramMap.put("appid", appid);
			paramMap.put("cha_id", cha_id);
			paramMap.put("cha_type_name", cha_type_name);
			paramMap.put("cha_media", cha_media);
			paramMap.put("user_group", user_group);
			paramMap.put("dau", dau);
			paramMap.put("order_str", order_str);
			paramMap.put("temp_id",temp_id);
			paramMap.put("temp_name",temp_name);
			/** 处理应用组数据 */
			paramMap.put("appGroup", appGroup);
			BlankUtils.setAppGroup(paramMap);

			paramMap.put("group", group);

			List<Map<String, Object>> list = null;
			if("v3".equals(ctype)){
				list = dnwxBiMapper.selectAdtypeTotalReviseV3(paramMap);

				result.put("total", dnwxBiMapper.selectAdtypeTotalReviseV3Sum(paramMap));

			}else{

				paramMap.put("tableName", "dnwx_bi.ads_dn_extend_revise_adtype_daily");
				list = dnwxBiMapper.selectAdtypeTotalRevise(paramMap);

				result.put("total", dnwxBiMapper.selectAdtypeTotalReviseSum(paramMap));

				/** 增加环比数据的处理 */
				if(group.contains("tdate")){
					String before = DateTime.parse(sdate).minusDays(1).toString("yyyy-MM-dd");
					paramMap.put("sdate", before);
					List<Map<String, Object>> twoList = dnwxBiMapper.selectAdtypeTotalRevise(paramMap);
					Map<String, Map<String, Object>> twoMap = new HashMap<>();
					twoList.forEach(act2 -> {
						twoMap.put(act2.get("mapkey")+"", act2);
					});

					list.stream().forEach(act -> {
						//默认环比增长为0.00%
						act.put("addrate_match", "0.00%");
						act.put("dau_arpu_match", "0.00%");

						String cur_date = (act.get("tdate")+"");
						String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
						String key = (act.get("mapkey")+"").replace(cur_date, yesterday);

						// 设置各类型的环比数值
						Map<String, Object> act2 = twoMap.get(key);
						if(act2 != null){
							String[] adtypes = {"addrate","dau_arpu"};
							for (String adtype : adtypes) {
								if(act.get(adtype) != null && act2.get(adtype) != null && BlankUtils.isNumeric(act2.get(adtype)+"")){
									act.put(adtype+"_match", strToTwoNumber(act.get(adtype).toString(),act2.get(adtype).toString()));
								}
							}
						}


					});
				}
			}


			// 赋值应用名称
			Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
			list.forEach(act -> {
				act.put("tdate", act.get("tdate")+"");
				if(act.get("addrate") != null){
					act.put("addrate", act.get("addrate")+"%");
				}

				Map<String, Object> app = appMap.get(act.get("appid")+"");
				if(app != null)
					act.put("appname", app.get("app_name")+"");

				//功能标识条件搜索需展示搜索的功能名称(单模块)
				if (!StringUtils.isEmpty(paramMap.get("temp_id"))) {
					try {
						String[] tempIds = act.get("temp_id").toString().split("\\|");
						String[] tempNames = act.get("temp_name").toString().split("\\|");
						for (int i = 0; i<tempIds.length;i++) {
							if (tempIds[i].contains(paramMap.get("temp_id")+"")) {
								act.put("temp_single_name",tempNames[i]);
							}
						}
					}catch (Exception e) {
						e.printStackTrace();
					}
				}
			});

			result.put("ret", 1);
			result.put("data", list);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 汇总数据校准.导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/bigdata/exportAdtypeTotalRevise", method={RequestMethod.GET, RequestMethod.POST})
	public void exportAdtypeTotalRevise(HttpServletRequest request,HttpServletResponse response,
										@ApiIgp({"value","version"}) ActivityTakeParam param) {

		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String cha_id = request.getParameter("cha_id");
		String cha_type_name = request.getParameter("cha_type_name");
		String cha_media = request.getParameter("cha_media");
		String user_group = request.getParameter("user_group");
		String dau = request.getParameter("dau");
		String order_str = request.getParameter("order_str");
		String ctype = request.getParameter("ctype");
		String group = request.getParameter("group");
		String temp_id = request.getParameter("temp_id");
		String temp_name = request.getParameter("temp_name");


		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return ;
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
			sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		paramMap.put("sdate", sdate);
		paramMap.put("edate", edate);
		paramMap.put("appid", appid);
		paramMap.put("cha_id", cha_id);
		paramMap.put("cha_type_name", cha_type_name);
		paramMap.put("cha_media", cha_media);
		paramMap.put("user_group", user_group);
		paramMap.put("dau", dau);
		paramMap.put("order_str", order_str);
		paramMap.put("temp_id",temp_id);
		paramMap.put("temp_name",temp_name);
		/** 处理应用组数据 */
		paramMap.put("appGroup", appGroup);
		BlankUtils.setAppGroup(paramMap);

		paramMap.put("group", group);

		List<Map<String, Object>> contentList = null;
		if("v3".equals(ctype)){

			contentList = dnwxBiMapper.selectAdtypeTotalReviseV3(paramMap);
		}else{

			String selectNew = request.getParameter("selectNew");
			/* 汇总数据校准V2: 产品-子渠道维度汇总数据，广告类型取自客户端上报的open_type -郑生.20241017*/
			String NewV2 = request.getParameter("NewV2");
			if (BlankUtils.isNotBlank(selectNew) && "true".equalsIgnoreCase(selectNew)) {
				paramMap.put("tableName", "dnwx_bi.ads_dn_extend_revise_adtype_new_daily");
			}else if (BlankUtils.isNotBlank(NewV2) && "true".equalsIgnoreCase(NewV2)) {
				paramMap.put("tableName", "dnwx_bi.ads_dn_extend_revise_log_adtype_daily");
			} else {
				paramMap.put("tableName", "dnwx_bi.ads_dn_extend_revise_adtype_daily");
			}

			contentList = dnwxBiMapper.selectAdtypeTotalRevise(paramMap);

			/** 增加环比数据的处理 */
			if(group.contains("tdate")){
				String before = DateTime.parse(sdate).minusDays(1).toString("yyyy-MM-dd");
				paramMap.put("sdate", before);
				List<Map<String, Object>> twoList = dnwxBiMapper.selectAdtypeTotalRevise(paramMap);
				Map<String, Map<String, Object>> twoMap = new HashMap<>();
				twoList.forEach(act2 -> {
					twoMap.put(act2.get("mapkey")+"", act2);
				});

				contentList.stream().forEach(act -> {
					//默认环比增长为0.00%
					act.put("addrate_match", "0.00%");
					act.put("dau_arpu_match", "0.00%");

					String cur_date = (act.get("tdate")+"");
					String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
					String key = (act.get("mapkey")+"").replace(cur_date, yesterday);

					// 设置各类型的环比数值
					Map<String, Object> act2 = twoMap.get(key);
					if(act2 != null){
						String[] adtypes = {"addrate","dau_arpu"};
						for (String adtype : adtypes) {
							if(act.get(adtype) != null && act2.get(adtype) != null && BlankUtils.isNumeric(act2.get(adtype)+"")){
								act.put(adtype+"_match", strToTwoNumber(act.get(adtype).toString(),act2.get(adtype).toString()));
							}
						}
					}
				});
			}
		}

		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			act.put("tdate", act.get("tdate")+"");
			if(act.get("addrate") != null){
				act.put("addrate", act.get("addrate")+"%");
			}

			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"");

			//功能标识条件搜索需展示搜索的功能名称(单模块)
			if (!StringUtils.isEmpty(paramMap.get("temp_id"))) {
				try {
					String[] tempIds = act.get("temp_id").toString().split("\\|");
					String[] tempNames = act.get("temp_name").toString().split("\\|");
					for (int i = 0; i<tempIds.length;i++) {
						if (tempIds[i].contains(paramMap.get("temp_id")+"")) {
							act.put("temp_single_name",tempNames[i]);
						}
					}
				}catch (Exception e) {
					e.printStackTrace();
				}
			}
		});


		String fileName = "汇总数据校准_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		if("v2".equals(ctype)){
			fileName = "汇总数据校准-大数据_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		}

		Map<String,String> head = new LinkedHashMap<>();
		try {
			String[] split = param.getValue().split(";");
			for (int i = 0;i<split.length;i++) {
				String[] s = split[i].split(",");
				head.put(s[0],s[1]);
			}
		}catch (Exception e) {
			Asserts.fail("自定义列导出异常");
		}

		ExportExcelUtil.exportXLSX(response,contentList,head,fileName);
//		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
	}

	/**
	 * 项目ID收入校准.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectPrjidTotalIncome", method={RequestMethod.GET, RequestMethod.POST})
	public String selectPrjidTotalIncome(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String prjid = request.getParameter("prjid");
		String cha_id = request.getParameter("cha_id");
		String cha_type_name = request.getParameter("cha_type_name");
		String cha_media = request.getParameter("cha_media");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");
		String temp_id = request.getParameter("temp_id");
		String temp_name = request.getParameter("temp_name");
		String prjid_group_id = request.getParameter("prjid_group_id");


		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
				sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			paramMap.put("sdate", sdate);
			paramMap.put("edate", edate);
			paramMap.put("appid", appid);
			paramMap.put("prjid", prjid);
			paramMap.put("cha_id", cha_id);
			paramMap.put("cha_type_name", cha_type_name);
			paramMap.put("cha_media", cha_media);
			paramMap.put("order_str", order_str);
			paramMap.put("temp_id",temp_id);
			paramMap.put("temp_name",temp_name);
			paramMap.put("group", group);
			/** 处理应用组数据 */
			paramMap.put("appGroup", appGroup);
			BlankUtils.setAppGroup(paramMap);


			/* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
			if(!BlankUtils.checkBlank(prjid_group_id)) {
				Map<String,String> params = new HashMap<>();
				params.put("ctype", "2");
				params.put("id", prjid_group_id);
				List<Map<String, Object>> groupList = yyhzMapper.selectPrjidGroupConfigConfig(params);

				if(groupList != null && groupList.size() > 0) {

					String collect = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("prjid","").toString()))
							.map(act -> act.getOrDefault("prjid","").toString())
							.collect(Collectors.joining(","));
					String collect2 = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("chastr","").toString()))
							.flatMap(act -> Arrays.stream((act.getOrDefault("chastr","").toString()).split(",")))
							.map(str -> "'"+str+"'")
							.collect(Collectors.joining(","));

					String match_str = "";
					if(!BlankUtils.checkBlank(collect)){
						match_str = String.format(" prjid in (%s) ", collect);
					}
					if(!BlankUtils.checkBlank(collect2)){
						if (!match_str.isEmpty()) {
							match_str += " or ";
						}
						match_str += String.format(" concat(appid,'#',cha_id) in (%s) ", collect2);
					}
					paramMap.put("match_str", "("+match_str+")");
				}
			}



			paramMap.put("tableName", "dnwx_bi.ads_dn_extend_prjid_income_daily");

			PageHelper.startPage(pageNo, pageSize);
			List<Map<String, Object>> list = dnwxBiMapper.selectPrjidTotalIncome(paramMap);
			long size = ((Page) list).getTotal();
			list.forEach(act -> act.put("tdate", act.get("tdate")+""));

			Map<String, Object> total = dnwxBiMapper.selectPrjidTotalIncomeSum(paramMap);
			list.forEach(act -> {
				//功能标识条件搜索需展示搜索的功能名称(单模块)
				if (!StringUtils.isEmpty(paramMap.get("temp_id"))) {
					try {
						String[] tempIds = act.get("temp_id").toString().split("\\|");
						String[] tempNames = act.get("temp_name").toString().split("\\|");
						for (int i = 0; i<tempIds.length;i++) {
							if (tempIds[i].contains(paramMap.get("temp_id")+"")) {
								act.put("temp_single_name",tempNames[i]);
							}
						}
					}catch (Exception e) {
						e.printStackTrace();
					}
				}
			});

			/** 增加环比数据的处理 */
			if(group.contains("tdate")){
				String before = DateTime.parse(sdate).minusDays(1).toString("yyyy-MM-dd");
				paramMap.put("sdate", before);
				List<Map<String, Object>> twoList = dnwxBiMapper.selectPrjidTotalIncome(paramMap);
				Map<String, Map<String, Object>> twoMap = new HashMap<>();
				twoList.forEach(act2 -> {
					twoMap.put(act2.get("mapkey")+"", act2);
				});

				list.stream().forEach(act -> {
					//默认环比增长为0.00%
					act.put("addrate_match", "0.00%");
					act.put("dau_arpu_match", "0.00%");

					String cur_date = (act.get("tdate")+"");
					String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
					String key = (act.get("mapkey")+"").replace(cur_date, yesterday);

					// 设置各类型的环比数值
					Map<String, Object> act2 = twoMap.get(key);
					if(act2 != null){
						String[] adtypes = {"addrate","dau_arpu"};
						//						String[] adtypes = {"addrate","dau_arpu","arpu_splash","arpu_plaque","arpu_banner","arpu_video","arpu_msg"};
						for (String adtype : adtypes) {
							if(act.get(adtype) != null && act2.get(adtype) != null && BlankUtils.isNumeric(act2.get(adtype)+"")){
								act.put(adtype+"_match", strToTwoNumber(act.get(adtype).toString(),act2.get(adtype).toString()));
							}
						}
					}
					act.put("addrate", act.get("addrate") + "%");
				});
			}

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
			result.put("total", total);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 项目ID收入校准.导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/bigdata/exportPrjidTotalIncome", method={RequestMethod.GET, RequestMethod.POST})
	public void exportPrjidTotalIncome(HttpServletRequest request,HttpServletResponse response,
									   @ApiIgp({"value","version"}) ActivityTakeParam param) {

		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String prjid = request.getParameter("prjid");
		String cha_id = request.getParameter("cha_id");
		String cha_type_name = request.getParameter("cha_type_name");
		String cha_media = request.getParameter("cha_media");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");
		String temp_id = request.getParameter("temp_id");
		String temp_name = request.getParameter("temp_name");
		String prjid_group_id = request.getParameter("prjid_group_id");

		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return ;
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
			sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		paramMap.put("sdate", sdate);
		paramMap.put("edate", edate);
		paramMap.put("appid", appid);
		paramMap.put("prjid", prjid);
		paramMap.put("cha_id", cha_id);
		paramMap.put("cha_type_name", cha_type_name);
		paramMap.put("cha_media", cha_media);
		paramMap.put("order_str", order_str);
		paramMap.put("temp_id",temp_id);
		paramMap.put("temp_name",temp_name);
		paramMap.put("group", group);
		/** 处理应用组数据 */
		paramMap.put("appGroup", appGroup);
		BlankUtils.setAppGroup(paramMap);

		/* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
		if(!BlankUtils.checkBlank(prjid_group_id)) {
			Map<String,String> params = new HashMap<>();
			params.put("ctype", "2");
			params.put("id", prjid_group_id);
			List<Map<String, Object>> groupList = yyhzMapper.selectPrjidGroupConfigConfig(params);

			if(groupList != null && groupList.size() > 0) {

				String collect = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("prjid","").toString()))
						.map(act -> act.getOrDefault("prjid","").toString())
						.collect(Collectors.joining(","));
				String collect2 = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("chastr","").toString()))
						.flatMap(act -> Arrays.stream((act.getOrDefault("chastr","").toString()).split(",")))
						.map(str -> "'"+str+"'")
						.collect(Collectors.joining(","));

				String match_str = "";
				if(!BlankUtils.checkBlank(collect)){
					match_str = String.format(" prjid in (%s) ", collect);
				}
				if(!BlankUtils.checkBlank(collect2)){
					if (!match_str.isEmpty()) {
						match_str += " or ";
					}
					match_str += String.format(" concat(appid,'#',cha_id) in (%s) ", collect2);
				}
				paramMap.put("match_str", "("+match_str+")");
			}
		}


		paramMap.put("tableName", "dnwx_bi.ads_dn_extend_prjid_income_daily");

		List<Map<String, Object>> contentList = dnwxBiMapper.selectPrjidTotalIncome(paramMap);
		/** 增加环比数据的处理 */
		if(group.contains("tdate")){
			String before = DateTime.parse(sdate).minusDays(1).toString("yyyy-MM-dd");
			paramMap.put("sdate", before);
			List<Map<String, Object>> twoList = dnwxBiMapper.selectPrjidTotalIncome(paramMap);
			Map<String, Map<String, Object>> twoMap = new HashMap<>();
			twoList.forEach(act2 -> {
				twoMap.put(act2.get("mapkey")+"", act2);
			});

			contentList.stream().forEach(act -> {
				//默认环比增长为0.00%
				act.put("addrate_match", "0.00%");
				act.put("dau_arpu_match", "0.00%");

				String cur_date = (act.get("tdate")+"");
				String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
				String key = (act.get("mapkey")+"").replace(cur_date, yesterday);

				// 设置各类型的环比数值
				Map<String, Object> act2 = twoMap.get(key);
				if(act2 != null){
					String[] adtypes = {"addrate","dau_arpu"};
					//						String[] adtypes = {"addrate","dau_arpu","arpu_splash","arpu_plaque","arpu_banner","arpu_video","arpu_msg"};
					for (String adtype : adtypes) {
						if(act.get(adtype) != null && act2.get(adtype) != null && BlankUtils.isNumeric(act2.get(adtype)+"")){
							act.put(adtype+"_match", strToTwoNumber(act.get(adtype).toString(),act2.get(adtype).toString()));
						}
					}
				}
				act.put("addrate", act.get("addrate") + "%");
			});
		}

		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			act.put("tdate", act.get("tdate")+"");
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));

			//功能标识条件搜索需展示搜索的功能名称(单模块)
			if (!StringUtils.isEmpty(paramMap.get("temp_id"))) {
				try {
					String[] tempIds = act.get("temp_id").toString().split("\\|");
					String[] tempNames = act.get("temp_name").toString().split("\\|");
					for (int i = 0; i<tempIds.length;i++) {
						if (tempIds[i].contains(paramMap.get("temp_id")+"")) {
							act.put("temp_single_name",tempNames[i]);
						}
					}
				}catch (Exception e) {
					e.printStackTrace();
				}
			}
		});


		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");

		String fileName = param.getReport()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
//		String fileName = "项目ID收入预估查询_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
		Map<String,String> head = new LinkedHashMap<>();
		try {
			String[] split = param.getValue().split(";");
			for (int i = 0;i<split.length;i++) {
				String[] s = split[i].split(",");
				head.put(s[0],s[1]);
			}
		}catch (Exception e) {
			Asserts.fail("自定义列导出异常");
		}

		ExportExcelUtil.exportXLSX(response,contentList,head,fileName);
//		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
	}


	/**
	 * 变现收入二级查询.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectSubchaTotalRevise", method={RequestMethod.GET, RequestMethod.POST})
	public String selectSubchaTotalRevise(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String cha_id = request.getParameter("cha_id");
		String cha_type_name = request.getParameter("cha_type_name");
		String cha_media = request.getParameter("cha_media");
		String sub_cha = request.getParameter("sub_cha");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
				sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			paramMap.put("sdate", sdate);
			paramMap.put("edate", edate);
			paramMap.put("appid", appid);
			paramMap.put("cha_id", cha_id);
			paramMap.put("cha_type_name", cha_type_name);
			paramMap.put("cha_media", cha_media);
			paramMap.put("sub_cha", sub_cha);
			paramMap.put("order_str", order_str);
			/** 处理应用组数据 */
			paramMap.put("appGroup", appGroup);
			BlankUtils.setAppGroup(paramMap);

			if(BlankUtils.isBlank(group)) {
				group = "tdate,appid,cha_id,sub_cha,cha_type_name,cha_media";
			}

			paramMap.put("group", group);
			paramMap.put("tableName", "dnwx_bi.ads_dn_extend_subcha_total_daily");

			PageHelper.startPage(pageNo, pageSize);
			List<Map<String, Object>> list = dnwxBiMapper.selectSubchaTotal(paramMap);
			long size = ((Page) list).getTotal();
			list.forEach(act -> act.put("tdate", act.get("tdate")+""));

			/** 增加环比数据的处理 */
			if(group.contains("tdate")){
				String before = DateTime.parse(sdate).minusDays(1).toString("yyyy-MM-dd");
				paramMap.put("sdate", before);
				List<Map<String, Object>> twoList = dnwxBiMapper.selectSubchaTotal(paramMap);
				Map<String, Map<String, Object>> twoMap = new HashMap<>();
				twoList.forEach(act2 -> {
					twoMap.put(act2.get("mapkey")+"", act2);
				});

				list.stream().forEach(act -> {
					//默认环比增长为0.00%
					act.put("addrate_match", "0.00%");
					act.put("dau_arpu_match", "0.00%");

					String cur_date = (act.get("tdate")+"");
					String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
					String key = (act.get("mapkey")+"").replace(cur_date, yesterday);

					// 设置各类型的环比数值
					Map<String, Object> act2 = twoMap.get(key);
					if(act2 != null){
						String[] adtypes = {"addrate","dau_arpu"};
						//						String[] adtypes = {"addrate","dau_arpu","arpu_splash","arpu_plaque","arpu_banner","arpu_video","arpu_msg"};
						for (String adtype : adtypes) {
							if(act.get(adtype) != null && act2.get(adtype) != null && BlankUtils.isNumeric(act2.get(adtype)+"")){
								act.put(adtype+"_match", strToTwoNumber(act.get(adtype).toString(),act2.get(adtype).toString()));
							}
						}
					}
					act.put("addrate", act.get("addrate") + "%");
				});
			}

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
			result.put("total", dnwxBiMapper.selectSubchaTotalSum(paramMap));
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 变现收入二级查询.导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/bigdata/exportSubchaTotalRevise", method={RequestMethod.GET, RequestMethod.POST})
	public void exportSubchaTotalRevise(HttpServletRequest request,HttpServletResponse response,
                                        String value, String report) {

		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String cha_id = request.getParameter("cha_id");
		String cha_type_name = request.getParameter("cha_type_name");
		String cha_media = request.getParameter("cha_media");
		String sub_cha = request.getParameter("sub_cha");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");

		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return ;
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
			sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		paramMap.put("sdate", sdate);
		paramMap.put("edate", edate);
		paramMap.put("appid", appid);
		paramMap.put("cha_id", cha_id);
		paramMap.put("cha_type_name", cha_type_name);
		paramMap.put("cha_media", cha_media);
		paramMap.put("sub_cha", sub_cha);
		paramMap.put("order_str", order_str);
		/** 处理应用组数据 */
		paramMap.put("appGroup", appGroup);
		BlankUtils.setAppGroup(paramMap);

		if(BlankUtils.isBlank(group)) {
			group = "tdate,appid,cha_id,sub_cha,cha_type_name,cha_media";
		}
		paramMap.put("group", group);

		paramMap.put("tableName", "dnwx_bi.ads_dn_extend_subcha_total_daily");

		List<Map<String, Object>> contentList = dnwxBiMapper.selectSubchaTotal(paramMap);

		/** 增加环比数据的处理 */
		if(group.contains("tdate")){
			String before = DateTime.parse(sdate).minusDays(1).toString("yyyy-MM-dd");
			paramMap.put("sdate", before);
			List<Map<String, Object>> twoList = dnwxBiMapper.selectSubchaTotal(paramMap);
			Map<String, Map<String, Object>> twoMap = new HashMap<>();
			twoList.forEach(act2 -> {
				twoMap.put(act2.get("mapkey")+"", act2);
			});

			contentList.stream().forEach(act -> {
				//默认环比增长为0.00%
				act.put("addrate_match", "0.00%");
				act.put("dau_arpu_match", "0.00%");

				String cur_date = (act.get("tdate")+"");
				String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
				String key = (act.get("mapkey")+"").replace(cur_date, yesterday);

				// 设置各类型的环比数值
				Map<String, Object> act2 = twoMap.get(key);
				if(act2 != null){
					String[] adtypes = {"addrate","dau_arpu"};
					//						String[] adtypes = {"addrate","dau_arpu","arpu_splash","arpu_plaque","arpu_banner","arpu_video","arpu_msg"};
					for (String adtype : adtypes) {
						if(act.get(adtype) != null && act2.get(adtype) != null && BlankUtils.isNumeric(act2.get(adtype)+"")){
							act.put(adtype+"_match", strToTwoNumber(act.get(adtype).toString(),act2.get(adtype).toString()));
						}
					}
				}
				act.put("addrate", act.get("addrate") + "%");
			});
		}

		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			act.put("tdate", act.get("tdate")+"");
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name"));
		});



        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = report+"_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
        ExportExcelUtil.export(response,contentList,head,fileName);
	}


	/**
	 * 聚合综合查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectExtendGroupCom", method={RequestMethod.GET, RequestMethod.POST})
	public String selectExtendGroupCom(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String start_date = request.getParameter("sdate");
		String end_date = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String strategy = request.getParameter("strategy");
		String adsid = request.getParameter("adsid");
		String adpos_type = request.getParameter("adpos_type");
		String agent = request.getParameter("agent");
		String is_newuser = request.getParameter("is_newuser");
		String sdk_adtype = request.getParameter("sdk_adtype");
		String order_str = request.getParameter("order_str");
		String income_beyond = request.getParameter("income_beyond");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			paramMap.put("start_date", start_date);
			paramMap.put("end_date", end_date);
			paramMap.put("appid", appid);
			paramMap.put("strategy", strategy);
			paramMap.put("adsid", adsid);
			paramMap.put("adpos_type", adpos_type);
			paramMap.put("agent", agent);
			paramMap.put("is_newuser", is_newuser);
			paramMap.put("sdk_adtype", sdk_adtype);
			paramMap.put("order_str", order_str);
			paramMap.put("income_beyond",income_beyond);

			PageHelper.startPage(pageNo, pageSize);
			List<Map<String, Object>> list = dnwxBiMapper.selectDnGroupCom(paramMap);
			long size = ((Page) list).getTotal();

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
			result.put("total", dnwxBiMapper.selectDnGroupComSum(paramMap));
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 聚合综合查询导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/bigdata/exportExtendGroupCom", method={RequestMethod.GET, RequestMethod.POST})
	public void exportExtendGroupCom(HttpServletRequest request,HttpServletResponse response,
									 @ApiIgp({"value","version"}) ActivityTakeParam param) {

		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		String start_date = request.getParameter("sdate");
		String end_date = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String strategy = request.getParameter("strategy");
		String adsid = request.getParameter("adsid");
		String adpos_type = request.getParameter("adpos_type");
		String agent = request.getParameter("agent");
		String is_newuser = request.getParameter("is_newuser");
		String sdk_adtype = request.getParameter("sdk_adtype");
		String order_str = request.getParameter("order_str");
		String income_beyond = request.getParameter("income_beyond");
		// token验证
//		String token = request.getParameter("token");
//		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
//			return ;
//		else
//			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		paramMap.put("appid", appid);
		paramMap.put("strategy", strategy);
		paramMap.put("adsid", adsid);
		paramMap.put("adpos_type", adpos_type);
		paramMap.put("agent", agent);
		paramMap.put("is_newuser", is_newuser);
		paramMap.put("sdk_adtype", sdk_adtype);
		paramMap.put("order_str", order_str);
		paramMap.put("income_beyond",income_beyond);

		List<Map<String, Object>> contentList = dnwxBiMapper.selectDnGroupCom(paramMap);
		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));

			String newuser = (act.get("is_newuser")+"");
			act.put("is_newuser", "0".equals(newuser)?"老用户":"1".equals(newuser)?"新用户":"不区分新老用户");
		});

		headerMap.put("date","日期");
		headerMap.put("appname","产品");
		headerMap.put("strategy","策略");
		headerMap.put("adsid","广告源");
		headerMap.put("agent","广告平台");
		headerMap.put("sdk_adtype","sdk广告源类型");
		headerMap.put("is_newuser","区分新老用户");
		headerMap.put("ad_load_duration","广告源加载时长");
		headerMap.put("ecpm","预估ecpm");
		headerMap.put("income","收入");
		headerMap.put("req_num","请求");
		headerMap.put("fill_num","填充");
		headerMap.put("fill_rate","填充率");
		headerMap.put("show_num","展示");
		headerMap.put("show_rate","展示率");
		headerMap.put("click_num","点击");
		headerMap.put("click_rate","点击率");
		headerMap.put("platform_ecpm","ecpm");
		headerMap.put("ecpm_gap","ecpmGAP");
		headerMap.put("platform_show","自统计展示");
		headerMap.put("show_gap","展示gap");
		headerMap.put("platform_fill","自统计填充");
		headerMap.put("fill_gap","填充gap");
		headerMap.put("platform_req","自统计请求");
		headerMap.put("req_gap","请求gap");

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");

		Map<String,String> head = new LinkedHashMap<>();
		try {
			String[] split = param.getValue().split(";");
			for (int i = 0;i<split.length;i++) {
				String[] s = split[i].split(",");
				head.put(s[0],s[1]);
			}
		}catch (Exception e) {
			Asserts.fail("自定义列导出异常");
		}
		String fileName = param.getReport()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		ExportExcelUtil.exportXLSX(response,contentList,head,fileName);
//		String fileName = "聚合综合查询_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
//		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,request, response);
	}

	/**
	 * 变现-数据gap统计查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectShowGapTotal", method={RequestMethod.GET, RequestMethod.POST})
	public String selectShowGapTotal(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String agent = request.getParameter("agent");
		String sdk_adtype = request.getParameter("sdk_adtype");
		String open_type = request.getParameter("open_type");
		String adsid = request.getParameter("adsid");
		String channel = request.getParameter("channel");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");
		String out = request.getParameter("out");

		//查询条件：策略
		String strategy = request.getParameter("strategy");
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
				sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			paramMap.put("sdate", sdate);
			paramMap.put("edate", edate);
			paramMap.put("appid", appid);
			paramMap.put("agent", agent);
			paramMap.put("sdk_adtype", sdk_adtype);
			paramMap.put("open_type", open_type);
			paramMap.put("adsid", adsid);
			paramMap.put("channel", channel);
			paramMap.put("out", out);
			paramMap.put("order_str", order_str);
			/** 处理应用组数据 */
			paramMap.put("appGroup", appGroup);
			BlankUtils.setAppGroup(paramMap);

			paramMap.put("group", group);

			/** 处理应用自定义分组筛选 */
			paramMap.put("appid_tag", request.getParameter("appid_tag"));
			paramMap.put("appid_tag_rev", request.getParameter("appid_tag_rev"));

			paramMap.put("tableName", "dnwx_bi.ads_dn_show_gap_daily");

			//根据策略，产品，子渠道获取广告配置符合的数据做为参数
			Map<String,String> adsidParamMap = selectAdConfig(strategy,appid,channel,null);
			paramMap.put("adconfig", String.join(",",adsidParamMap.keySet()));
			PageHelper.startPage(pageNo, pageSize);
			List<Map<String, Object>> list = dnwxBiMapper.selectDnShowGapTotal(paramMap);
			long size = ((Page) list).getTotal();
			boolean strategyFlag = !BlankUtils.checkBlank(group) && group.contains("adsid");
			if (strategyFlag && adsidParamMap.isEmpty() && !CollectionUtils.isEmpty(list)) {
				String adsids = "'" + list.stream().map(data -> data.get("adsid") + "").distinct().collect(Collectors.joining("','")) + "'";
				adsidParamMap = selectAdConfig(null,null,null,adsids);
			}
			list.forEach(act -> act.put("tdate", act.get("tdate")+""));
			for(Map<String, Object> act : list) {
				act.put("tdate", act.get("tdate")+"");
				if (strategyFlag) {
					act.put("strategy", adsidParamMap.get(act.get("adsid") + ""));
				}
			}
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
			result.put("total", dnwxBiMapper.selectDnShowGapTotalSum(paramMap));
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	private Map<String,String> selectAdConfig(String strategy, String appid, String channel,String adsid) {
		Map<String,String> adConfigMap = new HashMap<>();
		//不存在广告策略条件，不需要获取广告配置数据
		if (BlankUtils.checkBlank(strategy) && BlankUtils.checkBlank(adsid)) {
			return adConfigMap;
		}
		String sql = "select strategy,adsid from dn_extend_adconfig where 1=1";
		if(!BlankUtils.checkBlank(strategy)) {
			sql += " and strategy in ("+strategy+")";
		}
		if(!BlankUtils.checkBlank(appid)) {
			sql += " and appid in ("+appid+")";
		}
		if(!BlankUtils.checkBlank(channel)) {
			sql += " and cha_id in ("+channel+")";
		}
		if(!BlankUtils.checkBlank(adsid)) {
			sql += " and adsid in ("+adsid+")";
		}
		List<Map<String, String>> queryList = adv2Service.queryListMapOne(sql);
		if (CollectionUtils.isEmpty(queryList)) {
			adConfigMap.put("-1","-1");
		} else {
			queryList.forEach(data -> adConfigMap.put(data.getOrDefault("adsid",""),data.getOrDefault("strategy","")));
		}
		return adConfigMap;
	}

	/**
	 * 变现-数据gap统计导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/bigdata/exportShowGapTotal", method={RequestMethod.GET, RequestMethod.POST})
	public void exportShowGapTotal(HttpServletRequest request,HttpServletResponse response,
								   @ApiIgp({"value","version"}) ActivityTakeParam param) {

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");
		String agent = request.getParameter("agent");
		String sdk_adtype = request.getParameter("sdk_adtype");
		String open_type = request.getParameter("open_type");
		String adsid = request.getParameter("adsid");
		String channel = request.getParameter("channel");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");
		String out = request.getParameter("out");
		String strategy = request.getParameter("strategy");


		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
			sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		paramMap.put("sdate", sdate);
		paramMap.put("edate", edate);
		paramMap.put("appid", appid);
		paramMap.put("agent", agent);
		paramMap.put("sdk_adtype", sdk_adtype);
		paramMap.put("open_type", open_type);
		paramMap.put("adsid", adsid);
		paramMap.put("channel", channel);
		paramMap.put("out", out);
		paramMap.put("order_str", order_str);
		/** 处理应用组数据 */
		paramMap.put("appGroup", appGroup);
		BlankUtils.setAppGroup(paramMap);

		paramMap.put("group", group);

		/** 处理应用自定义分组筛选 */
		paramMap.put("appid_tag", request.getParameter("appid_tag"));
		paramMap.put("appid_tag_rev", request.getParameter("appid_tag_rev"));

		paramMap.put("tableName", "dnwx_bi.ads_dn_show_gap_daily");
		//根据策略，产品，子渠道获取广告配置符合的数据做为参数
		Map<String,String> adsidParamMap = selectAdConfig(strategy,appid,channel,null);
		paramMap.put("adconfig", String.join(",",adsidParamMap.keySet()));

		List<Map<String, Object>> contentList = dnwxBiMapper.selectDnShowGapTotal(paramMap);
		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		boolean strategyFlag = !BlankUtils.checkBlank(group) && group.contains("adsid");
		if (strategyFlag && adsidParamMap.isEmpty() && !CollectionUtils.isEmpty(contentList)) {
			String adsids = "'" + contentList.stream().map(data -> data.get("adsid") + "").distinct().collect(Collectors.joining("','")) + "'";
			adsidParamMap = selectAdConfig(null,null,null,adsids);
		}
		for(Map<String, Object> act : contentList) {
			act.put("tdate", act.get("tdate")+"");
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null) {
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));
			}
			if (strategyFlag) {
				act.put("strategy", adsidParamMap.get(act.get("adsid")+""));
			}
		}
		/*contentList.forEach(act -> {
			act.put("tdate", act.get("tdate")+"");
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));
		});*/


		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");

		Map<String,String> head = new LinkedHashMap<>();
		try {
			String[] split = param.getValue().split(";");
			for (int i = 0;i<split.length;i++) {
				String[] s = split[i].split(",");
				head.put(s[0],s[1]);
			}
		}catch (Exception e) {
			Asserts.fail("自定义列导出异常");
		}
		String fileName = param.getReport()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		ExportExcelUtil.exportXLSX(response,contentList,head,fileName);
	}

	/**
	 * 变现-广告位数据查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectAdposData", method={RequestMethod.GET, RequestMethod.POST})
	public String selectAdposData(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String appid = request.getParameter("appid");
		String cha_id = request.getParameter("cha_id");
		String prjid = request.getParameter("prjid");
		String adpos = request.getParameter("adpos");
		String adpos_type = request.getParameter("adpos_type");
		String adsid = request.getParameter("adsid");
		String appGroup = request.getParameter("appGroup");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			paramMap.put("start_date", start_date);
			paramMap.put("end_date", end_date);
			paramMap.put("appid", appid);
			paramMap.put("cha_id", cha_id);
			paramMap.put("prjid", prjid);
			paramMap.put("adpos", adpos);
			paramMap.put("adpos_type", adpos_type);
			paramMap.put("adsid", adsid);
			paramMap.put("appGroup", appGroup);
			paramMap.put("order_str", order_str);
			/** 处理应用组数据 */
			BlankUtils.setAppGroup(paramMap);

			paramMap.put("group", group);

			// 按照项目ID维度查询不同表
			paramMap.put("tableName", "dnwx_bi.ads_dn_extend_adpos_data_daily");
			if (!group.contains("prjid")){
				paramMap.put("tableName", "dnwx_bi.ads_dn_extend_adpos_data_daily_no_pid");
			}

			PageHelper.startPage(pageNo, pageSize);
			List<Map<String, Object>> list = dnwxBiMapper.selectAdposData(paramMap);
			long size = ((Page) list).getTotal();


			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
			result.put("total", dnwxBiMapper.selectAdposDataSum(paramMap));
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 变现-广告位数据导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/bigdata/exportAdposData", method={RequestMethod.GET, RequestMethod.POST})
	public void exportAdposData(HttpServletRequest request,HttpServletResponse response,
								@ApiIgp({"value","version"}) ActivityTakeParam param) {

		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String appid = request.getParameter("appid");
		String cha_id = request.getParameter("cha_id");
		String prjid = request.getParameter("prjid");
		String adpos = request.getParameter("adpos");
		String adpos_type = request.getParameter("adpos_type");
		String adsid = request.getParameter("adsid");
		String appGroup = request.getParameter("appGroup");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");


		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		paramMap.put("appid", appid);
		paramMap.put("cha_id", cha_id);
		paramMap.put("prjid", prjid);
		paramMap.put("adpos", adpos);
		paramMap.put("adpos_type", adpos_type);
		paramMap.put("adsid", adsid);
		paramMap.put("order_str", order_str);

		/** 处理应用组数据 */
		paramMap.put("appGroup", appGroup);
		BlankUtils.setAppGroup(paramMap);

		paramMap.put("group", group);

		// 按照项目ID维度查询不同表
		paramMap.put("tableName", "dnwx_bi.ads_dn_extend_adpos_data_daily");
		if (!group.contains("prjid")){
			paramMap.put("tableName", "dnwx_bi.ads_dn_extend_adpos_data_daily_no_pid");
		}

		List<Map<String, Object>> contentList = dnwxBiMapper.selectAdposData(paramMap);
		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));
		});

		// 隐藏该参数 2021.12.01
        /*// 带有广告位分组，则匹配广告样式参数
		if(!BlankUtils.checkBlank(adpos_group)){
			String query = "SELECT CONCAT(appid,cha_id,adpos) mapkey,adstyle FROM `dn_extend_adpos_manage` GROUP BY appid,cha_id,adpos";
			Map<String, Map<String, Object>> keyMap = adv2Service.queryListMapOfKey(query);
			contentList.forEach(act -> {
				Map<String, Object> map = keyMap.get(act.get("mapkey")+"");
				if(map != null)
					act.put("adstyle", map.get("adstyle"));
			});
		}*/

		headerMap.put("tdate","日期");
		headerMap.put("appname","应用");
		headerMap.put("cha_id","子渠道");
		headerMap.put("prjid","项目id");
		headerMap.put("adpos","广告位");
		headerMap.put("adpos_type","广告位类型");
		headerMap.put("adsid","广告源");
		/*headerMap.put("adstyle","广告样式");*/
		headerMap.put("ecpm","ecpm");
		headerMap.put("income","预估收入");
		headerMap.put("show_num","自统计展现数");
		headerMap.put("click_num","点击");
		headerMap.put("click_rate","点击率");
		headerMap.put("dau","DAU");
		headerMap.put("device_num","广告覆盖用户");
		headerMap.put("seep_rate","渗透率");
		headerMap.put("per_pv","人均pv");
		headerMap.put("seep_per_pv","渗透人均pv");

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");

		Map<String,String> head = new LinkedHashMap<>();
		try {
			String[] split = param.getValue().split(";");
			for (int i = 0;i<split.length;i++) {
				String[] s = split[i].split(",");
				head.put(s[0],s[1]);
			}
		}catch (Exception e) {
			Asserts.fail("自定义列导出异常");
		}
		String fileName = param.getReport()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		ExportExcelUtil.exportXLSX(response,contentList,head,fileName);
	}

	/**
	 * 实时瀑布流监控
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectExtendGroupData", method={RequestMethod.GET, RequestMethod.POST})
	public String selectExtendGroupData(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String start_date = request.getParameter("sdate");
		String end_date = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String cha_id = request.getParameter("cha_id");
		String prjid = request.getParameter("prjid");
		String adpos_type = request.getParameter("adpos_type");
		String user_group = request.getParameter("user_group");
		String statu = request.getParameter("statu");
		String adsid = request.getParameter("adsid");
		String agent = request.getParameter("agent");
		String order_str = request.getParameter("order_str");
		String ver = request.getParameter("ver");
		String sdktype = request.getParameter("sdktype");

		String prjid_group = request.getParameter("prjid_group");
		String chaid_group = request.getParameter("chaid_group");
		String adpos_type_group = request.getParameter("adpos_type_group");
		String user_group_group = request.getParameter("user_group_group");
		String agent_group = request.getParameter("agent_group");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			paramMap.put("start_date", start_date);
			paramMap.put("end_date", end_date);
			paramMap.put("appid", appid);
			paramMap.put("cha_id", cha_id);
			paramMap.put("prjid", prjid);
			paramMap.put("adpos_type", adpos_type);
			paramMap.put("user_group", user_group);
			paramMap.put("statu", statu);
			paramMap.put("adsid", adsid);
			paramMap.put("agent", agent);
			paramMap.put("sdktype", sdktype);

			/* 处理服务端排序 */
			paramMap.put("order_str", BlankUtils.transformOrder(order_str));

			String group = "tdate";
			if(!BlankUtils.checkBlank(prjid_group)){
				group += ",prjid";
			}
			if(!BlankUtils.checkBlank(chaid_group)){
				group += ",cha_id";
			}
			if(!BlankUtils.checkBlank(adpos_type_group)){
				group += ",adpos_type";
			}
			if(!BlankUtils.checkBlank(user_group_group)){
				group += ",user_group";
			}
			if(!BlankUtils.checkBlank(agent_group)){
				group += ",agent";
			}
			group += ",adsid";
			paramMap.put("group_column", group.replace(",cha_id", ""));
			paramMap.put("group", group);
			paramMap.put("groups", (group+",strategy").split(","));

			String tableName = "dnwx_bi.ads_dn_extend_group_data_two_daily";
			if("add".equals(ver)){
				tableName = "dnwx_bi.ads_dn_extend_group_data_add_daily";
			}

			paramMap.put("ver", ver);
			paramMap.put("tableName", tableName);

			PageHelper.startPage(pageNo, pageSize);
			List<Map<String, Object>> list = dnwxBiMapper.selectDnGroupData(paramMap);
			long size = ((Page) list).getTotal();

			Map<String, Object> total = dnwxBiMapper.selectDnGroupDataSum(paramMap);

			// 查询出前一天的数据，然后合并原有结果
			DecimalFormat df = new DecimalFormat("#0.00");
			String before = DateTime.parse(start_date).minusDays(1).toString("yyyy-MM-dd");
			paramMap.put("start_date", before);
			paramMap.put("end_date", before);
			List<Map<String, Object>> twoList = dnwxBiMapper.selectDnGroupData(paramMap);
			twoList.addAll(list);

			// 遍历原有结果，从结果2中匹配赋值
			List<Map<String, Object>> collect = null;
			if("v2".equals(ver)){

				collect = list.stream().map(act -> {

							// 按 昨天日期+项目+子渠道+广告源匹配
							String date = DateTime.parse(act.get("date")+"").minusDays(1).toString("yyyy-MM-dd");
							String mapkey = date+""+act.get("prjid")+act.get("cha_id")+act.get("adsid");

							return twoList.stream()
									.filter(act2 -> Objects.equals(act2.get("date")+""+act2.get("prjid")+act2.get("cha_id")+act2.get("adsid"), mapkey))
									.findFirst().map(act2 -> {
										// 计算填充率的环比，赋值给结果1中
										if(act.get("fill_rate") != null && act2.get("fill_rate") != null){
											Double fill = Double.valueOf((act.get("fill_rate")+"").replace("%", ""));
											Double fill2 = Double.valueOf((act2.get("fill_rate")+"").replace("%", ""));
											if(fill != 0 && fill2 != 0){
												act.put("fill_match", df.format((fill/fill2*100d)-100)+"%");
											}
										}
										return act;
									})
									.orElse(act);
						})
						.filter(Objects::nonNull).collect(Collectors.toList());
			}else if("add".equals(ver)){

				collect = list.stream().map(act -> {

							// 按 昨天日期+项目+子渠道+广告源匹配
							String date = DateTime.parse(act.get("date")+"").minusDays(1).toString("yyyy-MM-dd");
							String mapkey = date+""+act.get("prjid")+act.get("cha_id")+act.get("adsid");

							return twoList.stream()
									.filter(act2 -> Objects.equals(act2.get("date")+""+act2.get("prjid")+act2.get("cha_id")+act2.get("adsid"), mapkey))
									.findFirst().map(act2 -> {
										// 计算填充率的环比，赋值给结果1中
										if(act.get("fill_rate") != null && act2.get("fill_rate") != null){
											Double fill = Double.valueOf((act.get("fill_rate")+"").replace("%", ""));
											Double fill2 = Double.valueOf((act2.get("fill_rate")+"").replace("%", ""));
											if(fill != 0 && fill2 != 0){
												act.put("fill_match", df.format((fill/fill2*100d)-100)+"%");
											}
										}
										return act;
									})
									.orElse(act);
						})
						.filter(Objects::nonNull).collect(Collectors.toList());
			}else{

				collect = list.stream().map(act -> {

							// 按 昨天日期+项目+广告源匹配
							String date = DateTime.parse(act.get("date")+"").minusDays(1).toString("yyyy-MM-dd");
							String mapkey = date+""+act.get("prjid")+act.get("adsid");

							return twoList.stream()
									.filter(act2 -> Objects.equals(act2.get("date")+""+act2.get("prjid")+act2.get("adsid"), mapkey))
									.findFirst().map(act2 -> {
										// 计算填充率的环比，赋值给结果1中
										if(act.get("fill_rate") != null && act2.get("fill_rate") != null){
											Double fill = Double.valueOf((act.get("fill_rate")+"").replace("%", ""));
											Double fill2 = Double.valueOf((act2.get("fill_rate")+"").replace("%", ""));
											if(fill != 0 && fill2 != 0){
												act.put("fill_match", df.format((fill/fill2*100d)-100)+"%");
											}
										}
										return act;
									})
									.orElse(act);
						})
						.filter(Objects::nonNull).collect(Collectors.toList());
			}

			for (Map<String, Object> act : collect) {
				act.put("fill_rate", act.get("fill_rate")+"%");
				act.put("show_rate", act.get("show_rate")+"%");
				act.put("click_rate", act.get("click_rate")+"%");
			}

			result.put("ret", 1);
			result.put("data", collect);
			result.put("totalCount", size);
			result.put("total", total);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 实时瀑布流监控-数据导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/bigdata/exportExtendGroupData", method={RequestMethod.GET, RequestMethod.POST})
	public void exportExtendGroupData(HttpServletRequest request,HttpServletResponse response,
									  @ApiIgp({"value","version"}) ActivityTakeParam param) {

		String start_date = request.getParameter("sdate");
		String end_date = request.getParameter("edate");
		String appid = request.getParameter("appid");
		String cha_id = request.getParameter("cha_id");
		String prjid = request.getParameter("prjid");
		String adpos_type = request.getParameter("adpos_type");
		String user_group = request.getParameter("user_group");
		String statu = request.getParameter("statu");
		String adsid = request.getParameter("adsid");
		String agent = request.getParameter("agent");
		String order_str = request.getParameter("order_str");
		String ver = request.getParameter("ver");
		String sdktype = request.getParameter("sdktype");

		String prjid_group = request.getParameter("prjid_group");
		String chaid_group = request.getParameter("chaid_group");
		String adpos_type_group = request.getParameter("adpos_type_group");
		String user_group_group = request.getParameter("user_group_group");
		String agent_group = request.getParameter("agent_group");


		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
			start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		paramMap.put("start_date", start_date);
		paramMap.put("end_date", end_date);
		paramMap.put("appid", appid);
		paramMap.put("cha_id", cha_id);
		paramMap.put("prjid", prjid);
		paramMap.put("adpos_type", adpos_type);
		paramMap.put("user_group", user_group);
		paramMap.put("statu", statu);
		paramMap.put("adsid", adsid);
		paramMap.put("agent", agent);
		paramMap.put("sdktype", sdktype);

		/* 处理服务端排序 */
		paramMap.put("order_str", BlankUtils.transformOrder(order_str));


		String group = "tdate";
		if(!BlankUtils.checkBlank(prjid_group)){
			group += ",prjid";
		}
		if(!BlankUtils.checkBlank(chaid_group)){
			group += ",cha_id";
		}
		if(!BlankUtils.checkBlank(adpos_type_group)){
			group += ",adpos_type";
		}
		if(!BlankUtils.checkBlank(user_group_group)){
			group += ",user_group";
		}
		if(!BlankUtils.checkBlank(agent_group)){
			group += ",agent";
		}
		group += ",adsid";
		paramMap.put("group_column", group.replace(",cha_id", ""));
		paramMap.put("group", group);
		paramMap.put("groups", (group+",strategy").split(","));

		String tableName = "dnwx_bi.ads_dn_extend_group_data_two_daily";
		if("add".equals(ver)){
			tableName = "dnwx_bi.ads_dn_extend_group_data_add_daily";
		}

		paramMap.put("ver", ver);
		paramMap.put("tableName", tableName);

		List<Map<String, Object>> contentList = dnwxBiMapper.selectDnGroupData(paramMap);
		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"-"+app.get("mapkey"));
		});

		// 查询出前一天的数据，然后合并原有结果
		DecimalFormat df = new DecimalFormat("#0.0");
		String before = DateTime.parse(start_date).minusDays(1).toString("yyyy-MM-dd");
		paramMap.put("start_date", before);
		paramMap.put("end_date", before);
		List<Map<String, Object>> twoList = dnwxBiMapper.selectDnGroupData(paramMap);
		twoList.addAll(contentList);

		// 遍历原有结果，从结果2中匹配赋值
		List<Map<String, Object>> collect = null;
		if("v2".equals(ver)){

			collect = contentList.stream().map(act -> {

						// 按 昨天日期+项目+子渠道+广告源匹配
						String date = DateTime.parse(act.get("date")+"").minusDays(1).toString("yyyy-MM-dd");
						String mapkey = date+""+act.get("prjid")+act.get("cha_id")+act.get("adsid");

						return twoList.stream()
								.filter(act2 -> Objects.equals(act2.get("date")+""+act2.get("prjid")+act2.get("cha_id")+act2.get("adsid"), mapkey))
								.findFirst().map(act2 -> {
									// 计算填充率的环比，赋值给结果1中
									if(act.get("fill_rate") != null && act2.get("fill_rate") != null){
										Double fill = Double.valueOf((act.get("fill_rate")+"").replace("%", ""));
										Double fill2 = Double.valueOf((act2.get("fill_rate")+"").replace("%", ""));
										if(fill != 0 && fill2 != 0){
											act.put("fill_match", df.format((fill/fill2*100d)-100)+"%");
										}
									}
									return act;
								})
								.orElse(act);
					})
					.filter(Objects::nonNull).collect(Collectors.toList());
		}else if("add".equals(ver)){

			collect = contentList.stream().map(act -> {

						// 按 昨天日期+项目+子渠道+广告源匹配
						String date = DateTime.parse(act.get("date")+"").minusDays(1).toString("yyyy-MM-dd");
						String mapkey = date+""+act.get("prjid")+act.get("cha_id")+act.get("adsid");

						return twoList.stream()
								.filter(act2 -> Objects.equals(act2.get("date")+""+act2.get("prjid")+act2.get("cha_id")+act2.get("adsid"), mapkey))
								.findFirst().map(act2 -> {
									// 计算填充率的环比，赋值给结果1中
									if(act.get("fill_rate") != null && act2.get("fill_rate") != null){
										Double fill = Double.valueOf((act.get("fill_rate")+"").replace("%", ""));
										Double fill2 = Double.valueOf((act2.get("fill_rate")+"").replace("%", ""));
										if(fill != 0 && fill2 != 0){
											act.put("fill_match", df.format((fill/fill2*100d)-100)+"%");
										}
									}
									return act;
								})
								.orElse(act);
					})
					.filter(Objects::nonNull).collect(Collectors.toList());
		}else{

			collect = contentList.stream().map(act -> {

						// 按 昨天日期+项目+广告源匹配
						String date = DateTime.parse(act.get("date")+"").minusDays(1).toString("yyyy-MM-dd");
						String mapkey = date+""+act.get("prjid")+act.get("adsid");

						return twoList.stream()
								.filter(act2 -> Objects.equals(act2.get("date")+""+act2.get("prjid")+act2.get("adsid"), mapkey))
								.findFirst().map(act2 -> {
									// 计算填充率的环比，赋值给结果1中
									if(act.get("fill_rate") != null && act2.get("fill_rate") != null){
										Double fill = Double.valueOf((act.get("fill_rate")+"").replace("%", ""));
										Double fill2 = Double.valueOf((act2.get("fill_rate")+"").replace("%", ""));
										if(fill != 0 && fill2 != 0){
											act.put("fill_match", df.format((fill/fill2*100d)-100)+"%");
										}
									}
									return act;
								})
								.orElse(act);
					})
					.filter(Objects::nonNull).collect(Collectors.toList());
		}

		for (Map<String, Object> act : collect) {
			act.put("fill_rate", act.get("fill_rate")+"%");
			act.put("show_rate", act.get("show_rate")+"%");
			act.put("click_rate", act.get("click_rate")+"%");
		}


		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");

		Map<String,String> head = new LinkedHashMap<>();
		try {
			String[] split = param.getValue().split(";");
			for (int i = 0;i<split.length;i++) {
				String[] s = split[i].split(",");
				head.put(s[0],s[1]);
			}
		}catch (Exception e) {
			Asserts.fail("自定义列导出异常");
		}
		String fileName = param.getReport()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		ExportExcelUtil.exportXLSX(response,collect,head,fileName);
	}


	/**
	 * 汇总数据-用户群.查询
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/bigdata/selectAdtypeTotalGroup", method={RequestMethod.GET, RequestMethod.POST})
	public String selectAdtypeTotalGroup(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appGroup = request.getParameter("appGroup");
		String appid = request.getParameter("appid");
		String cha_id = request.getParameter("cha_id");
		String prjid = request.getParameter("prjid");
		String user_group = request.getParameter("user_group");
		String order_str = request.getParameter("order_str");

		// 日期，应用，子渠道，项目ID，用户群 维度分组
//		String date_group = request.getParameter("date_group");
//		String appid_group = request.getParameter("appid_group");
//		String cha_id_group = request.getParameter("cha_id_group");
//		String prjid_group = request.getParameter("prjid_group");
//		String user_group_group = request.getParameter("user_group_group");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
				sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
				edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			}
			paramMap.put("sdate", sdate);
			paramMap.put("edate", edate);
			paramMap.put("appid", appid);
			paramMap.put("cha_id", cha_id);
			paramMap.put("prjid", prjid);
			paramMap.put("user_group", user_group);
			paramMap.put("order_str", order_str);
			/** 处理应用组数据 */
			paramMap.put("appGroup", appGroup);
			BlankUtils.setAppGroup(paramMap);

//			// 获取分组
//			List<String> groups = new ArrayList<String>();
//			if(!BlankUtils.checkBlank(date_group))
//				groups.add("tdate");
//			if(!BlankUtils.checkBlank(appid_group))
//				groups.add("appid");
//			if(!BlankUtils.checkBlank(cha_id_group))
//				groups.add("cha_id");
//			if(!BlankUtils.checkBlank(prjid_group))
//				groups.add("prjid");
//			if(!BlankUtils.checkBlank(user_group_group))
//				groups.add("user_group");
			String groups = request.getParameter("group");
			paramMap.put("group", groups);

			paramMap.put("tableName", "dnwx_bi.ads_dn_extend_group_adtype_daily");

			PageHelper.startPage(pageNo, pageSize);
			List<Map<String, Object>> list = dnwxBiMapper.selectAdtypeTotalGroup(paramMap);
			long size = ((Page) list).getTotal();
			list.forEach(act -> act.put("tdate", act.get("tdate")+""));

			Map<String, Object> total = dnwxBiMapper.selectAdtypeTotalGroupSum(paramMap);

			/** 增加环比数据的处理 */
			if(groups.contains("tdate")){
				String before = DateTime.parse(sdate).minusDays(1).toString("yyyy-MM-dd");
				paramMap.put("sdate", before);
				paramMap.put("edate", DateTime.parse(edate).minusDays(1).toString("yyyy-MM-dd"));
				List<Map<String, Object>> twoList = dnwxBiMapper.selectAdtypeTotalGroup(paramMap);
				Map<String, Map<String, Object>> twoMap = new HashMap<>();
				twoList.forEach(act2 -> {
					twoMap.put(act2.get("mapkey")+"", act2);
				});

				list.stream().forEach(act -> {
					//默认环比增长为0.00%
					act.put("addrate_match", "0.00%");
					act.put("dau_arpu_match", "0.00%");

					String cur_date = (act.get("tdate")+"");
					String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
					String key = (act.get("mapkey")+"").replace(cur_date, yesterday);

					// 设置各类型的环比数值
					Map<String, Object> act2 = twoMap.get(key);
					if(act2 != null){
						String[] adtypes = {"addrate","dau_arpu"};
						//						String[] adtypes = {"addrate","dau_arpu","arpu_splash","arpu_plaque","arpu_banner","arpu_video","arpu_msg"};
						for (String adtype : adtypes) {
							if(act.get(adtype) != null && act2.get(adtype) != null && BlankUtils.isNumeric(act2.get(adtype)+"")){
								act.put(adtype+"_match", strToTwoNumber(act.get(adtype).toString(),act2.get(adtype).toString()));
							}
						}
					}
					act.put("addrate", act.get("addrate") + "%");
				});
			}

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
			result.put("total", total);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}

	/**
	 * 汇总数据-用户群.导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/bigdata/exportAdtypeTotalGroup", method={RequestMethod.GET, RequestMethod.POST})
	public void exportAdtypeTotalGroup(HttpServletRequest request,HttpServletResponse response,
									   @ApiIgp({"value","version"}) ActivityTakeParam param) {

		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String appGroup = request.getParameter("appGroup");
		String appid = request.getParameter("appid");
		String cha_id = request.getParameter("cha_id");
		String prjid = request.getParameter("prjid");
		String user_group = request.getParameter("user_group");
		String order_str = request.getParameter("order_str");
		String group = request.getParameter("group");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return ;
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
			sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			edate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
		}
		paramMap.put("sdate", sdate);
		paramMap.put("edate", edate);
		paramMap.put("appid", appid);
		paramMap.put("cha_id", cha_id);
		paramMap.put("prjid", prjid);
		paramMap.put("user_group", user_group);
		paramMap.put("order_str", order_str);
		/** 处理应用组数据 */
		paramMap.put("appGroup", appGroup);
		paramMap.put("group", group);
		BlankUtils.setAppGroup(paramMap);

		paramMap.put("tableName", "dnwx_bi.ads_dn_extend_group_adtype_daily");

		List<Map<String, Object>> contentList = dnwxBiMapper.selectAdtypeTotalGroup(paramMap);

		/** 增加环比数据的处理 */
		if(group.contains("tdate")){
			String before = DateTime.parse(sdate).minusDays(1).toString("yyyy-MM-dd");
			paramMap.put("sdate", before);
			List<Map<String, Object>> twoList = dnwxBiMapper.selectAdtypeTotalGroup(paramMap);
			Map<String, Map<String, Object>> twoMap = new HashMap<>();
			twoList.forEach(act2 -> {
				twoMap.put(act2.get("mapkey")+"", act2);
			});

			contentList.stream().forEach(act -> {
				//默认环比增长为0.00%
				act.put("addrate_match", "0.00%");
				act.put("dau_arpu_match", "0.00%");

				String cur_date = (act.get("tdate")+"");
				String yesterday = DateTime.parse(cur_date).minusDays(1).toString("yyyy-MM-dd");
				String key = (act.get("mapkey")+"").replace(cur_date, yesterday);

				// 设置各类型的环比数值
				Map<String, Object> act2 = twoMap.get(key);
				if(act2 != null){
					String[] adtypes = {"addrate","dau_arpu"};
					//						String[] adtypes = {"addrate","dau_arpu","arpu_splash","arpu_plaque","arpu_banner","arpu_video","arpu_msg"};
					for (String adtype : adtypes) {
						if(act.get(adtype) != null && act2.get(adtype) != null && BlankUtils.isNumeric(act2.get(adtype)+"")){
							act.put(adtype+"_match", strToTwoNumber(act.get(adtype).toString(),act2.get(adtype).toString()));
						}
					}
				}
				act.put("addrate", act.get("addrate") + "%");
			});
		}

		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			act.put("tdate", act.get("tdate")+"");
			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name"));
		});


		Map<String,String> head = new LinkedHashMap<>();
		try {
			String[] split = param.getValue().split(";");
			for (int i = 0;i<split.length;i++) {
				String[] s = split[i].split(",");
				head.put(s[0],s[1]);
			}
		}catch (Exception e) {
			Asserts.fail("自定义列导出异常");
		}
		String fileName = param.getReport()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		ExportExcelUtil.exportXLSX(response,contentList,head,fileName);
	}


	/**
     * 同步展示GAP统计数据
     * @param tdate
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/bigdata/syncShowGapTotal", method={RequestMethod.POST})
    public String syncShowGapTotal(String tdate, HttpServletRequest request, HttpServletResponse response) {

		try{
			if(BlankUtils.checkBlank(tdate))
				tdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");

			boolean resp = bigdataService.syncShowGapTotal(tdate);
			if(resp)
				return "{\"ret\":1,\"msg\":\"同步成功！\"}";
			else
				return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
		}catch(Exception e){
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
		}
    }
    /**
     * 同步变现收入预估数据.v2
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/bigdata/syncIncomeDataTwo", method={RequestMethod.GET,RequestMethod.POST})
    public String syncIncomeDataTwo(HttpServletRequest request, HttpServletResponse response) {

		try{

			String day = request.getParameter("tdate");
			if(BlankUtils.checkBlank(day))
				day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			String appid = request.getParameter("appid");

			/** 通过异步执行，不接收返回值 */
			if(!BlankUtils.checkBlank(appid)){
				bigdataService.syncIncomeDataTwo(day, appid);
			}else{
				bigdataService.syncIncomeDataTwo(day);
			}

			if(true)
				return "{\"ret\":1,\"msg\":\"同步执行成功，请稍后查看数据！\"}";
			else
				return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
		}catch(Exception e){
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
		}
    }
    /**
     * 同步收入校准数据
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/bigdata/syncExtendIncomeRevise", method={RequestMethod.POST})
    public String syncExtendIncomeRevise(HttpServletRequest request, HttpServletResponse response) {

		try{
			String day = request.getParameter("tdate");
			if(BlankUtils.checkBlank(day))
				day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			String appid = request.getParameter("appid");

			boolean resp = true;
			if(!BlankUtils.checkBlank(appid)){
				resp = bigdataService.syncExtendIncomeRevise(day, appid);
			}else{
				resp = bigdataService.syncExtendIncomeRevise(day);
			}

			if(resp)
				return "{\"ret\":1,\"msg\":\"同步成功！\"}";
			else
				return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
		}catch(Exception e){
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
		}
    }
    /**
     * 同步汇总数据校准
     * @param tdate
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/bigdata/syncExtendAdtypeRevise", method={RequestMethod.POST})
    public String syncExtendAdtypeRevise(String tdate, String ctype, HttpServletRequest request, HttpServletResponse response) {

		try{
			if(BlankUtils.checkBlank(tdate))
				tdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");

			boolean resp = true;
			if("v2".equals(ctype)){
				resp = bigdataService.syncExtendAdtypeReviseTwo(tdate);
			}else{
				resp = bigdataService.syncExtendAdtypeRevise(tdate);
			}

			if(resp)
				return "{\"ret\":1,\"msg\":\"同步成功！\"}";
			else
				return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
		}catch(Exception e){
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
		}
    }
    /**
     * 同步项目ID收入预估
     * @param tdate
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/bigdata/syncExtendPrjidIncome", method={RequestMethod.POST})
    public String syncExtendPrjidIncome(String tdate, HttpServletRequest request, HttpServletResponse response) {

		try{
			if(BlankUtils.checkBlank(tdate))
				tdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");

			boolean resp = bigdataService.syncExtendPrjidIncome(tdate);
			if(resp)
				return "{\"ret\":1,\"msg\":\"同步成功！\"}";
			else
				return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
		}catch(Exception e){
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
		}
    }
    
    /**
     * 同步变现-二级子渠道展示收入 
     * @param tdate
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/bigdata/syncSubchaTotalRevise", method={RequestMethod.POST})
    public String syncSubchaTotalRevise(String tdate, HttpServletRequest request, HttpServletResponse response) {

		try{
			if(BlankUtils.checkBlank(tdate))
				tdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");

			boolean resp = bigdataService.syncExtendSubchaTotal(tdate);
			if(resp)
				return "{\"ret\":1,\"msg\":\"同步成功！\"}";
			else
				return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
		}catch(Exception e){
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
		}
    }
    
    /**
     * 同步聚合综合查询
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/bigdata/syncGroupCom", method={RequestMethod.POST})
    public String syncGroupCom(HttpServletRequest request, HttpServletResponse response) {

		try{

			String day = request.getParameter("tdate");
			if(BlankUtils.checkBlank(day))
				day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");

			boolean resp = bigdataService.syncGroupCom(day);
			if(resp)
				return "{\"ret\":1,\"msg\":\"同步成功！\"}";
			else
				return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
		}catch(Exception e){
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
		}
    }
    
    /**
     * 同步汇总数据-用户群
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/bigdata/syncAdtypeTotalGroup", method={RequestMethod.POST})
    public String syncAdtypeTotalGroup(HttpServletRequest request, HttpServletResponse response) {

		try{

			String day = request.getParameter("tdate");
			if(BlankUtils.checkBlank(day))
				day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");

			boolean resp = bigdataService.syncExtendAdtypeGroup(day);
			if(resp)
				return "{\"ret\":1,\"msg\":\"同步成功！\"}";
			else
				return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
		}catch(Exception e){
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
		}
    }
    
}
