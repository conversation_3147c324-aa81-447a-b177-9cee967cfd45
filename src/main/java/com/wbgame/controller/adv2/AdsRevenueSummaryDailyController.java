package com.wbgame.controller.adv2;

import com.wbgame.aop.ApiIgp;
import com.wbgame.common.Asserts;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.adv2.AdsRevenueSummaryDailyDTO;
import com.wbgame.pojo.adv2.AdsRevenueSummaryDailyVO;
import com.wbgame.service.adv2.IAdsRevenueSummaryDailyService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2022/9/6
 * @class: AdsRevenueSummaryDailyController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/revenueSummary")
@Api(tags = "变现大盘数据表")
public class AdsRevenueSummaryDailyController {

    private IAdsRevenueSummaryDailyService adsRevenueSummaryDailyService;

    @Autowired
    public void setAdsRevenueSummaryDailyService(IAdsRevenueSummaryDailyService adsRevenueSummaryDailyService) {
        this.adsRevenueSummaryDailyService = adsRevenueSummaryDailyService;
    }


    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping(value = "/selectRevenueSummary")
    public Result<PageResult<AdsRevenueSummaryDailyVO>> selectRevenueSummary(@ApiIgp("version") @Validated(QueryGroup.class) AdsRevenueSummaryDailyDTO param) {

        return adsRevenueSummaryDailyService.selectRevenueSummary(param);

    }

    @ApiOperation(value = "坐标图-查询", notes = "坐标图-查询")
    @PostMapping(value = "/selectCoordinateMapRevenueSummary")
    public Result<List<AdsRevenueSummaryDailyVO>> selectCoordinateMapRevenueSummary(@ApiIgp("version") @Validated(QueryGroup.class) AdsRevenueSummaryDailyDTO param) {

        return adsRevenueSummaryDailyService.selectCoordinateMapRevenueSummary(param);

    }

    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping(value = "/export")
    public void export(HttpServletResponse response,
                       @Validated(QueryGroup.class) AdsRevenueSummaryDailyDTO param) {

        List<AdsRevenueSummaryDailyVO> list = adsRevenueSummaryDailyService.export(param);

        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = param.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = param.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX2(response, list, head, fileName);

    }

    @ApiOperation(value = "获取平台", notes = "获取平台")
    @PostMapping(value = "/getAgent")
    public Result<List<String>> getAgent() {

        return adsRevenueSummaryDailyService.getAgent();

    }
}
