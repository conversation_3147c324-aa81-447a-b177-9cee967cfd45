package com.wbgame.controller.adv2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.pojo.adv2.ApiPullConfigVo;
import com.wbgame.pojo.adv2.DnExtendGroupMonitorVo;
import com.wbgame.pojo.adv2.ExtendAdinfoVo;
import com.wbgame.service.AdService;
import com.wbgame.service.adv2.RealizationService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 广告产品展示屏蔽配置
 */
@CrossOrigin
@RestController
@RequestMapping("/adv2/appShowShield")
public class AppShowShieldController {

    @Autowired
    private RealizationService realizationService;
    @Autowired
    private AdService adService;
    @Autowired
    private YyhzMapper yyhzMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @RequestMapping("/list")
    public String list(HttpServletRequest request, HttpServletResponse response){

        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

        // 将下面的字段接收改正
        String[] args = {"appid", "cha_id", "prjid", "shield_name", "user_group", "prjid_group_id", "filter_id", "appid_tag", "appid_tag_rev"};
        Map<String, String> param = BlankUtils.getParameter(request, args);
        param.put("appid",getWhereParam(param.get("appid")));
        param.put("cha_id",getWhereParam(param.get("cha_id")));

        String prjid_group_id = param.get("prjid_group_id");

        /* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
        if(!BlankUtils.checkBlank(prjid_group_id)) {
            Map<String,String> params = new HashMap<>();
            params.put("ctype", "2");
            params.put("id", prjid_group_id);
            List<Map<String, Object>> groupList = yyhzMapper.selectPrjidGroupConfigConfig(params);

            if(groupList != null && groupList.size() > 0) {

                String collect = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("prjid","").toString()))
                        .map(act -> act.getOrDefault("prjid","").toString())
                        .collect(Collectors.joining(","));
                String collect2 = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("chastr","").toString()))
                        .flatMap(act -> Arrays.stream((act.getOrDefault("chastr","").toString()).split(",")))
                        .map(str -> "'"+str+"'")
                        .collect(Collectors.joining(","));

                String match_str = "";
                if(!BlankUtils.checkBlank(collect)){
                    match_str = String.format(" prjid in (%s) ", collect);
                }
                if(!BlankUtils.checkBlank(collect2)){
                    if (!match_str.isEmpty()) {
                        match_str += " or ";
                    }
                    match_str += String.format(" concat(appid,'#',cha_id) in (%s) ", collect2);
                }
                param.put("match_str", "("+match_str+")");
            }
        }


        JSONObject result = new JSONObject();
        try {
            PageHelper.startPage(param); // 进行分页
            List<ExtendAdinfoVo> list = realizationService.selectNewExtendAdinfoVo(param);
            long size = ((Page) list).getTotal();
            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    public String getWhereParam(String param) {
        if (!BlankUtils.checkBlank(param)) {
            String[] split = param.split(",");
            String in = "";
            for (String s : split) {
                in = in + "'" + s + "'" + ",";
            }
            return "("+in.substring(0,in.length()-1) +")";
        }else{
            return null;
        }
    }

    /**
     * 广告策略设置-新增修改删除操作
     * @param record
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/handle", method={RequestMethod.GET,RequestMethod.POST})
    public String extendAdinfoVoHandle(ExtendAdinfoVo record ,HttpServletRequest request, HttpServletResponse response) {
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        // 操作人登录名获取
        String cuser = "";
        if(token.startsWith("wbtoken")){
            CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
            cuser = curr.getLogin_name();
        }else if(token.startsWith("waibaotoken")){
            WaibaoUserVo curr = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            cuser = curr.getUser_id();
        }
        record.setCuser(cuser);
        record.setEuser(cuser);
        int result = 0;
        try {
            if ("add".equals(request.getParameter("handle")) || "edit".equals(request.getParameter("handle"))) {
                //新增，编辑 同纬度数据校验
                //二次确认新增标记
                String confirm = request.getParameter("second_confirm");
                if (BlankUtils.checkBlank(confirm) || !"ok".equals(confirm)) {
                    //第一次需要校验同纬度是否存在相同的配置，更新操作需要排除本身数据
                    Long count = realizationService.countNewExtendAdinfoConfig(record);
                    if(count > 0){
                        StringBuilder message = new StringBuilder();
                        if (!ObjectUtils.isEmpty(record.getAppid())) {
                            message.append("应用：").append(record.getAppid()).append(",");
                        }
                        if (!BlankUtils.checkBlank(record.getCha_id())) {
                            message.append("子渠道：").append(record.getCha_id()).append(",");
                        }
                        if (!ObjectUtils.isEmpty(record.getPrjid())) {
                            message.append("项目id：").append(record.getPrjid()).append(",");
                        }
                        if (!BlankUtils.checkBlank(record.getUser_group())) {
                            message.append("用户群：").append(record.getUser_group()).append(",");
                        }
                        JSONObject object = new JSONObject();
                        object.put("ret",2);
                        object.put("msg",message.append("已存在相同维度数据,是否继续提交!"));
                        return object.toJSONString();
                    }
                }
            }

            if ("add".equals(request.getParameter("handle"))) {
                /*Long count = realizationService.countNewExtendAdinfoVo(record);
                if(count > 0){
                    return "{\"ret\":2,\"msg\":\"已存在相同应用渠道或项目ID配置了该数据!\"}";
                }*/

                // 单项目ID配置时，自动关联对应的产品和子渠道 -林小敏.20240410
                if(record.getPrjid() != null && null == record.getAppid() && BlankUtils.checkBlank(record.getCha_id())) {
                    Map<String, Map<String, Object>> prjMap = adService.selectProjectidChannelMap();
                    Map<String, Object> prj = prjMap.get(record.getPrjid().toString());
                    if (null != prj) {
                        record.setAppid(Integer.valueOf(prj.get("appid").toString()));
                        record.setCha_id(prj.get("cha_id").toString());
                    }
                }

                result = realizationService.insertNewExtendAdinfoVo(record);
            } else if ("edit".equals(request.getParameter("handle"))) {

                result = realizationService.updateNewExtendAdinfoVo(record);
            } else if ("del".equals(request.getParameter("handle"))) {
                result = realizationService.deleteNewExtendAdinfoVo(record);
            }


            if(result > 0) {
                /* 公共刷新缓存 */
                try {
                    Map<String, String> info = new HashMap<String, String>(){{
                        put("mark", "newWjy");
                        put("mapid", "302");
                        put("appid", record.getAppid().toString());
                        put("title", "广告展示屏蔽设置(新)");
                        put("cuser", record.getCuser());
                    }};
                    adService.commonRecacheService(info);
                }catch (Exception e){
                    e.printStackTrace();
                }

                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            }else {
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";
            }

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }


    @RequestMapping("/export")
    public void export( HttpServletRequest request, HttpServletResponse response){
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            Asserts.fail(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        // 将下面的字段接收改正
        String[] args = {"appid", "cha_id", "prjid", "shield_name", "user_group", "prjid_group_id", "filter_id", "appid_tag", "appid_tag_rev"};
        Map<String, String> param = BlankUtils.getParameter(request, args);
        param.put("appid",getWhereParam(param.get("appid")));
        param.put("cha_id",getWhereParam(param.get("cha_id")));

        String prjid_group_id = param.get("prjid_group_id");

        /* prjid_group_id需要解析为appid+cha_id然后进行筛选 */
        if(!BlankUtils.checkBlank(prjid_group_id)) {
            Map<String,String> params = new HashMap<>();
            params.put("ctype", "2");
            params.put("id", prjid_group_id);
            List<Map<String, Object>> groupList = yyhzMapper.selectPrjidGroupConfigConfig(params);

            if(groupList != null && groupList.size() > 0) {

                String collect = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("prjid","").toString()))
                        .map(act -> act.getOrDefault("prjid","").toString())
                        .collect(Collectors.joining(","));
                String collect2 = groupList.stream().filter(act -> !BlankUtils.checkBlank(act.getOrDefault("chastr","").toString()))
                        .flatMap(act -> Arrays.stream((act.getOrDefault("chastr","").toString()).split(",")))
                        .map(str -> "'"+str+"'")
                        .collect(Collectors.joining(","));

                String match_str = "";
                if(!BlankUtils.checkBlank(collect)){
                    match_str = String.format(" prjid in (%s) ", collect);
                }
                if(!BlankUtils.checkBlank(collect2)){
                    if (!match_str.isEmpty()) {
                        match_str += " or ";
                    }
                    match_str += String.format(" concat(appid,'#',cha_id) in (%s) ", collect2);
                }
                param.put("match_str", "("+match_str+")");
            }
        }
        List<ExtendAdinfoVo> list = realizationService.selectNewExtendAdinfoVo(param);
        // 赋值应用名称
        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
        list.forEach(act -> {
            Map<String, Object> app = appMap.get(act.getAppid()+"");
            if(app != null){
                act.setAppname(app.get("app_name").toString());
            }

            act.setA_banner_name((1 == act.getA_banner()?"使用":"不使用"));
        });
        // list转换为contentList：List<Map<String, Object>>
        List<Map<String, Object>> contentList = new ArrayList<>();
        for (ExtendAdinfoVo adv : list) {

            Map<String, Object> obj = (Map<String, Object>)JSONObject.parseObject(JSONObject.toJSONString(adv), Map.class);
            contentList.add(obj);
        }



        String value = request.getParameter("value");
        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        ExportExcelUtil.exportXLSX(response,contentList,head,"广告展示屏蔽设置(新)_" + DateTime.now().toString("yyyyMMddHHmmss")+ ".xlsx");
    }

}
