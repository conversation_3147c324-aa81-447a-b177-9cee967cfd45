package com.wbgame.controller.adv2.tobid;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.adv2.*;
import com.wbgame.service.adv2.tobid.ToBidGroupService;
import com.wbgame.utils.BlankUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import jxl.read.biff.BiffException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.net.URISyntaxException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/6
 * @description 用于指定聚合广告位查询、编辑、新建流量分组。
 **/
@RequestMapping("/adv2")
@RestController
@CrossOrigin
@Slf4j
@Api("tobid 广告系列操作接口")
public class ToBidGroupController {

    @Resource
    private ToBidGroupService toBidGroupService;

    // 账户 ： ad_network_id的map
    private Map<String, Integer> accountAdNetworkIdMap = new HashMap<String, Integer>() {{
        put("Sigmob-Sigmob", 2037);
        put("穿山甲-飞鸟", 2126);
        put("腾讯广告-统掌", 2127);
        put("快手-动能", 2128);
        put("AdMob-dongneng", 2323);
    }};

    private volatile JSONArray tempApp;

    @ApiOperation("app列表")
    @ControllerLoggingEnhancer
    @PostMapping("/toBidApp/list")
    public String toBidAppList() throws IOException, URISyntaxException, NoSuchAlgorithmException {
        if (tempApp == null) {
            synchronized (this) {
                if (tempApp == null) {
                    tempApp = toBidGroupService.unionMediaAppList();
                }
            }
        }
        return ReturnJson.success(tempApp);
    }

    private Map<String, Object> adPosMap;
    @ApiOperation("广告位列表")
    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdPos/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "app_id", value = "app_id", dataType = "String")
    })
    public String toBidAdPosList(String app_id) throws IOException, URISyntaxException, NoSuchAlgorithmException {
        if (adPosMap == null) {
            adPosMap = new ConcurrentHashMap<>();
        }
        if (adPosMap.get(app_id) == null) {
            JSONArray array = toBidGroupService.unionMediaAdPosList(app_id);
            Map<String, String> map = toBidGroupService.selectAdsidByPubcode(array).stream()
                    .collect(Collectors.toMap(ExtendAdsidVo::getSdk_code, ExtendAdsidVo::getAdsid, (x, y) -> x));
            array.stream().map(o-> (JSONObject) o).forEach(o -> {
                String code = o.getString("pub_code");
                o.put("adsid", map.get(code));
            });
            adPosMap.put(app_id, array);
        }
        return ReturnJson.success(adPosMap.get(app_id));
    }
    @ApiOperation("添加广告位")
    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdPos/create")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adPosName", value = "广告位名称", dataType = "String"),
            @ApiImplicitParam(name = "ad_type", value = " 广告位类型 激励：1，开屏：2，插屏：4，原生：5，横幅：7", dataType = "int"),
            @ApiImplicitParam(name = "app_id", value = "", dataType = "String")
    })
    public Object toBidAdPosCreate(String adPosName, String app_id, Integer ad_type) throws IOException, URISyntaxException, NoSuchAlgorithmException {
        if (adPosMap == null) {
            adPosMap = new HashMap<>();
        }
        JSONObject jsonObject = toBidGroupService.unionMediaAdPosCreate(adPosName, app_id, ad_type);
        if (jsonObject.getIntValue("ret") == 1) {
            JSONArray array = toBidGroupService.unionMediaAdPosList(app_id);
            adPosMap.put(app_id, array);
        }
        return jsonObject;
    }

    private Map<String, Object> groupMap;
    @ControllerLoggingEnhancer
    @PostMapping("/toBidGroup/list")
    @ApiOperation("广告组列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pub_code", value = "广告位id", dataType = "String")
    })
    public String toBidGroupList(String pub_code) {
        try {
            if (groupMap == null) {
                groupMap = new HashMap<>();
            }
            if (groupMap.get(pub_code) == null) {
                JSONArray array = toBidGroupService.unionMediaGroupList(pub_code);
                groupMap.put(pub_code, array);
            }
        } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
            log.error("tobid error, ", e);
            return ReturnJson.toErrorJson(e.getMessage());
        }
        return ReturnJson.success(groupMap.get(pub_code));
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidGroup/create")
    @ApiOperation("添加广告组")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupName", value = "模板组名", dataType = "String"),
            @ApiImplicitParam(name = "pub_code", value = "广告位", dataType = "JSONArray")
    })
    public String toBidGroupCreate(String groupName, String pub_code) {
        if (groupMap == null) {
            groupMap = new HashMap<>();
        }
        boolean b = toBidGroupService.unionMediaGroupCreate(groupName, pub_code);
        if (b) {
            JSONArray array = null;
            try {
                array = toBidGroupService.unionMediaGroupList(pub_code);
            } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
                log.error("创建广告位失败: ", e);
                return ReturnJson.toErrorJson(e.getMessage());
            }
            adPosMap.put(pub_code, array);
            return ReturnJson.success("创建成功");
        }
        return ReturnJson.toErrorJson("失败");
    }

//    @ControllerLoggingEnhancer
//    @PostMapping("/toBidAdSource/create")
//    @ApiOperation("添加广告源")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "placementName", value = "聚合广告位名", dataType = "String"),
//            @ApiImplicitParam(name = "pub_code", value = "聚合广告位id", dataType = "String"),
//            @ApiImplicitParam(name = "account", value = "账户， 在定义中的map中", dataType = "String"),
//            @ApiImplicitParam(name = "appid", value = "tobid中的appid", dataType = "String"),
//            @ApiImplicitParam(name = "header_bidding_switch", value = "是否是应用内竞价广告源。 0:不是，1:是", dataType = "int"),
//            @ApiImplicitParam(name = "bidding_method", value = "应用内竞价模式枚举值: 0:服务端竞价 1:客户端竞价， 当广告网络同时支持两种竞价方式且 header_bidding_switch=1 时必填", dataType = "int"),
//            @ApiImplicitParam(name = "price", value = "瀑布流中的排序价格，单位分。 应用内竞价广告源无须填写", dataType = "int"),
//            @ApiImplicitParam(name = "vo", value = "创建广告位所需的vo", dataType = "Map")
//    })
//    public Object toBidAdSourceCreate(HttpServletRequest request) {
//        JSONObject jsonRequest = getJsonRequest(request);
//
//        return toBidGroupService.toBidAdSourceCreate(jsonRequest);
//    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSource/csj/create")
    @ApiOperation("添加广告源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "placementName", value = "聚合广告位名", dataType = "String"),
            @ApiImplicitParam(name = "pub_code", value = "聚合广告位id", dataType = "String"),
            @ApiImplicitParam(name = "account", value = "账户， 在定义中的map中", dataType = "String"),
            @ApiImplicitParam(name = "to_appid", value = "tobid中的appid", dataType = "String"),
            @ApiImplicitParam(name = "header_bidding_switch", value = "是否是应用内竞价广告源。 0:不是，1:是", dataType = "int"),
            @ApiImplicitParam(name = "bidding_method", value = "应用内竞价模式枚举值: 0:服务端竞价 1:客户端竞价， 当广告网络同时支持两种竞价方式且 header_bidding_switch=1 时必填", dataType = "int"),
            @ApiImplicitParam(name = "price", value = "瀑布流中的排序价格，单位分。 应用内竞价广告源无须填写", dataType = "int"),
            @ApiImplicitParam(name = "vo", value = "创建广告位所需的vo", dataType = "Map")
    })
    public Object toBidAdSourceCsjCreate(String placementName, String pub_code, String account, String to_appid,
                                         Integer header_bidding_switch, Integer bidding_method,
                                         Integer price, CSJAdcodeVo vo) {

        return toBidGroupService.toBidAdSourceCreate(placementName, pub_code, account, to_appid, header_bidding_switch,
                bidding_method, price, vo);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSource/ks/create")
    @ApiOperation("添加广告源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "placementName", value = "聚合广告位名", dataType = "String"),
            @ApiImplicitParam(name = "pub_code", value = "聚合广告位id", dataType = "String"),
            @ApiImplicitParam(name = "account", value = "账户， 在定义中的map中", dataType = "String"),
            @ApiImplicitParam(name = "to_appid", value = "tobid中的appid", dataType = "String"),
            @ApiImplicitParam(name = "header_bidding_switch", value = "是否是应用内竞价广告源。 0:不是，1:是", dataType = "int"),
            @ApiImplicitParam(name = "bidding_method", value = "应用内竞价模式枚举值: 0:服务端竞价 1:客户端竞价， 当广告网络同时支持两种竞价方式且 header_bidding_switch=1 时必填", dataType = "int"),
            @ApiImplicitParam(name = "price", value = "瀑布流中的排序价格，单位分。 应用内竞价广告源无须填写", dataType = "int"),
            @ApiImplicitParam(name = "vo", value = "创建广告位所需的vo", dataType = "Map")
    })
    public Object toBidAdSourceKsCreate(String placementName, String pub_code, String account, String to_appid,
                                        Integer header_bidding_switch, Integer bidding_method,
                                        Integer price, KSAdcodeVo vo) {
        return toBidGroupService.toBidAdSourceCreate(placementName, pub_code, account, to_appid, header_bidding_switch,
                bidding_method, price, vo);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSource/ylh/create")
    @ApiOperation("添加广告源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "placementName", value = "聚合广告位名", dataType = "String"),
            @ApiImplicitParam(name = "pub_code", value = "聚合广告位id", dataType = "String"),
            @ApiImplicitParam(name = "account", value = "账户， 在定义中的map中", dataType = "String"),
            @ApiImplicitParam(name = "to_appid", value = "tobid中的appid", dataType = "String"),
            @ApiImplicitParam(name = "header_bidding_switch", value = "是否是应用内竞价广告源。 0:不是，1:是", dataType = "int"),
            @ApiImplicitParam(name = "bidding_method", value = "应用内竞价模式枚举值: 0:服务端竞价 1:客户端竞价， 当广告网络同时支持两种竞价方式且 header_bidding_switch=1 时必填", dataType = "int"),
            @ApiImplicitParam(name = "price", value = "瀑布流中的排序价格，单位分。 应用内竞价广告源无须填写", dataType = "int"),
            @ApiImplicitParam(name = "vo", value = "创建广告位所需的vo", dataType = "Map")
    })
    public Object toBidAdSourceYlhCreate(String placementName, String pub_code, String account, String to_appid,
                                         Integer header_bidding_switch, Integer bidding_method,
                                         Integer price, YLHAdcodeVo vo) {
        return toBidGroupService.toBidAdSourceCreate(placementName, pub_code, account, to_appid, header_bidding_switch,
                bidding_method, price, vo);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSource/sigmob/create")
    @ApiOperation("添加广告源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "placementName", value = "聚合广告位名", dataType = "String"),
            @ApiImplicitParam(name = "pub_code", value = "聚合广告位id", dataType = "String"),
            @ApiImplicitParam(name = "account", value = "账户， 在定义中的map中", dataType = "String"),
            @ApiImplicitParam(name = "to_appid", value = "tobid中的appid", dataType = "String"),
            @ApiImplicitParam(name = "header_bidding_switch", value = "是否是应用内竞价广告源。 0:不是，1:是", dataType = "int"),
            @ApiImplicitParam(name = "bidding_method", value = "应用内竞价模式枚举值: 0:服务端竞价 1:客户端竞价， 当广告网络同时支持两种竞价方式且 header_bidding_switch=1 时必填", dataType = "int"),
            @ApiImplicitParam(name = "price", value = "瀑布流中的排序价格，单位分。 应用内竞价广告源无须填写", dataType = "int"),
            @ApiImplicitParam(name = "vo", value = "创建广告位所需的vo", dataType = "Map")
    })
    public Object toBidAdSourceSigmobCreate(String placementName, String pub_code, String account, String to_appid,
                                            Integer header_bidding_switch, Integer bidding_method,
                                            Integer price, SigmobAdVo vo) {
        return toBidGroupService.toBidAdSourceCreate(placementName, pub_code, account, to_appid, header_bidding_switch,
                bidding_method, price, vo);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSource/list")
    @ApiOperation("广告源列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pub_code", value = "聚合广告位id", dataType = "String")
    })
    public Object toBidAdSourceList(String pub_code) {

        return toBidGroupService.toBidAdSourceList(pub_code);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSource/import")
    @ApiOperation("广告源批量上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "聚合广告位id", dataType = "MultipartFile")
    })
    public Object toBidAdSourceImport(MultipartFile file) throws IOException, BiffException {
        return toBidGroupService.toBidAdSourceImport(file);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidUnionAdPos/list")
    @ApiOperation("聚合广告位广告源列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "app_id", value = "tobid appid", dataType = "String")
    })
    public Object toBidUnionAdPosList(String app_id) throws IOException, URISyntaxException, NoSuchAlgorithmException {
        return toBidGroupService.toBidUnionAdPosList(app_id);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdPos/insert")
    @ApiOperation("聚合广告位加入广告源和广告配置表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pub_code", value = "聚合广告位id", dataType = "String")
    })
    public Object toBidAdPosInsert(String pub_code) {
        return toBidGroupService.toBidAdSourceList(pub_code);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSourceStatus/update")
    @ApiOperation("广告源在分组中的开关切换")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ad_source_id", value = "广告源id", dataType = "int"),
            @ApiImplicitParam(name = "group_id", value = "聚合广告组id", dataType = "int"),
            @ApiImplicitParam(name = "enabled", value = "1启动，0不启用", dataType = "int"),
    })
    public Object toBidAdSourceStatusUpdate(int ad_source_id, int group_id,int enabled) {
        return toBidGroupService.toBidAdSourceStatusUpdate(ad_source_id, group_id, enabled);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSourceStatus/list")
    @ApiOperation("广告源在分组中的开关列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "group_id", value = "聚合广告组id", dataType = "int")
    })
    public Object toBidAdSourceStatusList(int group_id) {
        return toBidGroupService.toBidAdSourceStatusList(group_id);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidCache/update")
    @ApiOperation("后端刷新tobid的cache")
    public Object toBidCacheUpdate() throws IOException, URISyntaxException, NoSuchAlgorithmException {
        // app缓存
        tempApp = null;
        toBidAppList();

        if (!CollectionUtils.isEmpty(adPosMap)) {
            adPosMap.clear();
        }
        tempApp.stream().map(o -> (JSONObject) o).forEach(map ->{
            String id = map.getString("id");
            try {
                toBidAdPosList(id);
            } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            }
        });

        return ReturnJson.success("缓存刷新成功");
    }

    private JSONObject getJsonRequest(HttpServletRequest request) {
        JSONObject result = null;
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader();) {
            char[] buff = new char[1024 * 10];
            int len;
            while ((len = reader.read(buff)) != -1) {
                sb.append(buff, 0, len);
            }
            result = JSON.parseObject(sb.toString());
        } catch (IOException e) {
            log.error("", e);
        }
        return result;
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSource/adsid/import")
    @ApiOperation("广告源批量上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "MultipartFile")
    })
    public Object toBidAdSourceAdsidImport(MultipartFile file) throws IOException, BiffException {
        return toBidGroupService.toBidAdSourceAdsidImport(file);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSource/unionPos/import")
    @ApiOperation("广告源批量上传导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "MultipartFile")
    })
    public Object toBidAdSourceUnionPosImport(MultipartFile file) throws IOException, BiffException {
        return toBidGroupService.toBidAdSourceUnionPosImport(file);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSource/unionPosCreate/import")
    @ApiOperation("广告源批量上传创建")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "MultipartFile")
    })
    public Object toBidAdSourceUnionPosCreateImport(MultipartFile file) throws IOException, BiffException {
        return toBidGroupService.toBidAdSourceUnionPosCreateImport(file);
    }

    @ControllerLoggingEnhancer
    @PostMapping("/toBidAdSource/batchSwitch/update")
    @ApiOperation("广告源批量开关")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "group_id", value = "流量分组ID，即查询流量分组中返回的id", dataType = "Integer"),
            @ApiImplicitParam(name = "tobid_ids", value = "tobid id, 逗号分割拼接", dataType = "String"),
            @ApiImplicitParam(name = "enabled", value = "是否在当前流量分组内启用。 0: 不启用 1: 启用", dataType = "Integer"),
    })
    public Object toBidAdSourceUnionPosCreateImport(Integer group_id, String tobid_ids, Integer enabled) throws IOException, BiffException {
        String msg = Arrays.stream(tobid_ids.split(",")).map(BlankUtils::getInt)
                .map(ad_source_id -> toBidGroupService.toBidAdSourceStatusUpdate(ad_source_id, group_id, enabled))
                .filter(jsonObject -> jsonObject.getIntValue("ret") == 0)
                .map(jsonObject -> jsonObject.getString("msg"))
                .reduce((x,y) -> x + "<br>" + y).orElseGet(() -> "");

        if (BlankUtils.isNotBlank(msg)) {
            return ReturnJson.toErrorJson(msg);
        }
        return ReturnJson.success("批量修改成功");
    }
}
