package com.wbgame.controller.adv2;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.PlatformDataMapper;
import com.wbgame.pojo.adv2.platform.PlatformMsgInfoVo;
import com.wbgame.service.AdService;
import com.wbgame.service.PlatformDataService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @description: vivo小游戏数据报表
 * @author: caow
 * @date: 2023.12.05
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/adv2/vivoQuickGame")
public class VivoQuickGameController {

	@Autowired
	private PlatformDataService platformDataService;
    @Autowired
    private AdService adService;
	@Resource
	private PlatformDataMapper platformDataMapper;


    /**
     * 查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(Integer start,Integer limit,HttpServletRequest request,HttpServletResponse response) throws IOException {

        String[] args = {"sdate","edate","account","tappname","tpackage","tappid","order_str", "appid", "group"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        JSONObject result = new JSONObject();
        try {
			PageHelper.startPage(start,limit);
			List<Map<String, Object>> list = platformDataService.selectVivoQuickGameList(paramMap);
			long size = ((Page) list).getTotal();
			Map<String, Object> map = platformDataMapper.selectVivoQuickGameSum(paramMap);

			result.put("ret", 1);
            result.put("data", list);
			result.put("total", map);
            result.put("totalCount", size);
            
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }
    
    
    /**
	 * 导出
	 * @param request
	 * @param response
	 * @return
	 */ 
	@RequestMapping(value = "/export", method={RequestMethod.GET, RequestMethod.POST})
	public void export(HttpServletRequest request,HttpServletResponse response) {

		String[] args = {"sdate","edate","account","tappname","tpackage","tappid","order_str", "appid", "group"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);

		List<Map<String, Object>> contentList = platformDataService.selectVivoQuickGameList(paramMap);

    	
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }

		String fileName = "vivo小游戏数据报表_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);
	}

	/**
	 * 同步
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/sync", method={RequestMethod.POST})
	public String sync(HttpServletRequest request, HttpServletResponse response) {

		String sdate = request.getParameter("sdate");
		String edate = request.getParameter("edate");
		String account = request.getParameter("account");

		if(BlankUtils.sqlValidate(sdate) || BlankUtils.checkBlank(edate))
			return "{\"ret\":0,\"msg\":\"同步日期不能为空！\"}";

		try{
			List<String> dateList = DateUtil.getDays(sdate,edate);
			if (dateList.size() > 31){
				return ReturnJson.toErrorJson("一次最多只能拉取31天数据!");
			}

			/* 同步vivo小游戏数据 */
			platformDataService.syncVivoQuickGameList(account,sdate,edate);

			return "{\"ret\":1,\"msg\":\"同步任务执行中，请稍后查询最新数据！\"}";
		}catch(Exception e){
			e.printStackTrace();
			return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
		}
	}


}
