package com.wbgame.controller.adv2;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.adv2.YLHMediumVo;
import com.wbgame.service.AdService;
import com.wbgame.service.adv2.AdCodeConfigService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.Encript;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.HttpRequest;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HTTP;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname YLHMediumController
 * @Description TODO
 * @Date 2022/3/3 17:35
 */
@RequestMapping("/adv2/ylhMedium")
@RestController
@CrossOrigin
public class YLHMediumController {

    private static Logger logger = LoggerFactory.getLogger(YLHMediumController.class);

    private static final String YLH_MEDIUM_UPLOAD_URL ="https://api.adnet.qq.com/open/v1.1/image/upload";

    private static final String YLH_MEDIUM_ADD_URL = "https://api.adnet.qq.com/open/v1.1/medium/add";

    private static final String YLH_MEDIUM_UPDATE_URL = "https://api.adnet.qq.com/open/v1.1/medium/update";

    private static final String YLH_MEDIUM_LIST_URL = "https://api.adnet.qq.com/open/v1.1/medium/list";

    private static final Map<String, String> SECRET_MAP = new HashMap<String, String>() {{
        put("3012665093", "7hKmoeiEmBd8x4p7AbxsVJDviMY89mgY");
        put("101080513854", "Rp52XGqaI,mw!qWSb57xn&=aTAWwcV=>");
    }};

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    AdCodeConfigService adCodeConfigService;

    @Autowired
    private AdService adService;

    @Value("${tempPath}")
    private String tempPath;


    /**
     * 优量汇-媒体查询接口
     * @return
     */
    @RequestMapping("getList")
    public Object getList(HttpServletRequest request){
        JSONObject ret = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        Map<String,String> paramMap = new HashMap<>();

        String appid = request.getParameter("appid");
        String member_id = request.getParameter("member_id");
        String medium_name = request.getParameter("medium_name");
        paramMap.put("appid",appid);
        paramMap.put("member_id",member_id);
        paramMap.put("medium_name",medium_name);
        List<YLHMediumVo> list = adCodeConfigService.getYLHMediumList(paramMap);
        long size = ((Page) list).getTotal();
        ret.put("totalSize",size);
        ret.put("data",list);
        ret.put("ret",1);
        return ret;
    }

    /**
     * 优量汇媒体列表导出
     * @param request
     * @param response
     */
    @RequestMapping("export")
    public void export(HttpServletRequest request,HttpServletResponse response){
        Map<String,String> paramMap = new HashMap<>();

        String appid = request.getParameter("appid");
        String member_id = request.getParameter("member_id");
        String medium_name = request.getParameter("medium_name");
        paramMap.put("appid",appid);
        paramMap.put("member_id",member_id);
        paramMap.put("medium_name",medium_name);

        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
        List<YLHMediumVo> list = adCodeConfigService.getYLHMediumList(paramMap);
        for (YLHMediumVo vo:list){
            Map<String, Object> app = appMap.get(vo.getAppid());
            if(app != null){
                vo.setAppname(app.get("app_name")+"");
            }
        }
        Map<String,String> headerMap = new LinkedHashMap<>();
        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response,list,headerMap,"api创建应用_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx");
    }


    /**
     * 优量汇-媒体操作接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("handle")
    public Object handle(HttpServletRequest request, YLHMediumVo vo){
        JSONObject ret = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String username = "";
        CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (currUserVo!=null){
            username = currUserVo.getLogin_name();
        }
        vo.setCreate_user(username);
        vo.setModify_user(username);

        String handle = request.getParameter("handle");
        if ("add".equals(handle)){
            ret = addYLHMedium(vo);
        }else if ("sync".equals(handle)){
            ret = syncYLHMedium(vo);
        }else if ("updateBaseInfo".equals(handle)){
            ret = updateBaseInfo(vo);
        }else if ("update".equals(handle)){
            ret = updateYLHMedium(vo);
        }
        return ret;
    }


    /**
     * 优量汇新建媒体
     * @param vo
     * @return
     */
    public JSONObject addYLHMedium(YLHMediumVo vo){
        JSONObject ret = new JSONObject();
        try {
            if ((BlankUtils.checkBlank(vo.getIndustry_id())&&BlankUtils.checkBlank(vo.getIndustry_id_v2()))
                    ||(!BlankUtils.checkBlank(vo.getIndustry_id())&&!BlankUtils.checkBlank(vo.getIndustry_id()))){
                ret.put("ret",0);
                ret.put("msg","媒体所属2级行业id与媒体所属新3级行业id,不能同时为空||媒体所属2级行业id与媒体所属新3级行业id,不能同时存在");
                return ret;
            }
            //H5 3
            if (!"3".equals(vo.getOs())){
                if (BlankUtils.checkBlank(vo.getDetail_url())){
                    ret.put("ret",0);
                    ret.put("msg","当操作系统不为H5时,详情页url必填");
                    return ret;
                }
                if (BlankUtils.checkBlank(vo.getPackage_name())){
                    ret.put("ret",0);
                    ret.put("msg","当操作系统不为H5时,主程序包名必填");
                    return ret;
                }
            }
            //Android 1
            if ("1".equals(vo.getOs())){
                if (BlankUtils.checkBlank(vo.getFull_package_name())){
                    ret.put("ret",0);
                    ret.put("msg","当OS为Android时,完整的程序包名必填");
                    return ret;
                }
                if (BlankUtils.checkBlank(vo.getSha1())){
                    ret.put("ret",0);
                    ret.put("msg","当OS为Android时,应用sha1必填");
                    return ret;
                }
            }
            //H5 3
            if ("3".equals(vo.getOs())){
                if (BlankUtils.checkBlank(vo.getDomain())){
                    ret.put("ret",0);
                    ret.put("msg","当OS为H5时,网站域名必填");
                    return ret;
                }
                if (BlankUtils.checkBlank(vo.getIcp())){
                    ret.put("ret",0);
                    ret.put("msg","当OS为H5时,网站备案必填");
                    return ret;
                }
                if (BlankUtils.checkBlank(vo.getIcp_picture())&&BlankUtils.checkBlank(vo.getIcp_picture_img_id())){
                    ret.put("ret",0);
                    ret.put("msg","当OS为H5时，icp_picture 网站备案截图和icp_pictrue_id icp备案图片ID 两者不能同时为空");
                    return ret;
                }
            }


            Map<String,Object> param = new HashMap<>();
            param.put("member_id", Long.parseLong(vo.getMember_id()));
            param.put("medium_name",vo.getMedium_name());
            if (!BlankUtils.checkBlank(vo.getIndustry_id())){
                param.put("industry_id",Integer.parseInt(vo.getIndustry_id()));
            }
            if (!BlankUtils.checkBlank(vo.getIndustry_id_v2())){
                param.put("industry_id_v2",Integer.parseInt(vo.getIndustry_id_v2()));
            }
            param.put("os",Integer.parseInt(vo.getOs()));
            if (!BlankUtils.checkBlank(vo.getDetail_url())){
                param.put("detail_url",vo.getDetail_url());
            }
            param.put("affiliation",vo.getAffiliation());
            if (!BlankUtils.checkBlank(vo.getPackage_name())){
                param.put("package_name",vo.getPackage_name());
            }
            if (!BlankUtils.checkBlank(vo.getFull_package_name())){
                param.put("full_package_name",vo.getFull_package_name());
            }
            if (!BlankUtils.checkBlank(vo.getSha1())){
                param.put("sha1",vo.getSha1());
            }
            if (!BlankUtils.checkBlank(vo.getWechat_app_id())){
                param.put("wechat_app_id",vo.getWechat_app_id());
            }
            if (!BlankUtils.checkBlank(vo.getWechat_universal_link())){
                param.put("wechat_universal_link",vo.getWechat_universal_link());
            }
            if (!BlankUtils.checkBlank(vo.getDomain())){
                param.put("domain",vo.getDomain());
            }
            if (!BlankUtils.checkBlank(vo.getIcp())){
                param.put("icp",vo.getIcp());
            }
            if (!BlankUtils.checkBlank(vo.getIcp_picture_img_id())){
                param.put("icp_picture_img_id",Integer.parseInt(vo.getIcp_picture_img_id()));
            }
            if (!BlankUtils.checkBlank(vo.getSoft_right_img_id())){
                param.put("soft_right_img_id",Integer.parseInt(vo.getSoft_right_img_id()));
            }
            if (!BlankUtils.checkBlank(vo.getCompany_ship_img_id())){
                param.put("company_ship_img_id",Integer.parseInt(vo.getCompany_ship_img_id()));
            }
            if (!BlankUtils.checkBlank(vo.getGame_isbn_img_id())){
                param.put("game_isbn_img_id",Integer.parseInt(vo.getGame_isbn_img_id()));
            }
            if (!BlankUtils.checkBlank(vo.getMedium_test_status())){
                param.put("medium_test_status",vo.getMedium_test_status());
            }
            if (!BlankUtils.checkBlank(vo.getProfit_mode())){
                param.put("profit_mode",vo.getProfit_mode());
            }

            Map<String, String> headMap = new HashMap<>();
            String timestapm = System.currentTimeMillis() / 1000 + "";
            String sign = sign(vo.getMember_id(), SECRET_MAP.get(vo.getMember_id()), timestapm);
            String ylhToken = token(vo.getMember_id(), timestapm, sign);
            headMap.put("token", ylhToken);

            String result = sendMultipartPost(YLH_MEDIUM_ADD_URL,param,headMap);
            if (!BlankUtils.checkBlank(result)){
                JSONObject retJson = JSONObject.parseObject(result);
                if ("0".equals(retJson.getString("code"))){
                    JSONObject data = retJson.getJSONObject("data");
                    if (data!=null){
                        String app_id = data.getString("app_id");
                        ret.put("ret",1);
                        ret.put("app_id",app_id);
                        ret.put("msg","新增成功");
                        vo.setApp_id(app_id);
                        adCodeConfigService.saveYLHMedium(vo);
                    }
                }else{
                    ret.put("ret",0);
                    ret.put("msg",retJson.getString("message"));
                }
            }
        }catch (Exception e){
            logger.error("addYLHMedium error:",e);
            ret.put("ret",0);
            ret.put("msg",e.getMessage());
        }
        return ret;
    }

    public JSONObject updateYLHMedium(YLHMediumVo vo){
        JSONObject ret = new JSONObject();
        ret.put("ret",0);
        try {
            if ((BlankUtils.checkBlank(vo.getIndustry_id())&&BlankUtils.checkBlank(vo.getIndustry_id_v2()))
                    ||(!BlankUtils.checkBlank(vo.getIndustry_id())&&!BlankUtils.checkBlank(vo.getIndustry_id()))){
                ret.put("ret",0);
                ret.put("msg","媒体所属2级行业id与媒体所属新3级行业id,不能同时为空||媒体所属2级行业id与媒体所属新3级行业id,不能同时存在");
                return ret;
            }
            //H5 3
            if (!"3".equals(vo.getOs())){
                if (BlankUtils.checkBlank(vo.getDetail_url())){
                    ret.put("ret",0);
                    ret.put("msg","当操作系统不为H5时,详情页url必填");
                    return ret;
                }
                if (BlankUtils.checkBlank(vo.getPackage_name())){
                    ret.put("ret",0);
                    ret.put("msg","当操作系统不为H5时,主程序包名必填");
                    return ret;
                }
            }
            //Android 1
            if ("1".equals(vo.getOs())){
                if (BlankUtils.checkBlank(vo.getFull_package_name())){
                    ret.put("ret",0);
                    ret.put("msg","当OS为Android时,完整的程序包名必填");
                    return ret;
                }
                if (BlankUtils.checkBlank(vo.getSha1())){
                    ret.put("ret",0);
                    ret.put("msg","当OS为Android时,应用sha1必填");
                    return ret;
                }
            }
            //H5 3
            if ("3".equals(vo.getOs())){
                if (BlankUtils.checkBlank(vo.getDomain())){
                    ret.put("ret",0);
                    ret.put("msg","当OS为H5时,网站域名必填");
                    return ret;
                }
                if (BlankUtils.checkBlank(vo.getIcp())){
                    ret.put("ret",0);
                    ret.put("msg","当OS为H5时,网站备案必填");
                    return ret;
                }
                if (BlankUtils.checkBlank(vo.getIcp_picture())&&BlankUtils.checkBlank(vo.getIcp_picture_img_id())){
                    ret.put("ret",0);
                    ret.put("msg","当OS为H5时，icp_picture 网站备案截图和icp_pictrue_id icp备案图片ID 两者不能同时为空");
                    return ret;
                }
            }


            Map<String,Object> param = new HashMap<>();
            param.put("member_id", Long.parseLong(vo.getMember_id()));
            param.put("medium_name",vo.getMedium_name());
            if (!BlankUtils.checkBlank(vo.getIndustry_id())){
                param.put("industry_id",Integer.parseInt(vo.getIndustry_id()));
            }
            if (!BlankUtils.checkBlank(vo.getIndustry_id_v2())){
                param.put("industry_id_v2",Integer.parseInt(vo.getIndustry_id_v2()));
            }
            param.put("os",Integer.parseInt(vo.getOs()));
            if (!BlankUtils.checkBlank(vo.getDetail_url())){
                param.put("detail_url",vo.getDetail_url());
            }
            param.put("affiliation",vo.getAffiliation());
            if (!BlankUtils.checkBlank(vo.getPackage_name())){
                param.put("package_name",vo.getPackage_name());
            }
            if (!BlankUtils.checkBlank(vo.getFull_package_name())){
                param.put("full_package_name",vo.getFull_package_name());
            }
            if (!BlankUtils.checkBlank(vo.getSha1())){
                param.put("sha1",vo.getSha1());
            }
            if (!BlankUtils.checkBlank(vo.getWechat_app_id())){
                param.put("wechat_app_id",vo.getWechat_app_id());
            }
            if (!BlankUtils.checkBlank(vo.getWechat_universal_link())){
                param.put("wechat_universal_link",vo.getWechat_universal_link());
            }
            if (!BlankUtils.checkBlank(vo.getDomain())){
                param.put("domain",vo.getDomain());
            }
            if (!BlankUtils.checkBlank(vo.getIcp())){
                param.put("icp",vo.getIcp());
            }
            if (!BlankUtils.checkBlank(vo.getIcp_picture_img_id())){
                param.put("icp_picture_img_id",Integer.parseInt(vo.getIcp_picture_img_id()));
            }
            if (!BlankUtils.checkBlank(vo.getSoft_right_img_id())){
                param.put("soft_right_img_id",Integer.parseInt(vo.getSoft_right_img_id()));
            }
            if (!BlankUtils.checkBlank(vo.getCompany_ship_img_id())){
                param.put("company_ship_img_id",Integer.parseInt(vo.getCompany_ship_img_id()));
            }
            if (!BlankUtils.checkBlank(vo.getGame_isbn_img_id())){
                param.put("game_isbn_img_id",Integer.parseInt(vo.getGame_isbn_img_id()));
            }
            if (!BlankUtils.checkBlank(vo.getMedium_test_status())){
                param.put("medium_test_status",vo.getMedium_test_status());
            }
            if (!BlankUtils.checkBlank(vo.getProfit_mode())){
                param.put("profit_mode",vo.getProfit_mode());
            }
            if (!BlankUtils.checkBlank(vo.getConvert_to_formal())){
                param.put("convert_to_formal",vo.getConvert_to_formal());
            }

            Map<String, String> headMap = new HashMap<>();
            String timestapm = System.currentTimeMillis() / 1000 + "";
            String sign = sign(vo.getMember_id(), SECRET_MAP.get(vo.getMember_id()), timestapm);
            String ylhToken = token(vo.getMember_id(), timestapm, sign);
            headMap.put("token", ylhToken);

            String result = sendMultipartPost(YLH_MEDIUM_UPDATE_URL,param,headMap);
            if (!BlankUtils.checkBlank(result)){
                JSONObject retJson = JSONObject.parseObject(result);
                if ("0".equals(retJson.getString("code"))){
                    JSONObject data = retJson.getJSONObject("data");
                    if (data!=null){
                        String app_id = data.getString("app_id");
                        ret.put("ret",1);
                        ret.put("msg","修改成功");
                        vo.setApp_id(app_id);
                        adCodeConfigService.saveYLHMedium(vo);

                    }
                }else{
                    ret.put("ret",0);
                    ret.put("msg",retJson.getString("message"));
                }
            }
        }catch (Exception e){
            logger.error("updateYLHMedium error:",e);
            ret.put("ret",0);
            ret.put("msg",e.getMessage());
        }
        return ret;
    }

    /**
     * 优量汇获取媒体列表
     * @param vo
     * @return
     */
    public JSONObject syncYLHMedium(YLHMediumVo vo){
        JSONObject ret = new JSONObject();
        JSONArray data = new JSONArray();
        try {
            int page = 1;
            Map<String, String> headMap = new HashMap<>();
            Map<String,Object> paramMap = new HashMap<>();

            headMap.put("Content-Type", "application/json");
            String timestapm = System.currentTimeMillis() / 1000 + "";
            String sign = sign(vo.getMember_id(), SECRET_MAP.get(vo.getMember_id()), timestapm);
            String ylhToken = token(vo.getMember_id(), timestapm, sign);
            headMap.put("token", ylhToken);

            while (true){
                paramMap.put("page",page);
                paramMap.put("page_size",100);
                paramMap.put("member_id",Long.parseLong(vo.getMember_id()));
                String result = HttpRequest.httpPostJson(YLH_MEDIUM_LIST_URL,paramMap,headMap);
                try {
                    if (!BlankUtils.checkBlank(result)){
                        JSONObject resultJson = JSONObject.parseObject(result);
                        if ("0".equals(resultJson.getString("code"))){
                            JSONObject dataJson = resultJson.getJSONObject("data");
                            JSONObject pageJson = dataJson.getJSONObject("page_info");
                            if (dataJson!=null){
                                JSONArray eachArray = dataJson.getJSONArray("list");
                                for (Object obj:eachArray){
                                    data.add(obj);
                                }
                                if (page<pageJson.getInteger("total_page")){
                                    page++;
                                }else {
                                    break;
                                }
                            }else {
                                break;
                            }
                        }else {
                            break;
                        }
                    }else {
                        break;
                    }
                }catch (Exception e){
                    logger.error("parse medium  ylh return error:",e);
                    break;
                }
            }
        }catch (Exception e){
            ret.put("ret",0);
            ret.put("msg",e.getMessage());
        }
        int sum = 0;
        if (data.size()>0){
            List<YLHMediumVo> list = new ArrayList<>();
            for (Object obj:data){
                YLHMediumVo ylhMediumVo = JSONObject.toJavaObject((JSONObject)obj,YLHMediumVo.class);
                if (ylhMediumVo!=null){
                    ylhMediumVo.setMember_id(vo.getMember_id());
                    list.add(ylhMediumVo);
                }
            }
            if (list.size()>0){
                sum = adCodeConfigService.batchSaveYLHMedium(list);
            }
        }
        if (sum>0){
            ret.put("ret",1);
            ret.put("msg","success");
        }
        return ret;
    }

    public JSONObject updateBaseInfo(YLHMediumVo vo){
        JSONObject base = new JSONObject();
        if (BlankUtils.checkBlank(vo.getApp_id())){
            base.put("msg","必填参数为空");
            return base;
        }
        int succ = adCodeConfigService.updateYLHMediumBaseInfo(vo);
        if (succ>0){
            base.put("ret",1);
            base.put("msg","操作成功!");
        }else {
            base.put("msg","操作失败!");
        }
        return base;
    }

    @RequestMapping("upload")
    public Object uploadFile(@RequestParam(value = "fileName") MultipartFile file,HttpServletRequest request) throws IOException {
        JSONObject ret = new JSONObject();
        ret.put("ret",0);
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.toErrorJson("token失效,请重新登录!");
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String type = request.getParameter("type");
        String member_id = request.getParameter("member_id");
        if (BlankUtils.checkBlank(type)||BlankUtils.checkBlank(member_id)){
            return ReturnJson.toErrorJson("必填参数为空");
        }
        String timestapm = System.currentTimeMillis() / 1000 + "";
        String sign = sign(member_id, SECRET_MAP.get(member_id), timestapm);
        String ylhToken = token(member_id, timestapm, sign);

        String fileName = file.getOriginalFilename();
        InputStream in = file.getInputStream();
        FileOutputStream out = new FileOutputStream(tempPath + "/" + fileName);
        byte buffer[] = new byte[2048];
        int len = 0;
        while((len = in.read(buffer)) > 0){
            out.write(buffer, 0, len);
        }
        //写入文件
        File upFile = new File(tempPath + "/" + fileName);
        // HttpPost post = new HttpPost(uploadParams.get("uploadUrl") + uploadFile);
        HttpPost post = new HttpPost(YLH_MEDIUM_UPLOAD_URL);

        // File to upload.
        FileBody bin = new FileBody(upFile);
        // Construct a POST request.
        HttpEntity reqEntity = MultipartEntityBuilder.create()
                .addPart("file", bin)
                .addTextBody("type", type) // Obtain the authentication code.
                .addTextBody("member_id", member_id)
                .build();

        post.setEntity(reqEntity);
        post.addHeader("token", ylhToken);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            CloseableHttpResponse httpResponse = httpClient.execute(post);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader br =
                        new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent(), Consts.UTF_8));
                String result = br.readLine();
                JSONObject resultJson = JSONObject.parseObject(result);
                if (resultJson!=null){
                    if ("0".equals(resultJson.getString("code"))){
                        ret.put("ret",1);
                        ret.put("image_id",resultJson.getJSONObject("data").getString("image_id"));
                    }else {
                        ret.put("ret",0);
                        ret.put("msg",resultJson.getString("message"));
                    }
                }
            }
        } catch (Exception e) {
            logger.error("uploadFile error:",e);
        }
        upFile.delete();
        return ret;
    }

    /**
     * 生成sign
     *
     * @param memberid
     * @param secret
     * @param timestamp
     * @return
     */
    private static String sign(String memberid, String secret, String timestamp) {
        String sign = "";
        String encriptStr = memberid + secret + timestamp;
        sign = Encript.encryptSHA1(encriptStr, "");
        return sign;
    }

    /**
     * 生成token
     *
     * @param memberid
     * @param timestamp
     * @param sign
     * @return
     */
    private static String token(String memberid, String timestamp, String sign) {
        String token = "";
        String tokenStr = memberid + "," + timestamp + "," + sign;
        token = Base64.getEncoder().encodeToString(tokenStr.getBytes(StandardCharsets.UTF_8));
        return token;
    }


    public String sendMultipartPost(String url,Map<String,Object> params,Map<String,String> headers){
        String result = "";
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpPost httpPost = new HttpPost(url);
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setCharset(java.nio.charset.Charset.forName("UTF-8"));
            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            //解决中文乱码
            ContentType contentType = ContentType.create(HTTP.PLAIN_TEXT_TYPE, HTTP.UTF_8);
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if(entry.getValue() == null)
                    continue;
                StringBody stringBody = new StringBody(entry.getValue().toString(),contentType);
                // 类似浏览器表单提交，对应input的name和value
                builder.addPart(entry.getKey(), stringBody);
            }
            HttpEntity entity = builder.build();
            httpPost.setEntity(entity);
            for (Map.Entry<String,String> header:headers.entrySet()){
                httpPost.addHeader(header.getKey(),header.getValue());
            }
            CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader br =
                        new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent(), Consts.UTF_8));
                result = br.readLine();
                br.close();
            }
        }catch (Exception e){
            logger.error("sendMultipartPost error:",e);
        }
        return result;
    }
}
