package com.wbgame.controller.adv2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.pojo.adv2.*;
import com.wbgame.service.adv2.AdCodeConfigService;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpRequest;
import jxl.Sheet;
import jxl.Workbook;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;
import static com.wbgame.common.constants.AdcodeCommonConstants.TOBID_PREFIX;

/**
 * <AUTHOR>
 * @Classname KSAdCodeController
 * @Description TODO
 * @Date 2021/11/22 10:41
 */
@CrossOrigin
@RestController
@RequestMapping("/adv2/ksAdCode")
public class KSAdCodeController {

    @Autowired
    AdCodeConfigService adCodeConfigService;

    @Autowired
    private Adv2Service adv2Service;

    @Autowired
    RedisTemplate redisTemplate;
    @Resource
    private Redisson redisson;

    private static final String PLATFORM = "kuaishou";
    private String prefix;
    public static final String LOCK_KS_FILE_BATCH_IMPORT = "lock_ks_file_batch_import";

    private static Logger logger = LoggerFactory.getLogger(KSAdCodeController.class);

    private static String KS_REQ_URL ="https://u.kuaishou.com";

    private static String KS_CODE_CREATE_URL_PATH = "/api/position/add?";

    private static String KS_AD_CODE_CPM_UPDATE_URL_PATH ="/api/position/modCpmFloor?";

    private final static Map<String, String> KS_AD_TYPE_MAP = new HashMap<String, String>() {{
        //1 信息流 2激励视频  3全屏视频 4开屏 13插屏
        put("1", "msg");
        put("2","video");
        put("3","plaque");
        put("4", "splash");
        put("13","plaque");
    }};

    //SDK广告源类型
    private static Map<String,String> SDK_AD_TYPE_MAP = new HashMap<String,String>(){{
        put("开屏", "splash");
        put("原生开屏", "natSplash");
        put("普通banner/模板", "banner");
        put("banner自渲染", "natBanner");
        put("信息流模板", "yuans");
        put("信息流自渲染", "msg");
        put("普通插屏/模板", "plaque");
        put("自渲染插屏", "natPlaque");
        put("插屏视频", "plaqueVideo");
        put("视频", "video");
        put("视频自渲染", "natVideo");
        put("icon", "icon");
    }};
    //广告使用类型
    private static Map<String,String> OPEN_TYPE_MAP = new HashMap<String,String>(){{
        put("信息流-msg", "msg");
        put("banner-banner", "banner");
        put("开屏-splash", "splash");
        put("视频-video", "video");
        put("插屏-plaque", "plaque");
    }};

    //快手广告类型
    private static Map<String,String> AD_STYLE_MAP = new HashMap<String,String>(){{
        put("信息流", "1");
        put("激励视频", "2");
        put("插屏图片", "13");
        put("新插屏", "23");
        put("全屏视频", "3");
        put("开屏", "4");
        put("draw视频广告", "6");
    }};

    //渲染类型
    private static Map<String,String> RENDER_TYPE_MAP = new HashMap<String,String>(){{
        put("自渲染", "1");
        put("模板渲染", "2");
    }};

    //app类型
    private final String TEMPLATE_TYPE_GAME = "1";

    private final String TEMPLATE_TYPE_TOOL = "2";





    /**
     * 查询快手广告位
     * @param request
     * @return
     */
    @RequestMapping("getList")
    public Object getKSAdCodeList(HttpServletRequest request, KSAdcodeVo vo) {
        JSONObject ret = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token)||!redisTemplate.hasKey(token)) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<KSAdcodeVo> list = adCodeConfigService.getKSAdCodeList(vo);
        long size = ((Page) list).getTotal();
        ret.put("ret", 1);
        ret.put("data", list);
        ret.put("totalCount", size);
        return ret;
    }

    /**
     * 快手创建广告位
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("handle")
    public Object handleAdcdoe(HttpServletRequest request, KSAdcodeVo vo) {
        String token = request.getHeader("token");
        if (StringUtils.isBlank(token)) {
            token = request.getParameter("token");
        }
        if (BlankUtils.checkBlank(token)&&!redisTemplate.hasKey(token)) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        //创建人
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        }else if(token.startsWith("waibaotoken")){
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_id();
        }
        return handleAdcdoe(username, vo, false);
    }

    /**
     * isTobid true tobid广告源 不加入广告配置
     * @param username
     * @param vo
     * @param isTobid
     * @return
     */
    public Object handleAdcdoe(String username, KSAdcodeVo vo, boolean isTobid) {
        JSONObject ret = new JSONObject();

        prefix = PLATFORM;

        //判断广告源id是否已经存在
        String sdkAdType= vo.getSdk_ad_type();
        String appid = vo.getAppid();
        String adExtentionName = vo.getAdExtensionName();
        String adsid = prefix+"_"+sdkAdType+"_"+appid+"_"+adExtentionName;
        ExtendAdsidVo adsidVo = adCodeConfigService.getDnAdSid(adsid);
        if (adsidVo!=null){
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:生成规则:广告平台_广告位类型_产品id_扩展名生成的ad-sid 已经在广告源表存在");
            return ret;
        }

        JSONObject result = new JSONObject();
        Boolean hasCpm = BlankUtils.checkBlank(vo.getCpm());
        try {
            AdCodeAccountVo account = new AdCodeAccountVo();
            if (vo != null) {
                //1 信息流 2激励视频  3全屏视频 4开屏 13插屏
                account.setAppid(vo.getAppid());
                if (BlankUtils.checkBlank(vo.getApp_id()) && BlankUtils.isBlank(vo.getChannel())){
                    ret.put("ret", 0);
                    ret.put("msg", "创建广告位失败,原因:平台产品id参数未传");
                    return ret;
                }
                account.setTappid(vo.getApp_id());
                account.setChannel(vo.getChannel());
                List<AdCodeAccountVo> list = adCodeConfigService.getAdCodeAccountList(account);
                account = list.stream().filter(t->t.getAppid().equals(vo.getAppid())&&t.getPlatform().equals(PLATFORM)).findFirst().orElse(null);
                if (account==null){
                    ret.put("ret", 0);
                    ret.put("msg", "创建广告位失败,原因:该产品未配置变现平台账号");
                    return ret;
                }

                switch (vo.getAdStyle()) {
                    case "1":     //信息流
                        result = xxlAdcode(vo,account);
                        break;
                    case "2":     //激励视频
                        result = jlspAdcode(vo,account);
                        break;
                    case "3":     //全屏视频
                        result = qpspAdcode(vo,account);
                        break;
                    case "4":     //开屏
                        result = kpAdcode(vo,account);
                        break;
                    case "13":     //插屏
                        result = cpAdcode(vo,account);
                        break;
                    case "23":     // 新插屏
                        result = xcpAdcode(vo,account);
                    default:
                        break;
                }
            }
            if ("1".equals(result.getString("ret"))) {
                //写入到自己的表
                ret.put("ret", 1);
                ret.put("msg", "ok");
                //修改cpm
                if (!hasCpm){
                    JSONObject cpmRet = setCpm(account.getTappid(),result.getString("positionId"),vo.getCpm(),account);
                    if ("1".equals(cpmRet.getString("ret"))){
                        vo.setCpm(vo.getCpm());
                    }else {
                        vo.setCpm("");
                    }
                }

                vo.setApp_id(account.getTappid());
                vo.setCreateUser(username);
                vo.setPositionId(result.getString("positionId"));
                ret.put("code", result.getString("positionId"));
                ret.put("adsid", adsid);
                int succ = adCodeConfigService.saveKSAdCode(vo);
//                logger.info("ks handleAdcdoe info:"+JSON.toJSONString(vo));
                if (succ>0){
                    boolean saved = saveKSAdCodeToAdSidManage(vo,account);
                    ret.put("ret",1);
                    if (saved){
                        if (BlankUtils.checkBlank(vo.getCpm())){
                            if (!hasCpm){
                                ret.put("msg","创建广告位操作成功,但cpm设置失败");
                            }
                        }else{
                            ret.put("msg","操作成功");
                        }
                    }else {
                        ret.put("msg","创建广告位成功,但是写入广告源管理表失败!");
                    }
                    //以下渠道为+策略不为空进行写入广告配置表操作
                    String[] channelList = {"csj", "apple", "huaweiml", "huaweiml2", "vivoml", "vivomj",
                            "vivoml2", "xiaomimj", "oppomj", "opposd", "vivosd", "huaweisd", "huawei", "huawei2", "honor"};
                    if (!isTobid && Arrays.asList(channelList).contains(vo.getChannel())&&!BlankUtils.checkBlank(vo.getStrategy())){
                        JSONObject saveDnAdcode = saveAdcodeToDnExtendAdconfig(vo,account);
                        if (saveDnAdcode.getIntValue("ret") != 1){
                            ret.put("msg",(ret.getString("msg")==null?"":ret.getString("msg"))+"-但写入广告配置表失败");
                        }
                    }
                }else {
                    ret.put("ret",1);
                    ret.put("msg","操作失败,写入本地数据库失败请联系管理员!");
                }
            } else {
                ret.put("ret", 0);
                ret.put("msg", "创建广告位失败,原因:" + result.getString("msg"));
            }
        }catch (Exception e){
            logger.error("ks handleAdcdoe error:",e);
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:" + e.getMessage());
        }

        return ret;
    }

    /**
     * 保存快手创建的广告位至广告源管理表 dn_extend_adsid_manage
     * @param vo
     * @param acount
     * @return
     */
    public boolean saveKSAdCodeToAdSidManage(KSAdcodeVo vo, AdCodeAccountVo acount){
        ExtendAdsidVo app = new ExtendAdsidVo();
        try {
            //平台_广告源类型_产品id_扩展名
            String adType= KS_AD_TYPE_MAP.get(vo.getAdStyle());
            String sdkAdType = vo.getSdk_ad_type();
            String appid = acount.getAppid();
            String adExtentionName = vo.getAdExtensionName();
            String adsid = prefix+"_"+sdkAdType+"_"+appid+"_"+adExtentionName;
            //adsid
            app.setAdsid(adsid);
            //广告平台
            app.setAgent(PLATFORM);
            //广告位id
            app.setSdk_code(vo.getPositionId());
            //广告平台产品id
            app.setSdk_appid(acount.getTappid());
            app.setSdk_appkey("");
            //SDK广告类型
            app.setSdk_adtype(sdkAdType);
            //广告平台广告类型
            app.setAdpos_type(adType);
            //备注
            app.setNote(vo.getRemark());
            //动能产品id
            app.setAppid(acount.getAppid());
            //子渠道
            app.setCha_id(vo.getChannel());
            //创建人
            app.setCuser(vo.getCreateUser());
            //cpm
            app.setCpmFloor(vo.getCpm());
            //广告使用类型
            app.setOpen_type(vo.getOpen_type());
            app.setParams(vo.getParams());
            /** 快手api增加bidding判断 */
            if (BlankUtils.checkBlank(vo.getBidding_type())){
                app.setBidding("0");
            }else {
                if ("1".equals(vo.getBidding_type())){
                    app.setBidding("1");
                }else if ("2".equals(vo.getBidding_type())){
                    app.setBidding("2");
                }
            }
//            logger.info("saveKSAdCodeToAdSidManage:"+JSON.toJSONString(app));
            String sql = "insert into dnwx_cfg.dn_extend_adsid_manage(adsid,agent,sdk_code,sdk_appid,sdk_appkey,sdk_adtype,adpos_type,note,appid,cha_id,createtime,lasttime,cuser,cpmFloor,open_type,bidding,params) "+
                    "values(#{obj.adsid},#{obj.agent},#{obj.sdk_code},#{obj.sdk_appid},#{obj.sdk_appkey},#{obj.sdk_adtype},#{obj.adpos_type},#{obj.note},#{obj.appid},#{obj.cha_id},now(),now(),#{obj.cuser},#{obj.cpmFloor},#{obj.open_type},#{obj.bidding},#{obj.params}) ";
            int result = adv2Service.execSqlHandle(sql, app);
            if (result>0){
                return true;
            }
        }catch (Exception e){
            logger.error("saveKSAdCodeToAdSidManage error:",e);
        }


        return false;
    }

    /**
     * 批量上传文件的接口,为了避免多次误操作, 加上分布式锁
     * @param file  上传的文件
     * @param template_type 类型 1 游戏, 2 工具
     * @return 结果
     * @throws InterruptedException
     */
    @RequestMapping("batchImport")
    @ControllerLoggingEnhancer
    public Object lockedBatchImport(@RequestParam(value = "fileName") MultipartFile file,
                                    @RequestParam(value = "template_type") String template_type) throws InterruptedException {
        RLock lock = redisson.getLock(LOCK_KS_FILE_BATCH_IMPORT);
        if (lock.tryLock(1L, TimeUnit.SECONDS)) {
            try {
                return batchImport(template_type, file);
            } finally {
                lock.unlock();
            }
        } else {
            return ReturnJson.toErrorJson("有同步任务正在运行中，请稍后再试");
        }
    }

    /**
     *
     * @param request
     * @param file
     * @param response
     * @return
     */
    public Object batchImport(String template_type, MultipartFile file) {
        TreeMap<String,String> retMap = new TreeMap<>();
        try {
            String username = LOGIN_USER_NAME.get();
            //应用	子渠道	sdk广告源样式	广告使用类型	广告拓展名	备注	代码位名称	cpm设置
            // 广告平台	广告类型	渲染方式	素材类型	渲染模版ID	视频播放⽅向
            // 奖励名称	奖励数量	回调设置	回调url	跳过按钮	倒计时	播放声音

            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                TreeMap<Integer,KSAdcodeVo> list = new TreeMap<>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int column = sheet.getColumns();
                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())){
                        continue;
                    }
                    try {
                        String[] vals = new String[column];
                        for (int c = 0; c < column; c++) {
                            vals[c] = sheet.getCell(c, r).getContents();
                        }

                        KSAdcodeVo ki = resolveArrayToVo(template_type, vals);
                        if (ki==null){
                            return ReturnJson.toErrorJson("第"+r+"行配置错误,请检查");
                        }

                        list.put(r,ki);
                    }catch (Exception e){
                        logger.error("ks excel to vo error:",e);
                        return ReturnJson.toErrorJson("第"+r+"行配置错误,请检查");
                    }
                }
//                logger.info("ks batchImport:"+JSON.toJSONString(list));
                if (list.size()>0){
                    retMap = batchCreateAdcode(list,username);
                }

            } else {
                return ReturnJson.toErrorJson("上传文件有误，需要.xls格式文件");
            }
        } catch (Exception e) {
            logger.error("ks batchImport error:",e);
            return ReturnJson.toErrorJson("上传文件失败,请联系管理员!错误信息:"+e.getMessage());
        }
        StringBuffer sb = new StringBuffer();
        if (retMap.size()>0){
            for (Map.Entry<String,String> ret:retMap.entrySet()){
                sb.append(ret.getKey()+ret.getValue()).append("\t");
            }
            return ReturnJson.success(sb.toString());
        }else {
            return ReturnJson.success("全部创建成功");
        }
    }

    @Nullable
    public KSAdcodeVo resolveArrayToVo(String template_type, String[] vals) {
        KSAdcodeVo ki = null;
        if (TEMPLATE_TYPE_GAME.equals(template_type)){
            ki = getGameKSAdcode(vals);
            ki.setTemplate_type(TEMPLATE_TYPE_GAME);
        }
        if (TEMPLATE_TYPE_TOOL.equals(template_type)){
            ki = getToolKSAdcode(vals);
            ki.setTemplate_type(TEMPLATE_TYPE_TOOL);
        }
        return ki;
    }

    /**
     * 快手通用广告位创建处理-1
     * @param map
     * @param username
     * @return
     */
    public TreeMap batchCreateAdcode(TreeMap<Integer,KSAdcodeVo> map,String username){
        TreeMap<String,String> retMap = new TreeMap<>();
        for (Map.Entry<Integer,KSAdcodeVo> m:map.entrySet()){
            try {
                JSONObject ret = commonHandleAdcode(m.getValue(),username);
                if ("0".equals(ret.getString("ret"))){
                    retMap.put("第"+m.getKey()+"行配置创建广告位失败,失败原因:",ret.getString("msg"));
                }
            }catch (Exception e){
                retMap.put("第"+m.getKey()+"行配置创建广告位失败,失败原因:","服务器出错,信息:"+e.getMessage());
                logger.error("ks batchCreateAdcode error:",e);
            }
        }
        return retMap;
    }

    /**
     * 快手通用广告位创建处理-2
     * @param vo
     * @param username
     * @return
     */
    public JSONObject commonHandleAdcode(KSAdcodeVo vo,String username){
        JSONObject ret = new JSONObject();
        //判断广告源id是否已经存在
        String sdkAdType= vo.getSdk_ad_type();
        String appid = vo.getAppid();
        String adExtentionName = vo.getAdExtensionName();
        String adsid = prefix+"_"+sdkAdType+"_"+appid+"_"+adExtentionName;
        ExtendAdsidVo adsidVo = adCodeConfigService.getDnAdSid(adsid);
        if (adsidVo!=null){
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:生成规则:广告平台_广告位类型_产品id_扩展名生成的ad-sid 已经在广告源表存在");
            return ret;
        }

        prefix = PLATFORM;
        JSONObject result = new JSONObject();
        Boolean hasCpm = BlankUtils.checkBlank(vo.getCpm());
        try {
            AdCodeAccountVo account = new AdCodeAccountVo();
            if (vo != null) {
                //1 信息流 2激励视频  3全屏视频 4开屏 13插屏
                account.setAppid(vo.getAppid());
                if (TEMPLATE_TYPE_GAME.equals(vo.getTemplate_type())){
                    if (BlankUtils.checkBlank(vo.getApp_id()) && BlankUtils.isBlank(vo.getChannel())){
                        ret.put("ret", 0);
                        ret.put("msg", "创建广告位失败,原因:平台产品id参数未传");
                        return ret;
                    }
                    account.setTappid(vo.getApp_id());
                    account.setChannel(vo.getChannel());
                }
                List<AdCodeAccountVo> list = adCodeConfigService.getAdCodeAccountList(account);
                account = list.stream().filter(t->t.getAppid().equals(vo.getAppid())&&t.getPlatform().equals(PLATFORM)).findFirst().orElse(null);
                if (account==null){
                    ret.put("ret", 0);
                    ret.put("msg", "创建广告位失败,原因:该产品未配置变现平台账号");
                    return ret;
                }
                //控制请求频率 2.5秒请求一次
                try {
                    Thread.sleep(2500);
                }catch (Exception e){

                }
                switch (vo.getAdStyle()) {
                    case "1":     //信息流
                        result = xxlAdcode(vo,account);
                        break;
                    case "2":     //激励视频
                        result = jlspAdcode(vo,account);
                        break;
                    case "3":     //全屏视频
                        result = qpspAdcode(vo,account);
                        break;
                    case "4":     //开屏
                        result = kpAdcode(vo,account);
                        break;
                    case "13":     //插屏
                        result = cpAdcode(vo,account);
                        break;
                    case "23":     //新插屏
                        result = xcpAdcode(vo,account);
                        break;
                    default:
                        break;
                }
            }
            if ("1".equals(result.getString("ret"))) {
                //写入到自己的表
                ret.put("ret", 1);
                ret.put("msg", "ok");
                //修改cpm
                if (!hasCpm){
                    JSONObject cpmRet = setCpm(account.getTappid(),result.getString("positionId"),vo.getCpm(),account);
                    if ("1".equals(cpmRet.getString("ret"))){
                        vo.setCpm(vo.getCpm());
                    }else {
                        vo.setCpm("");
                    }
                }

                vo.setApp_id(account.getTappid());
                vo.setCreateUser(username);
                vo.setPositionId(result.getString("positionId"));
                int succ = adCodeConfigService.saveKSAdCode(vo);
//                logger.info("ks handleAdcdoe info:"+JSON.toJSONString(vo));
                if (succ>0){
                    boolean saved = saveKSAdCodeToAdSidManage(vo,account);
                    ret.put("ret",1);
                    if (saved){
                        if (BlankUtils.checkBlank(vo.getCpm())){
                            if (!hasCpm){
                                ret.put("msg","创建广告位操作成功,但cpm设置失败");
                            }
                        }else{
                            ret.put("msg","操作成功");
                        }
                        //以下渠道+策略不为空进行写入广告配置表操作
                        String[] channelList = {"csj", "apple", "huaweiml", "huaweiml2", "vivoml", "vivomj",
                                "vivoml2", "xiaomimj", "oppomj", "opposd", "vivosd", "huaweisd", "huawei", "huawei2", "honor"};
                        if (Arrays.asList(channelList).contains(vo.getChannel())&&!BlankUtils.checkBlank(vo.getStrategy())){
                            JSONObject saveDnAdcode = saveAdcodeToDnExtendAdconfig(vo,account);
                            if (saveDnAdcode.getIntValue("ret") != 1){
                                ret.put("msg",(ret.getString("msg")==null?"":ret.getString("msg"))+"-但写入广告配置表失败");
                            }
                        }
                    }else {
                        ret.put("msg","创建广告位成功,但是写入广告源管理表失败!");
                    }
                }else {
                    ret.put("ret",1);
                    ret.put("msg","操作失败,写入本地数据库失败请联系管理员!");
                }
            } else {
                ret.put("ret", 0);
                ret.put("msg", "创建广告位失败,原因:" + result.getString("msg"));
            }
        }catch (Exception e){
            logger.error("ks commonHandleAdcode error:",e);
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:" + e.getMessage());
        }
        return ret;
    }

    /**
     * 信息流广告位
     *
     * @param vo
     * @return
     */
    private static JSONObject xxlAdcode(KSAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);

        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        //请求参数
        TreeMap<String, Object> paramMap = new TreeMap<>();
        //sign参数
        TreeMap<String,Object> signMap = new TreeMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";

        signMap.put("timestamp",timestamp);
        signMap.put("ak",account.getTaccountid());
        signMap.put("sk",account.getTsecrect());


        paramMap.put("appId", account.getTappid());
        paramMap.put("name", vo.getName());

        /** 快手api增加bidding区分  -廖志坤 2023.01.05*/
        paramMap.put("priceStrategy", vo.getPriceStrategy());
        //竞价策略
        if ("2".equals(vo.getPriceStrategy())){
            if (BlankUtils.checkBlank(vo.getBidding_type())){
                ret.put("ret", 0);
                ret.put("msg","价格策略为实时竞价时,竞价策略必填");
                return ret;
            }
            paramMap.put("biddingStrategy",vo.getBidding_type());
        }

        //广告类型
        paramMap.put("adStyle", 1);
        //渲染方式
        paramMap.put("renderType", Integer.parseInt(vo.getRenderType()));
        //素材类型
        //信息流：渲染方式 1 自渲染 2模板渲染
        //信息流:1-竖版视频,2-横版视频 5-竖版图片 6-横版图片 自渲染可选 1、2、5、6  模版渲染可选 2、6
//        String[] self = {"1","2","5","6"};
//        String[] temple ={"2","6"};
        JSONArray array = JSONArray.parseArray(vo.getMaterialTypeList());

        // 自渲染模式不填写 MultiTemplateParams
        if (Integer.parseInt(vo.getRenderType()) == 1 && BlankUtils.isNotBlank(vo.getMultiTemplateParams())) {
            ret.put("ret", 0);
            ret.put("msg","自渲染模式不填写 MultiTemplateParams");
            return ret;
        }

        if (BlankUtils.isNotBlank(vo.getMultiTemplateParams())) {
            String[] templateIds = vo.getMultiTemplateParams().split(",");
            JSONArray templateIdArray = new JSONArray();
            for (String templateId : templateIds) {
                HashMap<String, Integer> template = new HashMap<>();
                template.put("templateId", Integer.valueOf(templateId));
                templateIdArray.add(template);
            }
            paramMap.put("multiTemplateParams", templateIdArray);
        }

        //素材类型
        if (!CollectionUtils.isEmpty(array)) {
            paramMap.put("materialTypeList", array);
        }
        String sign = sign(KS_CODE_CREATE_URL_PATH,signMap);

        String url = KS_REQ_URL+KS_CODE_CREATE_URL_PATH+"ak="+account.getTaccountid()+"&timestamp="+timestamp+"&sign="+sign;
        String result =  HttpRequest.httpPostJson(url, paramMap, headMap);
        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("1".equals(json.getString("result"))) {
                ret.put("ret", 1);
                ret.put("positionId", json.getString("data"));
            } else {
                ret.put("msg", json.getString("error_msg"));
                ret.put("ret", json.get("result"));
                logger.info("xxlAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("message"));
            }
        }
        return ret;
    }

    /**
     * 开屏广告位
     *
     * @param vo
     * @return
     */
    private static JSONObject kpAdcode(KSAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);
        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        //请求参数
        TreeMap<String, Object> paramMap = new TreeMap<>();
        //sign参数
        TreeMap<String,Object> signMap = new TreeMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";

        signMap.put("timestamp",timestamp);
        signMap.put("ak",account.getTaccountid());
        signMap.put("sk",account.getTsecrect());


        paramMap.put("appId", account.getTappid());
        paramMap.put("name", vo.getName());
        
        /** 快手api增加bidding区分  -廖志坤 2023.01.05*/
        paramMap.put("priceStrategy", vo.getPriceStrategy());
        //竞价策略
        if ("2".equals(vo.getPriceStrategy())){
            if (BlankUtils.checkBlank(vo.getBidding_type())){
                ret.put("ret", 0);
                ret.put("msg","价格策略为实时竞价时,竞价策略必填");
                return ret;
            }
            paramMap.put("biddingStrategy",vo.getBidding_type());
        }
        
        //广告类型
        paramMap.put("adStyle", 4);
        //跳过按钮
        paramMap.put("skipAdMode",Integer.parseInt(vo.getSkipAdMode()));
        //渲染方式 2-模版渲染 必填:2
        paramMap.put("renderType", 2);
        //渲染模板id
        paramMap.put("templateId",1000);
        //广告位创意类型 1-竖版视频 3-单图 必填：[1,3]
        JSONArray materialTypeList = JSONArray.parseArray("[1,3]");
        paramMap.put("materialTypeList",materialTypeList);
        //跳过按钮是否显示 倒计时
        if ("0".equals(vo.getSkipAdMode())){
            paramMap.put("countdownShow",Integer.parseInt(vo.getCountdownShow()));
        }
        //播放声音 1必填静音
        paramMap.put("voice",1);
        //sign
        String sign = sign(KS_CODE_CREATE_URL_PATH,signMap);

        String url = KS_REQ_URL+KS_CODE_CREATE_URL_PATH+"ak="+account.getTaccountid()+"&timestamp="+timestamp+"&sign="+sign;
        String result =  HttpRequest.httpPostJson(url, paramMap, headMap);

        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("1".equals(json.getString("result"))) {
                ret.put("ret", 1);
                ret.put("positionId", json.getString("data"));
            } else {
                ret.put("msg", json.getString("error_msg"));
                ret.put("ret", json.get("result"));
                logger.info("kpAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("message"));
            }
        }
        return ret;

    }

    /**
     * 插屏广告位
     *
     * @param vo
     * @return
     */
    private static JSONObject cpAdcode(KSAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);
        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        //请求参数
        TreeMap<String, Object> paramMap = new TreeMap<>();
        //sign参数
        TreeMap<String,Object> signMap = new TreeMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";

        signMap.put("timestamp",timestamp);
        signMap.put("ak",account.getTaccountid());
        signMap.put("sk",account.getTsecrect());


        paramMap.put("appId", account.getTappid());
        paramMap.put("name", vo.getName());
        /** 快手api增加bidding区分  -廖志坤 2023.01.05*/
        paramMap.put("priceStrategy", vo.getPriceStrategy());
        //竞价策略
        if ("2".equals(vo.getPriceStrategy())){
            if (BlankUtils.checkBlank(vo.getBidding_type())){
                ret.put("ret", 0);
                ret.put("msg","价格策略为实时竞价时,竞价策略必填");
                return ret;
            }
            paramMap.put("biddingStrategy",vo.getBidding_type());
        }
        
        //广告类型 13-插屏
        paramMap.put("adStyle", 13);
        //渲染方式 2-模版渲染 必填:2
        paramMap.put("renderType", 2);
        //渲染模板id 必填 9
        paramMap.put("templateId",9);
        //素材类型 1-竖版视频,2-横版视频 5-竖版图片 6-横版图片
        JSONArray materialTypeList = JSONArray.parseArray(vo.getMaterialTypeList());
        String[] materials ={"1","2","5","6"};
        if (!BlankUtils.checkBlank(vo.getMaterialTypeList())){
            for (int i =0;i<materialTypeList.size();i++){
                String n = String.valueOf(materialTypeList.get(i));
                if(!Arrays.asList(materials).contains(n)){
                    ret.put("ret", 0);
                    ret.put("msg","插屏广告位,素材类型 广告位创意类型 1-竖版视频,2-横版视频 5-竖版图片 6-横版图片");
                    return ret;
                }
            }
        }
        paramMap.put("materialTypeList",materialTypeList);

        //sign
        String sign = sign(KS_CODE_CREATE_URL_PATH,signMap);

        String url = KS_REQ_URL+KS_CODE_CREATE_URL_PATH+"ak="+account.getTaccountid()+"&timestamp="+timestamp+"&sign="+sign;
//        System.out.println(JSON.toJSONString(paramMap));
        String result =  HttpRequest.httpPostJson(url, paramMap, headMap);
        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("1".equals(json.getString("result"))) {
                ret.put("ret", 1);
                ret.put("positionId", json.getString("data"));
            } else {
                ret.put("msg", json.getString("error_msg"));
                ret.put("ret", json.get("result"));
//                logger.info(result);
                logger.info("cpAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("error_msg"));
            }
        }
        return ret;
    }


    /**
     * 新插屏 目前 广告铺开大小默认选择优选
     * @param vo
     * @param account
     * @return
     */
    private JSONObject xcpAdcode(KSAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);
        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        //请求参数
        TreeMap<String, Object> paramMap = new TreeMap<>();
        //sign参数
        TreeMap<String,Object> signMap = new TreeMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";

        signMap.put("timestamp",timestamp);
        signMap.put("ak",account.getTaccountid());
        signMap.put("sk",account.getTsecrect());


        paramMap.put("appId", account.getTappid());
        paramMap.put("name", vo.getName());
        /** 快手api增加bidding区分  -廖志坤 2023.01.05*/
        paramMap.put("priceStrategy", vo.getPriceStrategy());
        //竞价策略
        if ("2".equals(vo.getPriceStrategy())){
            if (BlankUtils.checkBlank(vo.getBidding_type())){
                ret.put("ret", 0);
                ret.put("msg","价格策略为实时竞价时,竞价策略必填");
                return ret;
            }
            paramMap.put("biddingStrategy",vo.getBidding_type());
        }

        //广告类型 13-插屏
        paramMap.put("adStyle", 23);
        //渲染方式 2-模版渲染 必填:2
        paramMap.put("renderType", 2);
        //渲染模板id 必填 9
        paramMap.put("templateId",9);
        //素材类型 1-竖版视频,2-横版视频 5-竖版图片 6-横版图片
        JSONArray materialTypeList = JSONArray.parseArray(vo.getMaterialTypeList());
        String[] materials ={"1","2","5","6"};
        if (!BlankUtils.checkBlank(vo.getMaterialTypeList())){
            for (int i =0;i<materialTypeList.size();i++){
                String n = String.valueOf(materialTypeList.get(i));
                if(!Arrays.asList(materials).contains(n)){
                    ret.put("ret", 0);
                    ret.put("msg","插屏广告位,素材类型 广告位创意类型 1-竖版视频,2-横版视频 5-竖版图片 6-横版图片");
                    return ret;
                }
            }
        }
        paramMap.put("materialTypeList",materialTypeList);
        paramMap.put("adRolloutSize","3");

        //sign
        String sign = sign(KS_CODE_CREATE_URL_PATH,signMap);

        String url = KS_REQ_URL+KS_CODE_CREATE_URL_PATH+"ak="+account.getTaccountid()+"&timestamp="+timestamp+"&sign="+sign;
//        System.out.println(JSON.toJSONString(paramMap));
        String result =  HttpRequest.httpPostJson(url, paramMap, headMap);
        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("1".equals(json.getString("result"))) {
                ret.put("ret", 1);
                ret.put("positionId", json.getString("data"));
            } else {
                ret.put("msg", json.getString("error_msg"));
                ret.put("ret", json.get("result"));
//                logger.info(result);
                logger.info("cpAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("error_msg"));
            }
        }
        return ret;
    }

    /**
     * 激励视频广告位
     *
     * @param vo
     * @return
     */
    private static JSONObject jlspAdcode(KSAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);
        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        //请求参数
        TreeMap<String, Object> paramMap = new TreeMap<>();
        //sign参数
        TreeMap<String,Object> signMap = new TreeMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";

        signMap.put("timestamp",timestamp);
        signMap.put("ak",account.getTaccountid());
        signMap.put("sk",account.getTsecrect());


        paramMap.put("appId", account.getTappid());
        paramMap.put("name", vo.getName());

        /** 快手api增加bidding区分  -廖志坤 2023.01.05*/
        paramMap.put("priceStrategy", vo.getPriceStrategy());
        //竞价策略
        if ("2".equals(vo.getPriceStrategy())){
            if (BlankUtils.checkBlank(vo.getBidding_type())){
                ret.put("ret", 0);
                ret.put("msg","价格策略为实时竞价时,竞价策略必填");
                return ret;
            }
            paramMap.put("biddingStrategy",vo.getBidding_type());
        }
        
        //广告类型 2-激励视频
        paramMap.put("adStyle", 2);
        //渲染方式 1-自渲染 必填:1
        paramMap.put("renderType", 1);
        //视频播放方向 1-竖屏,2-横屏
        JSONArray materialTypeList = JSONArray.parseArray(vo.getMaterialTypeList());
        if (materialTypeList.size()>1){
            ret.put("ret", 0);
            ret.put("msg","激励视频 视频播放方向 1-竖屏,2-横屏 只能二选一");
            return ret;
        }
        String[] materials ={"1","2"};
        if (!BlankUtils.checkBlank(vo.getMaterialTypeList())){
            for (int i =0;i<materialTypeList.size();i++){
                String n = String.valueOf(materialTypeList.get(i));
                if(!Arrays.asList(materials).contains(n)){
                    ret.put("ret", 0);
                    ret.put("msg","激励视频,视频播放方向 1-竖屏,2-横屏 只能二选一");
                    return ret;
                }
            }
        }
        paramMap.put("materialTypeList",materialTypeList);
        //奖励名称 1-虚拟金币, 2-积分,3-生命, 4-体力值, 5-通关机会, 6-新关卡机会, "7-阅读币, 8-新章节（小说类）,9-观影币, 10-观看机会, 11-其他
        paramMap.put("rewardedType",Integer.parseInt(vo.getRewardedType()));
        //奖励数量
        paramMap.put("rewardedNum",Integer.parseInt(vo.getRewardedNum()));
        //奖励发放设置
        paramMap.put("callbackStatus",Integer.parseInt(vo.getCallbackStatus()));
        //回调url
        if ("1".equals(vo.getCallbackStatus())){
            if (BlankUtils.checkBlank(vo.getCallbackUrl())){
                ret.put("ret", 0);
                ret.put("msg","激励视频,奖励发放设置为服务端创建时 必填服务器回调url");
                return ret;
            }
            paramMap.put("callbackUrl",vo.getCallbackUrl());
        }
        //sign
        String sign = sign(KS_CODE_CREATE_URL_PATH,signMap);

        String url = KS_REQ_URL+KS_CODE_CREATE_URL_PATH+"ak="+account.getTaccountid()+"&timestamp="+timestamp+"&sign="+sign;
        String result =  HttpRequest.httpPostJson(url, paramMap, headMap);

        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("1".equals(json.getString("result"))) {
                ret.put("ret", 1);
                ret.put("positionId", json.getString("data"));
            } else {
                ret.put("msg", json.getString("error_msg"));
                ret.put("ret", json.get("result"));
                logger.info("jlspAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("message"));
            }
        }
        return ret;
    }



    /**
     * 全屏视频广告位
     *
     * @param vo
     * @return
     */
    private static JSONObject qpspAdcode(KSAdcodeVo vo, AdCodeAccountVo account) {
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);
        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");
        //请求参数
        TreeMap<String, Object> paramMap = new TreeMap<>();
        //sign参数
        TreeMap<String,Object> signMap = new TreeMap<>();
        String timestamp = System.currentTimeMillis() / 1000 + "";

        signMap.put("timestamp",timestamp);
        signMap.put("ak",account.getTaccountid());
        signMap.put("sk",account.getTsecrect());


        paramMap.put("appId", account.getTappid());
        paramMap.put("name", vo.getName());

        /** 快手api增加bidding区分  -廖志坤 2023.01.05*/
        paramMap.put("priceStrategy", vo.getPriceStrategy());
        //竞价策略
        if ("2".equals(vo.getPriceStrategy())){
            if (BlankUtils.checkBlank(vo.getBidding_type())){
                ret.put("ret", 0);
                ret.put("msg","价格策略为实时竞价时,竞价策略必填");
                return ret;
            }
            paramMap.put("biddingStrategy",vo.getBidding_type());
        }
        
        //广告类型 3-全屏视频
        paramMap.put("adStyle", 3);
        //渲染方式 1-自渲染 必填:1
        paramMap.put("renderType", 1);
        //视频播放方向 1-竖屏,2-横屏
        JSONArray materialTypeList = JSONArray.parseArray(vo.getMaterialTypeList());
        if (materialTypeList.size()>1){
            ret.put("ret", 0);
            ret.put("msg","全屏视频 视频播放方向 1-竖屏,2-横屏 只能二选一");
            return ret;
        }
        String[] materials ={"1","2"};
        if (!BlankUtils.checkBlank(vo.getMaterialTypeList())){
            for (int i =0;i<materialTypeList.size();i++){
                String n = String.valueOf(materialTypeList.get(i));
                if(!Arrays.asList(materials).contains(n)){
                    ret.put("ret", 0);
                    ret.put("msg","全屏视频,视频播放方向 1-竖屏,2-横屏 只能二选一");
                    return ret;
                }
            }
        }
        paramMap.put("materialTypeList",materialTypeList);

        //sign
        String sign = sign(KS_CODE_CREATE_URL_PATH,signMap);

        String url = KS_REQ_URL+KS_CODE_CREATE_URL_PATH+"ak="+account.getTaccountid()+"&timestamp="+timestamp+"&sign="+sign;
        String result =  HttpRequest.httpPostJson(url, paramMap, headMap);

        if (!BlankUtils.checkBlank(result)) {
            JSONObject json = JSONObject.parseObject(result);
            if ("1".equals(json.getString("result"))) {
                ret.put("ret", 1);
                ret.put("positionId", json.getString("data"));
            } else {
                ret.put("msg", json.getString("error_msg"));
                ret.put("ret", json.get("result"));
                logger.info("qpspAdcode fail:" + JSON.toJSONString(vo) + "=====" + json.getString("message"));
            }
        }
        return ret;
    }

    /**
     * 快手-修改广告位
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("update")
    public Object updateKSAdcode(HttpServletRequest request,KSAdcodeVo vo){
        JSONObject ret = new JSONObject();
        ret.put("ret", 0);
        ret.put("msg", "操作失败");
        String token = request.getParameter("token");
        String handle = request.getParameter("handle");
        if (BlankUtils.checkBlank(token)&&!redisTemplate.hasKey(token)) {
            return ReturnJson.toErrorJson("登录token过期,请重新登录");
        }else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        //创建人
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        }else if(token.startsWith("waibaotoken")){
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_id();
        }
        //产品对应配置信息
        AdCodeAccountVo account = new AdCodeAccountVo();
        account.setAppid(vo.getAppid());
        account.setChannel(vo.getChannel());
        List<AdCodeAccountVo> list = adCodeConfigService.getAdCodeAccountList(account);
        account = list.stream().filter(t->t.getAppid().equals(vo.getAppid())&&t.getPlatform().equals(PLATFORM)).findFirst().orElse(null);
        if(account==null){
            ret.put("ret", 0);
            ret.put("msg", "修改广告位cpm失败,原因:该产品未配置变现平台账号");
            return ret;
        }
        vo.setModifyTime(username);
        vo.setModifyUser(username);
        //设置快手平台appid
        vo.setApp_id(account.getTappid());

        if ("cpm".equals(handle)){
            ret = updateCpm(account,vo);
        }
        try {
            if ("1".equals(ret.getString("ret"))){
                adCodeConfigService.updateKSCpm(vo);
            }
        }catch (Exception e){
            logger.error("ks update modify info error:",e);
        }

        return ret;
    }


    /**
     * 创建/修改广告位cpm
     * @param account
     * @param vo
     * @return
     */
    private static JSONObject updateCpm(AdCodeAccountVo account,KSAdcodeVo vo){
        JSONObject ret = new JSONObject();
        try {
            ret.put("ret",0);
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Content-Type", "application/json");
            //请求参数
            TreeMap<String, Object> paramMap = new TreeMap<>();
            //sign参数
            TreeMap<String,Object> signMap = new TreeMap<>();
            String timestamp = System.currentTimeMillis() / 1000 + "";

            signMap.put("timestamp",timestamp);
            signMap.put("ak",account.getTaccountid());
            signMap.put("sk",account.getTsecrect());
            String sign = sign(KS_AD_CODE_CPM_UPDATE_URL_PATH,signMap);
            paramMap.put("appId",vo.getApp_id());
            paramMap.put("positionId",Long.parseLong(vo.getPositionId()));
            //判断不能大于3000元
            if (new BigDecimal(vo.getCpm()).compareTo(new BigDecimal("3000"))>0){
                ret.put("msg","广告位价格输入值不合法,不能大于3000元");
                return ret;
            }
            paramMap.put("cpmFloor",Double.parseDouble(vo.getCpm()));
            String url = KS_REQ_URL+KS_AD_CODE_CPM_UPDATE_URL_PATH+"ak="+account.getTaccountid()+"&timestamp="+timestamp+"&sign="+sign;
            String result = HttpRequest.httpPostJson(url,paramMap,headMap);
            if (!BlankUtils.checkBlank(result)) {
                JSONObject json = JSONObject.parseObject(result);
                if ("1".equals(json.getString("result"))) {
                    ret.put("ret", 1);
                } else {
                    ret.put("msg", json.getString("error_msg"));
                    ret.put("ret", json.get("result"));
                    logger.info("ks updateCpm fail:" + JSON.toJSONString(paramMap) + "=====" + json.getString("error_msg"));
                }
            }
        }catch (Exception e){
            logger.error("ks updateCpm error:",e);
        }
        return ret;
    }


    /**
     * 创建/修改广告位cpm
     * @param request
     * @return
     */
    @RequestMapping("setCpm")
    public Object setCpm(HttpServletRequest request, AdCodeAccountVo vo){
        JSONObject ret = new JSONObject();
        String positionId = request.getParameter("positionId");
        String cpm = request.getParameter("cpm");
        AdCodeAccountVo account = new AdCodeAccountVo();
        account.setAppid(vo.getAppid());
        account.setChannel(vo.getChannel());
        List<AdCodeAccountVo> list = adCodeConfigService.getAdCodeAccountList(account);
        account = list.stream().filter(t->t.getAppid().equals(vo.getAppid())&&t.getPlatform().equals(PLATFORM)).findFirst().orElse(null);
        if (account==null){
            ret.put("ret", 0);
            ret.put("msg", "创建广告位失败,原因:该产品未配置变现平台账号");
            return ret;
        }
        JSONObject result = setCpm(account.getTappid(),positionId,cpm,account);
        if ("1".equals(result.getString("ret"))){
            //更新创建记录
            try {
                KSAdcodeVo po = new KSAdcodeVo();
                po.setPositionId(positionId);
                po.setCpm(cpm);
                adCodeConfigService.updateKSCpm(po);
            }catch (Exception e){
                logger.error("setCpm update error:",e);
            }
            ret.put("ret",1);
            ret.put("msg","ok");
        }else {
            ret.put("ret",0);
            ret.put("msg",result.getString("msg"));
        }
        return ret;
    }


    /**
     * 创建/修改广告位cpm
     * @param appId
     * @param positionId
     * @param cpm
     * @param account
     * @return
     */
    private static JSONObject setCpm(String appId, String positionId, String cpm, AdCodeAccountVo account){
        JSONObject ret = new JSONObject();
        try {
            ret.put("ret",0);
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Content-Type", "application/json");
            //请求参数
            TreeMap<String, Object> paramMap = new TreeMap<>();
            //sign参数
            TreeMap<String,Object> signMap = new TreeMap<>();
            String timestamp = System.currentTimeMillis() / 1000 + "";

            signMap.put("timestamp",timestamp);
            signMap.put("ak",account.getTaccountid());
            signMap.put("sk",account.getTsecrect());
            String sign = sign(KS_AD_CODE_CPM_UPDATE_URL_PATH,signMap);
            paramMap.put("appId",appId);
            paramMap.put("positionId",Long.parseLong(positionId));
            //判断不能大于3000元
            if (new BigDecimal(cpm).compareTo(new BigDecimal("3000"))>0){
                ret.put("msg","广告位价格输入值不合法,不能大于3000元");
                return ret;
            }
            paramMap.put("cpmFloor",Double.parseDouble(cpm));
            String url = KS_REQ_URL+KS_AD_CODE_CPM_UPDATE_URL_PATH+"ak="+account.getTaccountid()+"&timestamp="+timestamp+"&sign="+sign;
            String result = HttpRequest.httpPostJson(url,paramMap,headMap);
            if (!BlankUtils.checkBlank(result)) {
                JSONObject json = JSONObject.parseObject(result);
                if ("1".equals(json.getString("result"))) {
                    ret.put("ret", 1);
                } else {
                    ret.put("msg", json.getString("error_msg"));
                    ret.put("ret", json.get("result"));
                    logger.info("setCpm fail:" + JSON.toJSONString(paramMap) + "=====" + json.getString("error_msg"));
                }
            }
        }catch (Exception e){
            logger.error("setCpm error:",e);
        }
        return ret;
    }


    /**
     * 写入到广告配置表
     * @return
     */
    public JSONObject saveAdcodeToDnExtendAdconfig(KSAdcodeVo vo, AdCodeAccountVo account){
        JSONObject ret = new JSONObject();
        try {
            String sql = "insert into dn_extend_adconfig(appid,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser) "+
                    "values(#{obj.appid},#{obj.is_newuser},#{obj.user_group},#{obj.adpos_type},#{obj.strategy},#{obj.adsid},#{obj.statu},#{obj.ecpm},#{obj.priority},#{obj.rate},now(),now(),#{obj.cuser}) ";

            if (!BlankUtils.checkBlank(vo.getChannel())&&!"csj".equals(vo.getChannel())){
                sql = "insert into dn_extend_adconfig(appid,cha_id,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser) "+
                        "values(#{obj.appid},#{obj.cha_id},#{obj.is_newuser},#{obj.user_group},#{obj.adpos_type},#{obj.strategy},#{obj.adsid},#{obj.statu},#{obj.ecpm},#{obj.priority},#{obj.rate},now(),now(),#{obj.cuser}) ";
            }
            ExtendAdconfigVo config = new ExtendAdconfigVo();
            // 子渠道为 apple 则不填入
            config.setCha_id("apple".equals(vo.getChannel())? "" : vo.getChannel());
            config.setAppid(vo.getAppid());
            config.setIs_newuser("all");
            config.setUser_group("all");
            config.setAdpos_type(vo.getOpen_type());
            config.setStrategy(vo.getStrategy());


            String sdkAdType = vo.getSdk_ad_type();
            String appid = vo.getAppid();
            String adExtentionName = vo.getAdExtensionName();
            String adsid = prefix + "_" + sdkAdType + "_" + appid + "_" + adExtentionName;

            config.setAdsid(adsid);
            config.setStatu("1");
            //策略带rate 默认为0 其他默认值为3
            if (BlankUtils.checkBlank(vo.getCpm())){
                config.setEcpm(new BigDecimal("0"));
            }else {
                if (vo.getStrategy().contains("rate")){
                    config.setEcpm(new BigDecimal("0"));
                }else {
                    config.setEcpm(new BigDecimal(vo.getCpm()));
                }
            }

            //2022-09-01 开屏、banner、msg的无价格广告，在广告配置页面生成的预估ecpm默认为0；插屏、视频的无价格广告，在广告配置页面生成的预估ecpm默认为3
            String[] ecpm0 = {"splash","banner","msg"};
            if (Arrays.asList(ecpm0).contains(config.getAdpos_type())){
                if (BlankUtils.checkBlank(vo.getCpm())){
                    config.setEcpm(new BigDecimal("0"));
                }
            }

            String[] ecpm3 = {"plaque","video"};
            if (Arrays.asList(ecpm3).contains(config.getAdpos_type())){
                if (BlankUtils.checkBlank(vo.getCpm())){
                    config.setEcpm(new BigDecimal("3"));
                }
            }


            config.setPriority(0);
            config.setRate(100);
            config.setCuser(vo.getCreateUser());
            int result = adv2Service.execSqlHandle(sql, config);
            if (result>0){
                ret.put("ret",1);
                ret.put("msg","ok");
            }else {
                ret.put("ret",0);
                ret.put("ret","写入广告配置表失败");
            }
        }catch (Exception e){
            ret.put("ret",0);
            ret.put("ret","写入广告配置表失败");
            logger.error("saveAdcodeToDnExtendAdconfig error:",e);
        }
        return ret;
    }


    /**
     * 快手api加密
     * @param path
     * @param map
     * @return
     */
    private static String sign(String path,TreeMap<String, Object> map) {
        String sign = "";
        String param = "";
        for (Map.Entry<String, Object> m : map.entrySet()) {
            param +=m.getKey()+"="+m.getValue()+"&";
        }
        String signStr= path+param;
        if (!BlankUtils.checkBlank(signStr)&&signStr.endsWith("&")){
            sign = DigestUtils.md5Hex(signStr.substring(0,signStr.length()-1));
        }
        return sign;
    }

    private KSAdcodeVo getGameKSAdcode(String[] vals){
        KSAdcodeVo ki = new KSAdcodeVo();
        //应用
        ki.setAppid(vals[0]);
        //子渠道
        ki.setChannel(vals[1]);
        //sdk广告源样式 中文名称转成对应值
        String sdk_ad_type =SDK_AD_TYPE_MAP.get(vals[2]);
        ki.setSdk_ad_type(sdk_ad_type);
        //广告使用类型
        String open_type = OPEN_TYPE_MAP.get(vals[3]);
        ki.setOpen_type(open_type);
        //广告扩展名
        ki.setAdExtensionName(vals[4]);
        //备注
        ki.setRemark(vals[5]);
        //代码位名称
        ki.setName(vals[6]);
        //cpm设置
        ki.setCpm(vals[7]);
        //广告平台 vals[8]
        //平台appid
        ki.setApp_id(vals[9]);
        //广告类型 中文转成具体值
        String adStyle = AD_STYLE_MAP.get(vals[10]);
        ki.setAdStyle(adStyle);
        //渲染方式 中文转具体值
        String renderType = RENDER_TYPE_MAP.get(vals[11]);
        ki.setRenderType(renderType);
        //素材类型
        ki.setMaterialTypeList(vals[12]);
        //渲染模板id
        ki.setMultiTemplateParams(vals[13]);
        //视频播放方向 中文转成对应值 全屏视频 激励视频
        if ("2".equals(adStyle)||"3".equals(adStyle)){
            ki.setMaterialTypeList(vals[14]);
        }
        //奖励名称 中文转成对应值
        ki.setRewardedType(vals[15]);
        //奖励数量
        ki.setRewardedNum(vals[16]);
        //回调设置
        ki.setCallbackStatus(vals[17]);
        //回调url
        ki.setCallbackUrl(vals[18]);
        //跳过按钮
        ki.setSkipAdMode(vals[19]);
        //倒计时
        ki.setCountdownShow(vals[20]);
        //播放声音
        ki.setVoice(vals[21]);
        //策略
        if (!BlankUtils.checkBlank(vals[22])){
            ki.setStrategy(vals[22].trim());
        }
        // 价格策略：1-目标价，2实时竞价
        if (!BlankUtils.checkBlank(vals[23])){
            ki.setPriceStrategy(vals[23].trim());
        }
        ki.setBidding_type(vals[24]);
        return ki;
    }

    private KSAdcodeVo getToolKSAdcode(String[] vals){
        KSAdcodeVo ki = new KSAdcodeVo();
        //应用
        ki.setAppid(vals[0]);
        //子渠道
        ki.setChannel(vals[1]);
        //sdk广告源样式 中文名称转成对应值
        String sdk_ad_type =SDK_AD_TYPE_MAP.get(vals[2]);
        ki.setSdk_ad_type(sdk_ad_type);
        //广告使用类型
        String open_type = OPEN_TYPE_MAP.get(vals[3]);
        ki.setOpen_type(open_type);
        //广告扩展名
        ki.setAdExtensionName(vals[4]);
        //备注
        ki.setRemark(vals[5]);
        //代码位名称
        ki.setName(vals[6]);
        //cpm设置
        ki.setCpm(vals[7]);
        //广告类型 中文转成具体值
        String adStyle = AD_STYLE_MAP.get(vals[9]);
        ki.setAdStyle(adStyle);
        //渲染方式 中文转具体值
        String renderType = RENDER_TYPE_MAP.get(vals[10]);
        ki.setRenderType(renderType);
        //素材类型
        ki.setMaterialTypeList(vals[11]);
        //渲染模板id
        ki.setMultiTemplateParams(vals[12]);
        //视频播放方向 中文转成对应值 全屏视频 激励视频
        if ("2".equals(adStyle)||"3".equals(adStyle)){
            ki.setMaterialTypeList(vals[13]);
        }
        //奖励名称 中文转成对应值
        ki.setRewardedType(vals[14]);
        //奖励数量
        ki.setRewardedNum(vals[15]);
        //回调设置
        ki.setCallbackStatus(vals[16]);
        //回调url
        ki.setCallbackUrl(vals[17]);
        //跳过按钮
        ki.setSkipAdMode(vals[18]);
        //倒计时
        ki.setCountdownShow(vals[19]);
        //播放声音
        ki.setVoice(vals[20]);
        //策略
        if (!BlankUtils.checkBlank(vals[21])){
            ki.setStrategy(vals[21].trim());
        }
        // 价格策略：1-目标价，2实时竞价
        if (!BlankUtils.checkBlank(vals[22])){
            ki.setPriceStrategy(vals[22].trim());
        }
        ki.setBidding_type(vals[23]);
        return ki;
    }

}
