package com.wbgame.controller.game.set;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.game.GiftInfoVo;
import com.wbgame.service.AdService;
import com.wbgame.service.game.GiftService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.StringUtils;
import jxl.Sheet;
import jxl.Workbook;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname GiftConfigController
 * @Description TODO
 * @Date 2022/3/9 12:28
 */
@RequestMapping("/game/giftConfig")
@RestController
@CrossOrigin
public class GiftConfigController {

    private static Logger logger = LoggerFactory.getLogger(GiftConfigController.class);

    private static Map<String,String> GIFT_ITEM_TYPE = new HashMap<String,String>(){{
        put("1","新手礼包");
        put("2","限时礼包");
        put("3","活动礼包");
        put("4","常驻礼包");
    }};

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    GiftService giftService;

    @Autowired
    private AdService adService;

    @RequestMapping("getList")
    public Object getList(HttpServletRequest request, GiftInfoVo vo) {
        JSONObject ret = new JSONObject();
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize);

        List<GiftInfoVo> list = giftService.getGiftInfoList(vo);
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);
        for (GiftInfoVo po:list){
            //封装app_name
            if (appMap.containsKey(po.getAppid())) {
                Map<String, Object> appInfoMap = appMap.get(po.getAppid());
                po.setApp_name(appInfoMap.containsKey("app_name") ? appInfoMap.get("app_name").toString() : null);
            }
        }

        long size = ((Page) list).getTotal();
        ret.put("ret", 1);
        ret.put("msg", "ok");
        ret.put("data", list);
        ret.put("totalCount",size);
        return ret;
    }

    @RequestMapping("handle")
    public Object handle(HttpServletRequest request, GiftInfoVo vo) {
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String handle = request.getParameter("handle");
        String username = "";
        CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (currUserVo != null) {
            username = currUserVo.getLogin_name();
        }
        vo.setCreate_user(username);
        vo.setModify_user(username);
        vo.setCreate_time(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        vo.setModify_time(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        int result = 0;
        GiftInfoVo queryVo = new GiftInfoVo();
        queryVo.setAppid(vo.getAppid());
        List<GiftInfoVo> giftList = giftService.getGiftInfoList(queryVo);
        //校验金额数据格式校验
        if ("add".equals(handle) || "update".equals(handle)) {
            if(!BlankUtils.checkBlank(vo.getCheck_money())) {
                try {
                    if (vo.getCheck_money().contains("，")) {
                        return ReturnJson.toErrorJson("请使用英文的逗号分隔");
                    }
                    String[] split = vo.getCheck_money().split(",");
                    for (String s : split) {
                        if (!(s.matches("\\d+") && Integer.parseInt(s) > 0)) {
                            return ReturnJson.toErrorJson("校验金额必须为整数并且大于0");
                        }
                    }
                }catch (Exception e) {
                    return ReturnJson.toErrorJson("校验金额数据格式错误");
                }
            }
        }
        if ("add".equals(handle)) {
            GiftInfoVo exitVo = giftList
                    .stream()
                    .filter(t->t.getAppid().equals(vo.getAppid())&&t.getGift_item_id().equals(vo.getGift_item_id()))
                    .findFirst()
                    .orElse(null);
            if (exitVo!=null){
                return ReturnJson.toErrorJson("已经存在相同产品id+礼包id记录");
            }
            result = giftService.saveGiftInfo(vo);
        } else if ("update".equals(handle)) {
            GiftInfoVo exitVo = giftList
                    .stream()
                    .filter(t->t.getAppid().equals(vo.getAppid())&&t.getGift_item_id().equals(vo.getGift_item_id())&&!t.getId().equals(vo.getId()))
                    .findFirst()
                    .orElse(null);
            if (exitVo!=null){
                return ReturnJson.toErrorJson("已经存在相同产品id+礼包id记录");
            }
            //先删除这条记录
            giftService.delGiftInfo(vo);
            //然后写入
            result = giftService.updateGiftInfo(vo);
        } else if ("del".equals(handle)) {
            result = giftService.delGiftInfo(vo);
        }else if ("copy".equals(handle)){
            //批量复制 如果有选单条
            List<GiftInfoVo> fromList = giftService.getGiftInfoList(vo);
            String toAppid = request.getParameter("toAppid");
            for (GiftInfoVo po : fromList) {
                po.setAppid(toAppid);
                po.setCreate_user(username);
                po.setCreate_time(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
            }
            result = giftService.batchSaveGiftInfo(fromList);
        }
        if (result > 0) {
            return ReturnJson.success();
        } else {
            return ReturnJson.toErrorJson("操作失败!");
        }
    }

    /**
     * 批量导入
     * @param request
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping("batchImport")
    public Object batchImport(HttpServletRequest request, @RequestParam(value = "fileName") MultipartFile file) throws Exception {
        JSONObject ret = new JSONObject();
        ret.put("ret",0);
        ret.put("msg","操作失败!");
        String token = request.getParameter("token");

        String username = "";
        CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (currUserVo != null) {
            username = currUserVo.getLogin_name();
        }

        if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
            //获取所有的产品
            String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
            Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);
            List<GiftInfoVo> list = new ArrayList<>();
            TreeMap<String,String> map = new TreeMap<>();
            Workbook workbook = Workbook.getWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheet(0);

            int column = sheet.getColumns();
            int row = sheet.getRows();
            for (int r = 1; r < row; r++) {
                try {
                    GiftInfoVo ki = new GiftInfoVo();
                    String[] vals = new String[column];
                    for (int c = 0; c < column; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    boolean blankMatch = Arrays.stream(vals).allMatch(BlankUtils::checkBlank);
                    if (blankMatch) {
                        //当前条数据都为空，直接过滤
                        continue;
                    }
                    //导入数据相关字段判空
                    checkImportFields(appMap, map, r, vals);
                    //校验日期字段
                    DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
                    String online_time = vals[6];
                    String offline_time = vals[7];
                    if (!BlankUtils.checkBlank(online_time)) {
                        boolean flag = true;
                        try{
                            online_time = DateTime.parse(online_time, format).toString("yyyy-MM-dd");
                        }catch (Exception e){
                            flag = false;
                        }
                        if (flag){
                            ki.setOnline_time(online_time);
                        }else {
                            map.put(r+"行,数据上线时间格式不正确","");
                        }

                    }
                    if (!BlankUtils.checkBlank(offline_time)) {
                        boolean flag = true;
                        try{
                            offline_time = DateTime.parse(offline_time, format).toString("yyyy-MM-dd");
                        }catch (Exception e){
                            flag = false;
                        }
                        if (flag){
                            ki.setOffline_time(offline_time);
                        }else {
                            map.put(r+"行,数据下线时间格式不正确","");
                        }
                    }
                    ki.setCheck_money(vals[12]);
                    if(!BlankUtils.checkBlank(ki.getCheck_money())) {
                        try {
                            if (ki.getCheck_money().contains("，")) {
                                return ReturnJson.toErrorJson("请使用英文的逗号分隔");
                            }
                            String[] split = ki.getCheck_money().split(",");
                            for (String s : split) {
                                if (!(s.matches("\\d+") && Integer.parseInt(s) > 0)) {
                                    return ReturnJson.toErrorJson("校验金额必须为整数并且大于0");
                                }
                            }
                        }catch (Exception e) {
                            return ReturnJson.toErrorJson("校验金额数据格式错误");
                        }
                    }

                    ki.setAppid(vals[0].trim());
                    ki.setGift_item_id(vals[1].trim());
                    ki.setGift_item_name(vals[2]);
                    ki.setGift_item_content(vals[3]);
                    ki.setCurrency_type(vals[4]);
                    ki.setPrice(vals[5].trim());
                    ki.setOnline_time(online_time);
                    ki.setOffline_time(offline_time);
                    ki.setGift_period(vals[8]);
                    ki.setGift_item_type(vals[9]);
                    ki.setGift_activity_id(vals[10]);
                    ki.setGift_buy_type(vals[11]);
                    ki.setCreate_user(username);
                    ki.setCreate_time(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                    list.add(ki);
                } catch (Exception e) {
                    logger.error("gift config batchImport error:", e);
                    return ReturnJson.toErrorJson("导入出错,请联系管理员!");
                }
            }
            if (map.size()>0){
                StringBuffer sb = new StringBuffer();
                for (Map.Entry<String,String> errMap:map.entrySet()){
                    sb.append("第"+errMap.getKey()+"\t");
                }
                return ReturnJson.toErrorJson(sb.toString());
            }
            int succ = 0;
            for (GiftInfoVo vo:list){
                giftService.saveGiftInfo(vo);
                succ++;
            }
//            int succ = giftService.batchSaveGiftInfo(list);
            if (succ>0){
                return ReturnJson.success();
            }else {
                return ReturnJson.toErrorJson("导入失败!");
            }
        }else{
            ret.put("msg","文件为空或者文件格式不正确");
        }
        return ret;
    }

    private static void checkImportFields(Map<String, Map<String, Object>> appMap, TreeMap<String, String> map, int r, String[] vals) {
        //appid--应用
        if (BlankUtils.checkBlank(vals[0].trim())) {
            map.put(r +"行,数据产品不能为空","");
        } else {
            //应用是否存在校验
            if (!appMap.containsKey(vals[0].trim())) {
                map.put(r +"行,数据产品不存在","");
            }
        }
        //Gift_item_id--礼包id
        if (BlankUtils.checkBlank(vals[1].trim())) {
            map.put(r +"行,数据礼包id不能为空","");
        }
        //Gift_item_name--礼包名称
        if (BlankUtils.checkBlank(vals[2].trim())) {
            map.put(r +"行,数据礼包名称不能为空","");
        }
        //Gift_item_content- 礼包内容
        if (BlankUtils.checkBlank(vals[3])) {
            map.put(r +"行,数据礼包内容不能为空","");
        }
        //货币类型
        if (BlankUtils.checkBlank(vals[4])) {
            map.put(r +"行,数据货币类型不能为空","");
        } else {
            if (!"CNY".equals(vals[4]) && !"USD".equals(vals[4])) {
                map.put(r +"行,数据货币类型格式不正确","");
            }
        }
        //礼包分类
        if (BlankUtils.checkBlank(vals[9])) {
            map.put(r +"行,数据礼包类型不能为空","");
        } else {
            if (!GIFT_ITEM_TYPE.containsKey(vals[9])) {
                map.put(r +"行,数据礼包类型格式不正确","");
            }
        }
        if (!StringUtils.isNumberByRegex(vals[5])){
            map.put(r+"行,货币单价(分)格式不正确","");
        } else {
            //校验是否大于0
            try {
                if (Integer.parseInt(vals[5].trim()) <= 0) {
                    map.put(r+"行,货币单价(分)必须大于0的数值","");
                }
            } catch (NumberFormatException e) {
                map.put(r+"行,货币单价(分)必须为整数并且大于0","");
            }
        }
    }

    /**
     * 礼包管理-导出
     * @param request
     */
    @RequestMapping("exportGiftConfig")
    public void exportGiftConfig(HttpServletRequest request, HttpServletResponse response,GiftInfoVo vo){

        String export_file_name = request.getParameter("export_file_name");
        List<GiftInfoVo> list = giftService.getGiftInfoList(vo);

        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);
        for (GiftInfoVo po:list){
            po.setGift_item_type(GIFT_ITEM_TYPE.get(po.getGift_item_type())!=null?GIFT_ITEM_TYPE.get(po.getGift_item_type()):po.getGift_item_type());
            //封装app_name
            if (appMap.containsKey(po.getAppid())) {
                Map<String, Object> appInfoMap = appMap.get(po.getAppid());
                po.setApp_name(appInfoMap.containsKey("app_name") ? appInfoMap.get("app_name").toString() : null);
            }
        }

        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        try {
            ExportExcelUtil.exportXLSX2(response,list,headerMap, export_file_name + "_" + DateTime.now().toString("yyyyMMdd")+".xlsx");
        }catch (Exception e){
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
    }
}
