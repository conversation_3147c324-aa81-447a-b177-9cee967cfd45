package com.wbgame.controller.game.query;

import com.wbgame.aop.ApiNeed;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.game.report.ActiveActivityReportVo;
import com.wbgame.pojo.game.report.ActiveCurrencyReportVo;
import com.wbgame.service.game.AppService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname ActiveActivityController
 * @Description TODO
 * @Date 2023/5/25 15:36
 */
@RequestMapping("/game/currency")
@RestController
@CrossOrigin
@Api(tags ="活跃度用户代币存量监控")
public class ActiveCurrencyController {

    @Autowired
    AppService appService;

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping("list")
    @LoginCheck
    public Result<PageResult<ActiveCurrencyReportVo>> getActiveCurrencyList(@ApiNeed({
            "start_date",
            "end_date",
            "appidList",
            "limit",
            "order_str",
            "group_idList",
            "start",

    }) @Validated(QueryGroup.class) ActiveCurrencyReportVo query) {
        return appService.getActiveCurrencyList(query);
    }

    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping(value = "/export")
    public void exportActiveCurrencyList(HttpServletResponse response, @ApiNeed({
            "start_date",
            "end_date",
            "appidList",
            "order_str",
            "group_idList",
    })ActiveCurrencyReportVo query) {
        appService.exportActiveCurrency(query,response);
    }


    @ApiOperation(value = "查询-图表", notes = "图表")
    @PostMapping(value = "/listChart")
    @LoginCheck
    public List<ActiveCurrencyReportVo> getActiveCurrencyChart(@ApiNeed({
            "start_date",
            "end_date",
            "appidList",
            "order_str",
            "group_idList",
    })ActiveCurrencyReportVo query) {
        return appService.getActiveCurrencyChart(query);
    }

}
