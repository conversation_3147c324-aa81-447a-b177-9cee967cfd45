package com.wbgame.controller.game.query;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.controller.advert.query.total.AppRevenueTotalController;
import com.wbgame.pojo.game.report.PayDailyVo;
import com.wbgame.pojo.game.report.query.PayDailyQueryVo;
import com.wbgame.service.game.GiftService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import com.wbgame.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Classname PayDailyController
 * @Description TODO
 * @Date 2022/10/26 17:51
 */
@RequestMapping("/game/payDaily")
@RestController
@CrossOrigin
@Api(tags = "每日新老用户付费")
public class PayDailyController {

    Logger logger = LoggerFactory.getLogger(AppRevenueTotalController.class);

    @Autowired
    private GiftService giftService;

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping("get")
    public Result<PageResult<PayDailyVo>> getList(@Validated(QueryGroup.class) PayDailyQueryVo query){
        if (query.getStart()==null||query.getLimit()==null){
            return ResultUtils.failure("分页参数必传");
        }
        PageHelper.startPage(query.getStart(), query.getLimit());
        List<PayDailyVo> list = giftService.getPayDailyList(query);

        if (list.size()>0){
            list.forEach(t->{
                keepTwoDecimal(t);
                if (query.getDate_group().equals("weekly")) {
                    t.setTdate(weekToDay(t.getTdate()));
                }
            });
        }

        PageResult<PayDailyVo> page = PageResult.page(new PageInfo<>(list));
        PayDailyVo total = giftService.getPayDailySum(query);
        keepTwoDecimal(total);
        return ResultUtils.success(Constants.OK,page,total,page.getTotalSize());
    }

    /**
     * Turn first day + last day of the week into day.
     * <AUTHOR> <<EMAIL>>
     * @param week The week that needs to be converted into a day.
     * @return The day corresponding to first of the week or empty string.
     */
    private static SimpleDateFormat weekFormat = new SimpleDateFormat("yyyy-ww");
    private static SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd");
    public static String weekToDay(String week) {
        try {
            Date date = weekFormat.parse(week);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
            calendar.add(Calendar.DATE, 1);
            String first = dayFormat.format(calendar.getTime());
            calendar.add(Calendar.DATE, 6);
            String last = dayFormat.format(calendar.getTime());

            return first + "至" + last;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }


    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    @PostMapping(value = "/export")
    public void export(HttpServletResponse response,
                       @Validated(QueryGroup.class) PayDailyQueryVo query) {
        List<PayDailyVo> list = giftService.getPayDailyList(query);
        if (list.size()>0){
            list.forEach(t->{
                keepTwoDecimal(t);
                if (query.getDate_group().equals("weekly")) {
                    t.setTdate(weekToDay(t.getTdate()));
                }
            });
        }
        Map<String,String> head = new LinkedHashMap<>();
        if (query.getValue()!=null){
            try {
                String[] split = query.getValue().split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    head.put(s[0],s[1]);
                }
            }catch (Exception e) {
                Asserts.fail("自定义列导出异常");
            }
        }
        String fileName = query.getExport_file_name()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX2(response,list,head,fileName);
    }


    /**
     * 获取每日新老付费图表数据
     * @param query
     * @return
     */
    @ApiOperation(value = "图表", notes = "图表", httpMethod = "POST")
    @PostMapping("graph")
    public Object getGraphList(PayDailyQueryVo query){
        JSONObject ret = new JSONObject();
        JSONObject data = new JSONObject();
        List<PayDailyVo> list = giftService.getPayDailyList(query);
        Map<String,PayDailyVo> dataMap = new LinkedHashMap<>();
        for (PayDailyVo vo:list){
            keepTwoDecimal(vo);
            dataMap.put(vo.getTdate(),vo);
        }
        //组装x轴
        JSONArray xData = new JSONArray();
        //组装具体数据 key 属性值
        Map<String,JSONArray> detailDataMap = new HashMap<>();

        JSONObject dataInfo = new JSONObject();
        for (Map.Entry<String,PayDailyVo> map:dataMap.entrySet()){
            xData.add(map.getKey());
            PayDailyVo each = map.getValue();
            Map<String,String> eachFieldMap = getObjectFields(each);
            for (Map.Entry<String,String> eachMap:eachFieldMap.entrySet()){
                JSONArray dataArray = detailDataMap.get(eachMap.getKey());
                if (dataArray == null){
                    dataArray = new JSONArray();
                    dataArray.add(eachMap.getValue());
                }else {
                    dataArray.add(eachMap.getValue());
                }
                detailDataMap.put(eachMap.getKey(),dataArray);
            }
        }
        for (Map.Entry<String,JSONArray> dataInfoMap:detailDataMap.entrySet()){
            dataInfo.put(dataInfoMap.getKey(),dataInfoMap.getValue());
        }

        data.put("xData",xData);
        data.put("dataInfo",dataInfo);
        ret.put("ret", 1);
        ret.put("msg", "ok");
        ret.put("data", data);
        return ret;
    }


    /**
     * 保存两位小数
     * @param source
     * @return
     */
    public PayDailyVo keepTwoDecimal(PayDailyVo source) {
        if (source != null) {
            Class clazz = source.getClass();
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                //设置属性是可以访问的(私有的也可以)
                field.setAccessible(true);
                Object value = null;
                try {
                    value = field.get(source);
                    // 属性值不为空且包含小数点则保留一位小数
                    if (value != null&&value.toString().contains(".")&& StringUtils.isNumberByRegex(value.toString())) {
                        if (field.getName().contains("ratio")){
                            value = new BigDecimal(value.toString()).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).toString()+"%";
                        }else {
                            value = new BigDecimal(value.toString()).setScale(2, RoundingMode.HALF_UP).toString();
                        }
                        field.set(source,value);
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }

        }
        return source;
    }


    private Map<String,String> getObjectFields(Object vo){
        Map<String,String> fieldMap = new HashMap<>();
        try {
            Field[] fields = vo.getClass().getDeclaredFields();
            for (Field field : fields) {
                //设置允许通过反射访问私有变量
                field.setAccessible(true);
                //获取字段属性名称
                String name = field.getName();
                String value = field.get(vo)!=null?field.get(vo).toString():"";
                //其他自定义操作
                fieldMap.put(name,value);
            }
        }catch (Exception e){
            logger.error("getObjectFields error:",e);
        }
       return fieldMap;
    }

    public static void main(String[] args) {
        PayDailyVo vo = new PayDailyVo();
        // voucher是目标对象
        try{
            //通过getDeclaredFields()方法获取对象类中的所有属性（含私有）
            Field[] fields = vo.getClass().getDeclaredFields();
            for (Field field : fields) {
                //设置允许通过反射访问私有变量
                field.setAccessible(true);
                //获取字段的值

                //获取字段属性名称
                String name = field.getName();

                String value = field.get(vo)!=null?field.get(vo).toString():"";
                //其他自定义操作
                System.out.println(name+"==="+value);
            }
        }
        catch (Exception ex){
            //处理异常
            System.out.println(ex.getMessage());
        }
    }
}
