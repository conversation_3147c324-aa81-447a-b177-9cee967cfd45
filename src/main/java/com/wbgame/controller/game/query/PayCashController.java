package com.wbgame.controller.game.query;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.pojo.game.PayCashAnalysisVo;
import com.wbgame.service.AdService;
import com.wbgame.service.mobile.UserPayInfoService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname PayCashController
 * @Description TODO
 * @Date 2022/4/26 14:58
 */
@CrossOrigin
@RestController
@RequestMapping("/game/cashAnalysis")
public class PayCashController {

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    UserPayInfoService userPayInfoService;

    @Autowired
    private AdService adService;

    /**
     * 流水分析-当日数据查询
     * @param request
     * @return
     */
    @RequestMapping("getList")
    public Object getList(HttpServletRequest request){
        JSONObject ret = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize);
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid =request.getParameter("appid");
        String download_channel = request.getParameter("download_channel");
        String pid = request.getParameter("pid");
        String group = request.getParameter("group");
        //处理分组关联
        String appid_group = "";
        String download_channel_group ="";
        String pid_group = "";

        if (!BlankUtils.checkBlank(group)){
            String[] groups = group.split(",");
            for (String str:groups){
                if ("pid".equals(str)){
                    pid_group ="pid";
                }
                if ("appid".equals(str)){
                    appid_group = "appid";
                }
                if ("download_channel".equals(str)){
                    download_channel_group = "download_channel";
                }

            }
        }
        String[] replaceGroup ={appid_group,download_channel_group,pid_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str:replaceGroup){
            if (!BlankUtils.checkBlank(str)){
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size()>0){
            for (int i = 0; i < grouplist.size(); i++){
                if(i != grouplist.size() - 1){
                    newGroup.append(grouplist.get(i)).append(",");
                }else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }

        String order_str = request.getParameter("order_str");

        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid",appid);
        paramMap.put("download_channel",download_channel);
        paramMap.put("pid",pid);
        paramMap.put("group",group);
        paramMap.put("unionGroup",newGroup.toString());
        paramMap.put("appid_group",appid_group);
        paramMap.put("pid_group",pid_group);
        paramMap.put("download_channel_group",download_channel_group);
        paramMap.put("order_str",order_str);

        List<PayCashAnalysisVo> list = userPayInfoService.getPayCashList(paramMap);
        if (list.size()>0){
            for (PayCashAnalysisVo vo:list){
                keepTwoDecimal(vo);
            }
        }


        PayCashAnalysisVo total = userPayInfoService.getPayCashSum(paramMap);
        keepTwoDecimal(total);

        long size = ((Page) list).getTotal();
        ret.put("ret",1);
        ret.put("msg","ok");
        ret.put("data",list);
        ret.put("total",total);
        ret.put("totalSize",size);
        return ret;
    }

    /**
     * 流水分析-当日数据-导出
     * @param request
     * @param response
     */
    @RequestMapping("export")
    public void exportList(HttpServletRequest request, HttpServletResponse response){
        Map<String,String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid =request.getParameter("appid");
        String download_channel = request.getParameter("download_channel");
        String pid = request.getParameter("pid");
        String group = request.getParameter("group");
        //处理分组关联
        String appid_group = "";
        String download_channel_group ="";
        String pid_group = "";

        if (!BlankUtils.checkBlank(group)){
            String[] groups = group.split(",");
            for (String str:groups){
                if ("pid".equals(str)){
                    pid_group ="pid";
                }
                if ("appid".equals(str)){
                    appid_group = "appid";
                }
                if ("download_channel".equals(str)){
                    download_channel_group = "download_channel";
                }

            }
        }
        String[] replaceGroup ={appid_group,download_channel_group,pid_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str:replaceGroup){
            if (!BlankUtils.checkBlank(str)){
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size()>0){
            for (int i = 0; i < grouplist.size(); i++){
                if(i != grouplist.size() - 1){
                    newGroup.append(grouplist.get(i)).append(",");
                }else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }

        String order_str = request.getParameter("order_str");

        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid",appid);
        paramMap.put("download_channel",download_channel);
        paramMap.put("pid",pid);
        paramMap.put("group",group);
        paramMap.put("unionGroup",newGroup.toString());
        paramMap.put("appid_group",appid_group);
        paramMap.put("pid_group",pid_group);
        paramMap.put("download_channel_group",download_channel_group);
        paramMap.put("order_str",order_str);

        List<PayCashAnalysisVo> list = userPayInfoService.getPayCashList(paramMap);

        // 数据内容
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";;
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        for (PayCashAnalysisVo vo:list){
            keepTwoDecimal(vo);
            if (!BlankUtils.checkBlank(vo.getAppid())){
                vo.setAppid(appMap.get(vo.getAppid())!=null?appMap.get(vo.getAppid()).get("app_name")+"-"+vo.getAppid():"");
            }
        }
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(export_file_name)){
            export_file_name = "流水分析-当日";
        }
        ExportExcelUtil.exportXLSX2(response,list,headerMap, export_file_name + "_"+ DateTime.now().toString("yyyyMMdd")+".xlsx");
    }


    /**
     * 流水分析累计-查询
     * @param request
     * @return
     */
    @RequestMapping("getTotalList")
    public Object getTotalList(HttpServletRequest request) {
        JSONObject ret = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize);
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String download_channel = request.getParameter("download_channel");
        String pid = request.getParameter("pid");
        String group = request.getParameter("group");
        //处理分组关联
        String appid_group = "";
        String download_channel_group = "";
        String pid_group = "";

        if (!BlankUtils.checkBlank(group)) {
            String[] groups = group.split(",");
            for (String str : groups) {
                if ("pid".equals(str)) {
                    pid_group = "pid";
                }
                if ("appid".equals(str)) {
                    appid_group = "appid";
                }
                if ("download_channel".equals(str)) {
                    download_channel_group = "download_channel";
                }

            }
        }
        String[] replaceGroup = {appid_group, download_channel_group, pid_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str : replaceGroup) {
            if (!BlankUtils.checkBlank(str)) {
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size() > 0) {
            for (int i = 0; i < grouplist.size(); i++) {
                if (i != grouplist.size() - 1) {
                    newGroup.append(grouplist.get(i)).append(",");
                } else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }

        String order_str = request.getParameter("order_str");

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("appid", appid);
        paramMap.put("download_channel", download_channel);
        paramMap.put("pid", pid);
        paramMap.put("group", group);
        paramMap.put("unionGroup", newGroup.toString());
        paramMap.put("appid_group", appid_group);
        paramMap.put("pid_group", pid_group);
        paramMap.put("download_channel_group", download_channel_group);
        paramMap.put("order_str", order_str);

        List<String> dateList = DateUtil.getDays(start_date,end_date);
        List<PayCashAnalysisVo> list = new ArrayList<>();
        //for (String date:dateList){
        //    paramMap.put("tdate", date);
        //    List<PayCashAnalysisVo> eachList = userPayInfoService.getPayCashTotalList(paramMap);
        //    if (eachList.size()>0){
        //        list.addAll(eachList);
        //    }
        //}

        PayCashAnalysisVo total = new PayCashAnalysisVo();
        for (int i = 0; i < dateList.size(); i++) {

            String date = dateList.get(i);

            paramMap.put("tdate", date);
            List<PayCashAnalysisVo> eachList = userPayInfoService.getPayCashTotalList(paramMap);
            if (eachList.size()>0){
                list.addAll(eachList);
            }

            // 汇总
            if (i == dateList.size() -1) {

                // 用户数
                int total_pay_num_1 = 0;
                int total_pay_num_2 = 0;
                int total_pay_num_3 = 0;
                int total_pay_num_4 = 0;
                int total_pay_num_5 = 0;
                int total_pay_num_6 = 0;
                int total_pay_num_7 = 0;
                int total_pay_num_8 = 0;
                int total_pay_num_9 = 0;
                int total_pay_num_10 = 0;
                int total_pay_num_11 = 0;
                int total_pay_num_12 = 0;
                int total_pay_num_13 = 0;
                int total_pay_num_14 = 0;
                int total_pay_num_15 = 0;

                // 收入
                BigDecimal total_pay_money_1  = BigDecimal.ZERO;
                BigDecimal total_pay_money_2  = BigDecimal.ZERO;
                BigDecimal total_pay_money_3  = BigDecimal.ZERO;
                BigDecimal total_pay_money_4  = BigDecimal.ZERO;
                BigDecimal total_pay_money_5  = BigDecimal.ZERO;
                BigDecimal total_pay_money_6  = BigDecimal.ZERO;
                BigDecimal total_pay_money_7  = BigDecimal.ZERO;
                BigDecimal total_pay_money_8  = BigDecimal.ZERO;
                BigDecimal total_pay_money_9  = BigDecimal.ZERO;
                BigDecimal total_pay_money_10  = BigDecimal.ZERO;
                BigDecimal total_pay_money_11  = BigDecimal.ZERO;
                BigDecimal total_pay_money_12  = BigDecimal.ZERO;
                BigDecimal total_pay_money_13  = BigDecimal.ZERO;
                BigDecimal total_pay_money_14  = BigDecimal.ZERO;
                BigDecimal total_pay_money_15  = BigDecimal.ZERO;
                for (PayCashAnalysisVo vo : eachList) {

                    total_pay_num_1 += Integer.parseInt(vo.getTotal_pay_num_1());
                    total_pay_num_2 += Integer.parseInt(vo.getTotal_pay_num_2());
                    total_pay_num_3 += Integer.parseInt(vo.getTotal_pay_num_3());
                    total_pay_num_4 += Integer.parseInt(vo.getTotal_pay_num_4());
                    total_pay_num_5 += Integer.parseInt(vo.getTotal_pay_num_5());
                    total_pay_num_6 += Integer.parseInt(vo.getTotal_pay_num_6());
                    total_pay_num_7 += Integer.parseInt(vo.getTotal_pay_num_7());
                    total_pay_num_8 += Integer.parseInt(vo.getTotal_pay_num_8());
                    total_pay_num_9 += Integer.parseInt(vo.getTotal_pay_num_9());
                    total_pay_num_10 += Integer.parseInt(vo.getTotal_pay_num_10());
                    total_pay_num_11 += Integer.parseInt(vo.getTotal_pay_num_11());
                    total_pay_num_12 += Integer.parseInt(vo.getTotal_pay_num_12());
                    total_pay_num_13 += Integer.parseInt(vo.getTotal_pay_num_13());
                    total_pay_num_14 += Integer.parseInt(vo.getTotal_pay_num_14());
                    total_pay_num_15 += Integer.parseInt(vo.getTotal_pay_num_15());

                    total_pay_money_1 = total_pay_money_1.add(new BigDecimal(vo.getTotal_pay_money_1()));
                    total_pay_money_2 = total_pay_money_2.add(new BigDecimal(vo.getTotal_pay_money_2()));
                    total_pay_money_3 = total_pay_money_3.add(new BigDecimal(vo.getTotal_pay_money_3()));
                    total_pay_money_4 = total_pay_money_4.add(new BigDecimal(vo.getTotal_pay_money_4()));
                    total_pay_money_5 = total_pay_money_5.add(new BigDecimal(vo.getTotal_pay_money_5()));
                    total_pay_money_6 = total_pay_money_6.add(new BigDecimal(vo.getTotal_pay_money_6()));
                    total_pay_money_7 = total_pay_money_7.add(new BigDecimal(vo.getTotal_pay_money_7()));
                    total_pay_money_8 = total_pay_money_8.add(new BigDecimal(vo.getTotal_pay_money_8()));
                    total_pay_money_9 = total_pay_money_9.add(new BigDecimal(vo.getTotal_pay_money_9()));
                    total_pay_money_10 = total_pay_money_10.add(new BigDecimal(vo.getTotal_pay_money_10()));
                    total_pay_money_11 = total_pay_money_11.add(new BigDecimal(vo.getTotal_pay_money_11()));
                    total_pay_money_12 = total_pay_money_12.add(new BigDecimal(vo.getTotal_pay_money_12()));
                    total_pay_money_13 = total_pay_money_13.add(new BigDecimal(vo.getTotal_pay_money_13()));
                    total_pay_money_14 = total_pay_money_14.add(new BigDecimal(vo.getTotal_pay_money_14()));
                    total_pay_money_15 = total_pay_money_15.add(new BigDecimal(vo.getTotal_pay_money_15()));

                }
                total.setTotal_pay_num_1(total_pay_num_1 + "");
                total.setTotal_pay_num_2(total_pay_num_2 + "");
                total.setTotal_pay_num_3(total_pay_num_3 + "");
                total.setTotal_pay_num_4(total_pay_num_4 + "");
                total.setTotal_pay_num_5(total_pay_num_5 + "");
                total.setTotal_pay_num_6(total_pay_num_6 + "");
                total.setTotal_pay_num_7(total_pay_num_7 + "");
                total.setTotal_pay_num_8(total_pay_num_8 + "");
                total.setTotal_pay_num_9(total_pay_num_9 + "");
                total.setTotal_pay_num_10(total_pay_num_10 + "");
                total.setTotal_pay_num_11(total_pay_num_11 + "");
                total.setTotal_pay_num_12(total_pay_num_12 + "");
                total.setTotal_pay_num_13(total_pay_num_13 + "");
                total.setTotal_pay_num_14(total_pay_num_14 + "");
                total.setTotal_pay_num_15(total_pay_num_15 + "");

                total.setTotal_pay_money_1(total_pay_money_1.toString());
                total.setTotal_pay_money_2(total_pay_money_2.toString());
                total.setTotal_pay_money_3(total_pay_money_3.toString());
                total.setTotal_pay_money_4(total_pay_money_4.toString());
                total.setTotal_pay_money_5(total_pay_money_5.toString());
                total.setTotal_pay_money_6(total_pay_money_6.toString());
                total.setTotal_pay_money_7(total_pay_money_7.toString());
                total.setTotal_pay_money_8(total_pay_money_8.toString());
                total.setTotal_pay_money_9(total_pay_money_9.toString());
                total.setTotal_pay_money_10(total_pay_money_10.toString());
                total.setTotal_pay_money_11(total_pay_money_11.toString());
                total.setTotal_pay_money_12(total_pay_money_12.toString());
                total.setTotal_pay_money_13(total_pay_money_13.toString());
                total.setTotal_pay_money_14(total_pay_money_14.toString());
                total.setTotal_pay_money_15(total_pay_money_15.toString());
            }
        }

        if (list.size()>0){
            for (PayCashAnalysisVo vo:list){
                keepTwoDecimal(vo);
            }
        }

        ret.put("ret", 1);
        ret.put("msg", "ok");
        ret.put("data", list);
        ret.put("total", total);
        return ret;
    }



    /**
     * 流水分析累计-导出
     * @param request
     * @return
     */
    @RequestMapping("exportTotalList")
    public void exportTotalList(HttpServletRequest request,HttpServletResponse response) {
        Map<String,String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String download_channel = request.getParameter("download_channel");
        String pid = request.getParameter("pid");
        String group = request.getParameter("group");
        //处理分组关联
        String appid_group = "";
        String download_channel_group = "";
        String pid_group = "";

        if (!BlankUtils.checkBlank(group)) {
            String[] groups = group.split(",");
            for (String str : groups) {
                if ("pid".equals(str)) {
                    pid_group = "pid";
                }
                if ("appid".equals(str)) {
                    appid_group = "appid";
                }
                if ("download_channel".equals(str)) {
                    download_channel_group = "download_channel";
                }

            }
        }
        String[] replaceGroup = {appid_group, download_channel_group, pid_group};
        //移除空的
        List<String> grouplist = new ArrayList<>();
        for (String str : replaceGroup) {
            if (!BlankUtils.checkBlank(str)) {
                grouplist.add(str);
            }
        }
        StringBuffer newGroup = new StringBuffer();
        if (grouplist.size() > 0) {
            for (int i = 0; i < grouplist.size(); i++) {
                if (i != grouplist.size() - 1) {
                    newGroup.append(grouplist.get(i)).append(",");
                } else {
                    newGroup.append(grouplist.get(i));
                }
            }
        }

        String order_str = request.getParameter("order_str");
        paramMap.put("start_date", start_date);
        paramMap.put("appid", appid);
        paramMap.put("download_channel", download_channel);
        paramMap.put("pid", pid);
        paramMap.put("group", group);
        paramMap.put("unionGroup", newGroup.toString());
        paramMap.put("appid_group", appid_group);
        paramMap.put("pid_group", pid_group);
        paramMap.put("download_channel_group", download_channel_group);
        paramMap.put("order_str", order_str);

        List<String> dateList = DateUtil.getDays(start_date,end_date);
        List<PayCashAnalysisVo> list = new ArrayList<>();
        for (String date:dateList){
            end_date = date;
            paramMap.put("tdate", end_date);
            List<PayCashAnalysisVo> eachList = userPayInfoService.getPayCashTotalList(paramMap);
            if (eachList.size()>0){
                list.addAll(eachList);
            }
        }

        // 数据内容
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";;
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        for (PayCashAnalysisVo vo:list){
            keepTwoDecimal(vo);
            if (!BlankUtils.checkBlank(vo.getAppid())){
                vo.setAppid(appMap.get(vo.getAppid())!=null?appMap.get(vo.getAppid()).get("app_name")+"-"+vo.getAppid():"");
            }
        }
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(export_file_name)){
            export_file_name = "流水分析-累计";
        }
        ExportExcelUtil.exportXLSX2(response,list,headerMap, export_file_name + "_"+ DateTime.now().toString("yyyyMMdd")+".xlsx");
    }

    /**
     * 汇总流水充值金额
     * @param oldValue
     * @param newValue
     * @return
     */
    private String payCashPayTotal(String oldValue,String newValue){
        String value = oldValue ;
        try {
            if (BlankUtils.checkBlank(newValue)){
                newValue ="0.00";
            }
            BigDecimal a = new BigDecimal(oldValue);
            BigDecimal b = new BigDecimal(newValue);
            value = a.add(b).toString();
        }catch (Exception e){

        }
        return value;
    }

    /**
     * 汇总流水充值区间人数
     * @param oldValue
     * @param newValue
     * @return
     */
    private String payCashPayTotalNum(String oldValue,String newValue){
        String value = oldValue ;
        try {
            if (BlankUtils.checkBlank(newValue)){
                newValue ="0";
            }
            BigDecimal a = new BigDecimal(oldValue);
            BigDecimal b = new BigDecimal(newValue);
            value = a.add(b).toString();
        }catch (Exception e){

        }
        return value;
    }

    /**
     * 保存两位小数
     * @param source
     * @return
     */
    public PayCashAnalysisVo keepTwoDecimal(PayCashAnalysisVo source) {
        if (source != null) {
            Class clazz = source.getClass();
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                //设置属性是可以访问的(私有的也可以)
                field.setAccessible(true);
                Object value = null;
                try {
                    value = field.get(source);
                    // 属性值不为空且包含小数点则保留一位小数
                    if (value != null&&value.toString().contains(".")&& StringUtils.isNumberByRegex(value.toString())) {
                        value = new BigDecimal(value.toString()).setScale(2, RoundingMode.HALF_UP).toString();
                        field.set(source,value);
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }

        }
        return source;
    }

}
