package com.wbgame.controller.game.query;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.game.config.GameTableVo;
import com.wbgame.pojo.game.userinfo.LoginVo;
import com.wbgame.service.AdService;
import com.wbgame.service.game.ZdGameService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname GameController
 * @Description 游戏运营用户数据各类查询
 * @Date 2022/5/10 15:33
 */
@RequestMapping("/game/userinfo")
@CrossOrigin
@RestController
public class GameController {

    private static Logger logger = LoggerFactory.getLogger(GameController.class);

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    ZdGameService zdGameService;

    @Autowired
    private AdService adService;

    private final static Map<String,String> NET_TYPE_MAP = new HashMap<String,String>(){{
        put("0","无网络");
        put("1","手机网络");
        put("2","wifi网络");
        put("3","以太网");
        put("4","蓝牙网络");
        put("5","未知");
    }};

    private final static Map<String,String> ACCOUNT_TYPE_MAP = new HashMap<String,String>(){{
        put("0","游客");
        put("1","微信");
    }};

    /**
     * 用户登录查询
     * @param request
     * @return
     */
    @RequestMapping("getLoginList")
    public Object getLoginList(HttpServletRequest request){
        JSONObject ret = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        try {
            String start_date = request.getParameter("start_date");
            String end_date = request.getParameter("end_date");
            String appid =request.getParameter("appid");
            String loginId = request.getParameter("loginId");
            String device_id = request.getParameter("device_id");
            String device_type = request.getParameter("device_type");

            Map<String,String> paramMap = new HashMap<>();
            paramMap.put("start_date",start_date);
            paramMap.put("end_date",end_date);
            paramMap.put("appid",appid);
            paramMap.put("loginId",loginId);
            paramMap.put("device_id",device_id);
            paramMap.put("device_type",device_type);

            GameTableVo gameTableConfig= zdGameService.getGameTableConfigList(paramMap).stream().findFirst().orElse(null);
            if (gameTableConfig==null){
                return ReturnJson.toErrorJson("未配置有该产品的用户表");
            }
            List<LoginVo> retList = new ArrayList<>();
            //用户表名
            paramMap.put("user_info_table_name",gameTableConfig.getTable_name());
            List<String> dateList = DateUtil.getDays(start_date,end_date);
            Collections.reverse(dateList);
            if (dateList.size()>3){
                return ReturnJson.toErrorJson("查询时间跨度不能大于三天");
            }
            //登录日志表名
            String loginTableNamePre = "game_login_";
            for (String date:dateList){
                date = date.replace("-","");
                paramMap.put("login_log_table_name",loginTableNamePre+date);
                List<LoginVo> eachList = zdGameService.getLoginLogList(paramMap);
                if (eachList.size()>0){
                    retList.addAll(eachList);
                }
            }
            List<Map<String,Object>> cityListMap = zdGameService.getCityListMap();
            Map<String,String> cityMap = new HashMap<>();
            for (Map<String,Object> map:cityListMap){
                cityMap.put(map.get("cityId").toString(),map.get("cityName").toString());
            }
            //处理省市
            if (retList.size()>0){
                for (LoginVo vo:retList){
                    vo.setCityId(cityMap.get(vo.getCityId())!=null?cityMap.get(vo.getCityId()):"未知");
                }
            }
            ret.put("ret",1);
            ret.put("msg","ok");
            ret.put("data",retList);
            ret.put("totalSize",retList.size());
        }catch (Exception e){
            ret.put("ret",0);
            ret.put("msg","查询失败");
            logger.error("getLoginList error",e);
        }

        return ret;
    }


    /**
     * 用户登录-导出
     * @param request
     * @return
     */
    @RequestMapping("exportLoginList")
    public void exportLoginList(HttpServletRequest request, HttpServletResponse response) {
        try {
            String start_date = request.getParameter("start_date");
            String end_date = request.getParameter("end_date");
            String appid =request.getParameter("appid");
            String loginId = request.getParameter("loginId");
            String device_id = request.getParameter("device_id");
            String device_type = request.getParameter("device_type");
            String export_file_name = request.getParameter("export_file_name");

            Map<String,String> paramMap = new HashMap<>();
            paramMap.put("start_date",start_date);
            paramMap.put("end_date",end_date);
            paramMap.put("appid",appid);
            paramMap.put("loginId",loginId);
            paramMap.put("device_id",device_id);
            paramMap.put("device_type",device_type);


            GameTableVo gameTableConfig= zdGameService.getGameTableConfigList(paramMap).stream().findFirst().orElse(null);
            if (gameTableConfig==null){
                Asserts.fail("未配置有该产品的用户表");
            }
            List<LoginVo> retList = new ArrayList<>();
            //用户表名
            paramMap.put("user_info_table_name",gameTableConfig.getTable_name());
            List<String> dateList = DateUtil.getDays(start_date,end_date);
            Collections.reverse(dateList);
            if (dateList.size()>3){
                Asserts.fail("查询时间跨度不能大于三天");
            }
            //登录日志表名
            String loginTableNamePre = "game_login_";
            for (String date:dateList){
                date = date.replace("-","");
                paramMap.put("login_log_table_name",loginTableNamePre+date);
                List<LoginVo> eachList = zdGameService.getLoginLogList(paramMap);
                if (eachList.size()>0){
                    retList.addAll(eachList);
                }
            }
            List<Map<String,Object>> cityListMap = zdGameService.getCityListMap();
            Map<String,String> cityMap = new HashMap<>();
            for (Map<String,Object> map:cityListMap){
                cityMap.put(map.get("cityId").toString(),map.get("cityName").toString());
            }

            if (retList.size()>0){
                for (LoginVo vo:retList){
                    //处理省市
                    vo.setCityId(cityMap.get(vo.getCityId())!=null?cityMap.get(vo.getCityId()):"未知");
                    //处理网络类型
                    vo.setNetType(NET_TYPE_MAP.get(vo.getNetType())!=null?NET_TYPE_MAP.get(vo.getNetType()):"未知");
                    //处理账号类型
                    vo.setAccountType(ACCOUNT_TYPE_MAP.get(vo.getAccountType())!=null?ACCOUNT_TYPE_MAP.get(vo.getAccountType()):"未知");
                }
            }

            // 数据内容
            String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";;
            Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

            for (LoginVo vo:retList){
                if (!BlankUtils.checkBlank(vo.getAppid())){
                    vo.setAppid(appMap.get(vo.getAppid())!=null?appMap.get(vo.getAppid()).get("app_name")+"-"+vo.getAppid():"");
                }
            }
            Map<String, String> headerMap = new LinkedHashMap<String, String>();
            String value = request.getParameter("value");
            if (!BlankUtils.checkBlank(value)){
                try {
                    String[] split = value.split(";");
                    for (int i = 0;i<split.length;i++) {
                        String[] s = split[i].split(",");
                        headerMap.put(s[0],s[1]);
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                    Asserts.fail("自定义列导出异常");
                }
            }
            ExportExcelUtil.export2(response,retList,headerMap, export_file_name+ DateTime.now().toString("yyyyMMdd")+".xls");
        }catch (Exception e){
            logger.error("exportLoginList error",e);
            Asserts.fail("导出异常,请联系管理员");
        }

    }

}
