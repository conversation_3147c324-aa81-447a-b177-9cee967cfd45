package com.wbgame.controller.clean;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.UpdateGroup;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.clean.toonstory.OperatingAreaDTO;
import com.wbgame.pojo.clean.toonstory.OperatingAreaVO;
import com.wbgame.service.clean.IOperatingAreaService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.groups.Default;
import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/10/10
 * @class: OperatingAreaController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/bigDataEventConfig")
@Api(tags = "toonStory分类运营地区")
public class OperatingAreaController {

    private IOperatingAreaService operatingAreaService;

    @Autowired
    public void setOperatingAreaService(IOperatingAreaService operatingAreaService) {
        this.operatingAreaService = operatingAreaService;
    }


    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping(value = "/selectOperatingAreaMapper")
    public Result<PageResult<OperatingAreaVO>> selectOperatingAreaMapper(@ApiNeed({

            "areaName",
            "status",
            "limit",
            "start"
    }) @Validated(QueryGroup.class) OperatingAreaDTO dto) {

        return operatingAreaService.selectOperatingAreaMapper(dto);
    }


    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = "/insertOperatingArea")
    public Result<Long> insertOperatingArea(HttpServletRequest request, @ApiNeed({
            "areaName",
            "descTxt",
            "status",
            "areaList"
    }) @Validated(Default.class) OperatingAreaDTO dto) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        dto.setCreateUser(loginUser.getLogin_name());
        return operatingAreaService.insertOperatingArea(dto);
    }

    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping(value = "/updateOperatingArea")
    public Result<Integer> updateOperatingArea(HttpServletRequest request, @ApiNeed({
            "id",
            "areaName",
            "descTxt",
            "status",
            "areaList"
    }) @Validated(UpdateGroup.class) OperatingAreaDTO dto) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        dto.setModifyUser(loginUser.getLogin_name());
        return operatingAreaService.updateOperatingArea(dto);
    }

    @ApiOperation(value = "获取运营地区下拉", notes = "获取运营地区下拉")
    @PostMapping(value = "/getAreaName")
    public Result<List<OperatingAreaVO>> getAreaName() {

        return ResultUtils.success(operatingAreaService.getAreaName());
    }

}
