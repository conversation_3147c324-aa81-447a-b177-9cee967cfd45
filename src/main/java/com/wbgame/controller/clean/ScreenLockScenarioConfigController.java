package com.wbgame.controller.clean;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.UpdateGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.clean.ScreenLockScenarioConfigDTO;
import com.wbgame.pojo.clean.ScreenLockScenarioConfigVO;
import com.wbgame.service.clean.IScreenLockScenarioConfigService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/9/2
 * @class: ScreenLockScenarioConfigController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/screenLock")
@Api(tags = "锁屏v2场景配置")
public class ScreenLockScenarioConfigController {

    private IScreenLockScenarioConfigService service;

    @Autowired
    public void setService(IScreenLockScenarioConfigService service) {
        this.service = service;
    }


    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping(value = "/deleteByIdList")
    public Result<Integer> deleteByIdList(@RequestParam("idList") List<Integer> idList) {

        return service.deleteByIdList(idList);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = "/insertScreenLockScenarioConfig")
    public Result<Integer> insertScreenLockScenarioConfig(HttpServletRequest request,
                                         @ApiNeed({"txt",
                                                 "val",
                                                 "type",
                                                 "status"})
                                         @Validated ScreenLockScenarioConfigDTO dto) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        dto.setCreateUser(loginUser.getLogin_name());
        return service.insertScreenLockScenarioConfig(dto);
    }

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping(value = "/selectScreenLockScenarioConfig")
    public Result<PageResult<ScreenLockScenarioConfigVO>> selectScreenLockScenarioConfig(@ApiNeed({"txt","val","type", "start", "limit", "status"})
                                                                    @Validated(QueryGroup.class) ScreenLockScenarioConfigDTO dto) {

        return service.selectScreenLockScenarioConfig(dto);
    }

    @ApiOperation(value = "获取下拉数据", notes = "获取下拉数据(下拉框)")
    @PostMapping(value = "/getLockScenario")
    public Result<List<ScreenLockScenarioConfigVO>> getLockScenario() {

        return service.getLockScenario();
    }


    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping(value = "/updateScreenLockScenarioConfigById")
    public Result<Integer> updateScreenLockScenarioConfigById(HttpServletRequest request,
                                                          @ApiNeed({"id", "type",
                                                                  "status"})
                                                          @Validated(UpdateGroup.class) ScreenLockScenarioConfigDTO dto) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        dto.setCreateUser(loginUser.getLogin_name());
        return service.updateScreenLockScenarioConfigById(dto);
    }
}
