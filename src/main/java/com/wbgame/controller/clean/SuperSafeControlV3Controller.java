package com.wbgame.controller.clean;

import com.wbgame.common.Constants;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.clean.SuperSafeControlV3DTO;
import com.wbgame.pojo.clean.SuperSafeControlV3VO;
import com.wbgame.service.clean.ISuperSafeControlV3Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @authoer: zhangY
 * @createDate: 2022/7/8 18:03
 * @class: SuperSafeControlV3Controller
 * @description:
 */
@Api(tags = "新风控配置")
@RestController
@CrossOrigin
@RequestMapping(value = "/superSafe")
public class SuperSafeControlV3Controller {


    @Autowired
    private ISuperSafeControlV3Service superSafeControlV3Service;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping(value = "/deleteByIdList")
    public String deleteByIdList(@RequestParam(value = "idList")List<Integer> idList) {

        return ReturnJson.success(superSafeControlV3Service.deleteByIdList(idList));
    }



    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = "/insertSuperSafe")
    public String insertSuperSafe(HttpServletRequest request, @Validated SuperSafeControlV3DTO superSafeControlV3DTO) {

        try {
            superSafeControlV3DTO.setCreateUser(checkTokenAndReturnUserName(request, null));
        } catch (Exception e) {
            return ReturnJson.error(Constants.ErrorToken);
        }
        return superSafeControlV3Service.insertSuperSafe(superSafeControlV3DTO);
    }

    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = Constants.class)
    })
    @ApiOperation(value = "修改", notes = "修改 appid 必传")
    @PostMapping(value = "/updateSuperSafeControlV3")
    public String updateSuperSafeControlV3(HttpServletRequest request,
                                           @RequestParam(value = "idList")List<Integer> idList,
                                           @Validated SuperSafeControlV3DTO superSafeControlV3DTO) {


        try {
            superSafeControlV3DTO.setModifyUser(checkTokenAndReturnUserName(request, null));
        } catch (Exception e) {
            return ReturnJson.error(Constants.ErrorToken);
        }
        return ReturnJson.success(superSafeControlV3Service.updateSuperSafeControlV3(idList, superSafeControlV3DTO));
    }

    @ApiResponses({
            @ApiResponse(code = 0, message = "操作失败", response = Constants.class),
            @ApiResponse(code = 1, message = "操作成功", response = SuperSafeControlV3VO.class)
    })
    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping(value = "/selectByCondition")
    public String selectByCondition(@Validated(QueryGroup.class) SuperSafeControlV3DTO superSafeControlV3DTO) {

        return superSafeControlV3Service.selectByCondition(superSafeControlV3DTO);
    }

    /**
     * 校验token，获取用户名
     */
    public String checkTokenAndReturnUserName(HttpServletRequest request, String token) throws Exception {

        String headerToken = request.getHeader("token");
        token = org.apache.commons.lang3.StringUtils.isBlank(token) ?
                (org.apache.commons.lang3.StringUtils.isBlank(headerToken) ? request.getParameter("token") : headerToken) : token;

        if (org.apache.commons.lang3.StringUtils.isBlank(token) || !redisTemplate.hasKey(token)) {

            throw new RuntimeException();
        } else {

            String username;
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            return username;
        }
    }

}
