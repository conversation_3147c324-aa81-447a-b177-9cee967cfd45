package com.wbgame.controller.clean;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.clean.master.CleanWallMapper;
import com.wbgame.mapper.clean.master.CleanYdMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.clean.HttpRequester;
import com.wbgame.pojo.clean.HttpRespons;
import com.wbgame.pojo.clean.WaibaoUserVo;
import com.wbgame.pojo.clean.img.ImgConfigVo;
import com.wbgame.pojo.clean.img.ImgRandNumVo;
import com.wbgame.pojo.clean.img.ImgTypeVo;
import com.wbgame.service.AdService;
import com.wbgame.service.clean.CleanImgService;
import com.wbgame.service.clean.CleanWallService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExcelUtils;
import com.wbgame.utils.HttpClientUtils;
import com.wbgame.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname CleanImgController
 * @Description 壁纸配置
 * @Date 2021/3/19 12:05
 */
@CrossOrigin
@RestController
@RequestMapping("/wall")
@Api(tags = "壁纸配置")
public class CleanWallController {

    Logger logger = LoggerFactory.getLogger(CleanWallController.class);


    @Autowired
    CleanWallService cleanWallService;

    @Autowired
    CleanWallMapper cleanWallMapper;

    @Autowired
    private CleanYdMapper cleanYdMapper;

    @Autowired
    CleanImgService cleanImgService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private AdService adService;


    @RequestMapping("/type/selectAllWallTypeList")
    public String selectImgTypeList(HttpServletRequest request){
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        List<ImgTypeVo> list = cleanWallService.selectWallTypeList(new ImgTypeVo());
        JSONObject ret = new JSONObject();
        ret.put("data",list);
        ret.put("ret",1);
        ret.put("msg","ok");
        return ret.toJSONString();
    }

    @ApiOperation(value = "壁纸分配配置查询", notes = "壁纸分配配置查询", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start", value = "页码", dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "条数", dataType = "String")
    })
    @RequestMapping("/type/selectWallTypeList")
    public String selectImgTypeList(HttpServletRequest request,ImgTypeVo imgTypeVo){
        //校验权限
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<ImgTypeVo> list = cleanWallService.selectWallTypeList(imgTypeVo);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }


    /**
     *
     *
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value = "壁纸配置操作", notes = "增删改查都是这个方法", httpMethod = "POST")
    @RequestMapping("/type/handle")
    public String typeHandle(String handle, HttpServletRequest request,
                                    ImgTypeVo typeVo, HttpServletResponse response) {

        //校验权限
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        }else{
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }
        boolean result = false;
        Map<String, String> map = new HashMap<>();
        if (typeVo!=null&&!BlankUtils.checkBlank(typeVo.getType())){
            map.put("type",typeVo.getType());
        }
        if (typeVo!=null){
            typeVo.setModifyUser(username);
        }
        if ("add".equals(request.getParameter("handle"))) {
            try {
                if (typeVo==null||typeVo.getType()==null){
                    return ReturnJson.toErrorJson("必要输入项为空,请检查!");
                }

                List<ImgTypeVo> imgTypeList = cleanWallService.selectWallTypeByType(map);
                if (imgTypeList!=null&&imgTypeList.size()>0){
                    return ReturnJson.toErrorJson("存在相同的分类名称无法新增!");
                }

                // 分类排序查询
                map.put("typeSort",typeVo.getTypeSort());
                List<ImgTypeVo> isSortByCategoryList = cleanWallService.selectWallTypeByTypeSort(map);
                if (isSortByCategoryList.size() > 0) {
                    return ReturnJson.toErrorJson("该分类排序已经存在,如需调整分类排序可以设置一个不重复的值先改,后续可修改为需要数值");
                }

                result = cleanWallService.insertWallType(typeVo)>0;
            } catch (DataAccessException e) {
                logger.error("wall typeHandle:",e);
                return ReturnJson.toErrorJson("存在相同的分类名称无法新增!");
            }
        } else if ("edit".equals(request.getParameter("handle"))) {
            List<ImgTypeVo> list = cleanWallService.selectWallTypeByType(map);
            if (list.size()>0&&!list.get(0).getId().equals(typeVo.getId())){
                return ReturnJson.toErrorJson("存在相同的分类名称无法修改为该名称!");
            }
            //分类排序查询
            map.put("typeSort",typeVo.getTypeSort());
            List<ImgTypeVo> list2 = cleanWallService.selectWallTypeByTypeSort(map);
            if (list2.size()>0&&!list2.get(0).getId().equals(typeVo.getId())){
                return ReturnJson.toErrorJson("该分类排序已经存在,如需调整分类排序可以设置一个不重复的值先改,后续可修改为需要数值");
            }
            //如果是关闭的话 还需要更新 super_img_config表下所有数据
            ImgTypeVo dbVo = cleanWallService.selectWallTypeById(typeVo);
            typeVo.setOldTypeSort(dbVo.getTypeSort());

            cleanWallService.updateWallType(typeVo);

            cleanWallService.updateWallConfigByTypeChange(typeVo);
            //刷新配置缓存
            refreshImgConfigCommon("2");
            result = true ;
        } else if ("del".equals(request.getParameter("handle"))) {
            List<ImgTypeVo> exitsList = cleanWallService.selectWallTypeList(new ImgTypeVo());
            //先删除分类
            cleanWallService.deleteWallType(typeVo);
            //删除 分类下所有配置
            ImgTypeVo oldDataVo = exitsList.stream().filter(t->t.getId().equals(typeVo.getId())).findFirst().orElse(null);
            if (oldDataVo!=null){
                cleanWallService.deleteWallConfigByTypeSort(oldDataVo.getTypeSort());
            }
            //刷新配置缓存
            refreshImgConfigCommon("2");
            result = true ;
        }

        if (result) {
            return ReturnJson.success();
        } else {
            return ReturnJson.error();
        }

    }


    /**
     * 修图配置查询
     * @param img
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value = "壁纸配置查询", notes = "壁纸配置查询", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start", value = "页码", dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "条数", dataType = "String")
    })
    @RequestMapping("selectWallConfigList")
    public Object selectImgConfigList(ImgConfigVo img, HttpServletRequest request, HttpServletResponse response) {
        //校验权限
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();

        PageHelper.startPage(pageNo, pageSize); // 进行分页

        List<ImgConfigVo> list = cleanWallService.selectWallConfigList(img);

        long size = ((Page) list).getTotal();
        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);

        return result.toJSONString();
    }

    /**
     * 修图配置操作接口 新增-修改-删除-批量开关/修改分类
     * @param img
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value = "壁纸配置操作", notes = "新增-修改-删除-批量开关/修改分类", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "handle", value = "操作", dataType = "String")
    })
    @RequestMapping("handle")
    public Object handleImgConfig(ImgConfigVo img, HttpServletRequest request, HttpServletResponse response) {
        //校验权限
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        }else{
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        String handleType = request.getParameter("handle");

        int succ = 0;
        String updateUser = username;
        img.setModifyUser(updateUser);
        if (!BlankUtils.checkBlank(img.getId())){
            if (img.getId().endsWith(",")){
                img.setId(img.getId().substring(0,img.getId().length()-1));
            }
            if (!BlankUtils.checkBlank(img.getId())){
                img.setIds(img.getId().split(","));
            }
        }
        switch (handleType) {
            case "add":
                succ = cleanWallService.insertWallConfig(img);
                break;
            case "edit":
                succ = cleanWallService.updateWallConfig(img);
                break;
            case "del":
                succ = cleanWallService.deleteWallConfig(img);
                break;
            case "statusBatch":
                succ = cleanWallService.updateWallConfigStatusBatch(img);
                break;
            case "typeSortBatch":
                succ = cleanWallService.updateWallConfigTypeSortBatch(img);
                break;
        }
        if (succ > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }
    }

    /**
     * 批量开关
     * @param request
     * @param img
     * @return
     */
    @RequestMapping("updateStatus")
    public String openOrClose(HttpServletRequest request,ImgConfigVo img){
        //校验权限
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        int succ = cleanWallService.updateWallConfigStatus(img);
        if (succ>0){
            return ReturnJson.success();
        }else{
            return ReturnJson.error();
        }

    }

    /**
     * 批量操作-删除-修改
     * @param request
     * @return
     */
    @RequestMapping("batchUpdate")
    public Object batchUpdate(HttpServletRequest request,ImgConfigVo imgConfigVo){
        String ids = request.getParameter("ids");
        String status = request.getParameter("status");
        String typeSort = request.getParameter("typeSort");
        String type = request.getParameter("type");
        String handle = request.getParameter("handle");
        if (BlankUtils.checkBlank(ids)){
            return "{\"ret\":0,\"msg\":\"无效请求,没有配置id参数!\"}";
        }
        if ("del".equals(handle)){

        }else if ("edit".equals(handle)){

        }
        return ReturnJson.success();
    }



    /**
     * 批量复制-产品-产品 接口废弃
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("copyBatch")
    public Object copyImgConfigBatch(HttpServletRequest request, HttpServletResponse response) {


        return ReturnJson.toErrorJson("此接口废弃");

//        //校验权限
//        String token = request.getParameter("token");
//        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
//            return ReturnJson.error(Constants.ErrorToken);
//        } else {
//            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
//        }
//        String username = "";
//        if (token.startsWith("wbtoken")) {
//            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
//            username = currUserVo.getLogin_name();
//        }else{
//            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
//            username = WaibaoUser.getUser_name();
//        }
//
//        String fromAppId = request.getParameter("fAppid");
//        String toAppId = request.getParameter("tAppid");
//        ImgConfigVo img = new ImgConfigVo();
//        img.setAppid(fromAppId);
//        List<ImgConfigVo> list = cleanWallService.selectWallConfigList(img);
//        if (list.size()==0){
//            return "{\"ret\":0,\"msg\":\"原产品id下未配置有数据!\"}";
//        }
//        for (ImgConfigVo vo:list){
//            vo.setAppid(toAppId);
//            vo.setModifyUser(username);
//        }
//        int succ = cleanWallService.copyWallConfigBatch(list);
//        if (succ>0){
//            return ReturnJson.success();
//        }else{
//            return ReturnJson.error();
//        }
    }

    /**
     * excel上传壁纸配置
     * @param file
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @ApiOperation(value = "excel上传壁纸配置", notes = "excel上传壁纸配置", httpMethod = "POST")
    @RequestMapping("uploadWallConfig")
    public Object uploadImgConfig(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException{
        //校验权限
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String username = "";
        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        }else{
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        if (null == file || file.isEmpty() || !(file.getOriginalFilename().endsWith(".xls") || file.getOriginalFilename().endsWith(".xlsx"))) {
            Asserts.fail("上传文件有误，需要excel文件!");
        }
        JSONObject ret = new JSONObject();
        List<ImgConfigVo> list = new ArrayList<>();

        List<ImgTypeVo> typeList = cleanWallService.selectWallTypeList(new ImgTypeVo());
        Map<String,String> map = new HashMap<>();
        for (ImgTypeVo vo:typeList){
            map.put(vo.getTypeSort(),vo.getType());
        }


        ImgRandNumVo imgRandVo = cleanImgService.selectImgRandNum();
        Set<String> set = new TreeSet<>();
        for(int i=0;i<5000;i++){
            int randUse = StringUtils.getRandom(imgRandVo.getUseMin(),imgRandVo.getUseMax());
            int randLike = StringUtils.getRandom(imgRandVo.getLikeMin(),imgRandVo.getLikeMax());
            if (randLike>randUse){
                continue;
            }else{
                set.add(randUse+"_"+randLike);
            }
        }

        List<String> countList = new ArrayList<>(set);
        Collections.shuffle(countList);
        List<ArrayList<String>> arrayLists = ExcelUtils.readExcel(file);
        for(int i = 0; i < arrayLists.size(); i++) {
            if (i == 0) {
                //第一行为标题，不能为空
                for (int k = 0;k<5;k++) {
                    if (BlankUtils.checkBlank(arrayLists.get(i).get(k))) {
                        Asserts.fail("第一行标题不能出现空的情况");
                    }
                }
            } else {
                //第二行为内容
                String typeSort = arrayLists.get(i).get(1);
                String type = arrayLists.get(i).get(2);
                if (map.get(typeSort)==null){
                    ret.put("ret",0);
                    ret.put("msg","上传的excel文件错误:第"+(i+1)+"行,数据库配置不存在该分类排序");
                    return ret;
                }
                if (!map.get(typeSort).equals(type)){
                    ret.put("ret",0);
                    ret.put("msg","上传的excel文件异常:第"+(i+1)+"行,数据库配置为====分类排序:"+typeSort+",分类名称是:"+map.get(typeSort));
                    return ret;
                }

                ImgConfigVo imgConfigVo = new ImgConfigVo();
                imgConfigVo.setAppid(arrayLists.get(i).get(0));
                imgConfigVo.setTypeSort(arrayLists.get(i).get(1));
                imgConfigVo.setType(arrayLists.get(i).get(2));
                imgConfigVo.setImgSort(arrayLists.get(i).get(3));
                imgConfigVo.setName(arrayLists.get(i).get(4));
                imgConfigVo.setUrl(arrayLists.get(i).get(5));
                imgConfigVo.setFileType(arrayLists.get(i).get(6));
                imgConfigVo.setPreUrl(arrayLists.get(i).get(7));
                String useCount = countList.get(i).split("_")[0];
                imgConfigVo.setImgUseCount(useCount);
                String likeCount = countList.get(i).split("_")[1];
                imgConfigVo.setImgLikeCount(likeCount);

                imgConfigVo.setStatus("1");
                imgConfigVo.setModifyUser(username);
                list.add(imgConfigVo);
            }
        }
        int succ = cleanWallService.copyWallConfigBatch(list);
        if (succ>0){
            return ReturnJson.success("导入成功");
        }
        return ReturnJson.success("导入失败");
    }


    /**
     * 刷新修图配置缓存接口
     * @param request
     * @return
     */
    @RequestMapping(value="/refreshCahce", method={RequestMethod.GET,RequestMethod.POST})
    @CrossOrigin
    public Object refreshImgConfig(HttpServletRequest request) {
        String mapid = request.getParameter("mapid");
        String result = refreshImgConfigCommon(mapid);
        return result;
    }

    private String refreshImgConfigCommonTest(String mapid){
        String result = "fail";
        HttpRequester resq = new HttpRequester();
        try {
            HttpRespons res10 = resq.sendGet("http://192.168.1.38:6408/refreshCache/imgCfgCache/v1?mapid=" + mapid);
            String msg10 = res10.getMessage().replace("\r\n", "").trim();
            logger.info("refresg img cache:"+msg10);
            if (((msg10 != null && msg10.length() > 0 && (msg10.equals("ok") || msg10 == "ok"))))
            {
                result = "ok";
            }
        } catch (Exception e) {
            logger.error("refresh img v1 cache error:",e);
        }
        return result;
    }


    private String refreshImgConfigCommon(String mapid){
        String result = "fail";
        StringBuffer sbMsg = new StringBuffer();
        StringBuffer content = new StringBuffer();
        try {
            String sql = "select url from dn_recache_config where mark = 'Clean' ";
            List<String> urlList = adService.queryListString(sql);

            for (String url:urlList){
                String resp = HttpClientUtils.getInstance().httpGet(url+"/refreshCache/imgCfgCache/v1?mapid=" + mapid);
                if (resp == null || !"ok".equals(resp)){
                    content.append(url+";");
                }
            }
            if (content.length()>0){
                sbMsg.append("刷新失败的server:");
                sbMsg.append(content);
            }
            if (sbMsg.length()==0){
                result = "ok";
            }else {
                result = sbMsg.toString();
            }
        } catch (Exception e) {
            logger.error("refresh wall v1 cache error:",e);
        }
        return result;
    }


    /**
     * 壁纸自定义排序
     * @param id
     * @param appid
     * @param sort
     * @param typeSort
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value = "壁纸自定义排序", notes = "壁纸自定义排序", httpMethod = "POST")
    @RequestMapping(value = "/wallSort", method = { RequestMethod.GET, RequestMethod.POST })
    public String wallSort(String id,String appid,Integer sort,String typeSort,HttpServletRequest request, HttpServletResponse response) {
        //校验权限
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        if (BlankUtils.checkBlank(id)||sort==null||BlankUtils.checkBlank(typeSort)){
            return "{\"ret\":2,\"msg\":\"修改排序必须选择数据条目+产品+分类!\"}";
        }

        int result = 0;
        //设置当前数据排序
        result = cleanYdMapper.execSql(" update super_img_config  set imgSort = "+sort+" where id = "+ id);
        String sql =  " SELECT * FROM `super_img_config`  where id != "+id+" and dataType=2 and imgSort >=  "+ sort+
                "  and typeSort ="+typeSort+" ORDER BY imgSort ";

        sort++;

        List<Map<String, Object>> queryListMap = cleanYdMapper.queryListMap(sql);
        for (Map<String, Object> map : queryListMap) {
           cleanYdMapper.execSql(" update super_img_config  set imgSort = "+sort+" where dataType =2 "+
                    "  and typeSort ="+typeSort+
                    "  and id = " +map.get("id") );
            sort++;
        }
        if (result > 0)
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        else
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
    }


    /**
     * 壁纸批量删除
     * @param id
     * @param appid
     * @param typeSort
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/wallBatchDel", method = { RequestMethod.GET, RequestMethod.POST })
    public String wallBatchDel(String appid,String typeSort,HttpServletRequest request, HttpServletResponse response) {
        //校验权限
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return ReturnJson.error(Constants.ErrorToken);
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        if (BlankUtils.checkBlank(appid)||BlankUtils.checkBlank(typeSort)){
            return "{\"ret\":2,\"msg\":\"批量删除必须选择产品+分类!\"}";
        }
        //设置当前数据排序
        int succ = cleanYdMapper.execSql("delete from super_img_config  where dataType =2 and appid ="+appid+" and typeSort ="+typeSort);
        if (succ > 0)
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        else
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
    }
}
