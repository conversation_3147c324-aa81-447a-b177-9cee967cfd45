package com.wbgame.controller.clean;

import com.wbgame.common.QueryRemoveDateGroup;
import com.wbgame.common.UpdateGroup;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.clean.face.FaceOperateArea;
import com.wbgame.pojo.clean.face.FaceOpreateSort;
import com.wbgame.pojo.clean.face.FaceProductConfig;
import com.wbgame.pojo.clean.face.FaceProductLinArea;
import com.wbgame.service.clean.IFaceOperateAreaService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.groups.Default;
import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2022/12/02 002
 * @class: FaceOperateAreaController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/faceOperateArea")
@Api(tags = "faceplus商品拓展接口")
public class FaceOperateAreaController {

    @Autowired
    private IFaceOperateAreaService operateAreaService;


    @ApiOperation(value = "运营地区-新增", notes = "新增")
    @PostMapping(value = "/insertFaceOperateArea")
    public Result<Integer> insertFaceOperateArea(
            HttpServletRequest request,
            @RequestBody
            @Validated(Default.class) FaceOperateArea faceOperateArea) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        faceOperateArea.setCreateUser(loginUser.getLogin_name());

        return operateAreaService.insertFaceOperateArea(faceOperateArea);
    }

    @ApiOperation(value = "运营地区-删除", notes = "删除")
    @PostMapping(value = "/deleteFaceOperateArea")
    public Result<Integer> deleteFaceOperateArea(@RequestBody List<Integer> idList) {

        return operateAreaService.deleteFaceOperateArea(idList);
    }


    @ApiOperation(value = "运营地区-修改", notes = "修改")
    @PostMapping(value = "/updateGoodsConfig")
    public Result<Integer> updateGoodsConfig(
            HttpServletRequest request,
            @RequestBody
            @Validated(UpdateGroup.class) FaceOperateArea faceOperateArea) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        faceOperateArea.setUpdateUser(loginUser.getLogin_name());

        return operateAreaService.updateFaceOperateArea(faceOperateArea);
    }

    @ApiOperation(value = "运营地区-查询", notes = "查询")
    @PostMapping(value = "/selectGoodsConfig")
    public Result<PageResult<FaceOperateArea>> selectGoodsConfig(
            @RequestBody @Validated(QueryRemoveDateGroup.class) FaceOperateArea faceOperateArea) {

        return operateAreaService.selectFaceOperateArea(faceOperateArea);
    }

    @ApiOperation(value = "商品排序", notes = "商品排序")
    @PostMapping(value = "/updateSort")
    public Result<Integer> updateSort(
            @RequestBody
            @Validated(UpdateGroup.class) List<FaceOpreateSort> sortList) {

        return operateAreaService.updateSort(sortList);
    }

    @ApiOperation(value = "编辑商品地区", notes = "编辑商品地区")
    @PostMapping(value = "/insertFaceOpreateSort")
    public Result<Integer> insertFaceOpreateSort(HttpServletRequest request,
            @RequestBody
            @Validated(UpdateGroup.class) FaceProductLinArea productLinArea) {


        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        return operateAreaService.insertFaceOpreateSort(productLinArea, loginUser.getLogin_name());
    }

    @ApiOperation(value = "根据渠道获取渠道所对应的地区(下拉列表)", notes = "根据渠道获取渠道所对应的地区(下拉列表)")
    @PostMapping(value = "/getAreaListByCha")
    public Result<Map<String, List<FaceProductConfig>>> getAreaListByCha() {

        return operateAreaService.getAreaListByCha();
    }

    @ApiOperation(value = "根据地区获取对应的数据", notes = "根据地区获取对应的数据")
    @PostMapping(value = "/selectConfigByArea")
    public Result<List<FaceProductConfig>> selectConfigByArea(@RequestBody FaceProductConfig area) {

        return operateAreaService.selectConfigByArea(area);
    }

    @ApiOperation(value = "根据商品id查询地区", notes = "根据商品id查询地区")
    @PostMapping(value = "/selectOpreateSortByProductId")
    public Result<List<FaceOpreateSort>> selectOpreateSortByProductId(@RequestParam(value = "productId") Integer productId) {

        return ResultUtils.success(operateAreaService.selectOpreateSortByProductId(productId));
    }

    @ApiOperation(value = "根据渠道查询还未分配运营地区的商品", notes = "根据渠道查询还未分配运营地区的商品")
    @PostMapping(value = "/selectProductByChannelDownArea")
    public Result<List<FaceProductConfig>> selectProductByChannelDownArea(@RequestParam(value = "channel") String channel,
                                                                          @RequestParam(value = "area") String area) {

        return operateAreaService.selectProductByChannelDownArea(channel, area);
    }

    @ApiOperation(value = "商品排序-批量新增商品到地区-待删除", notes = "商品排序-批量新增商品到地区")
//    @PostMapping(value = "/batchAddProductToSort")
    public Result<Integer> batchAddProductToSort(
            @RequestBody
            @Validated(UpdateGroup.class) List<FaceOpreateSort> sortList) {


        return operateAreaService.batchAddProductToSort(sortList);
    }
}
