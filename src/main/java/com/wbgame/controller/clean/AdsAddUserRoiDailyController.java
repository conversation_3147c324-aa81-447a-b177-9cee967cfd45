package com.wbgame.controller.clean;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.Asserts;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.bigdata.AdsAddUserRoiDaily;
import com.wbgame.service.adb.IAdsAddUserRoiDailyService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2022/12/15 015
 * @class: AdsAddUserRoiDailyController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/addUserRoiController")
@Api(tags = "LTV增长倍数")
public class AdsAddUserRoiDailyController {

    @Autowired
    private IAdsAddUserRoiDailyService roiDailyService;


    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping(value = "/selectAdsAddUserRoiDaily")
    public Result<PageResult<AdsAddUserRoiDaily>> selectAdsAddUserRoiDaily(@ApiNeed({
            "start_date",
            "end_date",
            "appidList",
            "limit",
            "group",
            "order_str",
            "summary",
            "add_user",
            "exclusionDateList",
            "start"

    }) @Validated(QueryGroup.class) AdsAddUserRoiDaily example) {

        return roiDailyService.selectAdsAddUserRoiDaily(example);
    }

    @ApiOperation(value = "查询-图表", notes = "查询")
    @PostMapping(value = "/selectAdsAddUserRoiDailyScheme")
    public Result<List<AdsAddUserRoiDaily>> selectAdsAddUserRoiDailyScheme(@ApiNeed({
            "start_date",
            "end_date",
            "appidList",
            "limit",
            "group",
            "summary",
            "order_str",
            "exclusionDateList",
            "add_user",
            "start"

    }) @Validated(QueryGroup.class) AdsAddUserRoiDaily example) {

        return roiDailyService.selectAdsAddUserRoiDailyScheme(example);
    }

    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping(value = "/export")
    public void export(HttpServletResponse response, @ApiNeed({
            "start_date",
            "end_date",
            "appidList",
            "limit",
            "group",
            "add_user",
            "order_str",
            "exclusionDateList",
            "summary",
            "start"

    }) @Validated(QueryGroup.class) AdsAddUserRoiDaily example) {

        List<AdsAddUserRoiDaily> list = roiDailyService.export(example);
        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = example.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = example.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX2(response, list, head, fileName);

    }


}
