package com.wbgame.controller.clean;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.aop.ApiNeed;
import com.wbgame.common.*;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.clean.DnwxFilterListNewDTO;
import com.wbgame.pojo.clean.DnwxFilterListNewVO;
import com.wbgame.service.clean.IBigDataEventConfigService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jxl.read.biff.BiffException;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * @author: zhangY
 * @createDate: 2022/8/30
 * @class: BigDataEventConfigController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/bigDataEventConfig")
@Api(tags = "大数据事件配置")
public class BigDataEventConfigController {

    private IBigDataEventConfigService bigDataEventConfigService;

    @Autowired
    public void setDnwxFilterListNewMapper(IBigDataEventConfigService bigDataEventConfigService) {
        this.bigDataEventConfigService = bigDataEventConfigService;
    }


    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping(value = "/deleteFilterByIdList")
    public Result<Integer> deleteFilterListById(@RequestParam("idList") List<Long> idList) {

        return bigDataEventConfigService.deleteFilterByIdList(idList);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = "/insertDnwxFilterListNew")
    public Result<Long> insertFilterList(HttpServletRequest request,
                                         @ApiNeed({"topic",
                                                 "appid",
                                                 "itemId",
                                                 "title",
                                                 "appGroup",
                                                 "productId",
                                                 "prjid"})
                                         @Validated DnwxFilterListNewDTO filterDTO) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        filterDTO.setCreateUser(loginUser.getLogin_name());
        return bigDataEventConfigService.insertDnwxFilterListNew(filterDTO);
    }

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping(value = "/selectDnwxFilterListNew")
    public Result<PageResult<DnwxFilterListNewVO>> selectFilterList(@ApiNeed({"title","prjid","appidList", "start", "limit", "createUser", "topic"})
                                                                    @Validated(QueryGroup.class) DnwxFilterListNewDTO dnwxFilterListQuery) {

        return bigDataEventConfigService.selectDnwxFilterListNew(dnwxFilterListQuery);
    }

    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping(value = "/importFilter")
    public Result<Integer> importFilter(HttpServletRequest request,
                                        @RequestParam(value = "productId", required = false) Integer productId,
                                        @RequestParam("topic") String topic,
                                        @RequestParam(value = "itemId", required = false) String itemId,
                                        @RequestParam("file") MultipartFile file) {

        List<String[]> data = new LinkedList<>();
        try (InputStream input = file.getInputStream();
             InputStreamReader isr = new InputStreamReader(input);
             BufferedReader bf = new BufferedReader(isr)) {
            String str = "";
            int line = 1;
            while ((str = bf.readLine()) != null) {

                final String[] split = str.split(",");
                if (split.length < 2) {

                    return ResultUtils.failure("第" + line + "行数据不完成!");
                }

                for (String[] datum : data) {

                    if (split[0].equals(datum[0])) {

                        return ResultUtils.failure("第" + line + "行事件值和前面数据事件值重复");
                    }
                }
                data.add(split);

                line++;
            }


        } catch (Exception e) {

            ResultUtils.failure("解析文件异常");
        }

        data = data.stream().distinct().collect(Collectors.toList());

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        return bigDataEventConfigService.importFilter(productId, data, loginUser.getLogin_name(), itemId, topic);
    }

    @ApiOperation(value = "excel导入", notes = "excel导入")
    @ControllerLoggingEnhancer
    @RequestMapping(value="/batchImport", method={RequestMethod.GET,RequestMethod.POST})
    public String appBatchImport(@RequestParam(value = "fileName") MultipartFile file) {
        if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
            try {
                String user = LOGIN_USER_NAME.get();
                return bigDataEventConfigService.batchImport(file, user);
            } catch (IOException | BiffException e) {
                throw new RuntimeException(e);
            }
        } else {
            return ReturnJson.toErrorJson("上传文件有误，需要.xls文件!");
        }
    }


    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping(value = "/updateDnwxFilterListNew")
    public Result<Integer> updateDnwxFilterListNew(HttpServletRequest request, @ApiNeed({"id","title","productId", "topic", "itemId",})
                                                                    @Validated(UpdateGroup.class) DnwxFilterListNewDTO dnwxFilterListQuery) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        dnwxFilterListQuery.setUpdateUser(loginUser.getLogin_name());
        return bigDataEventConfigService.updateDnwxFilterListNew(dnwxFilterListQuery);
    }


    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping(value = "/export")
    public void export(HttpServletResponse response,  @ApiNeed({"title","prjid","appidList", "start", "limit", "createUser", "topic", "value", "export_file_name"})
                                                                    @Validated(QueryGroup.class) DnwxFilterListNewDTO dnwxFilterListQuery) {

        List<DnwxFilterListNewVO> list = bigDataEventConfigService.export(dnwxFilterListQuery);
        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = dnwxFilterListQuery.getValue().split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = dnwxFilterListQuery.getExport_file_name()+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX2(response,list,head,fileName);
    }

}
