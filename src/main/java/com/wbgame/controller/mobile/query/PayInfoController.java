package com.wbgame.controller.mobile.query;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.PayInfoVo;
import com.wbgame.pojo.custom.PageForList;
import com.wbgame.pojo.custom.Pager;
import com.wbgame.service.PayService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: 支付信息查询
 * @author: huangmb
 * @date: 2021/02/24
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/payInfo")
public class PayInfoController {

    @Autowired
    private PayService payService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 支付数据查询
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public void list(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            String pStart = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");
            String end = BlankUtils.checkNull(request, "end");
            String begin = BlankUtils.checkNull(request, "begin");
            String prjId = BlankUtils.checkNull(request, "prjId");
            String payWays = BlankUtils.checkNull(request, "payWay");
            String app = BlankUtils.checkNull(request, "app");
            String selectappid = BlankUtils.checkNull(request, "selectappid");    //应用名称分组
            String selectpayway = BlankUtils.checkNull(request, "selectpayway");  //支付方式分组
            // pageStart 和 limit设置值
            int pageStart = "".equals(pStart) == true ? 0 : Integer
                    .parseInt(pStart);
            int pageSize = "".equals(limit) == true ? 100 : Integer
                    .parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            // 转载查询语句条件的集合
            Map<String, String> parmMap = new HashMap<String, String>();
            parmMap.put("begin", begin);
            parmMap.put("end", end);
            parmMap.put("prjId", prjId);
            parmMap.put("app", app);
            parmMap.put("selectappid", selectappid);
            parmMap.put("selectpayway", selectpayway);
            //分组的查询结果
            if (Boolean.parseBoolean(selectappid) || Boolean.parseBoolean(selectpayway)) {
                List<PayInfoVo> buslist = new ArrayList<PayInfoVo>();
                List<PayInfoVo> mList = new ArrayList<PayInfoVo>();
                List<PayInfoVo> oppoList = payService.queryOppoPay(parmMap);
                List<PayInfoVo> viewList = payService.queryViewPay(parmMap);
                List<PayInfoVo> wxList = payService.queryWxPay(parmMap);
                List<PayInfoVo> aliList = payService.queryAliPay(parmMap);
                List<PayInfoVo> hwList = payService.queryHWPay(parmMap);
                List<PayInfoVo> xmList = payService.queryXMPay(parmMap);
                List<PayInfoVo> mzList = payService.queryMZPay(parmMap);
                List<PayInfoVo> jlList = payService.queryJLPay(parmMap);
                List<PayInfoVo> bdList = payService.queryBDPay(parmMap);
                List<PayInfoVo> sxList = payService.querySXPay(parmMap);
                List<PayInfoVo> lxList = payService.queryLXPay(parmMap);
                List<PayInfoVo> txList = payService.queryTXPay(parmMap);
                List<PayInfoVo> newoppoList = payService.queryOppoPayNew(parmMap);
                List<PayInfoVo> newviewList = payService.queryViewPayNew(parmMap);
                List<PayInfoVo> newwxList = payService.queryWxPayNew(parmMap);
                List<PayInfoVo> newaliList = payService.queryAliPayNew(parmMap);
                List<PayInfoVo> newhwList = payService.queryHWPayNew(parmMap);
                List<PayInfoVo> newxmList = payService.queryXMPayNew(parmMap);
                List<PayInfoVo> newmzList = payService.queryMZPayNew(parmMap);
                List<PayInfoVo> newjlList = payService.queryJLPayNew(parmMap);
                List<PayInfoVo> newbdList = payService.queryBDPayNew(parmMap);
                List<PayInfoVo> newsxList = payService.querySXPayNew(parmMap);
                List<PayInfoVo> newlxList = payService.queryLXPayNew(parmMap);
                List<PayInfoVo> newtxList = payService.queryTXPayNew(parmMap);
                if (payWays == null || payWays.equals("")) {
                    mList.addAll(bdList);
                    mList.addAll(xmList);
                    mList.addAll(mzList);
                    mList.addAll(jlList);
                    mList.addAll(oppoList);
                    mList.addAll(viewList);
                    mList.addAll(wxList);
                    mList.addAll(aliList);
                    mList.addAll(hwList);
                    mList.addAll(sxList);
                    mList.addAll(lxList);
                    mList.addAll(txList);

                    mList.addAll(newbdList);
                    mList.addAll(newxmList);
                    mList.addAll(newmzList);
                    mList.addAll(newjlList);
                    mList.addAll(newoppoList);
                    mList.addAll(newviewList);
                    mList.addAll(newwxList);
                    mList.addAll(newaliList);
                    mList.addAll(newhwList);
                    mList.addAll(newsxList);
                    mList.addAll(newlxList);
                    mList.addAll(newtxList);
                } else {
                    String[] split = payWays.split(",");
                    for (String payWay : split) {
                        if (payWay.equals("1")) {
                            mList.addAll(aliList);
                            mList.addAll(newaliList);
                        }
                        if (payWay.equals("2")) {
                            mList.addAll(wxList);
                            mList.addAll(newwxList);
                        }
                        if (payWay.equals("3")) {
                            mList.addAll(oppoList);
                            mList.addAll(newoppoList);
                        }
                        if (payWay.equals("4")) {
                            mList.addAll(viewList);
                            mList.addAll(newviewList);
                        }
                        if (payWay.equals("6")) {
                            mList.addAll(xmList);
                            mList.addAll(newxmList);
                        }
                        if (payWay.equals("7")) {
                            mList.addAll(bdList);
                            mList.addAll(newbdList);
                        }
                        if (payWay.equals("8")) {
                            mList.addAll(mzList);
                            mList.addAll(newmzList);
                        }
                        if (payWay.equals("9")) {
                            mList.addAll(jlList);
                            mList.addAll(newjlList);
                        }
                        if (payWay.equals("10")) {
                            mList.addAll(sxList);
                            mList.addAll(newsxList);
                        }
                        if (payWay.equals("11")) {
                            mList.addAll(lxList);
                            mList.addAll(newlxList);
                        }
                        if (payWay.equals("12")) {
                            mList.addAll(txList);
                            mList.addAll(newtxList);
                        }
                    }
                }

                if (Boolean.parseBoolean(selectappid) && Boolean.parseBoolean(selectpayway)) {
                    Map<String, List<PayInfoVo>> groupMap = new HashMap<>();
                    try {
                        for (PayInfoVo li : mList) {
                            if (groupMap.containsKey(li.getPayWay())) {
                                groupMap.get(li.getPayWay()).add(li);
                            } else {//map中不存在，新建key，用来存放数据
                                List<PayInfoVo> tmpList = new ArrayList<>();
                                tmpList.add(li);
                                groupMap.put(li.getPayWay(), tmpList);
                            }
                        }
                        Iterator<Map.Entry<String, List<PayInfoVo>>> iterator = groupMap.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, List<PayInfoVo>> payNext = iterator.next();
                            Map<String, List<PayInfoVo>> piMap = new HashMap<>();
                            for (PayInfoVo pi : payNext.getValue()) {
                                if (piMap.containsKey(pi.getApp())) {
                                    piMap.get(pi.getApp()).add(pi);
                                } else {//map中不存在，新建key，用来存放数据
                                    List<PayInfoVo> tmpList = new ArrayList<>();
                                    tmpList.add(pi);
                                    piMap.put(pi.getApp(), tmpList);
                                }
                            }
                            Iterator<Map.Entry<String, List<PayInfoVo>>> it = piMap.entrySet().iterator();
                            while (it.hasNext()) {
                                Map.Entry<String, List<PayInfoVo>> result = it.next();
                                PayInfoVo pay = new PayInfoVo();
                                pay.setPayWay(payNext.getKey());
                                pay.setApp(result.getKey());

                                List<PayInfoVo> value = result.getValue();
                                double sum = 0;
                                int payCount = 0;
                                int payNum = 0;
                                for (PayInfoVo pa : value) {
                                    sum += pa.getAmount();
                                    payCount += pa.getPayCount();
                                    payNum += pa.getPayNumber();
                                }
                                pay.setAmount(sum);
                                pay.setPayNumber(payNum);
                                pay.setPayCount(payCount);
                                if (payNum != 0) {
                                    pay.setArpu(sum / 100 / payNum);
                                }
                                buslist.add(pay);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    Map<String, List<PayInfoVo>> groupMap = new HashMap<>();
                    try {
                        for (PayInfoVo li : mList) {
                            if (Boolean.parseBoolean(selectappid)) {//按应用名称分组
                                if (groupMap.containsKey(li.getApp())) {
                                    groupMap.get(li.getApp()).add(li);
                                } else {//map中不存在，新建key，用来存放数据
                                    List<PayInfoVo> tmpList = new ArrayList<>();
                                    tmpList.add(li);
                                    groupMap.put(li.getApp(), tmpList);
                                }
                            } else {//按支付方式分组
                                if (groupMap.containsKey(li.getPayWay())) {
                                    groupMap.get(li.getPayWay()).add(li);
                                } else {//map中不存在，新建key，用来存放数据
                                    List<PayInfoVo> tmpList = new ArrayList<>();
                                    tmpList.add(li);
                                    groupMap.put(li.getPayWay(), tmpList);
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    Iterator<Map.Entry<String, List<PayInfoVo>>> iterator = groupMap.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<String, List<PayInfoVo>> next = iterator.next();
                        List<PayInfoVo> value = next.getValue();
                        PayInfoVo pay = new PayInfoVo();
                        if (Boolean.parseBoolean(selectappid))
                            pay.setApp(next.getKey());
                        else
                            pay.setPayWay(next.getKey());

                        double sum = 0;
                        int payCount = 0;
                        int payNum = 0;
                        for (PayInfoVo pa : value) {
                            sum += pa.getAmount();
                            payCount += pa.getPayCount();
                            payNum += pa.getPayNumber();
                        }
                        pay.setAmount(sum);
                        pay.setPayNumber(payNum);
                        pay.setPayCount(payCount);
                        if (payNum != 0) {
                            pay.setArpu(sum / 100 / payNum);
                        }
                        buslist.add(pay);
                    }
                }
                PayInfoVo pay = new PayInfoVo();
                double sum = 0;
                int payCount = 0;
                int payNum = 0;
                pay.setCreattime("总计：");
                if (buslist != null) {
                    Iterator<PayInfoVo> iterator = buslist.iterator();
                    while (iterator.hasNext()) {
                        PayInfoVo payInfoVo = iterator.next();
                        if (payInfoVo.getAmount() == 0) {
                            iterator.remove();
                        }
                    }
                }
                for (int i = 0; i < buslist.size(); i++) {
                    buslist.get(i).setAmount(buslist.get(i).getAmount() / 100);
                    sum += buslist.get(i).getAmount();
                    payCount += buslist.get(i).getPayCount();
                    payNum += buslist.get(i).getPayNumber();

                    // 小数点格式化处理
                    DecimalFormat df = new DecimalFormat("#0.00");
                    df.setRoundingMode(RoundingMode.FLOOR);
                    buslist.get(i).setArpu(Double.valueOf(df.format(buslist.get(i).getArpu())));
                }
                BigDecimal bigDecimal = new BigDecimal(sum);
                sum = bigDecimal.setScale(2, BigDecimal.ROUND_FLOOR).doubleValue();
                pay.setAmount(sum);
                pay.setPayNumber(payNum);
                pay.setPayCount(payCount);
                if (payNum != 0) {
                    pay.setArpu(sum / payNum);
                } else {
                    pay.setArpu(Double.valueOf(0));
                }

                PageForList<PayInfoVo> pager = new PageForList<>(pageNo, pageSize, buslist);
                pager.getResultList().add(pay); // 汇总添加到末尾

                buslist.add(pay);
                redisTemplate.opsForValue().set("oldPayInfoExcelList", buslist, 20, TimeUnit.MINUTES);
                pagerToJsonString(response, pager);
            } else {//未进行分组
                List<PayInfoVo> list = new ArrayList<>();
                if (payWays == null || payWays.equals("")) {
                    List<PayInfoVo> oppoList = payService.queryOppoPay(parmMap);
                    List<PayInfoVo> viewList = payService.queryViewPay(parmMap);
                    List<PayInfoVo> wxList = payService.queryWxPay(parmMap);
                    List<PayInfoVo> aliList = payService.queryAliPay(parmMap);
                    List<PayInfoVo> hwList = payService.queryHWPay(parmMap);
                    List<PayInfoVo> xmList = payService.queryXMPay(parmMap);
                    List<PayInfoVo> mzList = payService.queryMZPay(parmMap);
                    List<PayInfoVo> jlList = payService.queryJLPay(parmMap);
                    List<PayInfoVo> bdList = payService.queryBDPay(parmMap);
                    List<PayInfoVo> sxList = payService.querySXPay(parmMap);
                    List<PayInfoVo> lxList = payService.queryLXPay(parmMap);
                    List<PayInfoVo> txList = payService.queryTXPay(parmMap);

                    List<PayInfoVo> newoppoList = payService.queryOppoPayNew(parmMap);
                    List<PayInfoVo> newviewList = payService.queryViewPayNew(parmMap);
                    List<PayInfoVo> newwxList = payService.queryWxPayNew(parmMap);
                    List<PayInfoVo> newaliList = payService.queryAliPayNew(parmMap);
                    List<PayInfoVo> newhwList = payService.queryHWPayNew(parmMap);
                    List<PayInfoVo> newxmList = payService.queryXMPayNew(parmMap);
                    List<PayInfoVo> newmzList = payService.queryMZPayNew(parmMap);
                    List<PayInfoVo> newjlList = payService.queryJLPayNew(parmMap);
                    List<PayInfoVo> newbdList = payService.queryBDPayNew(parmMap);
                    List<PayInfoVo> newsxList = payService.querySXPayNew(parmMap);
                    List<PayInfoVo> newlxList = payService.queryLXPayNew(parmMap);
                    List<PayInfoVo> newtxList = payService.queryTXPayNew(parmMap);
                    list.addAll(bdList);
                    list.addAll(xmList);
                    list.addAll(mzList);
                    list.addAll(jlList);
                    list.addAll(oppoList);
                    list.addAll(viewList);
                    list.addAll(wxList);
                    list.addAll(aliList);
                    list.addAll(hwList);
                    list.addAll(sxList);
                    list.addAll(lxList);
                    list.addAll(txList);

                    list.addAll(newbdList);
                    list.addAll(newxmList);
                    list.addAll(newmzList);
                    list.addAll(newjlList);
                    list.addAll(newoppoList);
                    list.addAll(newviewList);
                    list.addAll(newwxList);
                    list.addAll(newaliList);
                    list.addAll(newhwList);
                    list.addAll(newsxList);
                    list.addAll(newlxList);
                    list.addAll(newtxList);
                } else {
                    String[] split = payWays.split(",");
                    for (String payWay : split) {

                        if (payWay.equals("1")) {
                            List<PayInfoVo> aliList = payService.queryAliPay(parmMap);
                            List<PayInfoVo> newaliList = payService.queryAliPayNew(parmMap);
                            list.addAll(newaliList);
                            list.addAll(aliList);
                        }
                        if (payWay.equals("2")) {
                            List<PayInfoVo> wxList = payService.queryWxPay(parmMap);
                            list.addAll(wxList);
                            List<PayInfoVo> newwxList = payService.queryWxPayNew(parmMap);
                            list.addAll(newwxList);
                        }
                        if (payWay.equals("3")) {
                            List<PayInfoVo> oppoList = payService.queryOppoPay(parmMap);
                            list.addAll(oppoList);
                            List<PayInfoVo> newoppoList = payService.queryOppoPayNew(parmMap);
                            list.addAll(newoppoList);
                        }
                        if (payWay.equals("4")) {
                            List<PayInfoVo> viewList = payService.queryViewPay(parmMap);
                            list.addAll(viewList);
                            List<PayInfoVo> newviewList = payService.queryViewPayNew(parmMap);
                            list.addAll(newviewList);
                        }
                        if (payWay.equals("5")) {
                            List<PayInfoVo> hwList = payService.queryHWPay(parmMap);
                            list.addAll(hwList);
                            List<PayInfoVo> newhwList = payService.queryHWPayNew(parmMap);
                            list.addAll(newhwList);
                        }
                        if (payWay.equals("6")) {
                            List<PayInfoVo> xmList = payService.queryXMPay(parmMap);
                            list.addAll(xmList);
                            List<PayInfoVo> newxmList = payService.queryXMPayNew(parmMap);
                            list.addAll(newxmList);
                        }
                        if (payWay.equals("7")) {
                            List<PayInfoVo> bdList = payService.queryBDPay(parmMap);
                            list.addAll(bdList);
                            List<PayInfoVo> newbdList = payService.queryBDPayNew(parmMap);
                            list.addAll(newbdList);
                        }
                        if (payWay.equals("8")) {
                            List<PayInfoVo> mzList = payService.queryMZPay(parmMap);
                            list.addAll(mzList);
                            List<PayInfoVo> newmzList = payService.queryMZPayNew(parmMap);
                            list.addAll(newmzList);
                        }
                        if (payWay.equals("9")) {
                            List<PayInfoVo> jlList = payService.queryJLPay(parmMap);
                            list.addAll(jlList);
                            List<PayInfoVo> newjlList = payService.queryJLPayNew(parmMap);
                            list.addAll(newjlList);
                        }
                        if (payWay.equals("10")) {
                            List<PayInfoVo> sxList = payService.querySXPay(parmMap);
                            list.addAll(sxList);
                            List<PayInfoVo> newsxList = payService.querySXPayNew(parmMap);
                            list.addAll(newsxList);
                        }
                        if (payWay.equals("11")) {
                            List<PayInfoVo> lxList = payService.queryLXPay(parmMap);
                            list.addAll(lxList);
                            List<PayInfoVo> newlxList = payService.queryLXPayNew(parmMap);
                            list.addAll(newlxList);
                        }
                        if (payWay.equals("12")) {
                            List<PayInfoVo> txList = payService.queryTXPay(parmMap);
                            list.addAll(txList);
                            List<PayInfoVo> newtxList = payService.queryTXPayNew(parmMap);
                            list.addAll(newtxList);
                        }
                    }
                }
                //List<PayInfoVo> buslist = new ArrayList<>();
                PayInfoVo pay = new PayInfoVo();
                double sum = 0;
                int payCount = 0;
                int payNum = 0;
                pay.setCreattime("总计：");
                if (list != null) {
                    Iterator<PayInfoVo> iterator = list.iterator();
                    while (iterator.hasNext()) {
                        PayInfoVo payInfoVo = iterator.next();
                        if (payInfoVo.getAmount() == 0) {
                            iterator.remove();
                        }
                    }
                }
                for (int i = 0; i < list.size(); i++) {
                    list.get(i).setAmount(list.get(i).getAmount() / 100);
                    sum += list.get(i).getAmount();
                    payCount += list.get(i).getPayCount();
                    payNum += list.get(i).getPayNumber();

                    // 小数点格式化处理
                    DecimalFormat df = new DecimalFormat("#0.00");
                    df.setRoundingMode(RoundingMode.FLOOR);
                    list.get(i).setArpu(Double.valueOf(df.format(list.get(i).getArpu())));
                }
                BigDecimal bigDecimal = new BigDecimal(sum);
                sum = bigDecimal.setScale(2, BigDecimal.ROUND_FLOOR).doubleValue();
                pay.setAmount(sum);
                pay.setPayNumber(payNum);
                pay.setPayCount(payCount);
                if (payNum != 0) {
                    pay.setArpu(sum / payNum);
                } else {
                    pay.setArpu(Double.valueOf(0));
                }

                PageForList<PayInfoVo> pager = new PageForList<>(pageNo, pageSize, list);
                pager.getResultList().add(pay);

                list.add(pay);
                redisTemplate.opsForValue().set("oldPayInfoExcelList", list, 20, TimeUnit.MINUTES);
                pagerToJsonString(response, pager);
            }
        } catch (Exception e) {
            List<PayInfoVo> list = new ArrayList<>();
            PayInfoVo pay = new PayInfoVo();
            list.add(pay);
            PageForList<PayInfoVo> pager = new PageForList<>(0, 100, list);
            pagerToJsonString(response, pager);
        }
    }

    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(HttpServletRequest request, HttpServletResponse response) {

        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        List<PayInfoVo> conList =
                (List<PayInfoVo>) redisTemplate.opsForValue().get("oldPayInfoExcelList");

        for (PayInfoVo adv : conList) {
            DecimalFormat df = new DecimalFormat("#0.00");
            df.setRoundingMode(RoundingMode.FLOOR);

            Map<String, Object> obj = (Map<String, Object>) JSONObject.parseObject(JSONObject.toJSONString(adv), Map.class);
            obj.put("arpu", df.format(Double.valueOf(obj.get("arpu") + "")));
            contentList.add(obj);
        }

        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("creattime", "时间");
        headerMap.put("prjid", "项目ID");
        headerMap.put("app", "应用名称");
        headerMap.put("payWay", "支付方式");
        headerMap.put("amount", "付费金额");
        headerMap.put("payNumber", "付费人数");
        headerMap.put("payCount", "付费次数");
        headerMap.put("arpu", "付费ARPU");

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "支付信息查询报表_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
    }

    public void pagerToJsonString(HttpServletResponse response, Pager pager) {
        String result = JSONArray.toJSONString(pager.getResultList());
        String jsonString = "{'totalCount':" + pager.getTotalRows() + ",'root':" + result + "}";
        try {
            response.setCharacterEncoding("utf-8");
            PrintWriter out = response.getWriter();
            out.print(jsonString);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
