package com.wbgame.controller.mobile.query;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.DiamondWithdrawInfo;
import com.wbgame.service.YdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: 红包产品提现查询
 * @author: huangmb
 * @date: 2021/02/24
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/redpackdraw")
public class RedpackdrawController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private YdService ydService;

    /**
     * 红包产品提现查询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public String list(HttpServletRequest request, HttpServletResponse response) {

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("pid", request.getParameter("pid"));
        map.put("appid", request.getParameter("appid"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("imei", request.getParameter("imei"));
        map.put("lsn", request.getParameter("lsn"));
        map.put("order_str",request.getParameter("order_str"));

        JSONObject result = new JSONObject();

        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<DiamondWithdrawInfo> list = ydService.selectRedpackdrawVo(map);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();

    }

    /**
     * 红包产品提现导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(HttpServletRequest request, HttpServletResponse response) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("pid", request.getParameter("pid"));
        map.put("appid", request.getParameter("appid"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        map.put("imei", request.getParameter("imei"));
        map.put("lsn", request.getParameter("lsn"));
        map.put("order_str",request.getParameter("order_str"));

        List<DiamondWithdrawInfo> list = ydService.selectRedpackdrawVo(map);

        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("appid", "appid");
        headerMap.put("pid", "pid");
        headerMap.put("lsn", "lsn");
        headerMap.put("imei", "imei");
        headerMap.put("openid", "openid");
        headerMap.put("amount", "金额");
        headerMap.put("type", "奖励类型");
        headerMap.put("createTime", "提现时间");

        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(export_file_name)){
            export_file_name = "红包产品提现查询";
        }
        String fileName = export_file_name +"_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX2(response,list,headerMap,fileName);
    }

}
