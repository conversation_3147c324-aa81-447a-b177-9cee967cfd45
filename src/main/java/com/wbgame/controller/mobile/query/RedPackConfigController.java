package com.wbgame.controller.mobile.query;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.RedPackConfigVo;
import com.wbgame.service.YdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: 红包产品数据查询
 * @author: huangmb
 * @date: 2021/02/24
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/redPackConfig")
public class RedPackConfigController {

    @Autowired
    private YdService ydService;

    /**
     * 移动-数据查询-单机查询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public String list(HttpServletRequest request,
                                HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("cPid", request.getParameter("cPid"));
        map.put("gameName", request.getParameter("gameName"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        // 是否隐藏 1为true 0 false
        map.put("hide", request.getParameter("hide"));
        map.put("order",request.getParameter("order"));

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<RedPackConfigVo> list = ydService.selectRedPack(map);
            long size = ((Page) list).getTotal();
            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    @RequestMapping(value = "/export", method = {
            RequestMethod.GET, RequestMethod.POST})
    public String export(HttpServletRequest request,
                                      HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        // 数据内容
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("cPid", request.getParameter("cPid"));
        map.put("gameName", request.getParameter("gameName"));
        map.put("startTime", request.getParameter("startTime"));
        map.put("endTime", request.getParameter("endTime"));
        // 是否隐藏 1为true 0 false
        map.put("hide", request.getParameter("hide"));
        map.put("order",request.getParameter("order"));
        List<RedPackConfigVo> list = ydService.selectRedPack(map);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;
        if (null != list.get(0).getVersionName()) {
            for (RedPackConfigVo temp : list) {
                headerMap.put("cDate", "日期");
                headerMap.put("gameName", "产品名称");
                headerMap.put("platform", "渠道标识");
                headerMap.put("versionName", "版本号");
                headerMap.put("cPid", "项目id");
//			headerMap.put("cPrice", "提现单价");
                headerMap.put("cUsers", "提现人数");
                headerMap.put("cTotal", "提现总额");
                headerMap.put("cTwoRate", "提现用户次日留存");
                headerMap.put("cThreeRate", "提现用户三日留存");
                headerMap.put("cSevenRate", "提现用户七日留存");
                headerMap.put("cDau", "活跃用户数");
                headerMap.put("rate", "提现比例");


                contentMap = new LinkedHashMap<String, Object>();
                contentMap.put("cDate", temp.getcDate());
                contentMap.put("gameName", temp.getGameName());
                contentMap.put("platform", temp.getPlatform());
                contentMap.put("versionName", temp.getVersionName());
                contentMap.put("cPid", temp.getcPid());
//			contentMap.put("cPrice", temp.getcPrice());
                contentMap.put("cUsers", temp.getcUsers());
                contentMap.put("cTotal", temp.getcTotal());
                contentMap.put("cTwoRate", temp.getcTwoRate());
                contentMap.put("cThreeRate", temp.getcThreeRate());
                contentMap.put("cSevenRate", temp.getcSevenRate());
                contentMap.put("cDau", temp.getcDau());
                contentMap.put("rate", temp.getRate());

                contentList.add(contentMap);
            }
        } else {
            for (RedPackConfigVo temp : list) {
                headerMap.put("cDate", "日期");
                headerMap.put("gameName", "产品名称");
                headerMap.put("platform", "渠道标识");
                headerMap.put("cUsers", "提现人数");
                headerMap.put("cTotal", "提现总额");
                headerMap.put("cTwoRate", "提现用户次日留存");
                headerMap.put("cThreeRate", "提现用户三日留存");
                headerMap.put("cSevenRate", "提现用户七日留存");
                headerMap.put("cDau", "活跃用户数");
                headerMap.put("rate", "提现比例");

                contentMap = new LinkedHashMap<String, Object>();
                contentMap.put("cDate", temp.getcDate());
                contentMap.put("gameName", temp.getGameName());
                contentMap.put("platform", temp.getPlatform());
                contentMap.put("cUsers", temp.getcUsers());
                contentMap.put("cTotal", temp.getcTotal());
                contentMap.put("cTwoRate", temp.getcTwoRate());
                contentMap.put("cThreeRate", temp.getcThreeRate());
                contentMap.put("cSevenRate", temp.getcSevenRate());
                contentMap.put("cDau", temp.getcDau());
                contentMap.put("rate", temp.getRate());

                contentList.add(contentMap);
            }
        }
        String fileName = "红包产品数据_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);

        return null;
    }
}
