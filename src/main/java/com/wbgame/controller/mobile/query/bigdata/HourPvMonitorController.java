package com.wbgame.controller.mobile.query.bigdata;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.wbgame.common.Asserts;
import com.wbgame.pojo.mobile.HourPvMonitorVo;
import com.wbgame.service.AdService;
import com.wbgame.service.mobile.BigDataService;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Classname HourPvMonitorController
 * @Description TODO
 * @Date 2022/1/17 10:01
 */
@RequestMapping("/mobile/hourpv")
@RestController
@CrossOrigin
public class HourPvMonitorController {

    Logger logger = LoggerFactory.getLogger(HourPvMonitorController.class);

    @Autowired
    private AdService adService;

    @Autowired
    private BigDataService bigDataService;

    /**
     * 广告pv小时表查询
     * @param request
     * @return
     */
    @RequestMapping("list")
    public Object list(HttpServletRequest request){

        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String pid = request.getParameter("pid");
        String ad_type = request.getParameter("ad_type");

        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid",appid);
        paramMap.put("pid",pid);
        paramMap.put("ad_type",ad_type);

        JSONObject result = new JSONObject();
        try {
            List<HourPvMonitorVo> list = bigDataService.getHourPvMonitorList(paramMap);
            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", list.size());
        } catch (Exception e) {
            result.put("ret", 0);
            result.put("msg", "错误消息："+e.getMessage());
            logger.error("HourPvMonitorController list error:",e);
        }
        return result;
    }
    /**
     * 广告pv小时表导出
     * @param request
     * @return
     */
    @RequestMapping("export")
    public void export(HttpServletRequest request,HttpServletResponse response){
        String value = request.getParameter("value");
        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();


        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String pid = request.getParameter("pid");
        String ad_type = request.getParameter("ad_type");

        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("start_date",start_date);
        paramMap.put("end_date",end_date);
        paramMap.put("appid",appid);
        paramMap.put("pid",pid);
        paramMap.put("ad_type",ad_type);


        List<HourPvMonitorVo> list = bigDataService.getHourPvMonitorList(paramMap);
        for (HourPvMonitorVo vo:list){
            vo.setBig_data_pv_rate(new BigDecimal(vo.getBig_data_pv_rate()).multiply(new BigDecimal("100")).setScale(2) +"%");
            vo.setBig_data_forward_pv_rate(new BigDecimal(vo.getBig_data_forward_pv_rate()).multiply(new BigDecimal("100")).setScale(2) +"%");

            Map<String, Object> app = appMap.get(vo.getAppid());
            if(app != null){
                vo.setAppname(app.get("app_name")+"");
            }
        }
        Map<String,String> headerMap = new LinkedHashMap<>();
        //自定义列数据
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                headerMap.put(s[0],s[1]);
            }
        }catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        ExportExcelUtil.export2(response,list,headerMap,"广告pv小时表_" + DateTime.now().toString("yyyyMMdd")+ ".xls");
    }
}
