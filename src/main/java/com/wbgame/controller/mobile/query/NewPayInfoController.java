package com.wbgame.controller.mobile.query;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.redpack.InGamePaymentConfigMapper;
import com.wbgame.pojo.custom.PageForList;
import com.wbgame.pojo.operate.InGamePaymentConfigVo;
import com.wbgame.pojo.operate.InGamePaymentReportVo;
import com.wbgame.service.AdService;
import com.wbgame.service.PayOrderService;
import com.wbgame.service.RedisService;
import com.wbgame.utils.*;
import com.wbgame.utils.wx.WechatPayHttpClientBuilder;
import com.wbgame.utils.wx.auth.AutoUpdateCertificatesVerifier;
import com.wbgame.utils.wx.auth.PrivateKeySigner;
import com.wbgame.utils.wx.auth.WechatPay2Credentials;
import com.wbgame.utils.wx.auth.WechatPay2Validator;
import com.wbgame.utils.wx.util.PemUtil;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.PrivateKey;
import java.util.*;

/**
 * @description: 新版支付信息查询
 * @author: huangmb
 * @date: 2021/02/24
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/newPayInfo")
public class NewPayInfoController {

    @Autowired
    private AdService adService;

    @Resource
    private InGamePaymentConfigMapper inGamePaymentConfigMapper;

    @Autowired
    private PayOrderService payOrderService;

    @Autowired
    private RedisService redisService;

    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String list(HttpServletRequest request) throws IOException {
        String begin = BlankUtils.checkNull(request, "begin");
        String end = BlankUtils.checkNull(request, "end");
        String prjId = BlankUtils.checkNull(request, "prjId");
        String payWays = BlankUtils.checkNull(request, "payWay");
        String app = BlankUtils.checkNull(request, "app");
        String order = BlankUtils.checkNull(request, "order");
        String zone_id = BlankUtils.checkNull(request,"zone_id");
        String appid_group = BlankUtils.checkNull(request, "appid_group");
        String prjid_group = BlankUtils.checkNull(request, "prjid_group");
        String payway_group = BlankUtils.checkNull(request, "payway_group");
        String date_group = BlankUtils.checkNull(request, "date_group");
        String appCategory_group = BlankUtils.checkNull(request,"appCategory_group");
        String mchid_group = BlankUtils.checkNull(request,"mchid_group");
        String zoneid_group = BlankUtils.checkNull(request,"zoneid_group");
        String type = BlankUtils.checkNull(request, "type");
        String appCategory = BlankUtils.checkNull(request,"appCategory");
        //获取页码
        String start = request.getParameter("start");
        String limit = request.getParameter("limit");
        //自定义时间段参数
        String custom_date = request.getParameter("custom_date");
        // pageStart 和 limit设置值
        int pageStart = StringUtils.isEmpty(start) ? 0 : Integer.parseInt(start);
        int pageSize = StringUtils.isEmpty(limit) ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        // 筛除sql敏感字符注入
        if (BlankUtils.sqlValidate(begin)
                || BlankUtils.sqlValidate(end)
                || BlankUtils.sqlValidate(app)
                || BlankUtils.sqlValidate(prjId)
                || BlankUtils.sqlValidate(payWays)) {
            return ReturnJson.error(Constants.ParamError);
        }
        //分组
        String group = "";
        if (!BlankUtils.checkBlank(date_group)) {
            group = group + "," + date_group;
        }
        if (!BlankUtils.checkBlank(appid_group)) {
            group = group + ",a.appid";
        }
        if (!BlankUtils.checkBlank(prjid_group)) {
            group = group +  ",pid";
        }
        if (!BlankUtils.checkBlank(payway_group)) {
            group = group +  ",paytype";
        }
        if (!BlankUtils.checkBlank(appCategory_group)) {
            group = group + ",app_category";
        }
        if (!BlankUtils.checkBlank(mchid_group)) {
            group = group + ",param2";
        }
        if (!BlankUtils.checkBlank(zoneid_group)){
            group = group + ",zone_id";
        }
        if (BlankUtils.checkBlank(group)) {
            group = "tdate,a.appid,pid,paytype,app_category";
        }else{
            group = group.substring(1);
        }
        if (BlankUtils.checkBlank(order)) {
            order = "amount desc";
        }
        Map<String,Object> param = new HashMap<>();
        param.put("begin",begin);
        param.put("end",end);
        param.put("prjId",prjId);
        param.put("payWays",payWays);
        param.put("app",app);
        param.put("order",order);
        param.put("group",group);
        param.put("type",type);
        param.put("appCategory",appCategory);
        param.put("zone_id",zone_id);
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> sum = null;
        if (StringUtils.isEmpty(custom_date)) {
            list = adService.selectNewPayInfo(param);
            sum = adService.countNewPayInfo(param);
        } else {
            //自定义时间段
            String[] split = custom_date.split(";");
            for (String date : split) {
                String[] split1 = date.split(",");
                param.put("begin",split1[0]);
                param.put("end",split1[1]);
                //封装这个参数是为了在sql层面判断是否为自定义查询
                param.put("custom_date","custom_date");
                list.addAll(adService.selectNewPayInfo(param));
            }
        }
        Map<String, Map<String, Object>> queryMap = adService.getAppInfoMap();
        Map<String, Map<String, Object>> prjMap = adService.selectProjectidChannelMap();
        //分页操作
        PageForList<Map<String, Object>> pageForList = new PageForList<>(pageNo, pageSize, list);
        List<Map<String, Object>> resultList = pageForList.getResultList();
        for (Map<String, Object> map : resultList) {
            if (map.get("appid") != null && queryMap.get(map.get("appid")) != null) {
                map.put("app", queryMap.get(map.get("appid")).get("app_name") + "");
            }
            //增加投放子渠道
            if(!BlankUtils.checkBlank(prjid_group)){
                map.put("channel_tag",prjMap.get(map.get("prjid"))==null?"":prjMap.get(map.get("prjid")).get("cha_id"));
            }
            String tdate = map.get("tdate").toString();
            //处理日期格式
            if (!StringUtils.isEmpty(group) && group.contains("tdate")) {
                map.put("creattime",tdate);
            }else if (!StringUtils.isEmpty(group) && group.contains("week")) {
                String[] split = tdate.split("-");
                if (split.length >= 2) {
                    int year = Integer.parseInt(split[0]);
                    int week = Integer.parseInt(split[1]);
                    String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week);
                    String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week);
                    map.put("creattime",year+"年第"+week+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                }
            }else if (!StringUtils.isEmpty(group) && group.contains("beek")) {
                String[] split = tdate.split("-");
                if (split.length >= 3) {
                    int year = Integer.parseInt(split[0]);
                    int week1 = Integer.parseInt(split[1]);
                    int week2 = Integer.parseInt(split[2]);
                    String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week1);
                    String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week2);
                    map.put("creattime",year+"年第"+week1+"-"+week2+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                }
            } else {
                map.put("creattime",tdate);
            }
        }
        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", resultList);
        result.put("totalCount", pageForList.getTotalRows());
        result.put("sum", sum);
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    @RequestMapping(value = "/export", method = {RequestMethod.POST, RequestMethod.GET})
    public void export(HttpServletRequest request, HttpServletResponse response) {
        String begin = BlankUtils.checkNull(request, "begin");
        String end = BlankUtils.checkNull(request, "end");
        String prjId = BlankUtils.checkNull(request, "prjId");
        String payWays = BlankUtils.checkNull(request, "payWay");
        String app = BlankUtils.checkNull(request, "app");
        String order = BlankUtils.checkNull(request, "order");
        String zone_id = BlankUtils.checkNull(request,"zone_id");
        String appid_group = BlankUtils.checkNull(request, "appid_group");
        String prjid_group = BlankUtils.checkNull(request, "prjid_group");
        String payway_group = BlankUtils.checkNull(request, "payway_group");
        String date_group = BlankUtils.checkNull(request, "date_group");
        String appCategory_group = BlankUtils.checkNull(request,"appCategory_group");
        String mchid_group = BlankUtils.checkNull(request,"mchid_group");
        String zoneid_group = BlankUtils.checkNull(request,"zoneid_group");
        String type = BlankUtils.checkNull(request, "type");
        String appCategory = BlankUtils.checkNull(request,"appCategory");
        //自定义时间段参数
        String custom_date = request.getParameter("custom_date");
        // 筛除sql敏感字符注入
        if (BlankUtils.sqlValidate(begin)
                || BlankUtils.sqlValidate(end)
                || BlankUtils.sqlValidate(app)
                || BlankUtils.sqlValidate(prjId)
                || BlankUtils.sqlValidate(payWays)) {
            Asserts.fail(Constants.ParamError);
        }
        //分组
        String group = "";
        if (!BlankUtils.checkBlank(date_group)) {
            group = group + "," + date_group;
        }
        if (!BlankUtils.checkBlank(appid_group)) {
            group = group + ",a.appid";
        }
        if (!BlankUtils.checkBlank(prjid_group)) {
            group = group +  ",pid";
        }
        if (!BlankUtils.checkBlank(payway_group)) {
            group = group +  ",paytype";
        }
        if (!BlankUtils.checkBlank(appCategory_group)) {
            group = group + ",app_category";
        }
        if (!BlankUtils.checkBlank(mchid_group)) {
            group = group + ",param2";
        }
        if (!BlankUtils.checkBlank(zoneid_group)){
            group = group + ",zone_id";
        }
        if (BlankUtils.checkBlank(group)) {
            group = "tdate,a.appid,pid,paytype,app_category";
        }else{
            group = group.substring(1);
        }
        if (BlankUtils.checkBlank(order)) {
            order = "amount desc";
        }
        Map<String,Object> param = new HashMap<>();
        param.put("begin",begin);
        param.put("end",end);
        param.put("prjId",prjId);
        param.put("payWays",payWays);
        param.put("app",app);
        param.put("order",order);
        param.put("group",group);
        param.put("type",type);
        param.put("appCategory",appCategory);
        param.put("zone_id",zone_id);

        List<Map<String, Object>> list = new ArrayList<>();
        if (StringUtils.isEmpty(custom_date)) {
            list = adService.selectNewPayInfo(param);
        } else {
            //自定义时间段
            String[] split = custom_date.split(";");
            for (String date : split) {
                String[] split1 = date.split(",");
                param.put("begin",split1[0]);
                param.put("end",split1[1]);
                //封装这个参数是为了在sql层面判断是否为自定义查询
                param.put("custom_date","custom_date");
                list.addAll(adService.selectNewPayInfo(param));
            }
        }
        Map<String, Map<String, Object>> queryMap = adService.getAppInfoMap();
        Map<String, Map<String, Object>> prjMap = adService.selectProjectidChannelMap();
        for (Map<String, Object> map : list) {
            if (map.get("appid") != null && queryMap.get(map.get("appid")) != null) {
                map.put("app", queryMap.get(map.get("appid")).get("app_name") + "");
            }
            //增加投放子渠道
            if(!BlankUtils.checkBlank(prjid_group)){
                map.put("channel_tag",prjMap.get(map.get("prjid"))==null?"":prjMap.get(map.get("prjid")).get("cha_id"));
            }
            String tdate = map.get("tdate").toString();
            //处理日期格式
            if (!StringUtils.isEmpty(group) && group.contains("tdate")) {
                map.put("creattime",tdate);
            }else if (!StringUtils.isEmpty(group) && group.contains("week")) {
                String[] split = tdate.split("-");
                if (split.length >= 2) {
                    int year = Integer.parseInt(split[0]);
                    int week = Integer.parseInt(split[1]);
                    String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week);
                    String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week);
                    map.put("creattime",year+"年第"+week+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                }
            }else if (!StringUtils.isEmpty(group) && group.contains("beek")) {
                String[] split = tdate.split("-");
                if (split.length >= 3) {
                    int year = Integer.parseInt(split[0]);
                    int week1 = Integer.parseInt(split[1]);
                    int week2 = Integer.parseInt(split[2]);
                    String firstDayOfWeek = DateUtil.getFirstDayOfWeek(year,week1);
                    String lastDayOfWeek = DateUtil.getLastDayOfWeek(year,week2);
                    map.put("creattime",year+"年第"+week1+"-"+week2+"周:"+firstDayOfWeek+"至"+lastDayOfWeek);
                }
            } else {
                map.put("creattime",tdate);
            }
        }
        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = BlankUtils.checkNull(request, "value");
        try {
            String[] split = value.split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                headerMap.put(s[0], s[1]);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }

        String fileName = "新版支付信息查询_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX(response,list,headerMap,fileName);
    }

    /**
     * 同步成功订单数据到adb
     * @return
     */
    @RequestMapping("/synSuccessOrder")
    public String synSuccessOrder(String startTime,String endTime){
        if (BlankUtils.checkBlank(startTime) || BlankUtils.checkBlank(endTime)) {
            return ReturnJson.toErrorJson("日期参数错误");
        }
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime start = DateTime.parse(startTime, format);
        DateTime end = DateTime.parse(endTime,format);
        if (start.compareTo(end) > 0) {
            return ReturnJson.toErrorJson("开始时间不能大于结束时间");
        }
        if (start.compareTo(end.minusDays(7)) < 0) {
            return ReturnJson.toErrorJson("同步时间不能大于7天");
        }
        //验证是否需要锁住方法
        if (redisService.hasKey("synadbSuccessOrder")) {
            return ReturnJson.toErrorJson("同步执行中,请稍后再试");
        }
        try {
            redisService.set("synadbSuccessOrder","lock");
            int index = payOrderService.synSuccessPayOrder(startTime,endTime);
            return ReturnJson.success("成功同步数据:"+index);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnJson.toErrorJson("同步数据异常:"+e.getMessage());
        }finally {
            redisService.del("synadbSuccessOrder");
        }
    }

    /**
     * 查询微信api订单
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/getOrder")
    public String getOrder(HttpServletRequest request) throws Exception{

        String appid = request.getParameter("appid");
        String channel = request.getParameter("channel");
        if (BlankUtils.checkBlank(channel)) {
            channel = "'wx'";
        }
        String orderid = request.getParameter("orderid");
        InGamePaymentReportVo param = new InGamePaymentReportVo();
        param.setAppid(appid);
        param.setChannel(channel);
        List<InGamePaymentConfigVo> inGamePaymentConfigVos = inGamePaymentConfigMapper.selectList(param);
        if (inGamePaymentConfigVos == null || inGamePaymentConfigVos.size() == 0) {
            return ReturnJson.toErrorJson("未找到微信内购参数配置,请先配置参数再查询订单");
        }


        String mchId = inGamePaymentConfigVos.get(0).getMachid();
        String privateKey = inGamePaymentConfigVos.get(0).getPrivate_key();
        String mchSerialNo = inGamePaymentConfigVos.get(0).getMch_serial();
        String apiV3Key = inGamePaymentConfigVos.get(0).getApi_key();
        // 加载商户私钥（privateKey：私钥字符串）
        PrivateKey merchantPrivateKey = PemUtil
                .loadPrivateKey(new ByteArrayInputStream(privateKey.getBytes("utf-8")));

        // 加载平台证书（mchId：商户号,mchSerialNo：商户证书序列号,apiV3Key：V3密钥）
        AutoUpdateCertificatesVerifier verifier = new AutoUpdateCertificatesVerifier(
                new WechatPay2Credentials(mchId, new PrivateKeySigner(mchSerialNo, merchantPrivateKey)), apiV3Key.getBytes("utf-8"));

        // 初始化httpClient
        CloseableHttpClient httpClient = WechatPayHttpClientBuilder.create()
                .withMerchant(mchId, mchSerialNo, merchantPrivateKey)
                .withValidator(new WechatPay2Validator(verifier)).build();

        //请求URL
        HttpGet httpGet = new HttpGet("https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/"+orderid+"?mchid="+mchId);
        httpGet.setHeader("Accept", "application/json");
        CloseableHttpResponse response = httpClient.execute(httpGet);
        try {
            int statusCode = response.getStatusLine().getStatusCode();
            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
            return EntityUtils.toString(response.getEntity());
        } finally {
            //关闭连接
            response.close();
            httpClient.close();
        }
    }
}
