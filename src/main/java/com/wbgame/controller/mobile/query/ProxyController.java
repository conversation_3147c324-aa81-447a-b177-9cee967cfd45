package com.wbgame.controller.mobile.query;

import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.response.Result;
import com.wbgame.service.mobile.CertificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

/**
 * 当前类用于请求转发http请求到指定url，并将消息内容返回
 */
@RestController
@RequestMapping("/mobile/proxy")
@CrossOrigin
public class ProxyController {
    private static final int MAX_CONNECT_TIMEOUT = 1000 * 30;
    private static final int MAX_READ_TIMEOUT = 1000 * 60 * 10;

    private final RestTemplate restTemplate;

    @Autowired
    private CertificateService certificateService;


    @Autowired
    public ProxyController(RestTemplateBuilder restTemplateBuilder) {
        //动态获取RestTemplate并设置：最大连接时间30秒，读取数据最大超时时间为10分钟
        this.restTemplate = restTemplateBuilder.setConnectTimeout(MAX_CONNECT_TIMEOUT).setReadTimeout(MAX_READ_TIMEOUT).build();
    }

    /**
     * 接收GET参数并转发请求的接口（支持动态目标URL）
     *
     * @param params 客户端传入的所有GET参数（若包含_getUrl_则优先作为目标URL）
     * @return 目标接口的响应内容
     */
    @GetMapping("/forward")
    public String forwardGetRequest(@RequestParam Map<String, String> params) {
        // 优先从参数中获取目标URL
        // 移除控制参数（避免传递到目标接口）
        String targetUrl = params.remove("_getUrl_");
        // 若未传入_getUrl_，使用默认基础URL（需替换为实际地址）
        boolean checkFlag = targetUrl == null || targetUrl.isEmpty();
        if (checkFlag) {
            targetUrl = "http://120.79.186.67/check_ssl"; // 默认目标URL
        }
        // 构建带参数的目标URL（自动处理参数编码）
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(targetUrl);
        params.forEach(urlBuilder::queryParam);
        String finalTargetUrl = urlBuilder.build().encode().toUriString();
        String responseBody = null;
        try {
            // 转发请求并获取响应
            ResponseEntity<String> targetResponse = restTemplate.getForEntity(finalTargetUrl, String.class);
            // 返回目标接口的原始响应（包含状态码、头信息和内容）
            ResponseEntity<String> response = ResponseEntity.status(targetResponse.getStatusCode()).headers(targetResponse.getHeaders()).body(targetResponse.getBody());
            responseBody = response.getBody();
        } catch (RestClientException e) {
            // 处理请求异常
            return ReturnJson.toErrorJson("请求转发失败：" + e.getMessage());
        }
        // 需要对申请结果.成功数据需要更新下载地址和自动申请证书操作
        //checkFlag = true 表示为证书检查接口转发， checkFlag = false 表示证书申请接口转发
        if (!checkFlag) {
            //证书申请
            Result<Long> uploaded = certificateService.uploadCertificate(responseBody);
            System.out.println("uploaded = " + uploaded);
            if (Constants.OK.getRet() == uploaded.getRet()) {
                //成功数据
                return ReturnJson.success(uploaded.getData());
            } else {
                //失败数据
                return ReturnJson.toErrorJson(uploaded.getMsg());
            }
        }
        return ReturnJson.success(responseBody);
    }
}