package com.wbgame.controller.mobile.detail;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.ApiIgp;
import com.wbgame.aop.PageLimit;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.adb.DataReportMapper;
import com.wbgame.pojo.mobile.AppModelSysKeepVo;
import com.wbgame.pojo.operate.CommonReportVo;
import com.wbgame.pojo.operate.MarketModelTotalVo;
import com.wbgame.pojo.operate.ModelCommonReportVo;
import com.wbgame.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.joda.time.DateTime;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@RestController
@CrossOrigin
@RequestMapping(value = "/operate/appModelSysKeep")
@Api(tags = "产品机型系统维度留存")
@ApiSupport(author = "huangmb")
public class AppModelSysKeepController {

    @Resource
    public DataReportMapper dataReportMapper;

    @RequestMapping(value = "/list")
    @PageLimit
    @ApiOperation(value = "查询", notes = "查询产品机型系统维度留存", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = AppModelSysKeepVo.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String list(HttpServletRequest request, @ApiIgp({"value"}) ModelCommonReportVo param){
        List<AppModelSysKeepVo> list = dataReportMapper.selectAppModelSysKeep(param);
        JSONObject jsonObject = new JSONObject();
        long size = ((Page) list).getTotal();
        jsonObject.put("total",size);
        jsonObject.put("list",list);
        AppModelSysKeepVo count = dataReportMapper.countAppModelSysKeep(param);
        jsonObject.put("count",count);
        return ReturnJson.success(jsonObject);
    }

    @RequestMapping(value = "/matrix")
    @ApiOperation(value = "矩阵", notes = "查询产品机型系统维度留存矩阵图", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = AppModelSysKeepVo.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String matrix(HttpServletRequest request, @ApiIgp({"value"}) ModelCommonReportVo param){
        param.setGroup("model,os_ver");
        List<AppModelSysKeepVo> list = dataReportMapper.selectAppModelSysKeep(param);
        List<String> vers = dataReportMapper.selectKeepOsVers(param);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list",list);
        jsonObject.put("vers",vers);
        return ReturnJson.success(jsonObject);
    }

    @RequestMapping(value = "/export")
    @ApiOperation(value = "导出", notes = "导出产品机型系统维度留存", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = AppModelSysKeepVo.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public void export(HttpServletResponse response, @ApiIgp({"start","limit"}) ModelCommonReportVo param){
        List<AppModelSysKeepVo> list = dataReportMapper.selectAppModelSysKeep(param);
        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = param.getValue().split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = "产品机型系统维度留存_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
        ExportExcelUtil.export2(response,list,head,fileName);
    }




}
