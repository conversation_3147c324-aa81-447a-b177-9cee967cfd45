package com.wbgame.controller.mobile.detail;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.adb.ADBUmengMapper;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 应用锁屏报表
 * @author: huangmb
 * @date: 2021/03/10
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/appLockReport")
public class AppLockReportController {

    @Resource
    private ADBUmengMapper adbUmengMapper;

    @RequestMapping(value = "/list")
    public String list (HttpServletRequest request, HttpServletResponse response) {
        //参数校验
        String begin = request.getParameter("begin");
        String end = request.getParameter("end");
        String appid = request.getParameter("appid");
        String channel = request.getParameter("channel");
        String version = request.getParameter("version");
        String model =request.getParameter("model");
        String isNew = request.getParameter("isNew");
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String orderStr = request.getParameter("order_str");
        if (BlankUtils.checkBlank(appid) || BlankUtils.checkBlank(begin) || BlankUtils.checkBlank(end)) {
            return ReturnJson.error(Constants.ParamError);
        }
        //条件
        Map param = new HashMap<>();
        param.put("begin", begin);
        param.put("end", end);
        param.put("appid", appid);
        param.put("channel", channel);
        param.put("version", version);
        param.put("model",model);
        param.put("isNew",isNew);
        param.put("order_str", orderStr);
        // 当前页
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<Map<String,Object>> list = adbUmengMapper.selectAppLockReport(param);
        Map result = new HashMap();
        result.put("list",list);
        result.put("total",((Page) list).getTotal());
        return ReturnJson.success(result);
    }

    @RequestMapping(value = "/export")
    public void export (HttpServletRequest request, HttpServletResponse response) {
        //参数校验
        String begin = request.getParameter("begin");
        String end = request.getParameter("end");
        String appid = request.getParameter("appid");
        String channel = request.getParameter("channel");
        String version = request.getParameter("version");
        String model =request.getParameter("model");
        String isNew = request.getParameter("isNew");
        String orderStr = request.getParameter("order_str");
        String value = request.getParameter("value");
        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(appid) || BlankUtils.checkBlank(begin) || BlankUtils.checkBlank(end)) {
             Asserts.fail(Constants.ParamError);
        }
        //条件
        Map param = new HashMap<>();
        param.put("begin", begin);
        param.put("end", end);
        param.put("appid", appid);
        param.put("channel", channel);
        param.put("version", version);
        param.put("model",model);
        param.put("isNew",isNew);
        param.put("order_str", orderStr);
        List<Map<String,Object>> list = adbUmengMapper.selectAppLockReport(param);
        Map<String,String> headerMap = new LinkedHashMap<>();
        if (!BlankUtils.checkBlank(value)){
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX(response,list,headerMap,export_file_name+"_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");

    }

}
