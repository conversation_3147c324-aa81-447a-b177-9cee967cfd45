package com.wbgame.controller.mobile.detail;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.wbgame.aop.PageLimit;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.adb.DataReportMapper;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 渠道新增留存分析报表（品牌）
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/addKeepBrandReport")
public class AddKeepBrandReportController {

    @Resource
    private DataReportMapper dataReportMapper;

    @RequestMapping(value = "/list")
    @PageLimit
    public String list(HttpServletRequest request, HttpServletResponse response){
        //处理请求参数
        Map<String, Object> param = handleParams(request);
        //查询
        List<Map<String,Object>> list = dataReportMapper.selectAddKeepBrandReport(param);
        JSONObject result = new JSONObject();
        result.put("list",list);
        result.put("total",((Page) list).getTotal());
        return ReturnJson.success(result);
    }


    @RequestMapping(value = "/export")
    public void export(HttpServletRequest request, HttpServletResponse response){
        //处理请求参数
        Map<String, Object> param = handleParams(request);
        //查询
        List<Map<String,Object>> list = dataReportMapper.selectAddKeepBrandReport(param);
        //自定义列
        String value = request.getParameter("value");
        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(export_file_name)){
            export_file_name = "品牌新增留存";
        }
        ExportExcelUtil.exportXLSX(response,list,head,export_file_name +"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx");
    }

    /**
     * 处理参数
     * @param request
     * @return
     */
    public Map<String,Object> handleParams(HttpServletRequest request){
        String appid = request.getParameter("appid");
        String brand = request.getParameter("brand");
        String channel = request.getParameter("channel");
        String version = request.getParameter("version");
        String group = request.getParameter("group");
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String order = request.getParameter("order");
        if (BlankUtils.checkBlank(group)) {
            group = "tdate,appid,download_channel,app_ver,brand";
        }
        Map<String,Object> param = new HashMap<>();
        param.put("appid",appid);
        param.put("brand", brand);
        param.put("channel",channel);
        param.put("version",version);
        param.put("group",group);
        param.put("startTime",startTime);
        param.put("endTime",endTime);
        param.put("order",order);
        return param;
    }

}
