package com.wbgame.controller.mobile.set.apiTransfer;



import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.mobile.request.ApiPacketParaConfigParam;
import com.wbgame.pojo.mobile.response.ApiPacketParaConfigResponseParam;
import com.wbgame.service.mobile.ApiPacketParaConfigService;
import com.wbgame.utils.PageResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * desc:API传包参数配置
 * createBy:xugx
 * date：2024-03-08
 */
@CrossOrigin(maxAge =300)
@RestController
@RequestMapping(value = "/api-packet/config")
@Api(tags = "API传包参数配置")
@ApiSupport(author = "xugx")
public class ApiPacketParaConfigController {
	Logger logger = LoggerFactory.getLogger(ApiPacketParaConfigController.class);
    @Autowired
    private ApiPacketParaConfigService apiPacketParaConfigService;
	
	/**
	 * 分页
	 * @param param  
	 * @return
	 */
	@RequestMapping(value="/page", method={RequestMethod.POST})
	@ApiOperation(value = "查询", notes = "API传包参数查询", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "查询异常",response = Result.class),
            @ApiResponse(code = 1, message = "成功",response = Result.class)
    })
	@LoginCheck
    public Result<PageResult<ApiPacketParaConfigResponseParam>> page(ApiPacketParaConfigParam param){
        	return apiPacketParaConfigService.page(param);
    }
	
	/**
	 * 删除
	 * @param param  
	 * @return
	 */
    @ApiOperation(value = "删除", notes = "删除", httpMethod = "POST")
    @RequestMapping(value = "/delete")
    @LoginCheck
    public Result<Integer> delete(@RequestParam("idList") List<Integer> id) {

        return apiPacketParaConfigService.delete(id);
    }
	/**
	 *新增
	 * @param param  
	 * @return
	 */
    
    @ApiOperation(value = "新增", notes = "新增", httpMethod = "POST")
    @RequestMapping(value = "/add")
    @LoginCheck
    public Result<Integer> add(HttpServletRequest request,ApiPacketParaConfigParam param) {
    	String userName=((CurrUserVo) request.getAttribute("LoginUser")).getLogin_name();
    	param.setUpdate_user(userName);
    	param.setCreate_user(userName);
        return apiPacketParaConfigService.add(param);
    }
	/**
	 *修改
	 * @param param  
	 * @return
	 */
    @ApiOperation(value = "修改", notes = "修改", httpMethod = "POST")
    @RequestMapping(value = "/update")
    @LoginCheck
    public Result<Integer> update(HttpServletRequest request,ApiPacketParaConfigParam param) {
    	String userName=((CurrUserVo) request.getAttribute("LoginUser")).getLogin_name();
    	param.setUpdate_user(userName);
        return apiPacketParaConfigService.update(param);
    }
    
    /**
     * 批量导入
     * @param file  文件
     * @param channel  渠道
     * @return
     * @throws IOException
     */
    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping("/import")
    @LoginCheck
    public Result<Integer> importConfig(@RequestParam(value = "file") MultipartFile file,HttpServletRequest request,String channel) {
    	CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        String userName=loginUser.getLogin_name();
        return apiPacketParaConfigService.importConfig(file, channel,userName);
    }
}
