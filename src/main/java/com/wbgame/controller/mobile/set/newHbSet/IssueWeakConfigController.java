package com.wbgame.controller.mobile.set.newHbSet;

import com.github.pagehelper.Page;
import com.wbgame.aop.PageLimit;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.redpack.IssueWeakConfigMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.redpack.HbIssueWeakConfig;
import com.wbgame.pojo.redpack.HbIssueWeakConfigParam;
import com.wbgame.utils.BlankUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 红包功能开关
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/issueWeakConfig")
public class IssueWeakConfigController {

    @Resource
    private IssueWeakConfigMapper issueWeakConfigMapper;

    @RequestMapping(value = "/list")
    @PageLimit
    public String list(HbIssueWeakConfig HbIssueWeakConfig){
        List<HbIssueWeakConfig> list = issueWeakConfigMapper.selectIssueWeakConfigs(HbIssueWeakConfig);
        long size = ((Page) list).getTotal();
        Map result = new HashMap();
        result.put("list",list);
        result.put("total",size);
        return ReturnJson.success(result);
    }

    @RequestMapping(value = "/add",method = RequestMethod.POST)
    public String add(@RequestBody HbIssueWeakConfigParam param,HttpServletRequest request) {
        if (param.getList() == null || param.getList().size() == 0) {
            return ReturnJson.toErrorJson("参数有误");
        }
        if (BlankUtils.checkBlank(param.getPid())) {
            return ReturnJson.toErrorJson("项目id不能为空");
        }
        if ("0".equals(param.getPid())) {
            return ReturnJson.toErrorJson("项目id为0不可配置");
        }
        HbIssueWeakConfig hbIssueWeakConfig = new HbIssueWeakConfig();
        hbIssueWeakConfig.setPid(param.getPid());
        List<HbIssueWeakConfig> list = issueWeakConfigMapper.selectIssueWeakConfigs(hbIssueWeakConfig);
        if (list != null && list.size() > 0) {
            return ReturnJson.toErrorJson("该配置已存在,请先删除旧配置再做新增操作");
        }
        //验证是否区间冲突,左闭右开
        for (int i = 0;i<param.getList().size();i++) {
            HbIssueWeakConfig one = param.getList().get(i);
            if (one.getMin_money().compareTo(one.getMax_money()) > 0) {
                return ReturnJson.toErrorJson("最小值提现金额不能大于最大值提现金额");
            }
            for (int j = i+1;j<param.getList().size();j++) {
                HbIssueWeakConfig two = param.getList().get(j);
                if (!(one.getMax_money().compareTo(two.getMin_money()) <= 0
                        || one.getMin_money().compareTo(two.getMax_money()) >= 0)) {
                    return ReturnJson.toErrorJson("提现金额范围区间冲突有误");
                }
            }
        }
        for (HbIssueWeakConfig h : param.getList()) {
            h.setPid(param.getPid());
            CurrUserVo currUserVo = (CurrUserVo)request.getAttribute("LoginUser");
            h.setCreate_owner(currUserVo.getLogin_name());
            h.setUpdate_owner(currUserVo.getLogin_name());
        }
        int ret = issueWeakConfigMapper.addIssueWeakConfig(param.getList());
        if (ret>0) {
            return ReturnJson.success("操作成功");
        }else{
            return ReturnJson.toErrorJson("操作失败");
        }
    }

    @RequestMapping(value = "/update")
    public String update(HbIssueWeakConfig param,HttpServletRequest request){
        if ("0".equals(param.getPid())) {
            return ReturnJson.toErrorJson("项目id为0不可配置");
        }
        if (BlankUtils.checkBlank(param.getPid())) {
            return ReturnJson.toErrorJson("项目id不能为空");
        }
        CurrUserVo currUserVo = (CurrUserVo)request.getAttribute("LoginUser");
        param.setUpdate_owner(currUserVo.getLogin_name());
        int ret = issueWeakConfigMapper.updateIssueWeakConfig(param);
        if (ret>0) {
            return ReturnJson.success("操作成功");
        }else{
            return ReturnJson.toErrorJson("操作失败");
        }
    }

    @RequestMapping(value = "/delete")
    public String delete(HbIssueWeakConfig param) {
        if ("0".equals(param.getPid())) {
            return ReturnJson.toErrorJson("项目id为0不可删除");
        }
        if (BlankUtils.checkBlank(param.getPid())) {
            return ReturnJson.toErrorJson("项目id不能为空");
        }
        int ret = issueWeakConfigMapper.deleteIssueWeakConfig(param);
        if (ret>0) {
            return ReturnJson.success("操作成功");
        }else{
            return ReturnJson.toErrorJson("操作失败");
        }
    }

}
