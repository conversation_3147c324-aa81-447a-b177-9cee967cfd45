package com.wbgame.controller.mobile.set.appSet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.ProjectVo;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @description: 单机兑换码
 * @author: huangmb
 * @date: 2021/02/23
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/dhmInfo")
public class DhmInfoController {

    @Autowired
    private SomeService someService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 兑换码查询
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public String list(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");
            String dhmid = BlankUtils.checkNull(request, "dhmid");
            String dhvalue = BlankUtils.checkNull(request, "dhvalue");
            String statu = BlankUtils.checkNull(request, "statu");
            Map<Object, Object> param = new HashMap<>();
            param.put("start", start);
            param.put("limit", limit);
            param.put("dhmid", dhmid);
            param.put("dhvalue", dhvalue);
            param.put("statu", statu);
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<ProjectVo> list = someService.selectDhmInfo(param);
            long size = ((Page) list).getTotal();
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret", 1);
            result.put("totalCount", size);
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 生成兑换码
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/add", method = {RequestMethod.POST})
    public String add(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {

        String username = null;
        try {
            username = getLoginInfo(request);
        } catch (Exception e) {
            return ReturnJson.toErrorJson("无效token");
        }
        try {
            String addvalue = BlankUtils.checkNull(request, "addvalue");
            Map<Object, Object> param = new HashMap<>();
            String val = "";
            Random random = new Random();
            for (int i = 0; i < 10; i++) {
                val += String.valueOf(random.nextInt(10));
            }
            Date currentTime = new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateString = formatter.format(currentTime);
            param.put("addvalue", addvalue);
            param.put("dhm", val);
            param.put("dateString", dateString);
            JSONObject result = new JSONObject();
            param.put("createUser", username);
            int res = someService.insertDhmInfo(param);
            if (res > 0) {
                result.put("ret", 1);
                result.put("msg", "生成成功");
                result.put("val", val);
            } else {
                result.put("ret", 0);
                result.put("msg", "生成失败");
            }
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 生成兑换码并导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/addExport")
    public void addExport(HttpServletRequest request, HttpServletResponse response) {

        String username = null;
        try {
            username = getLoginInfo(request);
        } catch (Exception e) {
            return;
        }

        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();

        String addvalue = request.getParameter("addvalue"); // 金额和数量
        String addnum = request.getParameter("addnum");
        for (int i = 0; i < Integer.valueOf(addnum); i++) {
            String dhm = "";
            for (int j = 0; j < 10; j++) {
                dhm = dhm + new Random().nextInt(10);
            }

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("createUser", username);
            param.put("dhm", dhm);
            param.put("addvalue", addvalue);
            contentList.add(param);
        }
        try {
            someService.insertDhmInfoList(contentList);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("批量生成兑换码异常...");
            return;
        }

        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("dhm", "兑换码");
        headerMap.put("addvalue", "兑换金额");

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "可使用兑换码_" + DateTime.now().toString("yyyyMMddHHmmss") + ".xlsx";
        ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);

//        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);
    }

    /**
     * 获取登录人信息
     * @param request
     * @return
     * @throws Exception
     */
    public String getLoginInfo(HttpServletRequest request) throws Exception {

        String token = request.getParameter("token");
        String username = null;

        if (token.startsWith("wbtoken")) {
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        return username;
    }
}
