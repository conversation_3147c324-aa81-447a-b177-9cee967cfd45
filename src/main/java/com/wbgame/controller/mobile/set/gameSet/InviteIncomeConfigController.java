package com.wbgame.controller.mobile.set.gameSet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.mobile.InviteCheckConfigVo;
import com.wbgame.pojo.mobile.InviteIncomeConfigVo;
import com.wbgame.service.mobile.InviteConfigService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname InviteConfigController
 * @Description 好友邀请收益配置v1版本
 * @Date 2021/7/6 19:35
 */
@RequestMapping("/mobile/invite")
@RestController
@CrossOrigin
public class InviteIncomeConfigController {

    @Autowired
    InviteConfigService inviteConfigService;

    @Autowired
    RedisTemplate redisTemplate;

    /**
     *
     * @param request
     * @return
     */
    @RequestMapping("selectIncomeConfig")
    public Object selectIncomeConfig(HttpServletRequest request){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        //分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        //查询
        PageHelper.startPage(pageNo, pageSize);
        String prjid = request.getParameter("prjid");
        InviteIncomeConfigVo vo = new InviteIncomeConfigVo();
        vo.setPrjid(prjid);
        List<InviteIncomeConfigVo> list = inviteConfigService.selectInviteIncomeConfig(vo);
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return result;
    }


    @RequestMapping("handleIncomeConfig")
    public Object handleIncomeConfig(HttpServletRequest request,InviteIncomeConfigVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String handle = request.getParameter("handle");
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (vo==null){
            return ReturnJson.toErrorJson("必要参数为空");
        }
        vo.setCuser(cuser.getLogin_name());
        vo.setEuser(cuser.getLogin_name());
        int succ = 0;
        if ("add".equals(handle)){
            succ = inviteConfigService.saveInviteIncomeConfig(vo);
        }else if("update".equals(handle)){
            succ = inviteConfigService.updateInviteIncomeConfig(vo);
        }else if("del".equals(handle)){
            succ = inviteConfigService.delInviteIncomeConfig(vo);
        }
        if (succ>0){
            return ReturnJson.success();
        }else {
            return ReturnJson.toErrorJson("操作失败,请联系管理员");
        }
    }


    /**
     *
     * @param request
     * @return
     */
    @RequestMapping("selectCheckConfig")
    public Object selectCheckConfig(HttpServletRequest request){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        //分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        //查询
        PageHelper.startPage(pageNo, pageSize);
        String prjid = request.getParameter("prjid");
        InviteCheckConfigVo vo = new InviteCheckConfigVo();
        vo.setPrjid(prjid);
        List<InviteCheckConfigVo> list = inviteConfigService.selectInviteCheckConfig(vo);
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return result;
    }


    @RequestMapping("handleCheckConfig")
    public Object handleCheckConfig(HttpServletRequest request, InviteCheckConfigVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String handle = request.getParameter("handle");
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (vo==null){
            return ReturnJson.toErrorJson("必要参数为空");
        }
        vo.setCuser(cuser.getLogin_name());
        vo.setEuser(cuser.getLogin_name());
        int succ = 0;
        if ("add".equals(handle)){
            succ = inviteConfigService.saveInviteCheckConfig(vo);
        }else if("update".equals(handle)){
            succ = inviteConfigService.updateInviteCheckConfig(vo);
        }else if("del".equals(handle)){
            succ = inviteConfigService.delInviteCheckConfig(vo);
        }
        if (succ>0){
            return ReturnJson.success();
        }else {
            return ReturnJson.toErrorJson("操作失败,请联系管理员");
        }
    }

}
