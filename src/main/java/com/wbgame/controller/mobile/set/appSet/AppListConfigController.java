package com.wbgame.controller.mobile.set.appSet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.wbgame.aop.PageLimit;
import com.wbgame.common.Asserts;
import com.wbgame.common.ProtocolConstant;
import com.wbgame.common.RefeshCacheConstant;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.mapper.clean.master.CleanYdMapper;
import com.wbgame.mapper.master.WbConfigMapper;
import com.wbgame.mapper.slave2.AdMsgMapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.pojo.*;
import com.wbgame.pojo.mobile.IosPasswordConfigDTO;
import com.wbgame.pojo.mobile.IosPasswordConfigQuery;
import com.wbgame.service.AdService;
import com.wbgame.service.SomeService;
import com.wbgame.service.mobile.IAppListConfigService;
import com.wbgame.utils.ApplicationContextUtils;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.HttpRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

/**
 * @description: 应用管理
 * @author: huangmb
 * @date: 2021/02/24
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/appListConfig")
@Api(tags = "应用管理")
public class AppListConfigController {

    private static Logger logger = LoggerFactory.getLogger(AppListConfigController.class);

    @Autowired
    private SomeService someService;

    @Autowired
    TfxtMapper tfxtMapper;

    @Autowired
    CleanYdMapper cleanYdMapper;
    @Autowired
    WbConfigMapper wbConfigMapper;

    @Autowired
    private AdService adService;

    @Autowired
    private AdMsgMapper adMsgMapper;

    @Resource
    private DnwxBiAdtMapper dnwxBiAdtMapper;

    @Autowired
    private IAppListConfigService appListConfigService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final Map<String,String> OS_TYPE_MAP = new HashMap<String,String>(){{
        put("1","安卓");
        put("2","ios");
        put("3","google");
        put("4","小游戏");
    }};

    private static final Map<String,String> SYNC_UMENG_MAP = new HashMap<String,String>(){{
        put("0","未同步");
        put("1","同步");
    }};


    /**
     * 查询应用列表
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    @PageLimit
    public String list(HttpServletRequest request, HttpServletResponse response) {
        String appName = BlankUtils.checkNull(request, "appName");
        String channelId = BlankUtils.checkNull(request, "channelId");
        String appid = BlankUtils.checkNull(request, "appid");
        String appCategory = BlankUtils.checkNull(request, "appCategory");
        String findVal = BlankUtils.checkNull(request, "findVal");
        String osType = BlankUtils.checkNull(request, "os_type");
        String reyun_key = BlankUtils.checkNull(request,"reyun_key");
        String bus_category = BlankUtils.checkNull(request,"bus_category");
        String two_app_category = BlankUtils.checkNull(request,"two_app_category");
        
        Map<Object, Object> map = new HashMap<>();
        map.put("appName", appName);
        map.put("channelId", channelId);
        map.put("appid", appid);
        map.put("appCategory", appCategory);
        map.put("findVal", findVal);
        map.put("osType", osType);
        map.put("reyun_key",reyun_key);
        map.put("bus_category",bus_category);
        map.put("two_app_category",two_app_category);
        
        List<AppInfoVo> list = someService.selectAppListInfo(map);
        long size = ((Page) list).getTotal();
        
        JSONObject result = new JSONObject();
        result.put("data", list);
        result.put("ret", 1);
        result.put("totalCount", size);
        return result.toJSONString();
    }

    /**
     * 导出应用列表
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/export", method = {RequestMethod.GET,RequestMethod.POST})
    public void export(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            //参数条件
            String appName = BlankUtils.checkNull(request, "appName");
            String channelId = BlankUtils.checkNull(request, "channelId");
            String appid = BlankUtils.checkNull(request, "appid");
            String appCategory = BlankUtils.checkNull(request, "appCategory");
            String findVal = BlankUtils.checkNull(request, "findVal");
            String osType = BlankUtils.checkNull(request, "os_type");
            String reyun_key = BlankUtils.checkNull(request,"reyun_key");
            String bus_category = BlankUtils.checkNull(request,"bus_category");
            String two_app_category = BlankUtils.checkNull(request,"two_app_category");

            Map<Object, Object> map = new HashMap<>();
            map.put("appName", appName);
            map.put("channelId", channelId);
            map.put("appid", appid);
            map.put("appCategory", appCategory);
            map.put("findVal", findVal);
            map.put("osType", osType);
            map.put("reyun_key",reyun_key);
            map.put("bus_category",bus_category);
            map.put("two_app_category",two_app_category);

            Map<String,String> channelNameMap = new HashMap<>();
            //渠道名称
            List<SelectStortVo> channelList = someService.selectChannelInfo();
            if (channelList.size()>0){
                for (SelectStortVo vo:channelList){
                    channelNameMap.put(vo.getOp_value(),vo.getOp_text());
                }
            }
            //应用分类
            Map<String,String> appCategoryNameMap = new HashMap<>();
            List<AppCategory> appCategoryList = someService.getAppCategorys();
            if (appCategoryList.size()>0){
                for (AppCategory vo:appCategoryList){
                    appCategoryNameMap.put(vo.getId().toString(),vo.getName());
                }
            }
            // 产品二级分类
            Map<String, Map<String, Object>> twoMap = someService.getTwoAppCategorys();

            //查询
            List<AppInfoVo> list = someService.selectAppListInfo(map);
            if (list.size()>0){
                for (AppInfoVo each:list){
                    if (!BlankUtils.checkBlank(each.getChannel_id())){
                        if (channelNameMap.get(each.getChannel_id())!=null){
                            each.setChannel_id(channelNameMap.get(each.getChannel_id()));
                        }
                    }
                    if (!BlankUtils.checkBlank(each.getApp_category())){
                        if (appCategoryNameMap.get(each.getApp_category())!=null){
                            each.setApp_category(appCategoryNameMap.get(each.getApp_category()));
                        }
                    }
                    if (!BlankUtils.checkBlank(each.getOs_type())){
                        if (OS_TYPE_MAP.get(each.getOs_type())!=null){
                            each.setOs_type(OS_TYPE_MAP.get(each.getOs_type()));
                        }
                    }
                    if (!BlankUtils.checkBlank(each.getSync_umeng())){
                        if (SYNC_UMENG_MAP.get(each.getSync_umeng())!=null){
                            each.setSync_umeng(SYNC_UMENG_MAP.get(each.getSync_umeng()));
                        }
                    }

                    if (!BlankUtils.checkBlank(each.getBus_category())){
                    	String bus = "3".equals(each.getBus_category())?"工具":"2".equals(each.getBus_category())?"小游戏":"游戏";
                    	each.setBus_category(bus);
                    }
                    
                    // 根据id匹配产品二级分类名称
                    if (!BlankUtils.checkBlank(each.getTwo_app_category())){
                    	Map<String, Object> act = twoMap.get(each.getTwo_app_category());
                    	if(act != null){
                    		each.setTwo_app_category(act.get("name")+"");
                    	}
                    }
                    
                }
            }

            //创建文件
            String export_file_name = request.getParameter("export_file_name");

            //数据头
//            Map<String, String> headerMap = new LinkedHashMap<String, String>();
//            headerMap.put("id", "产品ID");
//            headerMap.put("app_name", "应用名称");
//            headerMap.put("app_category", "产品类别");
//            headerMap.put("os_type", "系统类别");
//            headerMap.put("create_time", "创建时间");
//
//            JxlUtil.doSave(fileName, headerMap, list, null, null, null, request, response);

            Map<String, String> headerMap = new LinkedHashMap<String, String>();
            String value = request.getParameter("value");
            if (!BlankUtils.checkBlank(value)){
                try {
                    String[] split = value.split(";");
                    for (int i = 0;i<split.length;i++) {
                        String[] s = split[i].split(",");
                        headerMap.put(s[0],s[1]);
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                    Asserts.fail("自定义列导出异常");
                }
            }
            ExportExcelUtil.exportXLSX2(response,list,headerMap, export_file_name + "_" + DateTime.now().toString("yyyyMMdd")+".xlsx");

        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
            e.printStackTrace();
        }
    }

    /**
     * 新增应用列表
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/add", method = {RequestMethod.POST})
    public String add(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            String channelId = BlankUtils.checkNull(request, "channelId");
            String appName = BlankUtils.checkNull(request, "appName");
            String sync_umeng = BlankUtils.checkNull(request, "sync_umeng");
            String umeng_key = BlankUtils.checkNull(request, "umeng_key");
            String app_category = BlankUtils.checkNull(request, "appCategory");
            String bus_category = BlankUtils.checkNull(request, "bus_category");
            String umeng_account = BlankUtils.checkNull(request, "umeng_account");
            String find_vals = BlankUtils.checkNull(request, "find_vals");
            String os_type = BlankUtils.checkNull(request, "os_type");
            String reyun_key = BlankUtils.checkNull(request,"reyun_key");
            String two_app_category = BlankUtils.checkNull(request, "two_app_category");
            String cp = BlankUtils.checkNull(request, "cp");

            //umeng_key不为空时保证唯一性
            if (!BlankUtils.checkBlank(umeng_key)) {
                if (someService.countUmengKey(umeng_key) != 0) {
                    return ReturnJson.toErrorJson("新增的友盟key已存在");
                }
            }
            Map<Object, Object> map = new HashMap<>();
            map.put("channel_id", channelId);
            map.put("app_name", appName);
            map.put("sync_umeng", sync_umeng);
            map.put("umeng_key", umeng_key);
            map.put("app_category", app_category);
            map.put("bus_category", bus_category);
            map.put("app_id", genRandomNum(50));
            map.put("umeng_account", umeng_account);
            map.put("find_vals", find_vals);
            map.put("os_type", os_type);
            map.put("xyx_id", request.getParameter("xyx_id") == null ? "" : request.getParameter("xyx_id"));
            map.put("reyun_key",reyun_key);
            map.put("two_app_category",two_app_category);
            map.put("cp",cp);

            JSONObject result = new JSONObject();
            int res = someService.insertAppListInfo(map);
            if (res > 0) {
                // 更新负责人的产品收支权限配置
                if (map.get("id") != null && !find_vals.isEmpty()) {
                    someService.updateMarketListFind(map.get("id") + "", find_vals);
                }

                result.put("ret", 1);
                result.put("msg", "新增成功");
            } else {
                result.put("ret", 0);
                result.put("msg", "新增失败");
            }
            
            try {
            	if (res > 0 && map.get("id") != null){
            		/** 为v3角色增加该应用 
    				 * 1-超休闲,2-红包,3-工具,4-经典,5-中度,13-解压,15-海外工具,16-海外游戏,17-小游戏
    				 */
    				String role_id = "role_all_cxx";
    				if("1".equals(app_category))
    					role_id = "role_all_cxx";
    				if("2".equals(app_category))
    					role_id = "role_all_hb";
    				if("3".equals(app_category))
    					role_id = "role_all_app";
    				if("4".equals(app_category))
    					role_id = "role_all_jd";
    				if("5".equals(app_category))
    					role_id = "role_all_zd";
    				if("13".equals(app_category))
    					role_id = "role_all_jy";
    				if("15".equals(app_category))
    					role_id = "role_all_app_en";
    				if("16".equals(app_category))
    					role_id = "role_all_game";
    				if("17".equals(app_category))
    					role_id = "role_all_xyx";
    				if("18".equals(app_category))
    					role_id = "role_all_app_b";
					
					AppInfoVo app = new AppInfoVo();
					app.setId(map.get("id")+"");
					app.setRole_id(role_id);
					wbConfigMapper.updateSysRoleV3(app);
            	}
			} catch (Exception e) {
				e.printStackTrace();
			}
            
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 修改应用列表
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public String update(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            String channelId = BlankUtils.checkNull(request, "channelId");
            String appName = BlankUtils.checkNull(request, "appName");
            String sync_umeng = BlankUtils.checkNull(request, "sync_umeng");
            String umeng_key = BlankUtils.checkNull(request, "umeng_key");
            String hiddenId = BlankUtils.checkNull(request, "hiddenId");
            String appCategory = BlankUtils.checkNull(request, "appCategory");
            String umeng_account = BlankUtils.checkNull(request, "umeng_account");
            String find_vals = BlankUtils.checkNull(request, "find_vals");
            String os_type = BlankUtils.checkNull(request, "os_type");
            String reyun_key = BlankUtils.checkNull(request,"reyun_key");
            String bus_category = BlankUtils.checkNull(request,"bus_category");
            String two_app_category = BlankUtils.checkNull(request,"two_app_category");
            String cp = BlankUtils.checkNull(request,"cp");
            if (BlankUtils.checkBlank(appCategory)) {
                return "{\"ret\":0,\"msg\":\"产品分类不能为空\"}";
            }
            //umeng_key不为空时保证唯一性
            AppInfoVo appInfoVo = someService.selectAppInfoId(hiddenId);
            if (!BlankUtils.checkBlank(umeng_key) && !umeng_key.equals(appInfoVo.getUmeng_key())) {
                if (someService.countUmengKey(umeng_key) != 0) {
                    return ReturnJson.toErrorJson("更新的友盟key已存在");
                }
            }
            Map<Object, Object> map = new HashMap<>();
            map.put("channel_id", channelId);
            map.put("app_name", appName);
            map.put("sync_umeng", sync_umeng);
            map.put("umeng_key", umeng_key);
            map.put("id", hiddenId);
            map.put("app_category", appCategory);
            map.put("umeng_account", umeng_account);
            map.put("find_vals", find_vals);
            map.put("os_type", os_type);
            map.put("xyx_id", request.getParameter("xyx_id") == null ? "" : request.getParameter("xyx_id"));
            map.put("reyun_key",reyun_key);
            map.put("bus_category",bus_category);
            map.put("two_app_category",two_app_category);
            map.put("cp",cp);
            JSONObject result = new JSONObject();
            int res = someService.updateAppListInfo(map);
            if (res > 0) {

                // 更新负责人的产品收支权限配置
                if (!hiddenId.isEmpty() && !find_vals.isEmpty()) {
                    someService.updateMarketListFind(map.get("id") + "", find_vals);
                }

                result.put("ret", 1);
                result.put("msg", "修改成功");
            } else {
                result.put("ret", 0);
                result.put("msg", "修改失败");
            }
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 删除应用列表
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    public String delete(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            String ids = BlankUtils.checkNull(request, "ids");
            JSONObject result = new JSONObject();
            int res = someService.deleteAppListInfo(ids);
            if (res > 0) {
                result.put("ret", 1);
                result.put("msg", "删除成功");
            } else {
                result.put("ret", 0);
                result.put("msg", "删除失败");
            }
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 更新小游戏原始id
     * @param appid 动能appid
     * @param xyx_id 小游戏原始id
     * @return
     */
    @RequestMapping(value = "/updateXyxId", method = {RequestMethod.POST})
    public String updateXyxId(@RequestParam("appid")String appid,@RequestParam("xyx_id")String xyx_id,@RequestParam("find_vals")String find_vals) {

        someService.updateXyxId(appid,xyx_id,find_vals);
        return ReturnJson.success("更新成功");

    }

    /**
     * 同步应用列表至投放系统、清理王系统
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/syn", method = {RequestMethod.POST})
    public String syn(HttpServletRequest request, HttpServletResponse response) {

        try {

            // 批量导入应用列表
            String sql = "select a.id as app_id,a.app_id as app_key,a.app_name,concat('',a.create_time) create_time,IFNULL(a.app_category,1) app_category, b.platform as type " +
                    "from app_info a,dnwx_client.wbgui_gametype b where a.channel_id = '10118' and a.id = b.appid";
            List<Map<String, Object>> queryMap = adService.queryListMap(sql);

            String sql2 = "select a.id as app_id,a.app_id as app_key,a.app_name,concat('',a.create_time) create_time,IFNULL(a.app_category,1) app_category, 'xyx' as type " +
                    "from app_info a where a.channel_id = '51808' ";
            List<Map<String, Object>> queryMap2 = adService.queryListMap(sql2);

            queryMap.addAll(queryMap2);

            JSONObject result = new JSONObject();
            Environment environment = ApplicationContextUtils.get(Environment.class);
            String type = environment.getProperty("spring.profiles.active");
            if ("dev".equals(type)) {
                tfxtMapper.execSql("delete from dn_app where 1=1");
                int resp = tfxtMapper.insertDnapp(queryMap);
                System.out.println("投放系统更新成功" + resp);

                //应用appinfo跟新
                cleanYdMapper.execSql("delete from app_info where 1=1");
                String sql3 = " select * from  app_info ";
                List<Map<String, Object>> queryMap3 = adService.queryListMap(sql3);
                int update = cleanYdMapper.insertDnapp(queryMap3);
                if (update > 0) {
                    doGet("https://api.vzhifu.net/clean/refreshCache?mapid=5");
                }

                //红包appinfo更新
                adMsgMapper.queryListMap("delete from app_info where 1=1");
                int r = adMsgMapper.batchInsertDnapp(queryMap3);
                if (r > 0) {
                    doGet(ProtocolConstant.hb_large_test_url + "/gameRefresh");
                    try {
                        //刷新ssds应用组缓存
                        String url = "https://edc.vigame.cn:6115/recacheXyxtj?mapid=14";
                        String refreshXyxtj = HttpRequest.get(url,new HashMap<>());
                        logger.info("refreshXyxtj app_info cache result:"+refreshXyxtj);
                    }catch (Exception e){
                    }
                }

                //adb 投放库
                int adbr = 0;
                try {
                    dnwxBiAdtMapper.deleteAppInfo();
                    dnwxBiAdtMapper.batchInsertAppInfo(queryMap3);
                    dnwxBiAdtMapper.deleteDnwxBiAppInfo();
                    dnwxBiAdtMapper.batchInsertDnwxBiAppInfo(queryMap3);
                    adbr = 1;
                }catch (Exception e) {
                    e.printStackTrace();
                    System.out.println("adb投放库app_info同步失败:"+e.getMessage());
                }


                //新版红包刷新缓存
                try {
                    doGet(RefeshCacheConstant.recacheRedPack1 + 14);
                    doGet(RefeshCacheConstant.recacheRedPack2 + 14);
                }catch (Exception e) {
                    result.put("ret", 0);
                    result.put("msg", "新版红包刷新缓存失败");
                    return result.toJSONString();
                }

                if (adbr == 0) {
                    result.put("ret", 0);
                    result.put("msg", "dnwx_adt库同步失败");
                    return result.toJSONString();
                }

                if (resp > 0) {
                    result.put("ret", 1);
                    result.put("msg", "操作成功");
                } else {
                    result.put("ret", 0);
                    result.put("msg", "同步失败，请重试！");
                }
            } else {
                result.put("ret", 0);
                result.put("msg", "测试服务器无法同步");
            }
            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"出现错误，请稍后重试！\"}";
        }
    }

    public static String genRandomNum(int id_len) {
        final int maxNum = 36;
        int i;
        int count = 0;
        char[] str = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k',
                'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',
                'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};

        StringBuffer id = new StringBuffer("");
        Random r = new Random();
        while (count < id_len) {
            i = Math.abs(r.nextInt(maxNum));
            if (i >= 0 && i < str.length) {
                id.append(str[i]);
                count++;
            }
        }
        return id.toString();
    }

    public static String doGet(String httpurl) {
        HttpURLConnection connection = null;
        InputStream is = null;
        BufferedReader br = null;
        String result = null;// 返回结果字符串
        try {
            // 创建远程url连接对象
            URL url = new URL(httpurl);
            // 通过远程url连接对象打开一个连接，强转成httpURLConnection类
            connection = (HttpURLConnection) url.openConnection();
            // 设置连接方式：get
            connection.setRequestMethod("GET");
            // 设置连接主机服务器的超时时间：15000毫秒
            connection.setConnectTimeout(15000);
            // 设置读取远程返回的数据时间：60000毫秒
            connection.setReadTimeout(60000);
            // 发送请求
            connection.connect();
            // 通过connection连接，获取输入流
            if (connection.getResponseCode() == 200) {
                is = connection.getInputStream();
                // 封装输入流is，并指定字符集
                br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                // 存放数据
                StringBuffer sbf = new StringBuffer();
                String temp;
                while ((temp = br.readLine()) != null) {
                    sbf.append(temp);
                    sbf.append("\r\n");
                }
                result = sbf.toString();
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            if (null != br) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if (null != is) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            connection.disconnect();// 关闭远程连接
        }

        return result;
    }

    @ApiOperation(value = "添加海外秘钥配置", notes = "添加海外秘钥配置", httpMethod = "POST")
    @PostMapping(value = "/insertIosPasswordConfig")
    public String insertIosPasswordConfig(HttpServletRequest request,
                                          @RequestBody @Validated IosPasswordConfigDTO configDTO) {

        String token = request.getHeader("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        configDTO.setCreateOwner(username);
        configDTO.setUpdateOwner(username);
        return appListConfigService.insertIosPasswordConfig(configDTO);
    }


    @ApiOperation(value = "修改海外秘钥配置", notes = "修改海外秘钥配置", httpMethod = "PUT")
    @PutMapping(value = "/updateIosPasswordConfig")
    public String updateIosPasswordConfig(HttpServletRequest request,
                                          @RequestBody @Validated List<IosPasswordConfigDTO> configDTOList) {

        String token = request.getHeader("token");

        String username = "";
        if (token.startsWith("wbtoken")) {
            com.wbgame.pojo.CurrUserVo currUserVo = (com.wbgame.pojo.CurrUserVo) redisTemplate.opsForValue().get(token);
            username = currUserVo.getLogin_name();
        } else {
            WaibaoUserVo WaibaoUser = (WaibaoUserVo) redisTemplate.opsForValue().get(token);
            username = WaibaoUser.getUser_name();
        }

        String uname = username;
        configDTOList.forEach(configDTO -> {

            configDTO.setCreateOwner(uname);
            configDTO.setUpdateOwner(uname);
        });
        return appListConfigService.updateIosPasswordConfig(configDTOList);
    }

    @ApiOperation(value = "批量删除海外秘钥配置", notes = "批量删除海外秘钥配置")
    @DeleteMapping(value = "/deleteIosPasswordConfig")
    public String deleteIosPasswordConfig(HttpServletRequest request,
                                          @RequestBody @Validated List<Integer> appIdList) {

        String token = request.getHeader("token");

        try {
            if (token.startsWith("wbtoken")) {
                redisTemplate.opsForValue().get(token);

            } else {
                redisTemplate.opsForValue().get(token);

            }
        } catch (Exception e) {
            return ReturnJson.toErrorJson("无效token");
        }

        return appListConfigService.deleteIosPasswordConfig(appIdList);
    }

    @ApiOperation(value = "查询海外秘钥配置", notes = "查询海外秘钥配置", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true, type = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", required = true, type = "integer")
    })
    @PostMapping(value = "/selectIosPasswordConfig")
    public String selectIosPasswordConfig(HttpServletRequest request,
                                          @Validated IosPasswordConfigQuery query,
                                          @RequestParam(value = "pageNum") Integer pageNum,
                                          @RequestParam(value = "pageSize") Integer pageSize) {

        String token = request.getHeader("token");

        try {
            if (token.startsWith("wbtoken")) {
               redisTemplate.opsForValue().get(token);

            } else {
                redisTemplate.opsForValue().get(token);

            }
        } catch (Exception e) {
            return ReturnJson.toErrorJson("无效token");
        }

        return appListConfigService.selectIosPasswordConfig(query, pageNum, pageSize);
    }
}
