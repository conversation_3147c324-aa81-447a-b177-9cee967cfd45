package com.wbgame.controller.mobile.set.paySet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.wbgame.aop.PageLimit;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.redpack.HbMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.mobile.ApiPayConfigVo;
import com.wbgame.pojo.mobile.DouyinConfigVo;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 抖音支付配置
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/dyPayConfig")
public class DouyinPayConfigController {

    @Resource
    private HbMapper hbMapper;

    @RequestMapping(value = "/list")
    @PageLimit
    public String list(DouyinConfigVo param, HttpServletRequest request){
        List<DouyinConfigVo> list = hbMapper.selectDouyinConfig(param);
        JSONObject result = new JSONObject();
        result.put("list",list);
        result.put("total",((Page) list).getTotal());
        return ReturnJson.success(result);
    }

    @RequestMapping(value = "/add")
    public String add(DouyinConfigVo param,HttpServletRequest request){
        CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
        param.setCreate_owner(user.getLogin_name());
//        param.setUpdate_owner(user.getLogin_name());
        DouyinConfigVo params = new DouyinConfigVo();
        params.setAppid(param.getAppid());
        List<DouyinConfigVo> exists = hbMapper.selectDouyinConfig(params);
        if (exists != null && exists.size() != 0) {
            return ReturnJson.toErrorJson("该应用的配置已存在");
        }
        hbMapper.insertDouyinPayConfig(param);
        return ReturnJson.success("操作成功");
    }

    @RequestMapping(value = "/update")
    public String update(DouyinConfigVo param,HttpServletRequest request){
        CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
        param.setUpdate_owner(user.getLogin_name());
        hbMapper.updateDouyinPayConfig(param);
        return ReturnJson.success("操作成功");
    }

    @RequestMapping(value = "/delete")
    public String delete(DouyinConfigVo param,HttpServletRequest request){
        hbMapper.deleteDouyinPayConfig(param);
        return ReturnJson.success("操作成功");
    }

}
