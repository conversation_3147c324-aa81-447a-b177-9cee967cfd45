package com.wbgame.controller.mobile.set.paySet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.wbgame.aop.PageLimit;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.redpack.HbMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.mobile.ApiPayConfigVo;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 4399支付
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/ftnnPayConfig")
public class FtnnPayController {

    @Resource
    private HbMapper hbMapper;

    @RequestMapping(value = "/list")
    @PageLimit
    public String list(ApiPayConfigVo param, HttpServletRequest request){
        CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
        param.setApps(user.getApp_group());
        List<ApiPayConfigVo> list = hbMapper.selectFtnnPayConfig(param);
        JSONObject result = new JSONObject();
        result.put("list",list);
        result.put("total",((Page) list).getTotal());
        return ReturnJson.success(result);
    }

    @RequestMapping(value = "/add")
    public String add(ApiPayConfigVo param,HttpServletRequest request){
        CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
        param.setCreate_owner(user.getLogin_name());
        param.setUpdate_owner(user.getLogin_name());
        List<ApiPayConfigVo> exists = hbMapper.selectFtnnPayConfig(param);
        if (exists != null && exists.size() != 0) {
            return ReturnJson.toErrorJson("该配置已存在");
        }
        hbMapper.insertFtnnPayConfig(param);
        return ReturnJson.success("操作成功");
    }

    @RequestMapping(value = "/update")
    public String update(ApiPayConfigVo param,HttpServletRequest request){
        CurrUserVo user = (CurrUserVo) request.getAttribute("LoginUser");
        param.setUpdate_owner(user.getLogin_name());
        hbMapper.updateFtnnPayConfig(param);
        return ReturnJson.success("操作成功");
    }

    @RequestMapping(value = "/delete")
    public String delete(ApiPayConfigVo param,HttpServletRequest request){
        hbMapper.deleteFtnnPayConfig(param);
        return ReturnJson.success("操作成功");
    }

}
