package com.wbgame.controller.mobile.set;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.custom.WarmHomeMailVo;
import com.wbgame.service.AdmsgService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: 暖暖家园-邮件配置界面
 * @author: huangmb
 * @date: 2021/02/24
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/warmHomeMail")
public class WarmHomeMailController {


    @Autowired
    private AdmsgService admsgService;

    // 暖暖家园-邮件配置
    @RequestMapping(value="/list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(WarmHomeMailVo whm, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String loginId = request.getParameter("loginId");
        String mid = request.getParameter("mid");
        String status = request.getParameter("status");
        String platform = request.getParameter("platform");

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if(BlankUtils.sqlValidate(loginId)
                    || BlankUtils.sqlValidate(mid)
                    || BlankUtils.sqlValidate(status)){
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            // 默认为国内配置，可切换海外配置
            String table = "ddz_db.home_mail_list";
            if("haiwai".equals(platform))
                table = "nnjy_db.home_mail_list";

            String sql = "select *,buyId as giftId  from "+table+" where 1=1";
            if(!BlankUtils.checkBlank(loginId))
                sql = sql + " and loginId = '"+loginId+"'";
            if(!BlankUtils.checkBlank(mid))
                sql = sql + " and mid = '"+mid+"'";
            if(!BlankUtils.checkBlank(status))
                sql = sql + " and `status` = '"+status+"'";

            sql = sql + " order by mid desc";

            List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
            if("haiwai".equals(platform)) {
                PageHelper.startPage(pageNo, pageSize); // 进行分页
            }else {
                PageHelper.startPage(pageNo, pageSize); // 进行分页
                list = admsgService.queryListMap(sql);
            }
            long size = ((Page) list).getTotal();

            list.forEach(map -> map.put("getTime", map.get("getTime")+""));

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    /**
     * 邮件配置操作
     * @param handle
     * @param platform
     * @param whm
     * @param request
     * @param response
     * @return
     * @throws IOException
     * @throws Exception
     */
    @RequestMapping(value="/handle", method = RequestMethod.POST)
    public String handle(String handle, String platform, WarmHomeMailVo whm,
                                                   HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

        int result = 0;
        try {
            // 默认为国内配置，可切换海外配置
            String table = "ddz_db.home_mail_list";
            if("haiwai".equals(platform))
                table = "nnjy_db.home_mail_list";

            String sql = null;
            if("add".equals(handle)){
                sql = "insert into "+table+"( loginId, buyId, stars, life, status, getTime ) "
                        +" values( #{obj.loginId}, #{obj.giftId}, #{obj.stars}, #{obj.life}, #{obj.status}, now() )";
            }else if("edit".equals(handle)){
                sql = "update "+table+" set loginId = #{obj.loginId}, buyId = #{obj.giftId}, "
                        + "stars = #{obj.stars}, life = #{obj.life} where mid = #{obj.mid}";
            }else if("del".equals(handle)){
                sql = "delete from "+table+" where mid = #{obj.mid}";
            }



            if(result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }
}
