package com.wbgame.controller.mobile.set.chaSet;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wbgame.common.Asserts;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.clean.master.CleanAdMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.adv2.Message;
import com.wbgame.pojo.mobile.DnwxAllparamConfig;
import com.wbgame.service.AdService;
import com.wbgame.service.YdService;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.service.adv2.DelayQueueService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
import com.wbgame.utils.HttpRequest;

/**
 * @description: 分渠道联运配置界面
 * @author: caow
 * @date: 2022/02/25
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/chaRuleAllConfig")
public class ChaRuleAllConfigController {

	@Autowired
    private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private Adv2Service adv2Service;
    @Autowired
    private AdService adService;

    /**
     * 查询
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = {RequestMethod.GET,RequestMethod.POST})
    public String list(HttpServletRequest request, HttpServletResponse response) {

    	String[] args = {"appGroup","appid","cha_id","pid","mid","mname","note","status","cha_media","cuser","euser","stime","stime2","etime"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
		
        JSONObject result = new JSONObject();
        try {
        	String sql = "select xx.*,yy.cha_media from dnwx_charule_all_config xx " +
					"left join (select cha_id chaid,cha_media from dn_channel_info) yy ON xx.cha_id=yy.chaid where 1=1";
        	if(!BlankUtils.checkBlank(paramMap.get("appid")))
        		sql += " and appid in ("+paramMap.get("appid")+")";
        	if(!BlankUtils.checkBlank(paramMap.get("cha_id")))
        		sql += " and cha_id in ("+paramMap.get("cha_id")+")";
        	if(!BlankUtils.checkBlank(paramMap.get("pid")))
        		sql += " and pid = #{obj.pid}";
        	if(!BlankUtils.checkBlank(paramMap.get("mid")))
        		sql += " and mid = #{obj.mid}";
        	if(!BlankUtils.checkBlank(paramMap.get("mname")))
        		sql += " and mname like concat('%',#{obj.mname},'%') ";
    		if(!BlankUtils.checkBlank(paramMap.get("note")))
    			sql += " and note like concat('%',#{obj.note},'%') ";
    		if(!BlankUtils.checkBlank(paramMap.get("apps")))
    			sql += " and appid in ("+paramMap.get("apps")+")";
    		if(!BlankUtils.checkBlank(paramMap.get("status")))
        		sql += " and `status` = #{obj.status}";
    		if(!BlankUtils.checkBlank(paramMap.get("cha_media")))
        		sql += " and yy.cha_media in ("+paramMap.get("cha_media")+")";
			if(!BlankUtils.checkBlank(paramMap.get("cuser")))
				sql += " and cuser = #{obj.cuser}";
			if(!BlankUtils.checkBlank(paramMap.get("euser")))
				sql += " and euser = #{obj.euser}";
			if(!BlankUtils.checkBlank(paramMap.get("stime")))
				sql += " and stime BETWEEN #{obj.stime} AND #{obj.stime2} ";
			if(!BlankUtils.checkBlank(paramMap.get("etime")))
				sql += " and etime >= #{obj.etime}";

    		sql += " order by createtime desc ";
        		
        	PageHelper.startPage(paramMap); // 进行分页
            List<Map<String, Object>> list = adService.queryListMapTwo(sql, paramMap);
			long size = ((Page) list).getTotal();

			Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
            list.forEach(act -> {

				Map<String, Object> app = appMap.get(act.get("appid")+"");
				if(app != null)
					act.put("appname", app.get("app_name")+"");
            });

            
            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 新增修改删除 操作
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/handle", method = RequestMethod.POST)
    public String handle(String handle, DnwxAllparamConfig all, HttpServletRequest request, HttpServletResponse response) {
    	
    	String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
        int result = 0;
        try {
        	
        	try {
        		CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
        		all.setEuser(curr.getLogin_name());
			} catch (Exception e) {
				e.printStackTrace();
			}
        	
            if ("add".equals(handle)) {
            	
            	String sql = "insert into dnwx_charule_all_config(appid,cha_id,pid,mid,mname,stime,etime,note,weeks,wtime,`status`,cuser,euser,createtime) "+
            			"values(#{obj.appid},#{obj.cha_id},#{obj.pid},#{obj.mid},#{obj.mname},#{obj.stime},#{obj.etime},#{obj.note},#{obj.weeks},#{obj.wtime},#{obj.status},#{obj.euser},#{obj.euser},now())";
            	result = adService.execSqlHandle(sql, all);
            } else if ("edit".equals(handle)) {
            	
            	String sql = "update dnwx_charule_all_config set appid=#{obj.appid},cha_id=#{obj.cha_id},pid=#{obj.pid},mid=#{obj.mid},mname=#{obj.mname},stime=#{obj.stime},etime=#{obj.etime},note=#{obj.note},weeks=#{obj.weeks},wtime=#{obj.wtime},`status`=#{obj.status},euser=#{obj.euser} "+
            			"where id=#{obj.id}";
            	result = adService.execSqlHandle(sql, all);
            } else if ("del".equals(handle)) {
            	
            	String sql = "delete from dnwx_charule_all_config where id in ("+all.getId()+") ";
            	result = adService.execSqlHandle(sql, all);
            } else if ("editMid".equals(handle)) {
            	
            	String sets = "";
            	if(!BlankUtils.checkBlank(all.getStime())){
            		sets += ",stime=#{obj.stime},etime=#{obj.etime}";
            	}
            	if(!BlankUtils.checkBlank(all.getStatus())){
            		sets += ",`status`=#{obj.status}";
            	}
            	
            	String sql = "update dnwx_charule_all_config set mid=#{obj.mid},mname=#{obj.mname},euser=#{obj.euser} "+sets+" where id in ("+all.getId()+") ";
            	result = adService.execSqlHandle(sql, all);
            }
            
            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

	/**
	 * 导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/export", method={RequestMethod.GET, RequestMethod.POST})
	public void export(HttpServletRequest request,HttpServletResponse response) {


		String[] args = {"appGroup","appid","cha_id","pid","mid","mname","note","status","cha_media","cuser","euser","stime","stime2","etime"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);

		String sql = "select xx.*,yy.cha_media from dnwx_charule_all_config xx " +
				"left join (select cha_id chaid,cha_media from dn_channel_info) yy ON xx.cha_id=yy.chaid where 1=1";
		if(!BlankUtils.checkBlank(paramMap.get("appid")))
			sql += " and appid in ("+paramMap.get("appid")+")";
		if(!BlankUtils.checkBlank(paramMap.get("cha_id")))
			sql += " and cha_id in ("+paramMap.get("cha_id")+")";
		if(!BlankUtils.checkBlank(paramMap.get("pid")))
			sql += " and pid = #{obj.pid}";
		if(!BlankUtils.checkBlank(paramMap.get("mid")))
			sql += " and mid = #{obj.mid}";
		if(!BlankUtils.checkBlank(paramMap.get("mname")))
			sql += " and mname like concat('%',#{obj.mname},'%') ";
		if(!BlankUtils.checkBlank(paramMap.get("note")))
			sql += " and note like concat('%',#{obj.note},'%') ";
		if(!BlankUtils.checkBlank(paramMap.get("apps")))
			sql += " and appid in ("+paramMap.get("apps")+")";
		if(!BlankUtils.checkBlank(paramMap.get("status")))
			sql += " and `status` = #{obj.status}";
		if(!BlankUtils.checkBlank(paramMap.get("cha_media")))
			sql += " and yy.cha_media in ("+paramMap.get("cha_media")+")";
		if(!BlankUtils.checkBlank(paramMap.get("cuser")))
			sql += " and cuser = #{obj.cuser}";
		if(!BlankUtils.checkBlank(paramMap.get("euser")))
			sql += " and euser = #{obj.euser}";
		if(!BlankUtils.checkBlank(paramMap.get("stime")))
			sql += " and stime BETWEEN #{obj.stime} AND #{obj.stime2} ";
		if(!BlankUtils.checkBlank(paramMap.get("etime")))
			sql += " and etime >= #{obj.etime}";

		sql += " order by createtime desc ";

		List<Map<String, Object>> contentList = adService.queryListMapTwo(sql, paramMap);


		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			act.put("status", "1".equals(act.get("status")+"")?"启用":"停用");

			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"");
		});


		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		String value = request.getParameter("value");
		if (!BlankUtils.checkBlank(value)){
			//自定义列数据
			try {
				String[] split = value.split(";");
				for (int i = 0;i<split.length;i++) {
					String[] s = split[i].split(",");
					headerMap.put(s[0],s[1]);
				}
			}catch (Exception e) {
				e.printStackTrace();
				Asserts.fail("自定义列导出异常");
			}
		}

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");

		String export_file_name = request.getParameter("export_file_name");
		if (BlankUtils.checkBlank(export_file_name)){
			export_file_name = "分渠道联运配置";
		}
		String fileName = export_file_name + "_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
		ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);
	}

}
