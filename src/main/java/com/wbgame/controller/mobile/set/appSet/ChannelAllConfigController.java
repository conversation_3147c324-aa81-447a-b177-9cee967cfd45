package com.wbgame.controller.mobile.set.appSet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.clean.master.CleanAdMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.adv2.Message;
import com.wbgame.pojo.mobile.DnwxAllparamConfig;
import com.wbgame.service.AdService;
import com.wbgame.service.YdService;
import com.wbgame.service.adv2.DelayQueueService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.HttpRequest;
import com.wbgame.utils.MailTool;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @description: 分渠道全部参数配置
 * @author: caow
 * @date: 2021/11/19
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/channelAllConfig")
public class ChannelAllConfigController {

	@Autowired
    private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private YdService ydService;
    @Autowired
    private AdService adService;
    @Autowired
	private DelayQueueService delayService;
    @Autowired
    private CleanAdMapper cleanAdMapper;

    /**
     * 查询
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = {RequestMethod.GET,RequestMethod.POST})
    public String list(HttpServletRequest request, HttpServletResponse response) {

    	String[] args = {"appid","cha_id","prjid","area","note"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
		
        JSONObject result = new JSONObject();
        try {
        	String sql = "select * from dnwx_mmparam_all_config where 1=1";
        	if(!BlankUtils.checkBlank(paramMap.get("appid")))
        		sql += " and appid in ("+paramMap.get("appid")+") ";
    		if(!BlankUtils.checkBlank(paramMap.get("cha_id")))
    			sql += " and cha_id in ("+paramMap.get("cha_id")+") ";
    		if(!BlankUtils.checkBlank(paramMap.get("prjid")))
    			sql += " and pid in ("+paramMap.get("prjid")+") ";
    		if(!BlankUtils.checkBlank(paramMap.get("area")))
    			sql += " and area like concat('%',#{obj.area},'%') ";
    		if ((!BlankUtils.checkBlank(paramMap.get("note"))))
    			sql += " and note like concat('%',#{obj.note},'%') ";
        	
    		sql += " order by endtime desc ";
        		
        	PageHelper.startPage(paramMap); // 进行分页
            List<Map<String, Object>> list = adService.queryListMapTwo(sql, paramMap);
			long size = ((Page) list).getTotal();

			Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
			for (Map<String, Object> obj : list) {
				obj.put("createtime", obj.get("createtime")+"");
				obj.put("endtime", obj.get("endtime")+"");

				Map<String, Object> app = appMap.get(obj.get("appid")+"");
				if(app != null)
					obj.put("appname", app.get("app_name")+"");
			}

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 新增修改删除 操作
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/handle", method = RequestMethod.POST)
    public String handle(String handle, DnwxAllparamConfig all, HttpServletRequest request, HttpServletResponse response) {
    	
    	String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
    	
        int result = 0;
        try {
        	String euser = "";
        	try {
        		CurrUserVo curr = (CurrUserVo) redisTemplate.opsForValue().get(token);
				euser = curr.getLogin_name();
			} catch (Exception e) {
				e.printStackTrace();
			}
        	all.setEuser(euser);
        	all.setCuser(euser);
        	
            if ("add".equals(handle)) {
            	// 验证配置信息是否已经存在
            	String prjid = BlankUtils.checkBlank(all.getPid())?"11111":all.getPid();
            	String query = "select * from dnwx_mmparam_all_config "+
					"where (pid in ("+prjid+") and area=#{obj.area} and standard_type=#{obj.standard_type}) ";
//					"or (appid in (100"+all.getAppid()+") and cha_id=#{obj.cha_id} and area=#{obj.area} and standard_type=#{obj.standard_type})";
            	List<Map<String, Object>> confList = adService.queryListMapTwo(query,all);
            	if(confList != null && !confList.isEmpty()){
            		return "{\"ret\":0,\"msg\":\"该配置信息已存在，请检查后再新增!\"}";
            	}
            	
            	List<DnwxAllparamConfig> list = new ArrayList<DnwxAllparamConfig>();
            	if(all.getAppid() != null && all.getAppid().split(",").length > 1){
            		String[] split = all.getAppid().split(",");
            		for (String appid : split) {
            			// 将传入的参数拷贝为多个
            			DnwxAllparamConfig info = new DnwxAllparamConfig();
            			BeanUtils.copyProperties(all, info);
            			info.setAppid(appid);
            			list.add(info);
					}
            		
            	}else if(all.getPid() != null && all.getPid().split(",").length > 1){
            		String[] split = all.getPid().split(",");
            		for (String pid : split) {
            			// 将传入的参数拷贝为多个
            			DnwxAllparamConfig info = new DnwxAllparamConfig();
            			BeanUtils.copyProperties(all, info);
            			info.setAppid(pid.substring(0, 5));
            			info.setPid(pid);
            			list.add(info);
					}
            	}else if(all.getCha_id() != null && all.getCha_id().split(",").length > 1){
            		String[] split = all.getCha_id().split(",");
            		for (String cha_id : split) {
            			// 将传入的参数拷贝为多个
            			DnwxAllparamConfig info = new DnwxAllparamConfig();
            			BeanUtils.copyProperties(all, info);
            			info.setCha_id(cha_id);
            			list.add(info);
					}

            	}else{
            		list.add(all);
            	}
            	
            	// 循环写入批量的配置
            	for (DnwxAllparamConfig act : list) {
            		result = ydService.insertDnwxAllparamConfig(act);
            		
            		if(!BlankUtils.checkBlank(act.getPid())){
            			if("1".equals(act.getApp_audit()) || "4".equals(act.getApp_audit())){
            				// 添加屏蔽锁屏 和 D模式的项目ID
            				String sql = "replace into super_lock_shield(prjid) values(#{obj.pid})";
            				cleanAdMapper.execSqlHandle(sql, act);
            				
            				String sql2 = "replace into super_d_shield(prjid) values(#{obj.pid})";
            				cleanAdMapper.execSqlHandle(sql2, act);
            			}
            		}
            		
            		if(!BlankUtils.checkBlank(act.getPid())){
            			if(!"0".equals(act.getApp_audit())){
            				// 添加锁屏审核设置的状态
            				String sql = "replace into super_advance_config(appid,cha,prjid,createUser,createTime,showPrivacy,showWallpaper,modifyUser,modifyTime,`status`,showSplash,showPermission,audit,wallpaperChannel) "+
            						"values(#{obj.appid},#{obj.cha_id},#{obj.pid},#{obj.euser},now(),'0','1',#{obj.euser},now(),'1','0','1','1','[\"huawei\",\"xiaomi\",\"oppo\",\"vivo\"]')";
            				cleanAdMapper.execSqlHandle(sql, act);
            			}else{
            				String sql = "replace into super_advance_config(appid,cha,prjid,createUser,createTime,showPrivacy,showWallpaper,modifyUser,modifyTime,`status`,showSplash,showPermission,audit,wallpaperChannel) "+
            						"values(#{obj.appid},#{obj.cha_id},#{obj.pid},#{obj.euser},now(),'0','0',#{obj.euser},now(),'1','0','0','0', '[\"huawei\",\"xiaomi\",\"oppo\",\"vivo\"]')";
            				cleanAdMapper.execSqlHandle(sql, act);
            			}
            		}
				}
            	
            } else if ("edit".equals(handle)) {
            	result = ydService.updateDnwxAllparamConfig(all);
            	
            	if(!BlankUtils.checkBlank(all.getPid())){
	            	if("1".equals(all.getApp_audit()) || "4".equals(all.getApp_audit())){
	                	// 添加屏蔽锁屏的项目ID
	                	String sql = "replace into super_lock_shield(prjid) values(#{obj.pid})";
	                    cleanAdMapper.execSqlHandle(sql, all);
	                    
	                    String sql2 = "replace into super_d_shield(prjid) values(#{obj.pid})";
	                    cleanAdMapper.execSqlHandle(sql2, all);
	                }else{
	                	// 移除屏蔽锁屏的项目ID
	                	String sql = "delete from super_lock_shield where prjid = #{obj.pid}";
	                	cleanAdMapper.execSqlHandle(sql, all);
	                	
	                	String sql2 = "delete from super_d_shield where prjid = #{obj.pid}";
	                	cleanAdMapper.execSqlHandle(sql2, all);
	                }
            	}
            	
            	if(!BlankUtils.checkBlank(all.getPid())){
        			if(!"0".equals(all.getApp_audit())){
        				// 添加锁屏审核设置的状态
        				String sql = "update super_advance_config set audit=1,showPermission=1,showWallpaper=1,showSplash=0,showPrivacy=0 where prjid=#{obj.pid}";
        				cleanAdMapper.execSqlHandle(sql, all);
        			}else{
        				String sql = "update super_advance_config set audit=0,showPermission=0,showWallpaper=0,showSplash=0,showPrivacy=0 where prjid=#{obj.pid}";
        				cleanAdMapper.execSqlHandle(sql, all);
        			}
        		}
            	
            } else if ("del".equals(handle)) {
            	result = ydService.deleteDnwxAllparamConfig(all);
            	
            	if(!BlankUtils.checkBlank(all.getPid())){
	            	// 移除屏蔽锁屏的项目ID
	            	String sql = "delete from super_lock_shield where prjid = #{obj.pid}";
	                cleanAdMapper.execSqlHandle(sql, all);
	                
	                String sql2 = "delete from super_d_shield where prjid = #{obj.pid}";
                	cleanAdMapper.execSqlHandle(sql2, all);
            	}
            	
            	if(!BlankUtils.checkBlank(all.getPid())){
    				// 添加锁屏审核设置的状态
    				String sql = "delete from super_advance_config where prjid=#{obj.pid}";
    				cleanAdMapper.execSqlHandle(sql, all);
        		}
            }
            
            
            try {
            	// 刷新缓存 屏蔽锁屏项目ID
            	String url = "https://edc.vigame.cn:6115/clean/refreshCache?mapid=10";
            	String url2 = "https://edc.vigame.cn:6115/clean/refreshCache?mapid=24";
            	
            	String respStr = HttpRequest.get(url, null);
            	String respStr2 = HttpRequest.get(url2, null);
            	if(respStr == null || !respStr.trim().equals("ok")
            			|| respStr2 == null || !respStr2.trim().equals("ok")){
            		
					MailTool.sendMail("分渠道全部配置", "屏蔽锁屏项目ID缓存刷新失败，请检查目标地址有效性！<br>"+url+"<br>"+url2, "<EMAIL>;<EMAIL>", null);
            	}
			} catch (Exception e) {
				e.printStackTrace();
			}

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }
    
    /**
     * 定时任务 操作
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/task", method = RequestMethod.POST)
    public String task(String id, String statu, String time, HttpServletRequest request, HttpServletResponse response) {
    	
        try {
        	if(BlankUtils.checkBlank(id) || BlankUtils.checkBlank(time)){
    			return "{\"ret\":0,\"msg\":\"请求参数错误!\"}";
    		}
        	// 防沉迷开关处理
        	String antiAddiction = request.getParameter("antiAddiction");
        	
        	DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        	long millis = DateTime.parse(time, format).getMillis();

			Message message = new Message(id, "antiAddiction="+antiAddiction, millis);
			Boolean push = delayService.push(message);
			
			if(push)
				return "{\"ret\":1,\"msg\":\"操作成功!\"}";
			else
				return "{\"ret\":0,\"msg\":\"操作失败，请检查后重试!\"}";
        	
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

	/**
	 * 导出
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/export", method={RequestMethod.GET, RequestMethod.POST})
	public void export(HttpServletRequest request,HttpServletResponse response) {


		String[] args = {"appid","cha_id","prjid","area","note"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);

		String sql = "select * from dnwx_mmparam_all_config where 1=1";
		if(!BlankUtils.checkBlank(paramMap.get("appid")))
			sql += " and appid in ("+paramMap.get("appid")+") ";
		if(!BlankUtils.checkBlank(paramMap.get("cha_id")))
			sql += " and cha_id in ("+paramMap.get("cha_id")+") ";
		if(!BlankUtils.checkBlank(paramMap.get("prjid")))
			sql += " and pid in ("+paramMap.get("prjid")+") ";
		if(!BlankUtils.checkBlank(paramMap.get("area")))
			sql += " and area like concat('%',#{obj.area},'%') ";
		if ((!BlankUtils.checkBlank(paramMap.get("note"))))
			sql += " and note like concat('%',#{obj.note},'%') ";

		sql += " order by endtime desc ";

		List<Map<String, Object>> contentList = adService.queryListMapTwo(sql, paramMap);

		// 赋值应用名称
		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		contentList.forEach(act -> {
			act.put("createtime", act.get("createtime")+"");
			act.put("endtime", act.get("endtime")+"");

			Map<String, Object> app = appMap.get(act.get("appid")+"");
			if(app != null)
				act.put("appname", app.get("app_name")+"");
		});


		Map<String, String> headerMap = new LinkedHashMap<String, String>();

		String value = request.getParameter("value");
		if (!BlankUtils.checkBlank(value)){
			//自定义列数据
			try {
				String[] split = value.split(";");
				for (int i = 0;i<split.length;i++) {
					String[] s = split[i].split(",");
					headerMap.put(s[0],s[1]);
				}
			}catch (Exception e) {
				e.printStackTrace();
				Asserts.fail("自定义列导出异常");
			}
		}

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");

		String fileName = "分渠道全部配置" + DateTime.now().toString("yyyyMMdd")+ ".xls";
		ExportExcelUtil.export(response,contentList,headerMap,fileName);
	}

}
