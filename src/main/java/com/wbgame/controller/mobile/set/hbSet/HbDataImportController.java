package com.wbgame.controller.mobile.set.hbSet;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.DiamondWithdrawInfo;
import com.wbgame.service.YdService;
import com.wbgame.utils.BlankUtils;
import jxl.Sheet;
import jxl.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 红包数据导入
 * @author: huangmb
 * @date: 2021/02/24
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile")
public class HbDataImportController {

    @Autowired
    private YdService ydService;

    /**
     * 导入
     *
     * @param file
     * @param request
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/importRedPack", method = {RequestMethod.POST})
    public void importRedPack(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("text/html;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            PrintWriter out = response.getWriter();
            JSONObject obj = new JSONObject();

            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<DiamondWithdrawInfo> list = new ArrayList<>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int column = sheet.getColumns();
                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    DiamondWithdrawInfo ki = new DiamondWithdrawInfo();
                    String[] vals = new String[column];
                    for (int c = 0; c < column; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    ki.setAppid(Integer.parseInt(vals[0]));
                    ki.setPid(Integer.parseInt(vals[1]));
                    ki.setLsn(vals[2]);
                    ki.setImei(vals[3]);
                    ki.setOpenid(vals[4]);
                    ki.setAccessToken(vals[5]);
                    ki.setAmount(vals[6]);
                    list.add(ki);
                }

                ydService.batchInsertWithdraw(list);
                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
                out.write(obj.toString());
                out.close();
            } else {

                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls格式文件");
                out.write(obj.toString());
                out.close();
            }

        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            response.setCharacterEncoding("utf-8");
            PrintWriter pw;
            try {
                pw = response.getWriter();
                JSONObject obj = new JSONObject();
                //返回失败信息
                obj.put("ret", 0);
                obj.put("msg", e.getMessage());
                pw.write(obj.toString());
                pw.close();
            } catch (IOException e1) {
                e.printStackTrace();
            }
        }
    }
}
