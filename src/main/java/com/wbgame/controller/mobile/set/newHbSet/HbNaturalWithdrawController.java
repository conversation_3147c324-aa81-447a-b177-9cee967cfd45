package com.wbgame.controller.mobile.set.newHbSet;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.ApkEventMapper;
import com.wbgame.mapper.redpack.HbWithdrawChannelControlMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.mobile.HbWithdrawChannelControl;
import com.wbgame.utils.BlankUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 自然量控制提现配置
 * @author: huangmb
 * @date: 2021/11/10
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/hbNaturalWithdraw")
public class HbNaturalWithdrawController {

    @Resource
    private HbWithdrawChannelControlMapper hbWithdrawChannelControlMapper;

    @Resource
    private ApkEventMapper apkEventMapper;

    @RequestMapping(value = "/list", method = {RequestMethod.POST,RequestMethod.GET})
    public String list(HttpServletRequest request,HbWithdrawChannelControl param) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<HbWithdrawChannelControl> list = hbWithdrawChannelControlMapper.selectByAll(param);
        //查询应用名称
        List<Map<String,Object>> apps = apkEventMapper.selectAllAppInfo();
        Map<String,String> appinfo = new HashMap<>();
        for (Map<String,Object> map:apps) {
            appinfo.put(map.get("id")+"",map.get("app_name")+"");
        }
        for (HbWithdrawChannelControl h : list) {
            h.setAppName(appinfo.get(h.getAppid())+"_"+h.getAppid());
        }
        long size = ((Page) list).getTotal();
        Map result = new HashMap();
        result.put("list",list);
        result.put("total",size);
        return ReturnJson.success(result);
    }

    @RequestMapping(value = "/add", method = {RequestMethod.POST,RequestMethod.GET})
    public String add(HttpServletRequest request,HbWithdrawChannelControl param) {
        if (param.getAppid() == null || BlankUtils.checkBlank(param.getChannel())) {
            return ReturnJson.error(Constants.ParamError);
        }
        List<HbWithdrawChannelControl> list = hbWithdrawChannelControlMapper.selectByAll(param);
        if (list != null && list.size() > 0) {
            return ReturnJson.toErrorJson("该应用的配置已存在");
        }
        CurrUserVo loginUser = (CurrUserVo)request.getAttribute("LoginUser");
        param.setCreateTime(new Date());
        param.setUpdateTime(new Date());
        param.setCreateOwner(loginUser.getLogin_name());
        param.setUpdateOwner(loginUser.getLogin_name());
        hbWithdrawChannelControlMapper.insertSelective(param);
        return ReturnJson.success("插入成功");
    }

    @RequestMapping(value = "/delete", method = {RequestMethod.POST,RequestMethod.GET})
    public String delete(HbWithdrawChannelControl param) {
        hbWithdrawChannelControlMapper.deleteByPrimaryKey(param.getAppid(),param.getChannel());
        return ReturnJson.success("删除成功");
    }

}
