package com.wbgame.controller.mobile.set.gameSet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Constants;
import com.wbgame.common.ProtocolConstant;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.mobile.*;
import com.wbgame.service.mobile.PvpConfigService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname PvpConfigController
 * @Description TODO
 * @Date 2021/8/31 17:25
 */
@RequestMapping("/mobile/pvp")
@RestController
@CrossOrigin
public class PvpConfigController {


    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    PvpConfigService pvpConfigService;

    /**
     * 比赛配置查询接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("getMatchList")
    public Object matchList(HttpServletRequest request, PvpMatchVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        //分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        //查询
        PageHelper.startPage(pageNo, pageSize);
        List<PvpMatchVo> list = pvpConfigService.getPvpMatchList(vo);
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return result;
    }

    /**
     * 比赛配置操作接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("matchHandle")
    public Object matchHandle(HttpServletRequest request, PvpMatchVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String handle = request.getParameter("handle");
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (vo==null){
            return ReturnJson.toErrorJson("必要参数为空");
        }
        vo.setCreateUser(cuser.getLogin_name());
        vo.setModifyUser(cuser.getLogin_name());
        int succ = 0;
        List<PvpMatchVo> list = pvpConfigService.getPvpMatchList(new PvpMatchVo());
        if ("add".equals(handle)){
            PvpMatchVo pvpMatchVo = list
                    .stream()
                    .filter(t->(t.getPrjid()+t.getChannel()).equals((vo.getPrjid()+vo.getChannel())))
                    .findFirst()
                    .orElse(null);
            if (pvpMatchVo != null) {
                return ReturnJson.toErrorJson("新增失败,已经存在项目+渠道的比赛场次配置");
            }
            succ = pvpConfigService.addPvpMatchConfig(vo);
        }else if("update".equals(handle)){
            PvpMatchVo pvpMatchVo = list
                    .stream()
                    .filter(t->(t.getPrjid()+t.getChannel()).equals((vo.getPrjid()+vo.getChannel()))&&!t.getId().equals(vo.getId()))
                    .findFirst()
                    .orElse(null);
            if (pvpMatchVo != null) {
                return ReturnJson.toErrorJson("更新失败,已经存在项目+渠道的比赛场次配置");
            }
            succ = pvpConfigService.updatePvpMatchConfig(vo);
        }else if("del".equals(handle)){
            succ = pvpConfigService.delPvpMatchConfig(vo);
        }
        if (succ>0){
            return ReturnJson.success();
        }else {
            return ReturnJson.toErrorJson("操作失败,请联系管理员");
        }
    }



    /**
     * 比赛详情查询接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("getMatchDetailList")
    public Object matchDetailList(HttpServletRequest request, PvpMatchDetailVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        //分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        //查询
        PageHelper.startPage(pageNo, pageSize);
        List<PvpMatchDetailVo> list = pvpConfigService.getPvpMatchDetailList(vo);
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return result;
    }

    /**
     * 问卷问题操作接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("matchDetailHandle")
    public Object matchDetailHandle(HttpServletRequest request, PvpMatchDetailVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String handle = request.getParameter("handle");
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (vo==null){
            return ReturnJson.toErrorJson("必要参数为空");
        }
        vo.setCreateUser(cuser.getLogin_name());
        vo.setModifyUser(cuser.getLogin_name());
        int succ = 0;
        List<PvpMatchDetailVo> list = pvpConfigService.getPvpMatchDetailList(new PvpMatchDetailVo());
        if ("add".equals(handle)){
            PvpMatchDetailVo pvpMatchDetailVo = list.stream().filter(t->t.getMatchId().equals(vo.getMatchId())).findFirst().orElse(null);
            if (pvpMatchDetailVo!=null){
                return ReturnJson.toErrorJson("新增失败,已经该比赛场次id的比赛详情配置");
            }
            succ = pvpConfigService.addPvpMatchDetailConfig(vo);
        }else if("update".equals(handle)){
            PvpMatchDetailVo pvpMatchDetailVo = list
                    .stream()
                    .filter(t->t.getMatchId().equals(vo.getMatchId())&&!t.getId().equals(vo.getId()))
                    .findFirst()
                    .orElse(null);
            if (pvpMatchDetailVo!=null){
                return ReturnJson.toErrorJson("更新失败,已经该比赛场次id的比赛详情配置");
            }
            succ = pvpConfigService.updatePvpMatchDetailConfig(vo);

        }else if("del".equals(handle)){
            succ = pvpConfigService.delPvpMatchDetailConfig(vo);
        }
        if (succ>0){
            return ReturnJson.success();
        }else {
            return ReturnJson.toErrorJson("操作失败,请联系管理员");
        }
    }





    /**
     * 比赛胜率配置查询接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("getWinRatioList")
    public Object wnRatioList(HttpServletRequest request, PvpWinRatioVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        //分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        //查询
        PageHelper.startPage(pageNo, pageSize);
        List<PvpWinRatioVo> list = pvpConfigService.getPvpWinRatioList(vo);
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return result;
    }

    /**
     * 比赛胜率配置操作接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("winRatioHandle")
    public Object winRatioHandle(HttpServletRequest request, PvpWinRatioVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String handle = request.getParameter("handle");
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (vo==null){
            return ReturnJson.toErrorJson("必要参数为空");
        }
        vo.setCreateUser(cuser.getLogin_name());
        vo.setModifyUser(cuser.getLogin_name());
        int succ = 0;
        if ("add".equals(handle)){
            succ = pvpConfigService.addPvpWinRatioConfig(vo);
        }else if("update".equals(handle)){
            succ = pvpConfigService.updatePvpWinRatioConfig(vo);
        }else if("del".equals(handle)){
            succ = pvpConfigService.delPvpWinRatioConfig(vo);
        }
        if (succ>0){
            return ReturnJson.success();
        }else {
            return ReturnJson.toErrorJson("操作失败,请联系管理员");
        }
    }



    /**
     * 比赛奖励配置查询接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("getAwardList")
    public Object awardList(HttpServletRequest request, PvpAwardVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        //分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        //查询
        PageHelper.startPage(pageNo, pageSize);
        List<PvpAwardVo> list = pvpConfigService.getPvpAwardList(vo);
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return result;
    }

    /**
     * 问卷问题操作接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("awardHandle")
    public Object awardHandle(HttpServletRequest request, PvpAwardVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String handle = request.getParameter("handle");
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (vo==null){
            return ReturnJson.toErrorJson("必要参数为空");
        }
        vo.setCreateUser(cuser.getLogin_name());
        vo.setModifyUser(cuser.getLogin_name());
        int succ = 0;
        List<PvpAwardVo> list = pvpConfigService.getPvpAwardList(new PvpAwardVo());
        if ("add".equals(handle)){
            //判断输入条件
            PvpAwardVo awardVo = list.stream().filter(t->t.getAwardid().equals(vo.getAwardid())).findFirst().orElse(null);
            if (awardVo!=null){
                return ReturnJson.toErrorJson("新增失败,该奖励id已经存在");
            }
            succ = pvpConfigService.addPvpAwardConfig(vo);
        }else if("update".equals(handle)){
            PvpAwardVo awardVo = list.stream().filter(t->t.getAwardid().equals(vo.getAwardid())&&!vo.getId().equals(t.getId())).findFirst().orElse(null);
            if (awardVo!=null){
                return ReturnJson.toErrorJson("更新失败,该奖励id已经存在");
            }
            succ = pvpConfigService.updatePvpAwardConfig(vo);
        }else if("del".equals(handle)){
            succ = pvpConfigService.delPvpAwardConfig(vo);
        }
        if (succ>0){
            return ReturnJson.success();
        }else {
            return ReturnJson.toErrorJson("操作失败,请联系管理员");
        }
    }




    /**
     * 比赛门票配置查询接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("getTicketList")
    public Object ticketList(HttpServletRequest request, PvpTicketVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        //分页
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        //查询
        PageHelper.startPage(pageNo, pageSize);
        List<PvpTicketVo> list = pvpConfigService.getPvpTicketList(vo);
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        result.put("totalCount", size);
        return result;
    }

    /**
     * 问卷问题操作接口
     * @param request
     * @param vo
     * @return
     */
    @RequestMapping("ticketHandle")
    public Object ticketHandle(HttpServletRequest request, PvpTicketVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        else
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        String handle = request.getParameter("handle");
        CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (vo==null){
            return ReturnJson.toErrorJson("必要参数为空");
        }
        vo.setCreateUser(cuser.getLogin_name());
        vo.setModifyUser(cuser.getLogin_name());
        int succ = 0;
        List<PvpTicketVo> list = pvpConfigService.getPvpTicketList(new PvpTicketVo());
        if ("add".equals(handle)){
            PvpTicketVo pvpTicketVo = list.stream()
                    .filter(t->(t.getPrjid()+t.getChannel()).equals((vo.getPrjid()+vo.getChannel())))
                    .findFirst()
                    .orElse(null);
            if (pvpTicketVo!=null){
                return ReturnJson.toErrorJson("新增失败,已经存在项目id+渠道的门票配置");
            }
            succ = pvpConfigService.addPvpTicketConfig(vo);
        }else if("update".equals(handle)){
            PvpTicketVo pvpTicketVo = list.stream()
                    .filter(t->(t.getPrjid()+t.getChannel()).equals((vo.getPrjid()+vo.getChannel()))&&!t.getId().equals(vo.getId()))
                    .findFirst()
                    .orElse(null);
            if (pvpTicketVo!=null){
                return ReturnJson.toErrorJson("更新失败,已经存在项目id+渠道的门票配置");
            }
            succ = pvpConfigService.updatePvpTicketConfig(vo);
        }else if("del".equals(handle)){
            succ = pvpConfigService.delPvpTicketConfig(vo);
        }
        if (succ>0){
            return ReturnJson.success();
        }else {
            return ReturnJson.toErrorJson("操作失败,请联系管理员");
        }
    }

    /**
     * 查询用户信息
     * @param request
     * @return
     */
    @RequestMapping("getUserInfo")
    public Object getUserInfo(HttpServletRequest request){
        String userid = request.getParameter("userid");
        if (BlankUtils.checkBlank(userid)) {
            return ReturnJson.error(Constants.ParamError);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userid", userid);
        jsonObject.put("sign", "xyxtjpvp123..!@#$");
        String value = Base64.encodeBase64String(jsonObject.toJSONString().getBytes());
        String url = ProtocolConstant.pvp_url + "/pvp/getUserInfo?value=" + value;
        String result = HttpClientUtils.getInstance().httpGet(url);
        return result;
    }

    /**
     * 删除用户信息
     * @return
     */
    @RequestMapping("delUserInfo")
    public Object delUserInfo(HttpServletRequest request){
        String userid = request.getParameter("userid");
        if (BlankUtils.checkBlank(userid)) {
            return ReturnJson.error(Constants.ParamError);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userid", userid);
        jsonObject.put("sign", "xyxtjpvp123..!@#$");
        String value = Base64.encodeBase64String(jsonObject.toJSONString().getBytes());
        String url = ProtocolConstant.pvp_url + "/pvp/delUserInfo?value=" + value;
        String result = HttpClientUtils.getInstance().httpGet(url);
        return result;
    }



}
