package com.wbgame.controller.mobile.set.gameSet;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.ProductPriceConfigVo;
import com.wbgame.service.MobileIntegralConfigService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import java.sql.Date;
import java.util.List;

/**
 * @description: 移动积分兑换功能 产品价值配置表
 * @author: xugx
 * @date: 2021/05/25
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/productConfig")
public class MobileIntegralConfigController {
	Logger logger = LoggerFactory.getLogger(MobileIntegralConfigController.class);
    @Autowired
    public MobileIntegralConfigService mobileIntegralConfigService;

    @SuppressWarnings({ "unchecked", "rawtypes" })
	@RequestMapping(value = "/list")
    public String list(HttpServletRequest request,@RequestBody ProductPriceConfigVo config){
    	try {
    		 int pageStart = null==config.getStart() == true ? 0 : config.getStart();
 	        int pageSize =  null==config.getLimit() == true ? 100 : config.getLimit();
	        // pageStart 和 limit设置值
	        int pageNo = (pageStart / pageSize) + 1;
	        PageHelper.startPage(pageNo, pageSize); // 进行分页
	        List<ProductPriceConfigVo> list = mobileIntegralConfigService.selectByAll(config);
	        return ReturnJson.success(new PageInfo(list));
		} catch (Exception e) {
			logger.error("MobileIntegralConfigController:查询商品配置异常,msg:"+e.getMessage());
			e.printStackTrace();
			return ReturnJson.toErrorJson("查询商品配置异常");
		}
       
    }

    @RequestMapping(value = "/add")
    public String add(HttpServletRequest request,@RequestBody ProductPriceConfigVo config){
    	try {
    		 CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
    		 config.setCuser(loginUser.getLogin_name());
    		 config.setEuser(loginUser.getLogin_name());
    		 config.setCreatetime(new Date(Long.valueOf(System.currentTimeMillis())));
    		 config.setEndtime(new Date(Long.valueOf(System.currentTimeMillis())));
    		 config.setStatu(1);
    		 mobileIntegralConfigService.insert(config);
    	     return ReturnJson.success();
		}  catch (DuplicateKeyException e) {
			logger.error("MobileIntegralConfigController:新增商品配置失败,productId:"+config.getProductNo()+"已存在");
			return ReturnJson.toErrorJson("该商品编号已存在");
		}catch (Exception e) {
			logger.error("MobileIntegralConfigController:新增商品配置异常,msg:"+e.getMessage());
			e.printStackTrace();
			return ReturnJson.toErrorJson("新增商品配置异常");
		}
       
    }

    @RequestMapping(value = "/update")
    public String update(HttpServletRequest request,@RequestBody ProductPriceConfigVo config){
    	try {
    		CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
            config.setEndtime(new Date(Long.valueOf(System.currentTimeMillis())));
            config.setEuser(loginUser.getLogin_name());
            mobileIntegralConfigService.updateByPrimaryKey(config);
            return ReturnJson.success();
		} catch (Exception e) {
			logger.error("MobileIntegralConfigController:修改商品配置异常,msg:"+e.getMessage());
			e.printStackTrace();
			return ReturnJson.toErrorJson("修改商品配置异常");
		}
    }

    @RequestMapping(value = "/delete")
    public String delete(@RequestBody ProductPriceConfigVo record){
    	try {
    		mobileIntegralConfigService.deleteByPrimaryKey(record);
    		return ReturnJson.success();
		} catch (Exception e) {
			logger.error("MobileIntegralConfigController:删除商品配置异常,msg:"+e.getMessage());
			e.printStackTrace();
			return ReturnJson.toErrorJson("删除商品配置异常");
		}
       
    }


}
