package com.wbgame.controller.mobile.main;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.pojo.custom.GDTWorkReportVo;
import com.wbgame.service.AdService;
import com.wbgame.service.AdmsgService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 红包道具监控
 * @author: huangmb
 * @date: 2021/02/09
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/redPackRange")
public class RedPackRangeController {

    @Autowired
    private AdmsgService admsgService;

    @Autowired
    private AdService adService;

    /**
     * 查询
     * @param request
     * @param response
     */
    @RequestMapping(value="/list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(GDTWorkReportVo gdt, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String tdate = request.getParameter("tdate");
        String appid = request.getParameter("appid");
        String group_appid = request.getParameter("group_appid");
        String pid = request.getParameter("pid");
        String group_pid = request.getParameter("group_pid");

        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if(BlankUtils.sqlValidate(tdate)
                    || BlankUtils.sqlValidate(pid)
                    || BlankUtils.sqlValidate(appid)){
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }
            if(BlankUtils.checkBlank(tdate)){
                tdate = DateTime.now().toString("yyyy-MM-dd");
            }

            String sql = "select concat(tdate,'') tdate,appid,pid,tooltype,"
                    +"SUM(range1) range1,SUM(range2) range2,SUM(range3) range3,SUM(range4) range4,"
                    +"SUM(range5) range5,SUM(range6) range6,SUM(range7) range7,SUM(range8) range8"
                    + " from home_diamond_redpack_range"
                    + " where tdate BETWEEN '"+tdate+"' AND '"+tdate+"' ";
            if(!BlankUtils.checkBlank(pid))
                sql += " and pid = "+pid;
            if(!BlankUtils.checkBlank(appid))
                sql += " and appid = "+appid;

            if(!BlankUtils.checkBlank(group_pid))
                sql += " group by pid,tooltype";
            else
                sql += " group by appid,tooltype";

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<Map<String, Object>> list = admsgService.queryListMap(sql);
            long size = new PageInfo(list).getTotal();

            Map<String, Map<String, Object>> pidMap = adService.selectProjectidChannelMap();
            List<Map<String, Object>> collect = list.stream().map(map -> {
                pidMap.values().forEach(pMap -> {
                    if((map.get("appid")+"").equals(pMap.get("appid"+""))) {
                        map.put("ver", pMap.get("ver")+"");
                        map.put("appname", pMap.get("gname")+"");
                    }
                });

                String ty = map.get("tooltype")+"";
                map.put("tooltype", "1".equals(ty)?"金币":"2".equals(ty)?"iphone碎片":"3".equals(ty)?"华为碎片":"红包余额");

                if(BlankUtils.checkBlank(group_pid)) {
                    map.remove("ver");
                    map.remove("pid");
                }
                return map;
            }).collect(Collectors.toList());

            String query = "select * from home_diamond_redpack_config";
            List<Map<String, Object>> queryList = admsgService.queryListMap(query);

            result.put("ret", 1);
            result.put("data", collect);
            result.put("appList", queryList);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    /**
     * 导出
     * @param request
     * @param response
     */
    @RequestMapping(value="/export", method = RequestMethod.POST)
    public void export(HttpServletRequest request,HttpServletResponse response) {
        String tdate = request.getParameter("tdate");
        String appid = request.getParameter("appid");
        String group_appid = request.getParameter("group_appid");
        String pid = request.getParameter("pid");
        String group_pid = request.getParameter("group_pid");

        List<Map<String, Object>> contentList = null;
        if(BlankUtils.sqlValidate(tdate)
                || BlankUtils.sqlValidate(pid)
                || BlankUtils.sqlValidate(appid)){
            return ;
        }

        if(BlankUtils.checkBlank(tdate)){
            tdate = DateTime.now().toString("yyyy-MM-dd");
        }
        String sql = "select concat(tdate,'') tdate,appid,pid,tooltype,"
                +"SUM(range1) range1,SUM(range2) range2,SUM(range3) range3,SUM(range4) range4,"
                +"SUM(range5) range5,SUM(range6) range6,SUM(range7) range7,SUM(range8) range8"
                + " from home_diamond_redpack_range"
                + " where tdate BETWEEN '"+tdate+"' AND '"+tdate+"' ";
        if(!BlankUtils.checkBlank(pid))
            sql += " and pid = "+pid;
        if(!BlankUtils.checkBlank(appid))
            sql += " and appid = "+appid;

        if(!BlankUtils.checkBlank(group_pid))
            sql += " group by pid,tooltype";
        else
            sql += " group by appid,tooltype";

        List<Map<String, Object>> list = admsgService.queryListMap(sql);
        Map<String, Map<String, Object>> pidMap = adService.selectProjectidChannelMap();
        contentList = list.stream().map(map -> {
            pidMap.values().forEach(pMap -> {
                if((map.get("appid")+"").equals(pMap.get("appid"+""))) {
                    map.put("ver", pMap.get("ver")+"");
                    map.put("appname", pMap.get("gname")+"");
                }
            });

            String ty = map.get("tooltype")+"";
            map.put("tooltype", "1".equals(ty)?"金币":"2".equals(ty)?"iphone碎片":"3".equals(ty)?"华为碎片":"红包余额");

            if(BlankUtils.checkBlank(group_pid)) {
                map.remove("ver");
                map.remove("pid");
            }
            return map;
        }).collect(Collectors.toList());

        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("appname", "产品名称");
        if(!BlankUtils.checkBlank(group_pid)) {
            headerMap.put("pid", "项目id");
            headerMap.put("ver", "版本号");
        }
        headerMap.put("tooltype", "道具类型");
        headerMap.put("range1", "0-20%");
        headerMap.put("range2", "20-40%");
        headerMap.put("range3", "40-60%");
        headerMap.put("range4", "60-80%");
        headerMap.put("range5", "80-90%");
        headerMap.put("range6", "90-95%");
        headerMap.put("range7", "95-100%");
        headerMap.put("range8", "100%及以上");

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "红包道具监控_"+DateTime.now().toString("yyyyMMddHHmmss")+".xls";
        JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response);
    }

}
