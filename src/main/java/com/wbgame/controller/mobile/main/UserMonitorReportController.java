package com.wbgame.controller.mobile.main;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.custom.AdTotalHourTwoVo;
import com.wbgame.service.AdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @description: 用户监控报表
 * @author: huangmb
 * @date: 2021/02/09
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/userMonitorReport")
public class UserMonitorReportController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private AdService adService;

    /**
     * 实时新增监控报表、活跃用户监控报表、广告请求实时图表
     *
     */
    @RequestMapping(value="/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String list(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String sdate = request.getParameter("sdate");
        String edate = request.getParameter("edate");
        String adtype = request.getParameter("adtype"); // 1为自推广，2为市场，3为两者合计
        String ctype = request.getParameter("ctype"); // new-新增报表，active-活跃报表
        String pid = request.getParameter("pid");
        String appid = request.getParameter("appid");
		String appGroup = request.getParameter("appGroup");

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if(BlankUtils.sqlValidate(sdate)
                    || BlankUtils.sqlValidate(edate)
                    || BlankUtils.sqlValidate(adtype)
                    || BlankUtils.sqlValidate(ctype)){
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
                sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
                edate = DateTime.now().toString("yyyy-MM-dd");
            }
            // 应用组列表
            List<String> apps = CommonUtil.appGroupMap.get(appGroup);

            String table_name = "new_count_hour_two";
            if("active".equals(ctype))
                table_name = "new_active_hour_two";

            String pro = "";
            for (int i = 0; i < 24; i++) {
                if(i < 10)
                    pro += ",SUM(hour0"+i+") hour0"+i;
                else
                    pro += ",SUM(hour"+i+") hour"+i;
            }
            String sql = "select tdate,pid"+pro+" from "+table_name+" where tdate in ('"+sdate+"','"+edate+"') ";

            if(!BlankUtils.checkBlank(adtype))
                sql += " and adtype = "+adtype;
            if(!BlankUtils.checkBlank(pid))
                sql += " and pid = "+pid;
            if(!BlankUtils.checkBlank(appid))
            	sql += " and SUBSTR(pid,1,5) in ("+appid+")";
            if(apps != null && apps.size() > 0)
            	sql += " and SUBSTR(pid,1,5) in ("+String.join(",", apps)+")";
            
            sql += " group by tdate";

            List<AdTotalHourTwoVo> list = adService.queryHourReport(sql);

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", list.size());
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }

}
