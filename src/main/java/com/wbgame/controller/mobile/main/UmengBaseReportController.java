package com.wbgame.controller.mobile.main;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.DnChannelInfo;
import com.wbgame.service.mobile.UmengDataService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DataTransUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @description: 友盟产品基础数据报表
 * @author: huangmb
 * @date: 2021/11/30
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/umengBaseReport")
public class UmengBaseReportController {

    @Autowired
    private UmengDataService umengDataService;


    /**
     * 查询国家数据
     * @param request
     * @return 查询结果
     */
    @RequestMapping("/listCountry")
    public Result<List<String>> listCountry(HttpServletRequest request){
        List<String> countries = umengDataService.listCountry();
        return ResultUtils.success(countries);
    }


    /**
     * 友盟产品基础数据报表 查询接口
     * modify：xiaoxh-2024-08-06： 增加海外产品，通过国家/地区筛选项区分
     * @param request HttpServletRequest
     * @return 查询结果
     */
    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public String list (HttpServletRequest request){
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 当前页
        int pageNo = (pageStart / pageSize) + 1;
        //参数
        String appid = getWhereParam(request.getParameter("appid"));
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String app_category = request.getParameter("app_category");
        String version = getWhereParam(request.getParameter("version"));
        String orderStr = request.getParameter("order_str");
        if (BlankUtils.checkBlank(orderStr)) {
            orderStr = "tdate asc,act_num desc";
        }
        JSONObject result = new JSONObject();
        //由于渠道维护表与查询数据不同库，需特殊处理
        String media = getWhereParam(request.getParameter("media"));
        String cha_type = getWhereParam(request.getParameter("cha_type"));
        String channel = getWhereParam(request.getParameter("channel"));
        if (!BlankUtils.checkBlank(media) || !BlankUtils.checkBlank(cha_type) || !BlankUtils.checkBlank(channel)) {
            channel = umengDataService.selectChannelListByChaTypeAndMedia(cha_type,media,channel);
            if (BlankUtils.checkBlank(channel)) {
                result.put("list", new ArrayList<>());
                result.put("total", 0);
                result.put("sum",new HashMap<>());
                return ReturnJson.success(result);
            }
        }
        //获取国内海外的标识
        String overseaFlag = request.getParameter("overseaFlag");
        //是否中国国家标记
        boolean chinaFlag = StringUtils.isEmpty(overseaFlag) || !"1".equals(overseaFlag);
        Map param = new HashMap();
        param.put("appid",appid);
        param.put("startTime",startTime);
        param.put("endTime",endTime);
        param.put("app_category",app_category);
        param.put("channel",getWhereParam(channel));
        param.put("version",version);
        String[] groups = request.getParameter("group").split(",");
        String group = "";
        for (String s : groups) {
            group = group + "a."+s+",";
        }
        param.put("group",group.substring(0,group.length()-1).replace("tdate","a_day"));
        param.put("order_str",orderStr);
        PageHelper.startPage(pageNo, pageSize);
        List<Map<String,Object>> list = null;
        Map<String,Object> sum = null;
        if (chinaFlag) {
            //国内数据查询
            list = umengDataService.selectUmengBaseReport(param);
            sum = umengDataService.countUmengBaseReport(param);
        } else {
            // 海外数据查询：海外跟国内数据涉及的表不同，sql其实是相似的
            String country = request.getParameter("country");
            //封装国家参数
            param.put("country", DataTransUtils.transToSql(country));
            list = umengDataService.selectUmengOverseaReport(param);
            sum = umengDataService.countUmengOverseaReport(param);
        }
        Map<String, DnChannelInfo> chaInfos = umengDataService.getChaInfos();
        for (Map<String,Object> m : list) {
            if (m.get("install_channel") != null && chaInfos.get(m.get("install_channel") +"" ) != null) {
                m.put("cha_type",chaInfos.get(m.get("install_channel")).getChaType());
                m.put("media",chaInfos.get(m.get("install_channel")).getChaMedia());
            }
        }
        long size = ((Page) list).getTotal();
        result.put("list", list);
        result.put("total", size);
        result.put("sum",sum);
        return ReturnJson.success(result);
    }

    @RequestMapping(value = "/export", method = {RequestMethod.POST})
    public void export (HttpServletRequest request, HttpServletResponse response){
        //参数
        String appid = getWhereParam(request.getParameter("appid"));
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String app_category = request.getParameter("app_category");
        String version = getWhereParam(request.getParameter("version"));
        String orderStr = request.getParameter("order_str");
        if (BlankUtils.checkBlank(orderStr)) {
            orderStr = "tdate asc,act_num desc";
        }
        //由于渠道维护表与查询数据不同库，需特殊处理
        String media = getWhereParam(request.getParameter("media"));
        String cha_type = getWhereParam(request.getParameter("cha_type"));
        String channel = getWhereParam(request.getParameter("channel"));
        if (!BlankUtils.checkBlank(media) || !BlankUtils.checkBlank(cha_type) || !BlankUtils.checkBlank(channel)) {
            channel = umengDataService.selectChannelListByChaTypeAndMedia(cha_type,media,channel);
            if (BlankUtils.checkBlank(channel)) {
                Asserts.fail("未查询到导出数据");
            }
        }
        //获取国内海外的标识
        String overseaFlag = request.getParameter("overseaFlag");
        //是否中国国家标记
        boolean chinaFlag = StringUtils.isEmpty(overseaFlag) || !"1".equals(overseaFlag);
        Map param = new HashMap();
        param.put("appid",appid);
        param.put("startTime",startTime);
        param.put("endTime",endTime);
        param.put("app_category",app_category);
        param.put("channel",getWhereParam(channel));
        param.put("version",version);
        String[] groups = request.getParameter("group").split(",");
        String group = "";
        for (String s : groups) {
            group = group + "a."+s+",";
        }
        param.put("group",group.substring(0,group.length()-1).replace("tdate","a_day"));
        param.put("order_str",orderStr);
        List<Map<String,Object>> list = null;
        if (chinaFlag) {
            //中国
            list = umengDataService.selectUmengBaseReport(param);
        } else {
            //海外
            // 海外数据查询：海外跟国内数据涉及的表不同，sql其实是相似的
            String country = request.getParameter("country");
            //封装国家参数
            param.put("country", DataTransUtils.transToSql(country));
            list = umengDataService.selectUmengOverseaReport(param);
        }
        Map<String, DnChannelInfo> chaInfos = umengDataService.getChaInfos();
        Map<String, String> appCategoryName = umengDataService.getAppCategoryName();
        for (Map<String,Object> m : list) {
            if (m.get("app_category") != null) {
                m.put("app_category",appCategoryName.get(m.get("app_category")+""));
            }
            if (m.get("install_channel") != null && chaInfos.get(m.get("install_channel") +"" ) != null) {
                m.put("cha_type",chaInfos.get(m.get("install_channel")).getTypeName());
                m.put("media",chaInfos.get(m.get("install_channel")).getChaMedia());
            }
        }
        //自定义列
        String value = request.getParameter("value");
        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(export_file_name)){
            export_file_name = "产品基础数据(不含小游戏)";
        }
        String fileName = export_file_name+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX(response,list,head,fileName);
    }

    public String getWhereParam(String param) {
        if (!BlankUtils.checkBlank(param)) {
            String[] split = param.split(",");
            String in = "";
            for (String s : split) {
                in = in + "'" + s + "'" + ",";
            }
            return "("+in.substring(0,in.length()-1) +")";
        }else{
            return null;
        }
    }

}
