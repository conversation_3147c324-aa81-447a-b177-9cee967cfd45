package com.wbgame.controller.mobile.main;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.service.AdService;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @description: 留存汇总查询
 * @author: huangmb
 * @date: 2021/02/09
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/keepTotal")
public class KeepTotalController {

    @Autowired
    SomeService someService;

    @Autowired
    AdService adService;

    /**
     * 查询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    public String list(HttpServletRequest request, HttpServletResponse response) {
        // pageStart 和 limit设置值
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        String beginDt = BlankUtils.checkNull(request, "beginDt");
        beginDt = beginDt.replace("-", "");
        String endDt = BlankUtils.checkNull(request, "endDt");
        endDt = endDt.replace("-", "");
        String pid = BlankUtils.checkNull(request, "pid");
        String appid = BlankUtils.checkNull(request, "appid");
        String group_pid = BlankUtils.checkNull(request, "group_pid");
        String type = BlankUtils.checkNull(request, "type");
        String channel = BlankUtils.checkNull(request, "channel");

        String resp = "";
        if (!BlankUtils.checkBlank(type)) {
            for (int i = 1; i <= 30; i++) {
                resp += "CONCAT(TRUNCATE(avg(keep" + i + ")*100, 0),'%') keep" + i + ",";

                String kk = "";
                for (int p = 1; p <= i; p++) {
                    kk += "+avg(keep" + p + ")";
                }

                resp += "TRUNCATE(1" + kk + ", 2) keepnum" + i + ",";
            }
        } else {
            int[] mm = {1, 2, 3, 4, 5, 6, 7, 14, 30};
            for (int i = 0; i < mm.length; i++) {
                resp += "CONCAT(TRUNCATE(avg(keep" + mm[i] + ")*100, 0),'%') keep" + mm[i] + ",";

                String kk = "";
                for (int p = 1; p <= mm[i]; p++) {
                    kk += "+avg(keep" + p + ")";
                }

                resp += "TRUNCATE(1" + kk + ", 2) keepnum" + mm[i] + ",";
            }
        }

        String where = "";
        if (!BlankUtils.checkBlank(appid))
            where += " and product_id in (" + appid+") ";
        if (!BlankUtils.checkBlank(pid))
            where += " and projectid = " + pid;
        if (!BlankUtils.checkBlank(channel)) {
            where += " and wf.channelTag = '" + channel+"'";
        }

        String sql = "select wc.cha_sub_launch channel,mmdate dt,product_id appid,b.app_name appname,projectid pid," + resp + "SUM(usernum) usernum" +
                " from product_keep_num_total a left join app_info b on a.product_id = b.id" +
                " left join  dnwx_client.wbgui_formconfig wf ON wf.pjId = a.projectid LEFT JOIN dn_channel_info wc ON wf.channelTag = wc.cha_id " +
                " where mmdate BETWEEN '" + beginDt + "' AND '" + endDt + "' " + where +
                " group by mmdate,product_id" + ("ok".equals(group_pid) ? ",projectid" : "") +
                " order by mmdate desc,usernum desc";

        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<Map<String, Object>> list = adService.queryListMap(sql);
        list.forEach(map -> {
            JSONArray array = new JSONArray();
            if (!BlankUtils.checkBlank(type)) {
                for (int i = 1; i <= 30; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("a", map.get("keep" + i));
                    obj.put("b", map.get("keepnum" + i));
                    obj.put("title", i + "日留存");
                    obj.put("key", "keep" + i);
                    array.add(obj);
                }
            } else {
                int[] mm = {1, 2, 3, 4, 5, 6, 7, 14, 30};
                for (int p = 0; p < mm.length; p++) {
                    int i = mm[p];
                    JSONObject obj = new JSONObject();
                    obj.put("a", map.get("keep" + i));
                    obj.put("b", map.get("keepnum" + i));
                    obj.put("title", i + "日留存");
                    obj.put("key", "keep" + i);
                    array.add(obj);
                }
            }
            map.put("keep", array);
            map.put("dt", map.get("dt") + "");
        });
        long size = ((Page) list).getTotal();

        JSONObject result = new JSONObject();
        result.put("data", list);
        result.put("ret", 1);
        result.put("totalCount", size);
        return result.toJSONString();
    }

    /**
     * 导出
     *
     * @param map
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/export", method = {RequestMethod.GET, RequestMethod.POST})
    public String export(HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String beginDt = BlankUtils.checkNull(request, "beginDt");
        beginDt = beginDt.replace("-", "");
        String endDt = BlankUtils.checkNull(request, "endDt");
        endDt = endDt.replace("-", "");
        String pid = BlankUtils.checkNull(request, "pid");
        String appid = BlankUtils.checkNull(request, "appid");
        String group_pid = BlankUtils.checkNull(request, "group_pid");
        String type = BlankUtils.checkNull(request, "type");
        String channel = BlankUtils.checkNull(request, "channel");

        String resp = "";
        if (!BlankUtils.checkBlank(type)) {
            for (int i = 1; i <= 30; i++) {
                resp += "CONCAT(TRUNCATE(avg(keep" + i + ")*100, 0),'%') keep" + i + ",";

                String kk = "";
                for (int p = 1; p <= i; p++) {
                    kk += "+avg(keep" + p + ")";
                }

                resp += "TRUNCATE(1" + kk + ", 2) keepnum" + i + ",";
            }
        } else {
            int[] mm = {1, 2, 3, 4, 5, 6, 7, 14, 30};
            for (int i = 0; i < mm.length; i++) {
                resp += "CONCAT(TRUNCATE(avg(keep" + mm[i] + ")*100, 0),'%') keep" + mm[i] + ",";

                String kk = "";
                for (int p = 1; p <= mm[i]; p++) {
                    kk += "+avg(keep" + p + ")";
                }

                resp += "TRUNCATE(1" + kk + ", 2) keepnum" + mm[i] + ",";
            }
        }

        String where = "";
        if (!BlankUtils.checkBlank(appid))
            where += " and product_id in (" + appid+") ";
        if (!BlankUtils.checkBlank(pid))
            where += " and projectid = " + pid;
        if (!BlankUtils.checkBlank(channel)) {
            where += " and wf.channelTag = '" + channel+"'";
        }

        String sql = "select wc.cha_sub_launch channel,mmdate dt,product_id appid,b.app_name appname,projectid pid," + resp + "SUM(usernum) usernum" +
                " from product_keep_num_total a left join app_info b on a.product_id = b.id" +
                " left join  dnwx_client.wbgui_formconfig wf ON wf.pjId = a.projectid LEFT JOIN dn_channel_info wc ON wf.channelTag = wc.cha_id " +
                " where mmdate BETWEEN '" + beginDt + "' AND '" + endDt + "' " + where +
                " group by mmdate,product_id" + ("ok".equals(group_pid) ? ",projectid" : "") +
                " order by mmdate desc,usernum desc";

        // 数据内容
        List<Map<String, Object>> list = adService.queryListMap(sql);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;
        if (BlankUtils.checkBlank(type)) {
            for (Map<String, Object> listmap : list) {
                int[] mm = {1, 2, 3, 4, 5, 6, 7, 14, 30};
                headerMap.put("dt", "日期");
                headerMap.put("pid", "pid");
                headerMap.put("appid", "appid");
                headerMap.put("appname", "应用名称");
                headerMap.put("channel", "子渠道");
                headerMap.put("usernum", "渠道新增");
                for (int i = 0; i < mm.length; i++) {
                    headerMap.put("keep" + mm[i], mm[i] + "日留存");
                }

                contentMap = new LinkedHashMap<String, Object>();

                contentMap.put("dt", listmap.get("dt"));
                contentMap.put("pid", listmap.get("pid"));
                contentMap.put("appid", listmap.get("appid"));
                contentMap.put("appname", listmap.get("appname"));
                contentMap.put("channel", listmap.get("channel"));
                contentMap.put("usernum", listmap.get("usernum"));

                for (int i = 0; i < mm.length; i++) {
                    contentMap.put("keep" + mm[i], listmap.get("keep" + mm[i]) + "/" + listmap.get("keepnum" + mm[i]));
                }

                contentList.add(contentMap);
            }
        } else {
            for (Map<String, Object> listmap : list) {
                headerMap.put("dt", "日期");
                headerMap.put("pid", "pid");
                headerMap.put("appid", "appid");
                headerMap.put("channel", "子渠道");
                headerMap.put("usernum", "渠道新增");
                for (int i = 1; i <= 30; i++) {
                    headerMap.put("keep" + i, i + "日留存");
                }

                contentMap = new LinkedHashMap<String, Object>();

                contentMap.put("dt", listmap.get("dt"));
                contentMap.put("pid", listmap.get("pid"));
                contentMap.put("appid", listmap.get("appid"));
                contentMap.put("channel", listmap.get("channel"));
                contentMap.put("usernum", listmap.get("usernum"));

                for (int i = 1; i <= 30; i++) {
                    contentMap.put("keep" + i, listmap.get("keep" + i) + "/" + listmap.get("keepnum" + i));
                }

                contentList.add(contentMap);
            }

        }
        String fileName = "留存汇总数据_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
                request, response);
        return null;
    }

}
