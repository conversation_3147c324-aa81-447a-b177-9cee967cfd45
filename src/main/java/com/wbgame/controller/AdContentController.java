package com.wbgame.controller;

import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.UmengChannelTotalVo;
import com.wbgame.pojo.custom.AdvFeeVo;
import com.wbgame.pojo.custom.GDTWorkReportVo;
import com.wbgame.pojo.custom.WarmHomeMailVo;
import com.wbgame.service.AdService;
import com.wbgame.service.AdmsgService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;

import jxl.Sheet;
import jxl.Workbook;


/**
 * apk广告接口相关
 * <AUTHOR>
 */
@Controller
public class AdContentController {
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private AdService adService;
	@Autowired
	private AdmsgService admsgService;

	@CrossOrigin
	@RequestMapping(value="/ad/getCityListTab", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String getCityListTab(HttpServletRequest request,HttpServletResponse response){
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		
		String province = "select id as `value`,`name` as label from np_province";
		String city = "select id as `value`,`name` as label,provice_id from np_city";
		List<Map<String, Object>> pList = adService.queryListMap(province);
		List<Map<String, Object>> cList = adService.queryListMap(city);
		for (Map<String, Object> pro : pList) {
			
			List<Map<String, Object>> children = new ArrayList<Map<String, Object>>();
			for (Map<String, Object> ct : cList) {
				if(pro.get("value").toString().equals(ct.get("provice_id").toString())){
					children.add(ct);
				}
			}
			pro.put("children", children);
		}
		
		JSONObject result = new JSONObject();
		try {
			result.put("ret", 1);
			result.put("data", pList);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("ret", 0);
			result.put("msg", "错误消息："+e.getMessage());
		}
		return result.toJSONString();
	}

	@CrossOrigin
	@RequestMapping(value="/ad/selectGoogleFeishuTotal", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String selectGoogleFeishuTotal(GDTWorkReportVo gdt,HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String pid = request.getParameter("pid");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(start_date) 
					|| BlankUtils.sqlValidate(end_date)
					|| BlankUtils.sqlValidate(pid)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
			String yesterday = DateTime.now().minusDays(1).toString("yyyyMMdd");
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = yesterday;
				end_date = yesterday;
			}
						
			String sql = "select * from ad_google_feishu_total"
					+ " where tdate BETWEEN '"+start_date+"' AND '"+end_date+"'";
			if(!BlankUtils.checkBlank(pid))
				sql = sql + " and pid = '"+pid+"'";
			sql = sql + " order by tdate desc,act_num desc";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adService.queryListMap(sql);
			for (Map<String, Object> map : list) {
				map.put("tdate", map.get("tdate")+"");
			}
			long size = ((Page) list).getTotal();
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}
	@CrossOrigin
	@RequestMapping(value="/ad/exportGoogleFeishuTotal", method = RequestMethod.POST)
	public void exportGoogleFeishuTotal(HttpServletRequest request,HttpServletResponse response) {
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String pid = request.getParameter("pid");
		
		List<Map<String, Object>> contentList = null;
		try {
			if(BlankUtils.sqlValidate(start_date) 
					|| BlankUtils.sqlValidate(end_date)
					|| BlankUtils.sqlValidate(pid)){
				return ;
			}
			String yesterday = DateTime.now().minusDays(1).toString("yyyyMMdd");
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = yesterday;
				end_date = yesterday;
			}
						
			String sql = "select * from ad_google_feishu_total"
					+ " where tdate BETWEEN '"+start_date+"' AND '"+end_date+"'";
			if(!BlankUtils.checkBlank(pid))
				sql = sql + " and pid = '"+pid+"'";
			sql = sql + " order by tdate desc,act_num desc";
			
			contentList = adService.queryListMap(sql);
			contentList.forEach(map -> {
				map.put("tdate", map.get("tdate")+"");
			});
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		//数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		headerMap.put("tdate","日期");
		headerMap.put("pid","应用ID");
		headerMap.put("act_num","活跃人数");
		headerMap.put("add_num","新增人数");
		headerMap.put("msg_count","信息流(次数)");
		headerMap.put("banner_count","banner(次数)");
		headerMap.put("plaque_count","插屏(次数)");
		headerMap.put("splash_count","开屏(次数)");
		headerMap.put("video_count","视频广告(次数)");
		
		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		
		String fileName = "谷歌投放运营数据_"+DateTime.now().toString("yyyyMMddHHmmss")+".xls";
		JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response); 
	}
	
	// 猫游投放运营数据
	@CrossOrigin
	@RequestMapping(value="/ad/selectMaoyouTotal", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String selectMaoyouTotal(GDTWorkReportVo gdt,HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String pid = request.getParameter("pid");
		String channel = request.getParameter("channel");
		
		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
				
		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;
		
		JSONObject result = new JSONObject();
		try {
			// 筛除sql敏感字符注入
			if(BlankUtils.sqlValidate(start_date) 
					|| BlankUtils.sqlValidate(end_date)
					|| BlankUtils.sqlValidate(channel)
					|| BlankUtils.sqlValidate(pid)){
				return "{\"ret\":0,\"msg\":\"param is error!\"}";
			}
			String yesterday = DateTime.now().minusDays(1).toString("yyyyMMdd");
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = yesterday;
				end_date = yesterday;
			}
						
			String sql = "select *,concat(truncate(keep2/add_num*100,2),'%') as keep2_rate, "
						+"concat(truncate(keep3/add_num*100,2),'%') as keep3_rate,"
						+"concat(truncate(keep4/add_num*100,2),'%') as keep4_rate,"
						+"concat(truncate(keep7/add_num*100,2),'%') as keep7_rate"
					+ " from ad_maoyou_total"
					+ " where tdate BETWEEN '"+start_date+"' AND '"+end_date+"'";
			if(!BlankUtils.checkBlank(pid))
				sql += " and pid = '"+pid+"'";
			if(!BlankUtils.checkBlank(channel))
				sql += " and channel = '"+channel+"'";
			sql += " order by tdate desc,act_num desc";
			
			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<Map<String, Object>> list = adService.queryListMap(sql);
			for (Map<String, Object> map : list) {
				map.put("tdate", map.get("tdate")+"");
			}
			long size = ((Page) list).getTotal();
			redisTemplate.opsForValue().set("maoyouTotalExcelList", list, 20, TimeUnit.MINUTES);
			
			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}
	@CrossOrigin
	@RequestMapping(value="/ad/exportMaoyouTotal", method = RequestMethod.POST)
	public void exportMaoyouTotal(HttpServletRequest request,HttpServletResponse response) {
		
		List<Map<String, Object>> contentList = (List<Map<String, Object>>)redisTemplate.opsForValue().get("maoyouTotalExcelList");
		
		//数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		headerMap.put("tdate","日期");
		headerMap.put("pid","应用ID");
		headerMap.put("channel","渠道");
		headerMap.put("act_num","活跃人数");
		headerMap.put("add_num","新增人数");
		headerMap.put("video_count","视频广告(次数)");
		headerMap.put("keep2","次留数");
		headerMap.put("keep3","三留数");
		headerMap.put("keep4","四留数");
		headerMap.put("keep7","七留数");
		headerMap.put("keep2_rate","次留率");
		headerMap.put("keep3_rate","三留率");
		headerMap.put("keep4_rate","四留率");
		headerMap.put("keep7_rate","七留率");
		
		Map<String, String> inMap = new LinkedHashMap<String, String>();  
		inMap.put("strDt", "yyyy-MM-dd");
		
		String fileName = "猫游投放运营数据_"+DateTime.now().toString("yyyyMMddHHmmss")+".xls";
		JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response); 
	}

	@CrossOrigin
	@RequestMapping(value="/ad/selectAdvChannelFeeTotalNew", method={RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody String selectAdvChannelFeeTotalNew(HttpServletRequest request,HttpServletResponse response) throws IOException {
		String start = BlankUtils.checkNull(request, "start");
		String limit = BlankUtils.checkNull(request, "limit");
		String start_date = request.getParameter("start_date");
		String end_date = request.getParameter("end_date");
		String appid = request.getParameter("appid");
		String push_cha = request.getParameter("push_cha");
		String pri_id = request.getParameter("pri_id");
		String cha_type = request.getParameter("cha_type");
		String os_type = request.getParameter("os_type");

		String push_cha_group = request.getParameter("push_cha_group");
		String pri_id_group = request.getParameter("pri_id_group");
		String os_type_group = request.getParameter("os_type_group");
		String date_group = request.getParameter("date_group");
		String appid_group = request.getParameter("appid_group");

		// token验证
		String token = request.getParameter("token");
		if(BlankUtils.checkBlank(token)
				|| (!redisTemplate.hasKey(token) && !token.equals("wbtokenyn76mlju0ckdplvc7b2s6ol8")))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);

		CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);

		// pageStart 和 limit设置值
		int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
		int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
		int pageNo = (pageStart / pageSize) + 1;

		JSONObject result = new JSONObject();
		try {
			String yesterday = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
			if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
				start_date = yesterday;
				end_date = yesterday;
			}

			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("start_date", start_date);
			paramMap.put("end_date", end_date);
			if(appid != null && appid.split(",").length > 1) {
				paramMap.put("appids", appid);
			}else {
				paramMap.put("appid", appid);
			}

			paramMap.put("push_cha", push_cha);
			paramMap.put("pri_id", pri_id);
			paramMap.put("cha_type", cha_type);
			paramMap.put("os_type", os_type);
			paramMap.put("date_group", date_group);

			List<String> groupList = new ArrayList<String>();
			if(!BlankUtils.checkBlank(push_cha_group))
				groupList.add("push_cha");
			if(!BlankUtils.checkBlank(pri_id_group))
				groupList.add("pri_id");
			if(!BlankUtils.checkBlank(os_type_group))
				groupList.add("os_type");
			if(!BlankUtils.checkBlank(appid_group))
				groupList.add("appid");

			String group = "os_type";
			String group_match = " bb.os_type = aa.os_type";
			if(groupList.size() > 0) {
				group = String.join(",", groupList);
				group_match = "";
				for (String str : groupList) {
					group_match += " bb."+str+" = aa."+str+" and";
				}
				group_match = group_match.substring(0, group_match.length()-3);
			}
			paramMap.put("group", group);
			paramMap.put("group_match", group_match);

			PageHelper.startPage(pageNo, pageSize); // 进行分页
			List<AdvFeeVo> list = adService.selectAdvChannelFeeTotalNew(paramMap);
			long size = ((Page) list).getTotal();

			// 异步加载excel导出所需的无分页数据
			if(!token.equals("wbtokenyn76mlju0ckdplvc7b2s6ol8")) {
				new Thread(new Runnable() {
					@Override
					public void run() {
						List<AdvFeeVo> adList = adService.selectAdvChannelFeeTotalNew(paramMap);
						redisTemplate.opsForValue().set(
								cuser.getLogin_name()+"_advChannelFeeTotalNewExcelList", adList, 20, TimeUnit.MINUTES);
					}
				}).start();
			}

			result.put("ret", 1);
			result.put("data", list);
			result.put("totalCount", size);
		} catch (Exception e) {
			e.printStackTrace();

			result.put("ret", 0);
			result.put("msg", "错误信息: "+e.getMessage());
		}
		return result.toJSONString();
	}
	
}
