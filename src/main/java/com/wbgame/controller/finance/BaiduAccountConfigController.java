package com.wbgame.controller.finance;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.aop.LoginCheck;
import com.wbgame.aop.PageLimit;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.finance.WaibaoUserVo;
import com.wbgame.service.finance.FinanceService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExcelUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: 百度账号产品配置
 * @author: huangmb
 * @date: 2021/07/29
 **/
@CrossOrigin
@RestController
@RequestMapping(value = "/finance/baiduAccountConfig")
@LoginCheck
public class BaiduAccountConfigController {

    @Autowired
    private FinanceService financeService;

    @Autowired
    private RedisTemplate redisTemplate;

    @RequestMapping(value = "/list")
    @PageLimit
    public String list(HttpServletRequest request, HttpServletResponse response){
        Map param = new HashMap<>();
        param.put("account", request.getParameter("account"));
        param.put("appid", request.getParameter("appid"));
        param.put("bdappid", request.getParameter("bdappid"));
        // 进行分页
        List<Map<String,Object>> list = financeService.selectBaiduAccountConfigs(param);
        long size = ((Page) list).getTotal();
        Map result = new HashMap();
        result.put("list", list);
        result.put("totalCount", size);
        return ReturnJson.success(result);
    }

    @RequestMapping(value = "/export")
    public void export(HttpServletRequest request, HttpServletResponse response){
        Map param = new HashMap<>();
        param.put("account", request.getParameter("account"));
        param.put("appid", request.getParameter("appid"));
        param.put("bdappid", request.getParameter("bdappid"));

        List<Map<String,Object>> list = financeService.selectBaiduAccountConfigs(param);
        Map headerMap = new LinkedHashMap();
        headerMap.put("account", "登录账号");
        headerMap.put("appname", "产品名称");
        headerMap.put("appid", "产品id");
        headerMap.put("bdappid", "百度产品id");
        headerMap.put("remark","备注");
        headerMap.put("create_owner", "创建人");
        headerMap.put("update_owner", "更新人");
        headerMap.put("createStr", "创建时间");
        headerMap.put("updateStr", "更新时间");
        ExportExcelUtil.export(response,list,headerMap,"百度账号产品配置_" + DateTime.now().toString("yyyyMMdd")+ ".xls");

    }

    @RequestMapping(value = "/add")
    public String add(HttpServletRequest request, HttpServletResponse response){
        CurrUserVo currUserVo = (CurrUserVo)request.getAttribute("LoginUser");
        String username = currUserVo.getLogin_name();
        if (BlankUtils.checkBlank(request.getParameter("account")) || BlankUtils.checkBlank(request.getParameter("appid"))) {
            return ReturnJson.error(Constants.ParamError);
        }
        Map param = new HashMap<>();
        param.put("account", request.getParameter("account"));
        param.put("appid",request.getParameter("appid"));
        param.put("bdappid",request.getParameter("bdappid"));
        param.put("remark",request.getParameter("remark"));
        param.put("create_owner",username);
        param.put("update_owner",username);
        List<Map<String, Object>> list = financeService.selectBaiduAccountConfigs(param);
        if (list.size() != 0) {
            return ReturnJson.toErrorJson("该配置已存在");
        }
        financeService.insertBaiduAccountConfig(param);
        return ReturnJson.success();
    }

    @RequestMapping(value = "/update")
    public String update(HttpServletRequest request, HttpServletResponse response){
        CurrUserVo currUserVo = (CurrUserVo)request.getAttribute("LoginUser");
        String username = currUserVo.getLogin_name();
        if (BlankUtils.checkBlank(request.getParameter("account")) || BlankUtils.checkBlank(request.getParameter("appid"))) {
            return ReturnJson.error(Constants.ParamError);
        }
        Map param = new HashMap<>();
        param.put("account", request.getParameter("account"));
        param.put("appid",request.getParameter("appid"));
        param.put("bdappid",request.getParameter("bdappid"));
        param.put("remark",request.getParameter("remark"));
        param.put("update_owner",username);
        financeService.updateBaiduAccountConfig(param);
        return ReturnJson.success();
    }

    @RequestMapping(value = "/delete")
    public String delete(HttpServletRequest request, HttpServletResponse response){
        if (BlankUtils.checkBlank(request.getParameter("account")) || BlankUtils.checkBlank(request.getParameter("appid"))) {
            return ReturnJson.error(Constants.ParamError);
        }
        Map param = new HashMap<>();
        param.put("account", request.getParameter("account"));
        param.put("appid",request.getParameter("appid"));
        financeService.deleteBaiduAccountConfig(param);
        return ReturnJson.success();
    }


    @RequestMapping(value="/batchImport", method={ RequestMethod.GET,RequestMethod.POST})
    public  String batchImport(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (null == file || file.isEmpty() || !(file.getOriginalFilename().endsWith(".xls") || file.getOriginalFilename().endsWith(".xlsx"))) {
            Asserts.fail("上传文件有误，需要excel文件!");
        }
        CurrUserVo currUserVo = (CurrUserVo)request.getAttribute("LoginUser");
        String username = currUserVo.getLogin_name();
        List<Map<String,Object>> list = new ArrayList<>();
        List<ArrayList<String>> arrayLists = ExcelUtils.readExcel(file);
        for (int i = 0; i < arrayLists.size(); i++) {
            if (i == 0) {
                //第一行为标题，不能为空
                for (int k = 0; k < 2; k++) {
                    if (BlankUtils.checkBlank(arrayLists.get(i).get(k))) {
                        Asserts.fail("第1行标题不能出现空的情况");
                    }
                }
            } else {
                //第二行为内容
                Map<String,Object> f = new HashMap<>();
                if (BlankUtils.checkBlank(arrayLists.get(i).get(0)) || !BlankUtils.isNumeric(arrayLists.get(i).get(1))
                ) {
                    return ReturnJson.toErrorJson("第"+(i+1)+"行:导入数据账号为空或产品id不为数字");
                }
                f.put("account",arrayLists.get(i).get(0));
                f.put("appid",arrayLists.get(i).get(1));
                f.put("bdappid",arrayLists.get(i).get(2));
                f.put("create_owner",username);
                f.put("update_owner",username);
                f.put("remark",arrayLists.get(i).get(3));
                list.add(f);
            }
        }
        financeService.batchInsertBaiduAccountConfigImport(list);
        return ReturnJson.success("导入成功");
    }

}
