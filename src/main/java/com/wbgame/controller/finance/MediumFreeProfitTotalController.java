package com.wbgame.controller.finance;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.pojo.finance.MediumFreeImportVo;
import com.wbgame.service.finance.MediumFreeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import jxl.Sheet;
import jxl.Workbook;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * @author: caow
 * @date: 2023/05/24
 * @description: 中休产品毛利汇总
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/finance")
public class MediumFreeProfitTotalController {

    @Autowired
    private MediumFreeService mediumFreeService;
    @Autowired
    private YyhzMapper yyhzMapper;


    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/mediumFreeProfitTotal/list", method={RequestMethod.GET, RequestMethod.POST})
    public String mediumFreeProfit(HttpServletRequest request,HttpServletResponse response) throws IOException {

        String[] args = {"sdate","edate","bus_port","app_category","appid","date_group","order_str"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        JSONObject result = new JSONObject();
        try {

            /* 日期维度的分组解析 */
            if("day".equals(paramMap.get("date_group"))) {
                paramMap.put("group", "DATE_FORMAT(tdate,'%Y-%m-%d')");
            }else if("month".equals(paramMap.get("date_group"))) {
                paramMap.put("group", "DATE_FORMAT(tdate,'%Y-%m')");
            }else if("year".equals(paramMap.get("date_group"))) {
                paramMap.put("group", "DATE_FORMAT(tdate,'%Y')");
            }else{
                paramMap.put("group", null);
            }

            PageHelper.startPage(paramMap);
            List<Map<String, Object>> list = mediumFreeService.selectMediumFreeProfitTotalList(paramMap);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
            result.put("total", mediumFreeService.selectMediumFreeProfitTotalListSum(paramMap));
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }


    /**
     * 导出
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mediumFreeProfitTotal/export", method={RequestMethod.GET, RequestMethod.POST})
    public void mediumFreeProfitTotal_export(HttpServletRequest request,HttpServletResponse response) {

        String[] args = {"sdate","edate","bus_port","app_category","appid","date_group","order_str"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        /* 日期维度的分组解析 */
        if("day".equals(paramMap.get("date_group"))) {
            paramMap.put("group", "DATE_FORMAT(tdate,'%Y-%m-%d')");
        }else if("month".equals(paramMap.get("date_group"))) {
            paramMap.put("group", "DATE_FORMAT(tdate,'%Y-%m')");
        }else if("year".equals(paramMap.get("date_group"))) {
            paramMap.put("group", "DATE_FORMAT(tdate,'%Y')");
        }else{
            paramMap.put("group", null);
        }

        List<Map<String, Object>> contentList = mediumFreeService.selectMediumFreeProfitTotalList(paramMap);

        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "中休产品毛利汇总_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);
    }

}
