package com.wbgame.controller.finance;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.Asserts;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.finance.IncentiveIntroductionDTO;
import com.wbgame.pojo.finance.IncentiveIntroductionVO;
import com.wbgame.service.finance.IncentiveIntroductionService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import com.wbgame.utils.excel.POIExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.Default;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2022/8/15
 * @class: IncentiveIntroductionController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/face")
@Api(tags = "激励导入")
public class IncentiveIntroductionController {

    private IncentiveIntroductionService introductionService;

    @Autowired

    public void setIntroductionService(IncentiveIntroductionService introductionService) {
        this.introductionService = introductionService;
    }

    @ApiOperation(value = "导出", notes = "导出")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "value", value = "自定义字段", example = "media,媒体;secondAgent,二级部门", dataType = "String"),
//            @ApiImplicitParam(name = "export_file_name", value = "文件名", example = "业务模式.xlsx", dataType = "String")
//    })
    @PostMapping(value = "/exportIncentive")
    public void export(HttpServletResponse response, @Validated(Default.class) IncentiveIntroductionDTO dto) {

        List<IncentiveIntroductionVO> list = introductionService.export(dto);

        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = dto.getValue().split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = dto.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
        ExportExcelUtil.exportXLSX2(response, list, head, fileName);

    }


    @ApiOperation(value = "查询", notes = "查询")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "value", value = "自定义字段", example = "media,媒体;secondAgent,二级部门", dataType = "String"),
//            @ApiImplicitParam(name = "export_file_name", value = "文件名", example = "业务模式.xlsx", dataType = "String")
//    })
    @PostMapping(value = "/selectIncentiveIncomeByCondition")
    public Result<PageResult<IncentiveIntroductionVO>> selectIncentiveIncomeByCondition(@ApiNeed({
            "start_date", "end_date", "start", "limit", "appId", "modelId", "order_str"
    }) @Validated(QueryGroup.class) IncentiveIntroductionDTO dto) {

        return introductionService.selectIncentiveIncomeByCondition(dto);

    }

    @ApiOperation(value = "导入", notes = "导入")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "value", value = "自定义字段", example = "media,媒体;secondAgent,二级部门", dataType = "String"),
//            @ApiImplicitParam(name = "export_file_name", value = "文件名", example = "业务模式.xlsx", dataType = "String")
//    })
    @PostMapping(value = "/incentiveImport")
    public Result<Integer> incentiveImport(@RequestParam("file") MultipartFile file) {

        List<String[]> list;
        try {
            list = POIExcelUtils.readExcel(file);
        } catch (IOException e) {
            return ResultUtils.failure("读取文件错误!" + e.getMessage());
        }
        String s = list.get(0)[1];
        return introductionService.incentiveImport(list);

    }


    @ApiOperation(value = "新增", notes = "新增")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "value", value = "自定义字段", example = "media,媒体;secondAgent,二级部门", dataType = "String"),
//            @ApiImplicitParam(name = "export_file_name", value = "文件名", example = "业务模式.xlsx", dataType = "String")
//    })
    @PostMapping(value = "/insertIncentiveIntroduction")
    public Result<Long> insertIncentiveIntroduction(@ApiNeed({
            "appId", "spend", "day", "media", "modelId"
    }) IncentiveIntroductionVO vo) {

        return introductionService.insertIncentiveIntroduction(vo);

    }

    @ApiOperation(value = "修改", notes = "修改")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "value", value = "自定义字段", example = "media,媒体;secondAgent,二级部门", dataType = "String"),
//            @ApiImplicitParam(name = "export_file_name", value = "文件名", example = "业务模式.xlsx", dataType = "String")
//    })
    @PostMapping(value = "/updateIncentiveIntroduction")
    public Result<Integer> updateIncentiveIntroduction(@ApiNeed({
            "spend", "id"
    }) IncentiveIntroductionVO vo) {

        return ResultUtils.success(introductionService.updateIncentiveIntroduction(vo));

    }

    @ApiOperation(value = "删除", notes = "删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "value", value = "自定义字段", example = "media,媒体;secondAgent,二级部门", dataType = "String"),
//            @ApiImplicitParam(name = "export_file_name", value = "文件名", example = "业务模式.xlsx", dataType = "String")
//    })
    @PostMapping(value = "/deleteIncentive")
    public Result<Integer> deleteIncentive(@RequestParam("idList") List<Integer> idList) {

        return introductionService.deleteIncentive(idList);

    }

}
