package com.wbgame.controller.finance;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.Asserts;
import com.wbgame.common.UpdateGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.finance.DnRebateRateConfigDTO;
import com.wbgame.pojo.finance.DnRebateRateConfigKey;
import com.wbgame.pojo.finance.DnRebateRateConfigVO;
import com.wbgame.pojo.finance.DnYearlyRateConfigVo;
import com.wbgame.service.finance.IDnRebateRateConfigService;
import com.wbgame.service.finance.IDnYearlyRateConfigService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.Default;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: caow
 * @createDate: 2023/05/23
 * @class: DnYearlyRateConfigController
 * @description:年框返点率配置
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/fin")
@Api(tags = "年框返点率配置")
public class DnYearlyRateConfigController {


    @Autowired
    private IDnYearlyRateConfigService rateConfigService;

    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/deleteDnYearlyRateConfigById")
    public Result<Integer> deleteDnYearlyRateConfigById(@RequestBody List<DnYearlyRateConfigVo> keyList) {

        return rateConfigService.deleteDnYearlyRateConfigById(keyList);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/insertDnYearlyRateConfig")
    public Result<Integer> insertDnYearlyRateConfig(HttpServletRequest request,
                                                    @ApiNeed({"rebate_rate","media",
                                                            "app_category"})
                                                    @Validated(Default.class) DnYearlyRateConfigVo record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        if(loginUser != null) {
            record.setCreate_user(loginUser.getLogin_name());
        }
        return rateConfigService.insertDnYearlyRateConfig(record);
    }

    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping("/updateDnYearlyRateConfig")
    public Result<Integer> updateDnYearlyRateConfig(HttpServletRequest request,
                                                         @ApiNeed({"rebate_rate", "media",
                                                                 "app_category"})
                                                         @Validated(UpdateGroup.class) DnYearlyRateConfigVo record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        if(loginUser != null) {
            record.setUpdate_user(loginUser.getLogin_name());
        }
        return rateConfigService.updateDnYearlyRateConfig(record);
    }

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping("/selectDnYearlyRateConfig")
    public Result<PageResult<DnYearlyRateConfigVo>>
    selectDnYearlyRateConfig(@RequestParam("start") Integer start,
                                @RequestParam("limit") Integer limit,
                                @RequestParam(value = "order_str", defaultValue = "create_time desc", required = false) String order_str,
                                @RequestParam(value = "app_category", required = false) List<String> app_category,
                                @RequestParam(value = "media", required = false) List<String> media) {

        return rateConfigService.selectDnYearlyRateConfig(start, limit, order_str, app_category, media);
    }


    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "value", value = "自定义字段", example = "app_category,应用分类;media,媒体", dataType = "String"),
            @ApiImplicitParam(name = "export_file_name", value = "文件名", example = "年框返点率配置.xlsx", dataType = "String")
    })
    @PostMapping(value = "/exportDnYearlyRateConfig")
    public void export(HttpServletResponse response,
                       @RequestParam(name = "value", defaultValue = "app_category,应用分类;media,媒体") String value,
                       @RequestParam(name = "export_file_name", defaultValue = "年框返点率配置.xlsx") String export_file_name,
                       @RequestParam(value = "order_str", defaultValue = "create_time desc", required = false) String order_str,
                       @RequestParam(value = "app_category", required = false) List<String> app_category,
                       @RequestParam(value = "media", required = false) List<String> media) {

        List<DnYearlyRateConfigVo> list = rateConfigService.exportDnYearlyRateConfig(order_str, app_category, media);

        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = export_file_name + "_" + DateTime.now().toString("yyyyMMdd") + ".xls";
        ExportExcelUtil.export2(response, list, head, fileName);

    }

}
