package com.wbgame.controller.finance;

import com.mysql.jdbc.StringUtils;
import com.wbgame.mapper.master.finance.master.FinanceMapper;
import com.wbgame.pojo.finance.FinanceOtherCost;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.service.finance.FinanceService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.jettison.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jxl.Sheet;
import jxl.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.alicp.jetcache.Cache.logger;

@CrossOrigin
@RestController
@Api(tags = "支出月度手工维护表")
@RequestMapping(value = "/fin")
public class FinanceOtherCostController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private FinanceService financeService;

    @Autowired
    private FinanceMapper financeMapper;
    /**
     * 财务系统-支出月度手工维护表 查询
     *
     * @return
     */
    @PostMapping(value = "/fd/getFinanceOtherCostList")
    public InfoResult getFinanceOtherCostList(FinanceOtherCost financeOtherCost, HttpServletRequest request) {
        InfoResult infoResult = new InfoResult();
        try {
            // token验证
            String token = request.getParameter("token");
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            } else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            infoResult = financeService.getFinanceOtherCostList(financeOtherCost);

        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getFinanceOtherCostList: ", e);
        }
        return infoResult;
    }

    /**
     * 财务系统-支出月度手工维护表 导出
     *
     * @return
     */
    @ApiOperation(value = "支出月度手工维护表导出")
    @PostMapping(value = "/fd/exportFinanceOtherCost")
    public void financeOtherCostExport(FinanceOtherCost param,HttpServletResponse response,HttpServletRequest request) {
        try {
            // token验证
            String token = request.getParameter("token");
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
                return;
            } else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            financeService.financeOtherCostExport(param,response);

        } catch (Exception e) {
            logger.error("getSpendReportExport: ", e);
        }

    }


    /**
     * 财务系统-支出月度手工维护表 导入
     *
     * @return
     */
    @PostMapping(value = "/fd/importFinanceOtherCost")
    public InfoResult importFinanceOtherCost(@RequestParam(value = "file") MultipartFile file) throws IOException {
        InfoResult infoResult = new InfoResult();
        logger.info("财务系统-支出月度手工维护表导入----------开始");

        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {

                Map<String, String> businessMap = new HashMap<>();
                Map<String, String> appCategoryMap = new HashMap<>();
                Map<String,String> appNameMap = new HashMap<>();

                List<Map<String, String>> businessList = financeMapper.getBusiness();
                List<Map<String, String>> appCategoryList = financeMapper.getAppCategory();
                List<Map<String,String>> appNameList = financeMapper.getAppName();

                if (!CollectionUtils.isEmpty(businessList)) {
                    businessMap = businessList.stream().collect(Collectors.toMap(map -> StringUtil.getString(map.get("bus_model")), map -> StringUtil.getString(map.get("id")), (k1, k2) -> k2));
                }
                if (!CollectionUtils.isEmpty(appCategoryList)) {
                    appCategoryMap = appCategoryList.stream().collect(Collectors.toMap(map -> StringUtil.getString(map.get("name")), map -> StringUtil.getString(map.get("id")), (k1, k2) -> k2));
                }

                if(!CollectionUtils.isEmpty(appNameList)){
                    appNameMap = appNameList.stream().collect(Collectors.toMap(map -> StringUtil.getString(map.get("app_name")),map -> StringUtil.getString(map.get("id")),(k1, k2)->k2));
                }

                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int row = sheet.getRows();
                // 从第2行开始，第1行为标题，第1列内容为空则不执行
                for (int r = 1; r < row; r++) {
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                        continue;
                    }

                    String[] vals = new String[9];
                    for (int c = 0; c < 9; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    //判断年度和月份是否为数字
                    for (int i = 0; i < 2; i++) {
                        if (!BlankUtils.isNumeric(vals[i])) {
                            infoResult.setRet(0);
                            infoResult.setMsg(vals[i] + "不是数字");
                            return infoResult;
                        }
                    }
                    //判断年度是否符合四位数
                    Integer y = Integer.parseInt(vals[0].trim());
                    if (y < 1 || y > 9999 || vals[0].trim().length() != 4) {
                        infoResult.setRet(0);
                        infoResult.setMsg(vals[0] + " 不符合正常年份");
                        return infoResult;
                    }
                    FinanceOtherCost gad = new FinanceOtherCost();
                    gad.setYear(vals[0]);
                    //防止传08 这样的数据 主键不唯一
                    String month = vals[1].trim();
                    Integer m = Integer.parseInt(month);
                    if (m < 1 || m > 12) {
                        infoResult.setRet(0);
                        infoResult.setMsg(vals[1] + " 不符合正常月份");
                        return infoResult;
                    }
                    gad.setMonth1(month);
                    if(month.length()==1){
                        month = "0"+ month;
                    }
                    gad.setMonth(month);

                    String start_date = vals[0]+"-"+month+"-01";
                    String end_time = vals[0]+"-"+month+"-31";
                    gad.setStart_date(start_date);
                    gad.setEnd_date(end_time);
                    gad.setPayType(vals[2].trim());
                    String osType = "";
                    if(!StringUtils.isNullOrEmpty(vals[3].trim())) {
                        switch (vals[3].trim()) {
                            case "安卓":
                                osType = "1";
                                break;
                            case "IOS":
                                osType = "2";
                                break;
                            case "Google":
                                osType = "3";
                                break;
                            case "小游戏":
                                osType = "4";
                                break;
                            default:
                                break;
                        }
                    }
                    gad.setOsType(osType);
                    gad.setBusiness(businessMap.get(vals[4].trim()));
                    gad.setMedia(vals[5].trim());
                    gad.setAppCategory(appCategoryMap.get(vals[6].trim()));
                    gad.setAppId(appNameMap.get(vals[7].trim()));
                    if (!BlankUtils.isNumeric(vals[8])) {
                        infoResult.setRet(0);
                        infoResult.setMsg(vals[8] + "不是数字");
                        return infoResult;
                    }
                    gad.setSpend(StringUtil.getDouble(vals[8].trim()));
                    List<FinanceOtherCost> newList = new ArrayList<>();
                    //按产品划分
                    if("预计年框".equals(vals[2].trim()) || "预计活动返货".equals(vals[2].trim()) || "赔付消耗".equals(vals[2].trim())){
                        newList = financeService.getWithoutAppReport(gad);
                    }
                    //按媒体划分
                    if("预计小游戏激励金".equals(vals[2].trim())){
                        newList = financeService.getWithoutMediaReport(gad);
                        //如果未找到该产品的消耗
                        if (newList.size() == 0) {
                            gad.setRebateSpend("0");
                            gad.setServiceSpend("0");
                            gad.setDate(gad.getYear()+"-"+gad.getMonth());
                            gad.setMonth(gad.getMonth1());
                            newList.add(gad);
                        }
                    }
                    //平均划分
//                    if("分成支出".equals(vals[2].trim()) || "红包支出".equals(vals[2].trim())){
                    if("红包支出".equals(vals[2].trim())){
                        newList = financeService.getSameReport(gad);
                        if(newList.size()>0) {
                            for (FinanceOtherCost financeOtherCost1 : newList) {
                                financeOtherCost1.setSpend(financeOtherCost1.getSpend() / newList.size());
                            }
                        }else{
                            gad.setDate(gad.getYear()+"-"+gad.getMonth());
                            gad.setMonth(gad.getMonth1());
                            gad.setMedia("未知");
                            gad.setBusiness("20");
                            newList.add(gad);
                        }
                    }
                    logger.info("类型：【"+vals[2].trim()+"】,共"+newList.size()+"条");
                    if(newList.size()>0) {
                        try {
                            financeService.insertFinanceOtherCost(newList);
                        }catch (Exception e){
                            logger.error("支出月度手工维护表 导入 第"+r+"行失败：原因："+e.getMessage());
                        }
                    }
                }
            } else {
                infoResult.setRet(0);
                infoResult.setMsg("上传文件有误，需要.xls文件!");
            }
        } catch (Exception e) {
            //上传异常
            e.printStackTrace();
            infoResult.setRet(0);
            infoResult.setMsg("导入失败");
            logger.info("财务系统-支出月度手工维护表导入----------异常结束");

        }
        infoResult.setRet(1);
        infoResult.setMsg("文件上传成功");
        logger.info("财务系统-支出月度手工维护表导入----------结束");

        return infoResult;
    }



}
