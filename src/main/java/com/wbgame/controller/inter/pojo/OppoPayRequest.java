package com.wbgame.controller.inter.pojo;

import java.io.Serializable;

public class OppoPayRequest implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String notifyId;
	private String partnerOrder;
	private String productName;
	private String productDesc;
	private Integer price;
	private Integer count;
	private String attach;
	private String sign;
	public String getNotifyId() {
		return notifyId == null?"":notifyId;
	}
	public void setNotifyId(String notifyId) {
		this.notifyId = notifyId;
	}
	public String getPartnerOrder() {
		return partnerOrder;
	}
	public void setPartnerOrder(String partnerOrder) {
		this.partnerOrder = partnerOrder;
	}
	public String getProductName() {
		return productName == null?"":productName.trim();
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
	public String getProductDesc() {
		return productDesc == null?"":productDesc.trim();
	}
	public void setProductDesc(String productDesc) {
		this.productDesc = productDesc;
	}
	public Integer getPrice() {
		return price;
	}
	public void setPrice(Integer price) {
		this.price = price;
	}
	public Integer getCount() {
		return count;
	}
	public void setCount(Integer count) {
		this.count = count;
	}
	public String getAttach() {
		return attach == null?"":attach;
	}
	public void setAttach(String attach) {
		this.attach = attach;
	}
	public String getSign() {
		return sign == null?"":sign;
	}
	public void setSign(String sign) {
		this.sign = sign;
	}
}
