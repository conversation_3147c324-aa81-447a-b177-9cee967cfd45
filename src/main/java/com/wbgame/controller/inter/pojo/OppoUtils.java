package com.wbgame.controller.inter.pojo;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.Random;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;

public class OppoUtils {
	
	public static final String OAUTH_CONSUMER_KEY = "oauthConsumerKey"; 
	public static final String OAUTH_APPKEY ="1kc4D4ok3pwK40oCSoW8GoKgg";
	public static final String OAUTH_APPSECRET ="Ea279eDf62d641D7629a42F45d7c7349";
	public static final String OAUTH_TOKEN = "oauthToken"; 
	public static final String OAUTH_SIGNATURE_METHOD = "oauthSignatureMethod"; 
	public static final String OAUTH_SIGNATURE = "oauthSignature"; 
	public static final String OAUTH_TIMESTAMP = "oauthTimestamp"; 
	public static final String OAUTH_NONCE = "oauthNonce"; 
	public static final String OAUTH_VERSION = "oauthVersion"; 
	public static final String CONST_SIGNATURE_METHOD = "HMAC-SHA1"; 
	public static final String CONST_OAUTH_VERSION = "1.0";
	
	/**
	 * 生成字符串
	 * @param token
	 * @param ssoid
	 * @return
	 */
	public static String getBaseStr(String token){
		 //系统当前时间
		 Date date = new Date();
		 String str = getStringRandom(10);
		 StringBuilder sb = new StringBuilder();     
		 try {      
				 sb.append(OAUTH_CONSUMER_KEY).
				 append("=").
				 append(URLEncoder.encode(OAUTH_APPKEY,"UTF-8")).
				 append("&").
				 append(OAUTH_TOKEN).
				 append("=").
				 append(URLEncoder.encode(token,"UTF-8")).         
				 append("&").
				 append(OAUTH_SIGNATURE_METHOD).         
				 append("=").
				 append(URLEncoder.encode(CONST_SIGNATURE_METHOD,"UTF-8")).         
				 append("&").
				 append(OAUTH_TIMESTAMP).
				 append("=").         
				 append(URLEncoder.encode(String.valueOf(date.getTime()),"UTF-8")).
				 append("&").         
				 append(OAUTH_NONCE).
				 append("=").
				 append(URLEncoder.encode(str,"UTF-8")).
				 append("&").
				 append(OAUTH_VERSION).append("=").
				 append(URLEncoder.encode(CONST_OAUTH_VERSION,"UTF-8")).         
				 append("&");  
			 } 
		 catch (Exception e1) {   // TODO Auto-generated catch block 
			   e1.printStackTrace(); 
			   }    
		 return sb.toString(); 
	}
	
	/**
	 * 签名
	 * @param baseStr
	 * @return
	 * @throws UnsupportedEncodingException 
	 */
	public static String getSign(String baseStr) throws UnsupportedEncodingException{
		 byte[] byteHMAC = null;  
		 try {   
			 Mac mac = Mac.getInstance("HmacSHA1");   
			 SecretKeySpec spec = null;   
			 String oauthSignatureKey =OAUTH_APPSECRET + "&";  
			 spec = new SecretKeySpec(oauthSignatureKey.getBytes(),"HmacSHA1");   
			 mac.init(spec);   byteHMAC = mac.doFinal(baseStr.getBytes());  
	     } 
		 catch (Exception e) {   
			 e.printStackTrace();
	     } 
		 return URLEncoder.encode(String.valueOf(base64Encode(byteHMAC)) ,"UTF-8"); 
	}
	
	public static String genSign(String secretKey,Map<String,String> params,String fuhao) {
			String[] sortedParams = new String[params.size()];
			params.keySet().toArray(sortedParams);
			Arrays.sort(sortedParams);
			StringBuilder sb = new StringBuilder();
			for (String key : sortedParams) {
				sb.append(String.format("&%s=%s", key, params.get(key)));
			}

			return MD5lower(sb.toString().substring(1)+fuhao+secretKey);
	}
	
	/**
	 * md5算法
	 * @param s
	 * @return
	 */
	public final static String MD5lower(String s) {
        char hexDigits[]={'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f'};       
        try {
            byte[] btInput = s.getBytes();
            // 获得MD5摘要算法的 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(btInput);
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
	
	/**
	 * base64编码
	 * @param data
	 * @return
	 */
	public static char[] base64Encode(byte[] data) {   
		final char[] alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".toCharArray();   
		char[] out = new char[((data.length + 2) / 3) * 4];   
		for (int i = 0, index = 0; i < data.length; i += 3, index += 4) {    
			boolean quad = false;    
			boolean trip = false;    
			int val = (0xFF & (int) data[i]);    val <<= 8;    
			if ((i + 1) < data.length){     
				val |= (0xFF & (int) data[i + 1]);     
				trip = true;    
			}    
			val <<= 8;    
			if ((i + 2) < data.length){     
				val |= (0xFF & (int) data[i + 2]);
	            quad = true;    
	        }    
			out[index + 3] = alphabet[(quad ? (val & 0x3F) : 64)];    
			val >>= 6;    
			out[index + 2] = alphabet[(trip ? (val & 0x3F) : 64)];    
			val >>= 6;    
			out[index + 1] = alphabet[val & 0x3F];    
			val >>= 6;    
			out[index + 0] = alphabet[val & 0x3F];   
			}   
		return out;  
		
	}  
	
	/**
	 * 生成随机数
	 * @param length
	 * @return
	 */
    public static String getStringRandom(int length) {  
          
        String val = "";  
        Random random = new Random();  
          
        //参数length，表示生成几位随机数  
        for(int i = 0; i < length; i++) {  
              
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";  
            //输出字母还是数字  
            if( "char".equalsIgnoreCase(charOrNum) ) {  
                //输出是大写字母还是小写字母  
                int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;  
                val += (char)(random.nextInt(26) + temp);  
            } else if( "num".equalsIgnoreCase(charOrNum) ) {  
                val += String.valueOf(random.nextInt(10));  
            }  
        }  
        return val.toUpperCase();  
    } 
    
    /**
     * 向指定 URL 发送POST方法的请求
     * 
     * @param url
     *            发送请求的 URL
     * @param param
     *            请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
     
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param.trim().toString());  
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！"+e);
            e.printStackTrace();
        }
        //使用finally块来关闭输出流、输入流
        finally{
            try{
                if(out!=null){
                    out.close();
                }
                if(in!=null){
                    in.close();
                }
            }
            catch(IOException ex){
                ex.printStackTrace();
            }
        }
        return result;
    }
    
    public static String getBaseString(OppoPayRequest ne) {
		StringBuilder sb = new StringBuilder();
		sb.append("notifyId=").append(ne.getNotifyId());
		sb.append("&partnerOrder=").append(ne.getPartnerOrder());
		sb.append("&productName=").append(ne.getProductName());
		sb.append("&productDesc=").append(ne.getProductDesc());
		sb.append("&price=").append(ne.getPrice());
		sb.append("&count=").append(ne.getCount());
		sb.append("&attach=").append(ne.getAttach());
		return sb.toString();
	}

	public static boolean doCheck(String content, String sign, String publicKey) throws Exception {
		KeyFactory keyFactory = KeyFactory.getInstance("RSA");
		byte[] encodedKey = Base64.decodeBase64(publicKey);
		PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));

		java.security.Signature signature = java.security.Signature.getInstance("SHA1WithRSA");

		signature.initVerify(pubKey);
		signature.update(content.getBytes("UTF-8"));
		boolean bverify = signature.verify(Base64.decodeBase64(sign));
		return bverify;
	}

}
