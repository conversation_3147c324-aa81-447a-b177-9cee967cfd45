package com.wbgame.controller.inter.pojo;

public class ConstValue {
	
	public static String HOST = "http://siapcn1.ipengtai.com:7002";
	public static String PRIVATE_KEY = "MIICXAIBAAKBgQCIYdT8GzRQkgtlgJcdjUN3CHwq1MwTfWwzOkyTcW/GfcQ9Au7fgPp3SQTBuQo3OO/53M4wUJXZ3kMBhh5FfoaUb0MBsh2XrCErK8D3puHHwFSM50Oq0ft+oju6OjVQ0hRW7aE1GLszt/4RzqmgSzZz1CF7OuxxgS3pxn5H85UnMwIDAQABAoGAC5sPrdh+zjBvadbUhT5FNHbzYg2zxCDTOVTHuXBYy7JeJbb/LuPh7I65KeceOG7O9RtWLIRfDCg+2X1ZTB6lQxITnJ4bXII/yg3BDH1vrb2b2FGjlnzS37Zj7GMZcLpSBowDG2oNV8L/y3JQLWzObvzwMK3BwQ55SnwDoyUlJHECQQDgQ0znmbuBLuNTybSjLj0PcRfCnciqG/G5P9m3oeN9t3IGCgPGortfS+oB8w3+EnD8Zd1XC8GSAZ4guUfns6+rAkEAm66/aFR1xsXVMwhHz96LaYdVfnAr21YvQZfRrrJIHQPwyrANatktvRsRn6djwbHEuBkAK6Zac+hz+2spOzF+mQJAMB6Aq9kSoH9dC6dl+PV86IlNndaZLdbdIPCQWJSF6X2pG56efMqCBcqWJWDviaqPT7jTq0+8cwWh1kerJA4sDQJBAIvREyI1yFiuT903MJdlfLtXydZ2E003R9WW7cABaXZ1lYnTNxmg1s0hRGCoSpUofE4CVf7dthqmx5MjGLKKrvECQAh9N7ghT2BXo2q+N1lQFdgkrMobgqpXjz9eM2u6EvSpL5bRmZjk4BkJUXZruG3oiTK094gRD+38cb0iNMjBy7c=";
	public static String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCSwVH9iS4vRdcJ19WC0Ayrfc27tOpEwHjFT2iKSgI1y/F8WGIImL7DyKqJsSYGrsYcAbhFCQ5pCUagEGl8vQaf6ZfyC8ePHzY141MAFzHHz/22nnOXYL2c1JN1/mELa46So8QwkX+ZXTfeYisFiVLKGVqRaqx6fTR5vlZwpRq7AQIDAQAB";
	
	public static final int HTTP_REQ_ERROR = 400;
	
	public static final int HTTP_REQ_GET = 100;
	
	public static final String BLANK = " ";
	
	public static final String BLANK_LINE = "\r\n\r\n";
	
	public static final String LINE_SEPERATOR = "\r\n";
	
	public static final String HEADER_SEPERATOR = ": ";

	public static final String HTTP_POST = "POST";

	public static final String HTTP_GET = "GET";
	
	public static final String CONTENT_LENGTH = "Content-Length";
	
	public static final String BODY_SIGN = "Body-Sign";
	
	public static final String CONTENT_LENGTH_STR = "Content-Length";
	
	public static final String HTTP_VERSION = "HTTP/1.1";
	
	public static final int STATUS_CODE_OK = 200;
	
	public static final String STATUS_MESSAGE_OK = "OK";
	
	public static final String CONTENT_TYPE_KEY = "Content-Type";
	
	public static final String CONTENT_TYPE_VALUE = "text/json; charset=";
	
	public static final String CHARSET_TOKEN = "charset=";
	
	public static final String DEFAULT_HTTP_ENCODING = "UTF-8";
	

	public final static String SIGN = "sign";
	public final static String SIGN_TYPE = "signtype";
	public final static String SIGN_TYPE_VALUE = "RSA";
	public final static String TRANSDATA = "transdata";
	
	public final static String CODE = "code";
	public final static String ERROR_MSG = "errmsg";
	
	
	public final static String LOGIN_NAME = "loginname";
	public final static String USER_ID = "userid";
	
	
	public final static String TRANSID	= "transid";
	
	public final static String APP_ID	= "appid";
	public final static String WARES_ID	= "waresid";
	public final static String WARES_NAME = "waresname";
	public final static String CP_ORDER_ID = "cporderid";
	public final static String PRICE = "price";
	public final static String CURRENCY = "currency";
	public final static String APP_USER_ID = "appuserid";
	public final static String CP_PRIVATE_INFO = "cpprivateinfo";
	public final static String CP_PRIVATE = "cpprivate";
	public final static String NOTIFY_URL = "notifyurl";
	
	public final static String FEE_TYPE = "feetype";
	public final static String MONEY = "money";
	public final static String RESULT = "result";
	public final static String TRANS_TIME = "transtime";
	public final static String PAY_TYPE = "paytype";
	
	public final static String SUB_NUM	= "subsnum";
	public final static String SUBS_LIST = "subslist";
	public final static String LEFT_COUNT = "leftcount";
	public final static String END_TIME = "endtime";
	public final static String SUBS_STATUS = "subsstatus";
	
	public final static String 	TRANS_TYPE = "transtype";
	
	

}
