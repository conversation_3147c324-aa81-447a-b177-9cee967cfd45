package com.wbgame.controller.inter;


import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.wbgame.controller.inter.pojo.ComOrderVo;
import com.wbgame.controller.inter.pojo.WbPayInfo;
import com.wbgame.service.AdService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Controller
public class AliAppGetRes{

	@Autowired
	private AdService adService;
	
	/**
	 * 支付宝回调
	 */
	//private static final long serialVersionUID = 1L;
	private static final String ALIPAY_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDI6d306Q8fIfCOaTXyiUeJHkrIvYISRcc73s3vF1ZT7XN8RNPwJxo8pWaJMmvyTn9N4HQ632qJBVHf8sxHi/fEsraprwCtzvzQETrNRwVxLO5jVmRGi60j8Ue1efIlzPXV9je9mkjzOmdssymZkh2QhUrCmZYI/FCEa3/cNMW0QIDAQAB";
	public static Logger log = LoggerFactory.getLogger(AliAppGetRes.class);
	@RequestMapping("/aliappres")
	public void aliappres(HttpServletRequest request, HttpServletResponse response)
	  throws ServletException, IOException {
		PrintWriter out = response.getWriter();
		Map<String,String> paramMap = new HashMap<String, String>();
		
		/*StringBuffer sb = new StringBuffer() ; 
	      try{
		      InputStream is = request.getInputStream(); 
		      InputStreamReader isr = new InputStreamReader(is);   
		      BufferedReader br = new BufferedReader(isr); 
		      String s ;
		      while((s=br.readLine())!=null){ 
		      sb.append(s) ; 
		      } 
	        }catch(Exception ex)
	      	{ 
	        	log.error("客户端上传数据格式错误-----"+ex);
	      	}
	    String queryString =sb.toString();
		
	    log.debug("@@queryString:"+queryString);
	    
		Map requestParams = getParamsMap(queryString,"utf-8");
		
		log.debug("@@requestParams:"+requestParams.size());*/
		
		// 接收参数调整为同v2版本一致
		Map requestParams = request.getParameterMap();
		for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
		    String name = (String) iter.next();
		    String[] values = (String[]) requestParams.get(name);
		    String valueStr = "";
		    for (int i = 0; i < values.length; i++) {
		        valueStr = (i == values.length - 1) ? valueStr + values[i]
		                    : valueStr + values[i] + ",";
		    }
		    //乱码解决，这段代码在出现乱码时使用。
		    //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
		    paramMap.put(name, valueStr);
		}
		String cpres = "success";
		try {					
			log.info("paramMap=>"+paramMap.toString());
			boolean signRes = AlipaySignature.rsaCheckV1(paramMap, ALIPAY_PUBLIC_KEY, "utf-8");
			
			if(signRes){
				String out_trade_no=paramMap.get("out_trade_no");
				String trade_no=paramMap.get("trade_no");
				String trade_status=paramMap.get("trade_status");
				String total_fee=paramMap.get("total_amount"); 
				String gmt_create=paramMap.get("gmt_create");
				
				ComOrderVo payVo = new ComOrderVo();  
				payVo.setTrade_status(trade_status);  
				payVo.setOrderId(out_trade_no);
				payVo.setTrade_no(trade_no); 
				if(out_trade_no.contains("_"))
				{  
					String out_trade[]=out_trade_no.split("_");   
					
					if("37666".equals(out_trade[0])){//疯狂庄园支付宝回调特殊处理
						payVo.setOrderId(out_trade[1]); 
						
						if(trade_status.equals("TRADE_SUCCESS")||trade_status.equals("TRADE_FINISHED"))//交易成功
						{// 1支付成功 
							log.info("符合条件的上报内容...");
							payVo.setCp_result(cpres); 
							java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyyMM");
							payVo.setTname("alone_game_"+format.format(new Date()));
							
//							AliPayServiceFactory.getInstance().updateAlongAili(payVo);
							String update = "update "+payVo.getTname()+" set trade_status=#{obj.trade_status},trade_no=#{obj.trade_no},cp_result=#{obj.cp_result} "
									+ "where orderId=#{obj.orderId}";
							adService.execSqlHandle(update, payVo);
							
					        CloseableHttpClient httpClient = HttpClients.createDefault();
					        try {//12082测试服端口;12087正式服端口
					        	String url = "http://*************:12087/pay?"
					        			+ "out_trade_no="+out_trade[1]+"&transaction_id="+trade_no;
					        	log.info("url == "+url);
					        	HttpPost httpPost = new HttpPost(url);
					            CloseableHttpResponse resp = httpClient.execute(httpPost);
					            try {  
					                HttpEntity resEntity = resp.getEntity();
					                String respStr = EntityUtils.toString(resEntity);
					                
					                log.info("返回结果："+respStr);
					            } finally {  
					            	resp.close();  
					            }
					        } catch (Exception e) {
					            e.printStackTrace();
					        } finally { 
					            try {  
					                httpClient.close();  
					            } catch (IOException e) {  
					                e.printStackTrace();  
					            }  
					        }  
						
						}
					}else{//疯狂庄园支付宝回调特殊处理 end
					
					payVo.setOrderId(out_trade[0]); 
					Map<String,String> paramsMap=new HashMap<String,String>();
					paramsMap.put("Recharge_Id", out_trade[0]);
					paramsMap.put("Urecharge_Id", out_trade[1]); 
					paramsMap.put("Recharge_Money",total_fee); 
					paramsMap.put("Create_Time", getTime(gmt_create).toString());
					paramsMap.put("paymethod", "11"); 
					//String statu="2";
					if(trade_status.equals("TRADE_SUCCESS")||trade_status.equals("TRADE_FINISHED"))//交易成功
					{// 1支付成功 
						//statu="1";
						paramsMap.put("Pay_Status", "1");
					}else if(trade_status.equals("TRADE_CLOSED")||trade_status.equals("WAIT_BUYER_PAY"))//订单状态：-1取消交易  0未操作 1支付成功  2支付失败
					{ //0未操作
						//statu="0";
						paramsMap.put("Pay_Status", "0");
					}else
					{ //2   
						//statu="2";
						paramsMap.put("Pay_Status","2");
					}
					}
					//cpres=BuyuContant.getBuyuPayRes(out_trade[0], out_trade[1], total_fee, getTime(gmt_create).toString(), statu, paramsMap);
				} 
				payVo.setCp_result(cpres); 
				java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyyMM");
				payVo.setTname("alone_game_"+format.format(new Date()));
				
//				AliPayServiceFactory.getInstance().updateAlongAili(payVo);
				String update = "update "+payVo.getTname()+" set trade_status=#{obj.trade_status},trade_no=#{obj.trade_no},cp_result=#{obj.cp_result} "
						+ "where orderId=#{obj.orderId}";
				adService.execSqlHandle(update, payVo);
				
				log.debug("sign ok");
				out.println("success"); // 请不要修改或删除			
			}else{
				log.debug("sign fail");
				out.println("fail");
			}
		} catch (AlipayApiException e) {
			// TODO Auto-generated catch block
			log.debug("sign error");
			out.println("fail");
			e.printStackTrace();
		}
		
	}

    /**
	 *
	 * 新版支付宝回调
	 * @param request
	 * @param response
	 * @throws ServletException
	 * @throws IOException
     */
	@RequestMapping("/aliappres/v1")
	public void aliappresnew(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		PrintWriter out = response.getWriter();
		Map<String,String> paramMap = new HashMap<String, String>();

		StringBuffer sb = new StringBuffer() ;
		try{
			InputStream is = request.getInputStream();
			InputStreamReader isr = new InputStreamReader(is);
			BufferedReader br = new BufferedReader(isr);
			String s ;
			while((s=br.readLine())!=null){
				sb.append(s) ;
			}
		}catch(Exception ex)
		{
			log.error("客户端上传数据格式错误-----"+ex);
		}
		String queryString =sb.toString();

		log.debug("@@queryString:"+queryString);

		Map requestParams = getParamsMap(queryString,"utf-8");

		log.debug("@@requestParams:"+requestParams.size());

		for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
			String name = (String) iter.next();
			String[] values = (String[]) requestParams.get(name);
			String valueStr = "";
			for (int i = 0; i < values.length; i++) {
				valueStr = (i == values.length - 1) ? valueStr + values[i]
						: valueStr + values[i] + ",";
			}
			// 乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
			// valueStr = new String(valueStr.getBytes("ISO-8859-1"), "gbk");
			paramMap.put(name, valueStr);
		}
		String cpres = "success";
		try {
			log.debug("paramMap=>"+paramMap.toString());
			boolean signRes = AlipaySignature.rsaCheckV1(paramMap, ALIPAY_PUBLIC_KEY, "utf-8");

			if(signRes){
				String out_trade_no=paramMap.get("out_trade_no");
				String trade_no=paramMap.get("trade_no");
				String trade_status=paramMap.get("trade_status");
				String total_fee=paramMap.get("total_amount");
				String gmt_create=paramMap.get("gmt_create");

				ComOrderVo payVo = new ComOrderVo();
				payVo.setTrade_status(trade_status);
				payVo.setOrderId(out_trade_no);
				payVo.setTrade_no(trade_no);
				if(out_trade_no.contains("_"))
				{
					String out_trade[]=out_trade_no.split("_");
					payVo.setOrderId(out_trade[0]);
					Map<String,String> paramsMap=new HashMap<String,String>();
					paramsMap.put("Recharge_Id", out_trade[0]);
					paramsMap.put("Urecharge_Id", out_trade[1]);
					paramsMap.put("Recharge_Money",total_fee);
					paramsMap.put("Create_Time", getTime(gmt_create).toString());
					paramsMap.put("paymethod", "11");
					//String statu="2";
					if(trade_status.equals("TRADE_SUCCESS")||trade_status.equals("TRADE_FINISHED"))//交易成功
					{// 1支付成功
						//statu="1";
						paramsMap.put("Pay_Status", "1");
					}else if(trade_status.equals("TRADE_CLOSED")||trade_status.equals("WAIT_BUYER_PAY"))//订单状态：-1取消交易  0未操作 1支付成功  2支付失败
					{ //0未操作
						//statu="0";
						paramsMap.put("Pay_Status", "0");
					}else
					{ //2
						//statu="2";
						paramsMap.put("Pay_Status","2");
					}
					//cpres=BuyuContant.getBuyuPayRes(out_trade[0], out_trade[1], total_fee, getTime(gmt_create).toString(), statu, paramsMap);
				}
				payVo.setCp_result(cpres);
				java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyyMM");
				payVo.setTname("alone_game_"+format.format(new Date()));
				WbPayInfo wbPayInfo = new WbPayInfo();
				wbPayInfo.setOrderstatus(cpres);
				wbPayInfo.setOrderid(out_trade_no);
				wbPayInfo.setUid(trade_no);
				//AliPayServiceFactory.getInstance().updateAlongAili(payVo);
				
				
//				CommonServiceFactory.getInstance().updateWbPayInfoStatus(wbPayInfo);
				String up = "update wb_pay_info set orderstatus = #{obj.orderstatus} where orderid = #{obj.orderid}";
				adService.execSqlHandle(up, wbPayInfo);
				
				log.debug("sign ok");
				out.println("success"); // 请不要修改或删除
			}else{
				log.debug("sign fail");
				out.println("fail");
			}
		} catch (AlipayApiException e) {
			// TODO Auto-generated catch block
			log.debug("sign error");
			out.println("fail");
			e.printStackTrace();
		}

	}

	public Long getTime(String time)
	{
		  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			try {
				java.util.Date date_util = sdf.parse(time);
				 return date_util.getTime()/100;
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} //转换为util.date
			return null;
	}
	
	private static Map<String, String[]> getParamsMap(String queryString, String enc) {  
	    Map<String, String[]> paramsMap = new HashMap<String, String[]>();  
	    if (queryString != null && queryString.length() > 0) {  
	      int ampersandIndex, lastAmpersandIndex = 0;  
	      String subStr, param, value;  
	      String[] paramPair, values, newValues;  
	      do {  
	        ampersandIndex = queryString.indexOf('&', lastAmpersandIndex) + 1;  
	        if (ampersandIndex > 0) {  
	          subStr = queryString.substring(lastAmpersandIndex, ampersandIndex - 1);  
	          lastAmpersandIndex = ampersandIndex;  
	        } else {  
	          subStr = queryString.substring(lastAmpersandIndex);  
	        }  
	        paramPair = subStr.split("=");  
	        param = paramPair[0];  
	        value = paramPair.length == 1 ? "" : paramPair[1];  
	        try {  
	          value = URLDecoder.decode(value, enc);  
	        } catch (UnsupportedEncodingException ignored) {  
	        }  
	        if (paramsMap.containsKey(param)) {  
	          values = paramsMap.get(param);  
	          int len = values.length;  
	          newValues = new String[len + 1];  
	          System.arraycopy(values, 0, newValues, 0, len);  
	          newValues[len] = value;  
	        } else {  
	          newValues = new String[] { value };  
	        }  
	        paramsMap.put(param, newValues);  
	      } while (ampersandIndex > 0);  
	    }  
	    return paramsMap;  
	  }
}
