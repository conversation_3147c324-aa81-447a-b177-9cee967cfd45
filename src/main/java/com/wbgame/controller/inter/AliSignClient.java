package com.wbgame.controller.inter;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.wbgame.controller.inter.pojo.ComOrderVo;
import com.wbgame.controller.inter.pojo.OrderInfoUtil2_0;
import com.wbgame.controller.inter.pojo.OrderWapUrl;
import com.wbgame.controller.inter.pojo.WbPayInfo;
import com.wbgame.service.AdService;
import com.wbgame.servlet.qihoo.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.Random;


/**
 * 
 * <AUTHOR>
 *
 */
@Controller
public class AliSignClient {
	/**
	 * 
	 */
	//private static final long serialVersionUID = 1L;
	private final static String APP_ID = "2016112503264954";
	private final static String RSA_KEY = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANSq70MyMRHzLlrzmYamdMp8LMSwajjqr7kdj0+iCS8ny/RDvWi9yeEj4o1JnCS3W3LVRwTHYB7xZF3dCALthBzpu9ymwQdzG+mNTlbJZLMkrcT5vr8MUn9smWth5nNSsedaYyvqGjNZfBHekDaLLgr3dOr5w4thkAtCpdlNoyBxAgMBAAECgYBpFuLOwMGwzguNFC08dYrm+D+jHk4PPCGDh1DnF/oT3EWTudmiJY7Z+0KbcNGA7z54W14RxXgBMmXXYD7yO255d7PmqmYWa+VPAuvAtQ/6Sa383N1AJ/WuW/RNXAHaG9b81FpDoP0fcd65Z4Vu9UqZmgfo2q0SViftQahVD/tQ5QJBAP38vtI+KylQKjqnfQersm6op/6Re0O4HXlQ1doUGDfm2YWzuU+kCaVS+PnNbgT/+CiUiwtb28Ffbpk0452OOK8CQQDWWl2gzqVpoYs6UIAf5Wrod4IwTgG2fH/lnl+Z1dVG+Q4i61nh/K1UBRaZZ6DP544ERyOm3cbzh3OtpFoFrEDfAkEA7jvEQTBPC9FFyv6PKELEe0d3TDDpjfuEqeZIsOVoUgcWwXJyv2D+KzRIlQc7yzTNcELkqSLgXeFFvC7/0Sk1wQJAKr0LzANH0d7mzrK8SR/KCB3D9HOpPcRnD9ZbA2YyRxtjaSz51+g0evskvWN/GdT2aP3yKtQvVeEc/HM3i8gDiwJBANndMhH/e0QoTdwqaLi8AfeE+omlt5zc4c4KxQjjNV7VGtCkOx1+KPRnA98cYKv428XV+HwLErKchTVjd9KWIe8=";

    //private final static String NOTIFY_YRL = "http://510694d7.nat123.net:35233/dnwx-zhpay/aliappres";
	private final static String NOTIFY_YRL = "http://zf.vigame.cn:8998/aliappres";
	
	private final static String PARTNER = "2088911658329262";
	private final static String NOTIFY_URL = "http://zf.vigame.cn:8998/getres";
	//private final static String RETURN_URL = "http://zf.vigame.cn:8998/stop";
	//private final static String SHOW_URL = "http://zf.vigame.cn:8998/success";
	
	private final static String RSA_KEY_WAP = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALNWq1LCYzj9BPMF4qu1hq7afHsUIhvQL1pUOguHBQ7P0SRJqt8Bj1MptKL87IGSbJWOSsz4lSdEmGAbbd9sANxsIaYPDx4YWxuKAsYMAak77otKgwBGrepOiJpUA4/npv/X1AQ0RIbuQBVoACFTyO6hBpS9HtBq8SCq/mN+8tsPAgMBAAECgYEArlu0I7k0SMLOXgy5zWGmeTeY0PzJEKt+bl73NB/ojfcEijhJNZnJo/wnUFOBCZS3l0wJc3C3ar8pOj9QumjCB+qy1fzN5zNihVx3kPjE8gmFUmgnQWgDmDdNwmTJv6LNDgzfTuc68LnbvbYlPk0AuxfVFOnwwT1eE/9LdKlyiskCQQDhoXMx7PaupY2ylx0jaZgJ40uE4/EagXMIMHDt9KcmbUe6eIeC0QjrG8nVd5jvfn65CSt6xAMWCTe5T3cPp2q1AkEAy3oiFDq/JpbqZREysk5pBaBHyA60657IUUr7CyK4FuKu1HUkNCdaCsHxKDQNrh8BM9MTtXT9ORLUjGO7M5DVMwJAbeouqeWcDlxL+PIwma25CSgXA/fe3mCtXxHqEaGbQ9b8mFRa/1lBlPcGjd0jPZhLt2xGKBoQvs1ivf1+LLwL+QJAMhuahtbMuekSQd9RV72deUg2RRryDNAmTnajieN76A7ctO9AAiPqtZl7FMZ00/tIpYWGZfowXby4YPYyZIl3JwJAZVCEnGewjpHHyD0A6BLnfzWjRZRovUX7eROGd8HVnnaWfDEgDfCqUn7U/48ZlrbNYGSBd5F206UW/kMtXFLvzQ==";
	public static Logger log = LoggerFactory.getLogger(AliSignClient.class);
	
	@Autowired
	private AdService adService;
	
	/**
	 * 支付宝支付
	 * @param request
	 * @param response
	 * @throws ServletException
	 * @throws IOException
	 */
	@RequestMapping("/alisign")
	public void alisign(HttpServletRequest request, HttpServletResponse response)
	  throws ServletException, IOException {
		String value = request.getParameter("value");
		
		StringBuffer resSb = new StringBuffer();
		
		log.debug("value=>"+value);
		
		if (value != null && value.length() > 0) {
			// lsn=&imsi=&appid=&prjid=&channel=&dhm=
			Base64 base64 = new Base64();
			
			//System.out.println("value=>"+value);
			
			//value = URLDecoder.decode(value,"utf-8");
			
			String jvalue = new String(base64.decode(value), "utf-8");
			
			log.debug("value decode=>"+jvalue);
			
			String[] ary = jvalue.split("&");
			
			String pack = null;
			String appid = null;
			String pid = null;
			String lsn = null;
			String imei = null;
			String imsi = null;
			String mmappid = null;
			String chlid = null;
			String payType = null;
			String payGoodsId = null;
			String payPrice = null;
			String paySubject = null;
			String payBody = null;
			String userdata = null;
			String return_url = null;
			String show_url = null;
			for (String kv : ary) {
				if (kv != null && !"".equals(kv)) {
					String[] kvarr = kv.split("=");
					if (kvarr != null && kvarr.length > 1) {
						if ("pack".equals(kvarr[0])) {
							pack = kvarr[1];
						} else if ("appid".equals(kvarr[0])) {
							appid = kvarr[1];
						} else if ("pid".equals(kvarr[0])) {
							pid = kvarr[1];
						} else if ("lsn".equals(kvarr[0])) {
							lsn = kvarr[1];
						} else if ("imei".equals(kvarr[0])) {
							imei = kvarr[1];
						} else if ("imsi".equals(kvarr[0])) {
							imsi = kvarr[1];
						} else if ("mmappid".equals(kvarr[0])) {
							mmappid = kvarr[1];
						} else if ("chlid".equals(kvarr[0])) {
							chlid = kvarr[1];
						} else if ("payType".equals(kvarr[0])) {
							payType = kvarr[1];
						} else if ("payGoodsId".equals(kvarr[0])) {
							payGoodsId = kvarr[1];
						} else if ("payPrice".equals(kvarr[0])) {
							payPrice = kvarr[1];
						} else if ("paySubject".equals(kvarr[0])) {
							paySubject = kvarr[1];
						}else if ("payBody".equals(kvarr[0])) {
							payBody = kvarr[1];
						}else if ("userdata".equals(kvarr[0])) {
							userdata = kvarr[1];
						}
						else if ("return_url".equals(kvarr[0])) {
							return_url = kvarr[1];
						}
						else if ("show_url".equals(kvarr[0])) {
							show_url = kvarr[1];
						}
					}
				}
			}
			
			if(payPrice!=null){
				DecimalFormat df = new DecimalFormat("#.##");
				payPrice = df.format(Double.parseDouble(payPrice)/100);
			}
			
			//System.out.println("payPrice=>"+payPrice+"#paySubject=>"+paySubject+"#payBody=>"+payBody);
			//String out_trade_no = OrderInfoUtil2_0.getOutTradeNo();
			String out_trade_no=getCharAndNumr(20);//生成商户ID
			ComOrderVo payVo = new ComOrderVo();
			payVo.setFeenum(payPrice);
			payVo.setTrade_status("TRADE_START");//进入
			payVo.setPrjid(pid);
			if(!StringUtils.isEmpty(mmappid)){
				payVo.setMid(mmappid);//mmid  
			}else{
				payVo.setMid(chlid);//mmid  
			}
			payVo.setOrderId(out_trade_no);
			payVo.setTrade_no(getCharAndNumr(14));
			payVo.setImsi(imsi);
			payVo.setAppid(appid);   
		
			if(userdata!=null&&userdata.length()>0)
			{ 
				payVo.setCp_order_id(userdata);
		        //2019.6.25 疯狂庄园增加支付宝支付处理 yangk
		        if("37666".equals(appid)){
		        	payVo.setOrderId(userdata);
		        	out_trade_no=appid+"_"+userdata;
		        }else{
		        	out_trade_no=out_trade_no+"_"+userdata;
		        }
		        //2019.6.25 end
			}else 
			{     
				 payVo.setCp_order_id(out_trade_no);   
			}
			if("ali".equals(payType)){
				String res = OrderInfoUtil2_0.generateOrderInfo(APP_ID, RSA_KEY, NOTIFY_YRL, paySubject, payBody, payPrice,out_trade_no);
				
				resSb.append(res);
			}else if("aliwap".equals(payType)){
				String res = OrderWapUrl.genUrl(PARTNER, PARTNER, NOTIFY_URL, return_url, show_url, paySubject, payBody, payPrice, RSA_KEY_WAP);
				
				resSb.append(res);
			}else{
				resSb.append("payType is error.");
			}
			
			
			//System.out.println("res=>"+res);
			java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyyMM");
			payVo.setTname("alone_game_"+format.format(new Date()));
			
			
//			AliPayServiceFactory.getInstance().addAlongAili(payVo);
			String ins = "insert into "+payVo.getTname()+"(mid,imsi,appid,prjid,feenum,orderId,trade_no,trade_status,createtime,cp_order_id,cp_result) "
					+ "values(#{obj.mid},#{obj.imsi},#{obj.appid},#{obj.prjid},#{obj.feenum},#{obj.orderId},#{obj.trade_no},#{obj.trade_status},now(),#{obj.cp_order_id},#{obj.cp_result})";
			adService.execSqlHandle(ins, payVo);
			
		}else{
			resSb.append("value is null!");
		}
		PrintWriter out = response.getWriter();
		
		out.print(resSb.toString());
		
		out.close();
	}

    /**
	 * 新版支付宝支付
	 * @param request
	 * @param response
	 * @throws ServletException
	 * @throws IOException
     */
	@RequestMapping("/alisign/v1")
	public void alisignnew(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		String value = request.getParameter("value");

		StringBuffer resSb = new StringBuffer();

		log.debug("value=>"+value);

		if (value != null && value.length() > 0) {
			// lsn=&imsi=&appid=&prjid=&channel=&dhm=
			Base64 base64 = new Base64();

			//System.out.println("value=>"+value);

			//value = URLDecoder.decode(value,"utf-8");

			String jvalue = new String(base64.decode(value), "utf-8");

			log.debug("value decode=>"+jvalue);

			String[] ary = jvalue.split("&");

			String pack = null;
			String appid = null;
			String pid = null;
			String lsn = null;
			String imei = null;
			String imsi = null;
			String mmappid = null;
			String chlid = null;
			String payType = null;
			String payGoodsId = null;
			String payPrice = null;
			String paySubject = null;
			String payBody = null;
			String userdata = null;
			String return_url = null;
			String show_url = null;
			for (String kv : ary) {
				if (kv != null && !"".equals(kv)) {
					String[] kvarr = kv.split("=");
					if (kvarr != null && kvarr.length > 1) {
						if ("pack".equals(kvarr[0])) {
							pack = kvarr[1];
						} else if ("appid".equals(kvarr[0])) {
							appid = kvarr[1];
						} else if ("pid".equals(kvarr[0])) {
							pid = kvarr[1];
						} else if ("lsn".equals(kvarr[0])) {
							lsn = kvarr[1];
						} else if ("imei".equals(kvarr[0])) {
							imei = kvarr[1];
						} else if ("imsi".equals(kvarr[0])) {
							imsi = kvarr[1];
						} else if ("mmappid".equals(kvarr[0])) {
							mmappid = kvarr[1];
						} else if ("chlid".equals(kvarr[0])) {
							chlid = kvarr[1];
						} else if ("payType".equals(kvarr[0])) {
							payType = kvarr[1];
						} else if ("payGoodsId".equals(kvarr[0])) {
							payGoodsId = kvarr[1];
						} else if ("payPrice".equals(kvarr[0])) {
							payPrice = kvarr[1];
						} else if ("paySubject".equals(kvarr[0])) {
							paySubject = kvarr[1];
						}else if ("payBody".equals(kvarr[0])) {
							payBody = kvarr[1];
						}else if ("userdata".equals(kvarr[0])) {
							userdata = kvarr[1];
						}
						else if ("return_url".equals(kvarr[0])) {
							return_url = kvarr[1];
						}
						else if ("show_url".equals(kvarr[0])) {
							show_url = kvarr[1];
						}
					}
				}
			}

			if(payPrice!=null){
				DecimalFormat df = new DecimalFormat("#.##");
				payPrice = df.format(Double.parseDouble(payPrice)/100);
			}

			//System.out.println("payPrice=>"+payPrice+"#paySubject=>"+paySubject+"#payBody=>"+payBody);
			//String out_trade_no = OrderInfoUtil2_0.getOutTradeNo();
			String out_trade_no=getCharAndNumr(20);//生成商户ID
			ComOrderVo payVo = new ComOrderVo();
			payVo.setFeenum(payPrice);
			payVo.setTrade_status("TRADE_START");//进入
			payVo.setPrjid(pid);
			if(!StringUtils.isEmpty(mmappid)){
				payVo.setMid(mmappid);//mmid
			}else{
				payVo.setMid(chlid);//mmid
			}
			payVo.setOrderId(out_trade_no);
			payVo.setTrade_no(getCharAndNumr(14));
			payVo.setImsi(imsi);
			payVo.setAppid(appid);

			if(userdata!=null&&userdata.length()>0)
			{
				payVo.setCp_order_id(userdata);
				out_trade_no=out_trade_no+"_"+userdata;
			}else
			{
				payVo.setCp_order_id(out_trade_no);
			}
			if("ali".equals(payType)){
				String res = OrderInfoUtil2_0.generateOrderInfo(APP_ID, RSA_KEY, NOTIFY_YRL, paySubject, payBody, payPrice,out_trade_no);

				resSb.append(res);
			}else if("aliwap".equals(payType)){
				String res = OrderWapUrl.genUrl(PARTNER, PARTNER, NOTIFY_URL, return_url, show_url, paySubject, payBody, payPrice, RSA_KEY_WAP);

				resSb.append(res);
			}else{
				resSb.append("payType is error.");
			}


			//System.out.println("res=>"+res);
			java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyyMM");
			payVo.setTname("alone_game_"+format.format(new Date()));
			WbPayInfo wbPayInfo = new WbPayInfo();
			wbPayInfo.setMoney(String.valueOf(Integer.valueOf(payPrice)*100));
			wbPayInfo.setPaytype("支付宝");
			wbPayInfo.setOrderstatus("TRADE_START");
			wbPayInfo.setPid(pid);
			wbPayInfo.setOrderid(out_trade_no);
			wbPayInfo.setUid(getCharAndNumr(14));
			//param1保留字段 保存imsi
			wbPayInfo.setParam1(imsi);
			wbPayInfo.setImei(imei);
			wbPayInfo.setAppid(appid);
			
			
//			CommonServiceFactory.getInstance().updateWbPayInfo(wbPayInfo);
			String ins = "insert into wb_pay_info(orderid,uid,money,paytype,imei,pid,appid,orderstatus, "+
		        "payname,paynote,createtime,param1,param2,param3) "+
				"values(#{obj.orderid},#{obj.uid},#{obj.money},#{obj.paytype},#{obj.imei},#{obj.pid},#{obj.appid}, "+
		        "#{obj.orderstatus},#{obj.payname},#{obj.paynote},now(),#{obj.param1},#{obj.param2},#{obj.param3})";
			adService.execSqlHandle(ins, wbPayInfo);
			
		}else{
			resSb.append("value is null!");
		}
		PrintWriter out = response.getWriter();

		out.print(resSb.toString());

		out.close();
	}
	
	@RequestMapping("/success")
	public void success(HttpServletRequest request, HttpServletResponse response)
	  throws ServletException, IOException {
		PrintWriter out = response.getWriter();
		
		out.print("pay success.");
		
		out.close();
	}
	
	@RequestMapping("/stop")
	public void stop(HttpServletRequest request, HttpServletResponse response)
	  throws ServletException, IOException {
		PrintWriter out = response.getWriter();
		
		out.print("pay stop.");
		
		out.close();
	}
	/**
	  * java生成随机数字和字母组合
	  * @param
	  * @return
	  */
	 public static String getCharAndNumr(int length) {
	  String val = "";
	  Random random = new Random();
	  for (int i = 0; i < length; i++) {
	   // 输出字母还是数字
	   String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num"; 
	   // 字符串
	   if ("char".equalsIgnoreCase(charOrNum)) {
	    // 取得大写字母还是小写字母
	    int choice = random.nextInt(2) % 2 == 0 ? 65 : 97; 
	    val += (char) (choice + random.nextInt(26));
	   } else if ("num".equalsIgnoreCase(charOrNum)) { // 数字
	    val += String.valueOf(random.nextInt(10));
	   }
	  }
	  return val;
	 }
}
