package com.wbgame.controller.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.test.MailResultVo;
import com.wbgame.pojo.test.query.SingleCommonQueryVo;
import com.wbgame.service.AdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpRequest;
import com.wbgame.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;

/**
 * <AUTHOR>
 * @Classname TestMailController
 * @Description TODO
 * @Date 2022/7/27 19:49
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/test/mail")
@Api(tags = "邮件单独服务")
@ApiSupport(author = "chensq")
public class TestMailController {

    private static final String URL = "https://ddz.vigame.cn:6601/mail/v1/get";
    private static final String URL_TEST = "http://************:6409/mail/v1/get";

    @Autowired
    private AdService adService;

    @RequestMapping("get")
    @ApiOperation(value = "获取邮件", notes = "获取邮件单独服务", httpMethod = "POST")
    public Result<List<MailResultVo>> getMail(@Valid SingleCommonQueryVo vo){

        String query = "select concat(id,'') as mapkey,app_id from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);
        if (appMap.get(vo.getAppid())==null){
            return ResultUtils.failure("未配置接口");
        }

        SortedMap<String,String> params = JSON.parseObject(JSON.toJSONString(vo), SortedMap.class);
        String signKey = appMap.get(vo.getAppid()).get("app_id") + "";
        String sign = StringUtils.sign(params,signKey);

        params.put("sign",sign);
        String value = org.apache.commons.codec.binary.Base64.encodeBase64String((JSON.toJSONString(params)).getBytes());

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("value", value);
        String result = HttpRequest.httpPost(URL,paramMap,new HashMap<>());
        JSONObject retJson = null;
        if (BlankUtils.checkBlank(result)){
            return ResultUtils.failure("服务器无响应");
        }
        retJson = JSONObject.parseObject(result);
        if (!"200".equals(retJson.getString("code"))){
            return ResultUtils.failure(retJson.getString("retMsg"));
        }
        JSONObject dataJson = retJson.getJSONObject("data");
        JSONArray mailJson = dataJson.getJSONArray("mail");
        List<MailResultVo> mails = new ArrayList<>();
        if (mailJson!=null&&mailJson.size()>0){
            for (Object obj : mailJson) {
                JSONObject each = (JSONObject) obj;
                MailResultVo eachMail = new MailResultVo();
                try {
                    eachMail = JSON.toJavaObject(each, MailResultVo.class);
                } catch (Exception e) {
                    continue;
                }
                mails.add(eachMail);
            }
        }
        return ResultUtils.success(mails);
    }
}
