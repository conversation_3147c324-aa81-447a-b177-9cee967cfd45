package com.wbgame.controller.test;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.common.Constants;
import com.wbgame.common.ProtocolConstant;
import com.wbgame.utils.HttpClientUtils;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@RequestMapping(value = "/test")
@Api(tags = "微信登录认证")
@ApiSupport(author = "huangmb")
public class TestWxCodeTokenController {

    @RequestMapping(value = "/wxCodeToken")
    @ApiOperation(value = "微信登录认证", notes = "微信登录认证", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 500, message = "操作失败",response = Constants.class)
    })
    @ApiImplicitParam(name = "ver",value = "协议版本",example = "v2")
    public String wxCodeToken(@ApiParam(value = "sdk获取微信登录的code") String js_code
            ,@ApiParam(value = "项目id")String pid
            , String ver
            , @ApiParam(value = "appid")String appid
            , @ApiParam(value = "渠道")String channel
            , @ApiParam(value = "平台")String platform){
        String url = ProtocolConstant.hb_new_redpack_url+"/wxCodeToken/"+ver+"?platform="+platform+"&channel="+channel+"&pid="+pid+"&js_code="+js_code+"&appid="+appid;
        String result = HttpClientUtils.getInstance().httpGet(url);
        return result;
    }

    @RequestMapping(value = "/getWxuser")
    @ApiOperation(value = "获取微信用户信息", notes = "获取微信用户信息(根据openid或usercode查询相对于的数据)", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 500, message = "操作失败",response = Constants.class)
    })
    @ApiImplicitParam(name = "token",value = "token",example = "dnwx202420242024")
    public String getWxuser(@ApiParam(value = "用户id") String usercode
            ,String token
            , @ApiParam(value = "appid")String appid
            , @ApiParam(value = "openid")String openid){
        String url = ProtocolConstant.hb_new_redpack_url+"/getWxuser/?usercode="+usercode+"&token="+token+"&appid="+appid+"&openid="+openid;
        String result = HttpClientUtils.getInstance().httpGet(url);
        return result;
    }

}
