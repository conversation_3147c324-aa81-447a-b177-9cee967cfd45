package com.wbgame.controller.test;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.utils.HttpRequest;
import com.wbgame.utils.realAuth.Base64;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname TestABConfigController
 * @Description TODO
 * @Date 2022/9/21 12:26
 */
@RequestMapping("/test/abtest")
@RestController
@CrossOrigin
@Api(tags = "产品AB配置测试")
@ApiSupport(author = "chensq")
public class TestABConfigController {

    private static final String URL = "https://u.vigame.cn/u/abtest/v1/getConfig";
    private static final String URL_TEST = "http://***********:6708/u/abtest/v1/getConfig";

    @PostMapping(value = "getConfig")
    @ApiOperation(value = "产品AB配置测试下发接口", notes = "产品AB配置测试下发接口", httpMethod = "POST")
    public String getTestABTestConfig(
            @ApiParam(name = "appid", value = "应用id") String appid,
            @ApiParam(name = "cha_id", value = "子渠道") String cha_id,
            @ApiParam(name = "ver", value = "版本号") String ver,
            @ApiParam(name = "prjid", value = "项目id") String prjid) {

        JSONObject json = new JSONObject();
        json.put("appid",appid);
        json.put("cha_id",cha_id);
        json.put("ver",ver);
        json.put("prjid",prjid);
        String value = Base64.encode(json.toJSONString().getBytes());
        Map<String,String> paramsMap = new HashMap<>();
        paramsMap.put("value",value);
        String result = HttpRequest.httpPost(URL,paramsMap,new HashMap<>());
        return result;
    }

}
