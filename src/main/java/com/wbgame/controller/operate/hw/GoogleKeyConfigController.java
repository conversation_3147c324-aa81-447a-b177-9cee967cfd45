package com.wbgame.controller.operate.hw;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.operate.GoogleKeyConfig;
import com.wbgame.pojo.operate.GoogleKeyConfigDTO;
import com.wbgame.service.haiwai.GoogleKeyConfigService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: google支付秘钥配置
 * @Author: xiaoxh
 * @Date: 2024/08/29
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/googleKeyConfig")
@Api(tags = "google支付秘钥配置")
public class GoogleKeyConfigController {

    @Autowired
    private GoogleKeyConfigService googleKeyConfigService;


    @ApiOperation(value = "google支付秘钥配置删除", notes = "google支付秘钥配置删除")
    @PostMapping(value = "/deleteByAppid")
    public Result<Integer> deleteByAppid(@ApiNeed({"appidList"}) @RequestParam("appidList") List<Integer> appidList) {
        return googleKeyConfigService.deleteByAppid(appidList);
    }

    @ApiOperation(value = "google支付秘钥配置新增", notes = "google支付秘钥配置新增")
    @PostMapping(value = "/insert")
    public Result<Integer> insertGoogleKey(HttpServletRequest request,
                                           @ApiNeed({"appid", "googlePackage", "keyJson"}) GoogleKeyConfigDTO dto) {
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        dto.setCreateOwner(loginUser.getLogin_name());
        return googleKeyConfigService.insertGoogleKey(dto);
    }

    @ApiOperation(value = "google支付秘钥配置修改", notes = "google支付秘钥配置修改")
    @PostMapping(value = "/update")
    public Result<Integer> updateGoogleKey(HttpServletRequest request,
                                           @ApiNeed({"appid", "googlePackage", "keyJson"}) GoogleKeyConfigDTO dto) {
        //封装当前操作人信息
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        dto.setUpdateOwner(loginUser.getLogin_name());
        return googleKeyConfigService.updateGoogleKey(dto);
    }

    @ApiOperation(value = "google支付秘钥配置查询", notes = "google支付秘钥配置查询")
    @PostMapping(value = "/list")
    public Result<PageResult<GoogleKeyConfig>> selectByCondition(@ApiNeed({"appid", "start", "limit", "order_str"}) GoogleKeyConfigDTO dto) {
        return googleKeyConfigService.selectByCondition(dto);
    }


    /**
     * google支付秘钥配置 刷新缓存接口
     * @return 处理结果
     */
    @GetMapping("/refreshCache")
    public Result<String> refreshCache(){
        return googleKeyConfigService.refreshCache();
    }


}
