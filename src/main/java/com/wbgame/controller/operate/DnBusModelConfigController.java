package com.wbgame.controller.operate;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.Asserts;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.UpdateGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.DnBusModelConfigDTO;
import com.wbgame.pojo.DnBusModelConfigVO;
import com.wbgame.service.IDnBusModelConfigService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.Default;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2022/8/8 14:21
 * @class: DnBusModelConfigController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/busModelConfig")
@Api(tags = "业务模式配置")
public class DnBusModelConfigController {

    @Autowired
    private IDnBusModelConfigService dnBusModelConfigService;

    @ApiOperation(value = "删除", notes = "删除", httpMethod = "POST")
    @PostMapping(value = "/deleteDnBusModelConfigById")
    public Result<Integer> deleteDnBusModelConfigById(@RequestParam("idList") List<Integer> id) {

        return dnBusModelConfigService.deleteDnBusModelConfigById(id);
    }

    @ApiOperation(value = "新增", notes = "新增", httpMethod = "POST")
    @PostMapping(value = "/insertDnBusModelConfig")
    public Result<Integer> insertDnBusModelConfig(HttpServletRequest request,
                                                  @Validated(Default.class)
                                                  @ApiNeed({"busModel", "description", "status"})
                                                          DnBusModelConfigDTO record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        record.setCreateUser(loginUser.getLogin_name());
        return dnBusModelConfigService.insertDnBusModelConfig(record);

    }

    @ApiOperation(value = "查询", notes = "查询", httpMethod = "POST")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "start", value = "页码", dataType = "String", required = true),
//            @ApiImplicitParam(name = "limit", value = "条数", dataType = "String", required = true)
//    })
    @PostMapping(value = "/selectDnBusModelConfigByCondition")
    public Result<PageResult<DnBusModelConfigVO>>
    selectDnBusModelConfigByCondition(@RequestParam("start") Integer start,
                                      @RequestParam("limit") Integer limit,
                                      @ApiNeed({"busModel","busPort", "status"})
                                      @Validated(QueryGroup.class) DnBusModelConfigDTO dnBusModelConfigDTO) {

        return dnBusModelConfigService.selectDnBusModelConfigByCondition(start, limit, dnBusModelConfigDTO);

    }

    @ApiOperation(value = "修改", notes = "修改", httpMethod = "POST")
    @PostMapping(value = "/updateDnBusModelConfigById")
    public Result<Integer> updateDnBusModelConfigById(HttpServletRequest request,
                                                      @Validated(UpdateGroup.class)
                                                      @ApiNeed({"id", "busModel","busPort", "description", "status"})
                                                              DnBusModelConfigDTO record) {

        record.setUpdateUser(((CurrUserVo) request.getAttribute("LoginUser")).getLogin_name());
        return dnBusModelConfigService.updateDnBusModelConfigById(record);
    }

    @ApiOperation(value = "获取业务配置列表", notes = "获取业务配置列表", httpMethod = "POST")
    @PostMapping(value = "/getModelList")
    public Result<List<DnBusModelConfigVO>> getModelList() {

        return dnBusModelConfigService.getModelList();
    }

    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "value", value = "自定义字段", example = "busModel,业务模式;status,状态", dataType = "String"),
            @ApiImplicitParam(name = "export_file_name", value = "文件名", example = "业务模式.xlsx", dataType = "String")
    })
    @PostMapping(value = "/export")
    public void export(HttpServletResponse response,
                       @RequestParam(name = "value", defaultValue = "busModel,业务模式;status,状态") String value,
                       @RequestParam(name = "export_file_name", defaultValue = "业务模式.xlsx") String export_file_name,
                       @ApiNeed({"busModel", "status"}) @Validated(QueryGroup.class) DnBusModelConfigDTO dnBusModelConfigDTO) {

        List<DnBusModelConfigVO> list = dnBusModelConfigService.export(dnBusModelConfigDTO);

        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = export_file_name+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX2(response,list,head,fileName);

    }
}
