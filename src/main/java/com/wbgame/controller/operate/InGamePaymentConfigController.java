package com.wbgame.controller.operate;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.ApiIgp;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.redpack.InGamePaymentConfigMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.operate.InGamePaymentConfigVo;
import com.wbgame.pojo.operate.InGamePaymentHandelVo;
import com.wbgame.pojo.operate.InGamePaymentReportVo;
import com.wbgame.utils.BlankUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @description: 小游戏内购支付配置
 * @author: yyc
 * @date: 2022/05/12
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/inGamePaymentConfig")
@Api(tags = "小游戏内购支付配置")
@ApiSupport(author = "yyc")
public class InGamePaymentConfigController {

    @Resource
    private InGamePaymentConfigMapper inGamePaymentConfigMapper;

    @Autowired
    RedisTemplate redisTemplate;

    @RequestMapping(value = "/list")
    @ApiOperation(value = "查询", notes = "查询小游戏内购支付配置", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = JSONObject.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String list(HttpServletRequest request, @ApiIgp({"version","value"}) InGamePaymentReportVo param){
        //默认返回结果
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("total",0);
        jsonObject.put("list",new ArrayList<>());
        int pageStart = param.getStart() == null ? 0 : param.getStart();
        int pageSize = param.getLimit() == null ? 100 : param.getLimit();
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize);
        List<InGamePaymentConfigVo> resList = inGamePaymentConfigMapper.selectList(param);
        if (resList == null || resList.size() == 0) {
            return ReturnJson.success(jsonObject);
        }
        jsonObject.put("list",resList);
        long size = ((Page) resList).getTotal();
        jsonObject.put("total",size);



        return ReturnJson.success(jsonObject);
    }

    @RequestMapping(value = "/handel")
    @ApiOperation(value = "操作", notes = "查询小游戏内购支付配置", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = JSONObject.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String handel(HttpServletRequest request, @ApiIgp({"version","value"}) InGamePaymentHandelVo param){
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String username = "";
        CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (currUserVo != null) {
            username = currUserVo.getLogin_name();
        }

        //默认返回结果
        String handle = param.getHandle();
        switch(handle){
            case "add" :
                param.setCreate_owner(username);
                return handelAdd(param);
            case "edit":
                param.setUpdate_owner(username);
                return handelEdit(param);
            case "del" :
                param.setUpdate_owner(username);
                return handelDel(param);
            default :

        }

        return ReturnJson.toErrorJson("操作失败,联系管理员");
    }

    private String handelDel(InGamePaymentHandelVo param) {
        int count = inGamePaymentConfigMapper.inGamePaymentDel(param);
        if(count>0){
            return ReturnJson.success("操作成功");
        }
        return ReturnJson.toErrorJson("操作失败,联系管理员");
    }

    private String handelEdit(InGamePaymentHandelVo param) {
        int count = inGamePaymentConfigMapper.inGamePaymentEdit(param);
        if(count>0){
            return ReturnJson.success("操作成功");
        }
        return ReturnJson.toErrorJson("操作失败,联系管理员");
    }

    /**
     * 新增方法
     * @param param 新增对象
     */
    private String handelAdd(InGamePaymentHandelVo param) {
        InGamePaymentReportVo params = new InGamePaymentReportVo();
        params.setAppid(param.getAppid());
        params.setChannel("'"+param.getChannel()+"'");
        List <InGamePaymentConfigVo> resList =  inGamePaymentConfigMapper.selectList(params);
        if(resList.size()>0){
            return ReturnJson.toErrorJson("操作失败,已存在应用id和子渠道都相同的数据");
        }else{
            int count = inGamePaymentConfigMapper.inGamePaymentAdd(param);
            if(count>0){
                return ReturnJson.success("操作成功");
            }
        }

        return ReturnJson.toErrorJson("操作失败,联系管理员");
    }


}
