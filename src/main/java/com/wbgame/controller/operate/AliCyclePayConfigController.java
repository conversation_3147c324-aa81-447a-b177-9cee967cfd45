package com.wbgame.controller.operate;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.UpdateGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.clean.AliCyclePayConfig;
import com.wbgame.pojo.clean.AliCyclePayConfigDTO;
import com.wbgame.pojo.clean.AliCyclePayConfigVO;
import com.wbgame.service.clean.IAliCyclePayConfigService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.groups.Default;
import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/9/14
 * @class: AliCyclePayConfigServiceController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/aliCyclePayConfig")
@Api(tags = "支付宝周期扣款配置")
public class AliCyclePayConfigController {

    @Autowired
    private IAliCyclePayConfigService aliCyclePayConfigService;

    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/deleteByKey")
    public Result<Integer> deleteByKey(@RequestBody List<AliCyclePayConfig> keyList) {

        return aliCyclePayConfigService.deleteByKey(keyList);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/insertAliCyclePayConfig")
    public Result<Integer> insertAliCyclePayConfig(HttpServletRequest request,
                                                   @ApiNeed({"money", "remark", "day", "appid", "type","first_money","first_day","free_use"})
                                                   @Validated(Default.class) AliCyclePayConfigDTO record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        record.setCreateOwner(loginUser.getLogin_name());
        return aliCyclePayConfigService.insertAliCyclePayConfig(record);
    }

    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping("/updateByKey")
    public Result<Integer> updateByKey(HttpServletRequest request,
                                       @ApiNeed({"money", "remark", "day", "appid", "type","first_money","first_day","free_use"})
                                       @Validated(UpdateGroup.class) AliCyclePayConfigDTO record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        record.setUpdateOwner(loginUser.getLogin_name());

        return aliCyclePayConfigService.updateByKey(record);
    }

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping("/selectAliCyclePayConfig")
    public Result<PageResult<AliCyclePayConfigVO>> selectAliCyclePayConfig(
            HttpServletRequest request,
            @ApiNeed({"appidList", "type", "limit", "start"})
            @Validated(QueryGroup.class) AliCyclePayConfigDTO record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        record.setUpdateOwner(loginUser.getLogin_name());

        return aliCyclePayConfigService.selectAliCyclePayConfig(record);
    }
}
