package com.wbgame.controller.operate;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.operate.ActiveUserOnlineDurationVo;
import com.wbgame.pojo.param.ActivePaidUserRetentioParam;
import com.wbgame.service.adb.ActivePaidUserRetentionService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: xugx
 * @createDate: 2023/03/23 
 * @class: ActiveUserOnlineDurationController
 * @description:活跃度用户在线时长
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/actuser/online/duration")
@Api(tags = "活跃度用户在线时长")
public class ActiveUserOnlineDurationController {
	
	@Autowired
    private ActivePaidUserRetentionService service;


    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping("/list")
    Result<PageResult<ActiveUserOnlineDurationVo>> list( @ApiNeed({
            "order_str",
            "start_date",
            "end_date",
            "start",
            "limit",
    }) ActivePaidUserRetentioParam dto) {

        return service.OnlineDurationList(dto);
    }


    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping("/export")
    void export(HttpServletResponse response, @ApiNeed({
            "order_str",
            "start_date",
            "end_date",
            "value",
    }) ActivePaidUserRetentioParam dto, HttpServletRequest request) {

        List<ActiveUserOnlineDurationVo> list = service.OnlineDurationExport(dto);
        
        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = dto.getValue().split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(export_file_name)){
            export_file_name = "活跃度用户在线时长";
        }
        String fileName =export_file_name +"_" +DateUtil.getToday()+ ".xlsx";
        ExportExcelUtil.exportXLSX2(response,list,head,fileName);

    }
    @ApiOperation(value = "图表", notes = "图表")
    @PostMapping("/chart")
    Result<Map<String,List<ActiveUserOnlineDurationVo>>> chart( @ApiNeed({
        "start_date",
        "end_date",
	}) ActivePaidUserRetentioParam dto) {
		List<ActiveUserOnlineDurationVo> list = service.OnlineDurationChart(dto);
		if(null==list){
			return ResultUtils.success();
		}
		return ResultUtils.success(Constants.OK,list.stream().collect(Collectors.groupingBy(ActiveUserOnlineDurationVo::getTdate)),null,0l);
	}
}
