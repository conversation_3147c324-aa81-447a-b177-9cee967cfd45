package com.wbgame.controller.operate;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.UpdateGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.operate.WxAmountWithdrawMonitorDTO;
import com.wbgame.pojo.operate.WxAmountWithdrawMonitorVO;
import com.wbgame.service.clean.IWxAmountWithdrawMonitorService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.groups.Default;
import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/9/14
 * @class: wxAmountWithdrawMonitorServiceController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/wxAmountWithdrawMonitor")
@Api(tags = "商户号监控配置")
public class WxAmountWithdrawMonitorController {

    @Autowired
    private IWxAmountWithdrawMonitorService wxAmountWithdrawMonitorService;

    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/deleteByKey")
    public Result<Integer> deleteByKey(@RequestParam("keyList") List<String> keyList) {

        return wxAmountWithdrawMonitorService.deleteByKey(keyList);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/insertWxAmountWithdrawMonitor")
    public Result<Integer> insertWxAmountWithdrawMonitor(HttpServletRequest request,
                                                   @ApiNeed({"mchid", "amount", "scanTime", "status", "alarmAmount"})
                                                   @Validated(Default.class) WxAmountWithdrawMonitorDTO record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        record.setCreateOwner(loginUser.getLogin_name());
        return wxAmountWithdrawMonitorService.insertWxAmountWithdrawMonitor(record);
    }

    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping("/updateByKey")
    public Result<Integer> updateByKey(HttpServletRequest request,
                                       @ApiNeed({"mchid", "amount", "scanTime", "status", "alarmAmount"})
                                       @Validated(UpdateGroup.class) WxAmountWithdrawMonitorDTO record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        record.setUpdateOwner(loginUser.getLogin_name());

        return wxAmountWithdrawMonitorService.updateByKey(record);
    }

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping("/selectWxAmountWithdrawMonitor")
    public Result<PageResult<WxAmountWithdrawMonitorVO>> selectWxAmountWithdrawMonitor(
            HttpServletRequest request,
            @ApiNeed({"mchid", "status", "limit", "start"})
            @Validated(QueryGroup.class) WxAmountWithdrawMonitorDTO record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        record.setUpdateOwner(loginUser.getLogin_name());

        return wxAmountWithdrawMonitorService.selectWxAmountWithdrawMonitor(record);
    }
}
