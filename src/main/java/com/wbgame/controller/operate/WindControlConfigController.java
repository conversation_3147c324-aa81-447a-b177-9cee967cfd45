package com.wbgame.controller.operate;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.operate.WindControlConfig;
import com.wbgame.pojo.operate.WindControlConfigDTO;
import com.wbgame.service.IWindControlConfigService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @author: zhangY
 * @createDate: 2022/8/31
 * @class: WindControlConfigController
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/windControlConfig")
@Api(tags = "新风控V3配置")
public class WindControlConfigController {

    private IWindControlConfigService windControlConfigService;

    @Autowired
    public void setWindControlConfigService(IWindControlConfigService windControlConfigService) {
        this.windControlConfigService = windControlConfigService;
    }

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping(value = "/selectWindControlConfig")
    public Result<PageResult<WindControlConfig>> selectWindControlConfig(@ApiNeed({
            "appidList", "channelList", "prjid", "limit", "start"
    }) @Validated(QueryGroup.class) WindControlConfigDTO dto) {

        return windControlConfigService.selectWindControlConfig(dto);

    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = "/insertSWindControlConfig")
    public Result<Integer> insertSWindControlConfig(@ApiNeed({
            "appid", "cha_id", "prjid", "wifiKeyList", "pkgKeyList",
            "pkgSelfList", "queryInvTime", "appNumMax", "chargeLastValue",
            "gyroLastValue", "stationLastValue", "brandList", "similarAppNum"}) @Validated WindControlConfigDTO windControlConfig) {

        return windControlConfigService.insertSWindControlConfig(windControlConfig);

    }
}
