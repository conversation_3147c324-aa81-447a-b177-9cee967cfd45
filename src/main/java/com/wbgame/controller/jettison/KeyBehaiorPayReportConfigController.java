package com.wbgame.controller.jettison;



import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.pojo.jettison.vo.KeyBehaiorPayReportConfigVo;
import com.wbgame.service.WxGameAppTypeManageService;
import com.wbgame.utils.PageResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * desc:关键行为付费回传配置
 * createBy:xugx
 * date：2023-11-20
 */
@CrossOrigin(maxAge =300)
@RestController
@RequestMapping(value = "/keybehavior/pay/config")
@Api(tags = "关键行为付费回传管理")
@ApiSupport(author = "xugx")
public class KeyBehaiorPayReportConfigController {
	Logger logger = LoggerFactory.getLogger(KeyBehaiorPayReportConfigController.class);
    @Autowired
    private WxGameAppTypeManageService wxGameAppTypeManageService;
    
	/**
	 * 回传比例列表
	 * @param vo  
	 * @return
	 */
	@RequestMapping(value="/list", method={RequestMethod.POST})
	@ApiOperation(value = "查询", notes = "回传比例查询", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = -1, message = "查询异常",response = InfoResult.class),
            @ApiResponse(code = 1, message = "成功",response = InfoResult.class)
    })
	@LoginCheck
    public Result<PageResult<KeyBehaiorPayReportConfigVo>> list(KeyBehaiorPayReportConfigVo vo){
        	return wxGameAppTypeManageService.keybehaviorPayConfigList(vo);
    }
	
	/**
	 * 回传比例删除
	 * @param vo  
	 * @return
	 */
    @ApiOperation(value = "删除", notes = "删除", httpMethod = "POST")
    @RequestMapping(value = "/delete")
    @LoginCheck
    public Result<Integer> delete(@RequestParam("idList") List<Integer> id) {

        return wxGameAppTypeManageService.deleteKeybehaviorPayConfigConfigById(id);
    }
	/**
	 *新增回传比例
	 * @param vo  
	 * @return
	 */
    
    @ApiOperation(value = "新增", notes = "新增", httpMethod = "POST")
    @RequestMapping(value = "/add")
    @LoginCheck
    public Result<Integer> add(HttpServletRequest request, KeyBehaiorPayReportConfigVo vo) {
    	CurrUserVo user=(CurrUserVo)request.getAttribute("LoginUser");
    	String user_name=user.getLogin_name();
    	vo.setUpdate_user(user_name);
    	vo.setCreate_user(user_name);
    	Result<Integer> res = wxGameAppTypeManageService.addKeybehaviorPayConfig(vo);
        return res;
    }
	/**
	 *修改回传比例
	 * @param vo  
	 * @return
	 */
    @ApiOperation(value = "修改", notes = "修改", httpMethod = "POST")
    @RequestMapping(value = "/update")
    @LoginCheck
    public Result<Integer> update(HttpServletRequest request,KeyBehaiorPayReportConfigVo vo) {

    	vo.setUpdate_user(((CurrUserVo) request.getAttribute("LoginUser")).getLogin_name());
        return wxGameAppTypeManageService.updateKeybehaviorPayConfig(vo);
    }
	/**
	 *修改状态
	 * @param vo  
	 * @return
	 */
    @ApiOperation(value = "修改状态", notes = "修改状态", httpMethod = "POST")
    @RequestMapping(value = "/update/status")
    @LoginCheck
    public Result<Integer> updateStatus(HttpServletRequest request,KeyBehaiorPayReportConfigVo vo) {

    	vo.setUpdate_user(((CurrUserVo) request.getAttribute("LoginUser")).getLogin_name());
        return wxGameAppTypeManageService.updateKeybehaviorPayConfigStatus(vo);
    }
}
