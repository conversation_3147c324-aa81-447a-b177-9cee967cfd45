package com.wbgame.controller.jettison;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.utils.HttpClientUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.ExecutionException;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * <AUTHOR>
 * @date 2024/6/12
 * @description
 **/
@CrossOrigin
@RestController
@RequestMapping("/spend/syncdata")
@Api(tags = "投放国内报表ovhm媒体数据同步")
@ApiSupport(author = "shenl")
@Slf4j
public class ChannelSpendSyncController {

    private final static String SYNC_DATA_URL = "http://***********:6115/spend/syncdata/account";

    @PostMapping("/account")
    @ControllerLoggingEnhancer
    @ApiOperation(value = "投放国内报表ovhm媒体数据同步")
    public String syncData(@RequestBody SpendSyncDataParam param) throws ExecutionException, InterruptedException {
        param.setUserName(LOGIN_USER_NAME.get());
        return HttpClientUtils.getInstance().httpPostThrow(SYNC_DATA_URL, JSONObject.toJSONString(param), null);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpendSyncDataParam {
        private String ad_platform;
        private String startDay;
        private String userName;
        private String endDay;
        private List<String> spiderAccountList;
    }
}
