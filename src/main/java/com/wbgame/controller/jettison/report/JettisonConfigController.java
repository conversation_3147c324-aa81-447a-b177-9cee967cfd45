package com.wbgame.controller.jettison.report;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.aop.LoginCheck;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.pojo.jettison.report.dto.ArtistDTO;
import com.wbgame.pojo.jettison.report.dto.PutUserDTO;
import com.wbgame.service.jettison.report.JettisonConfigService;
import com.wbgame.utils.BlankUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

@CrossOrigin//跨域用
@RestController
@RequestMapping("/jettisonConfig")
@Api(tags = "投放系统-数据配置")
@ApiSupport(author = "xujy")
public class JettisonConfigController {

    Logger logger = LoggerFactory.getLogger(JettisonConfigController.class);

    @Autowired
    JettisonConfigService jettisonConfigService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

//    @ApiOperation(value = "广告位配置-查询")
//    @GetMapping("/adsense/list")
//    @LoginCheck
//    public InfoResult getAdsense(@RequestBody AdsenseCondition adsenseCondition){
//        InfoResult infoResult = new InfoResult();
//        try {
//
//        } catch (Exception e) {
//            infoResult.setRet(0);
//            infoResult.setMsg("查询失败");
//            logger.error("getAdsense: ", e);
//        }
//        return infoResult;
//    }
//
//
//    @ApiOperation(value = "广告位配置-新增")
//    @GetMapping("/adsense/list")
//    @LoginCheck
//    public InfoResult getAdsense(@RequestBody AdsenseCondition adsenseCondition){
//        InfoResult infoResult = new InfoResult();
//        try {
//
//        } catch (Exception e) {
//            infoResult.setRet(0);
//            infoResult.setMsg("查询失败");
//            logger.error("getAdsense: ", e);
//        }
//        return infoResult;
//    }
//
//    @ApiOperation(value = "广告位配置-修改")
//    @GetMapping("/toutiao/parent/list")
//    @LoginCheck
//    public InfoResult getAdsense(@RequestBody AdsenseCondition adsenseCondition){
//        InfoResult infoResult = new InfoResult();
//        try {
//
//        } catch (Exception e) {
//            infoResult.setRet(0);
//            infoResult.setMsg("查询失败");
//            logger.error("getAdsense: ", e);
//        }
//        return infoResult;
//    }


    @ApiOperation(value = "美术人员配置-查询")
    @PostMapping("/artist/list")
    @LoginCheck
    public InfoResult getArtist(@RequestParam("artistName") String artistName,@RequestParam("limit") Integer limit,@RequestParam("start") Integer start){
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = jettisonConfigService.getArtist(artistName,limit,start);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getArtist: ", e);
        }
        return infoResult;
    }
    @ApiOperation(value = "美术人员配置-新增")
    @PostMapping("/artist/add")
    public InfoResult addArtist(ArtistDTO artist){
        InfoResult infoResult = new InfoResult();
        try {
            String token = artist.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            String username = currUserVo.getLogin_name();
            artist.setCreateUser(username);
            infoResult = jettisonConfigService.addArtist(artist);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("新增失败");
            logger.error("addArtist: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "美术人员配置-修改")
    @PostMapping("/artist/update")
    public InfoResult updateArtist(ArtistDTO artist){
        InfoResult infoResult = new InfoResult();
        try {
            String token = artist.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            String username = currUserVo.getLogin_name();
            artist.setUpdateUser(username);
            infoResult = jettisonConfigService.updateArtist(artist);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("修改失败");
            logger.error("updateArtist: ", e);
        }
        return infoResult;
    }


//    @ApiOperation(value = "代理商配置-查询")
//    @GetMapping("/toutiao/parent/list")
//    @LoginCheck
//
//    @ApiOperation(value = "代理商配置-新增")
//    @GetMapping("/toutiao/parent/list")
//    @LoginCheck
//
//    @ApiOperation(value = "代理商配置-修改")
//    @GetMapping("/toutiao/parent/list")
//    @LoginCheck

    @ApiOperation(value = "投放人员配置-查询")
    @PostMapping("/putUser/list")
    @LoginCheck
    public InfoResult getPutUser(@RequestParam("putUserName") String putUserName,@RequestParam("limit") Integer limit,@RequestParam("start") Integer start){
        InfoResult infoResult = new InfoResult();
        try {
            infoResult = jettisonConfigService.getPutUser(putUserName,limit,start);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("查询失败");
            logger.error("getPutUser: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "投放人员配置-新增")
    @PostMapping("/putUser/add")
    public InfoResult addPutUser(PutUserDTO putUser){
        InfoResult infoResult = new InfoResult();
        try {
            String token = putUser.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            String username = currUserVo.getLogin_name();
            putUser.setCreateUser(username);
            infoResult = jettisonConfigService.addPutUser(putUser);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("新增失败");
            logger.error("addPutUser: ", e);
        }
        return infoResult;
    }

    @ApiOperation(value = "投放人员配置-修改")
    @PostMapping("/putUser/update")
    public InfoResult updatePutUser(PutUserDTO putUser){
        InfoResult infoResult = new InfoResult();
        try {
            String token = putUser.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
                infoResult.setRet(0);
                infoResult.setMsg("token不存在");
                return infoResult;
            }else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }
            CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
            String username = currUserVo.getLogin_name();
            putUser.setUpdateUser(username);
            infoResult = jettisonConfigService.updatePutUser(putUser);
        } catch (Exception e) {
            infoResult.setRet(0);
            infoResult.setMsg("修改失败");
            logger.error("updatePutUser: ", e);
        }
        return infoResult;
    }
}
