package com.wbgame.controller.jettison;


import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.common.Constant;
import com.wbgame.pojo.jettison.AdRoiDTO;
import com.wbgame.pojo.jettison.param.AdRoiParam;
import com.wbgame.pojo.jettison.vo.AdRoiVo;
import com.wbgame.service.jettison.AdRoiService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.StringUtils;
import com.wbgame.utils.jettison.CommonUtil;
import com.wbgame.utils.jettison.JSONResultModel;
import com.wbgame.utils.jettison.JSONResultString;
import com.wbgame.utils.jettison.StringUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * desc:OPPO代理商广告实时ROI报表
 * createBy:xugx
 * date：2022-11-02
 */
@CrossOrigin(maxAge =300)
@RestController
@RequestMapping(value = "/oppo/roi/report")
@Api(tags = "OPPO代理商广告实时ROI报表")
@ApiSupport(author = "xugx")
public class OppoAgentRoiController extends BaseController {
	Logger logger = LoggerFactory.getLogger(OppoAgentRoiController.class);
	@Autowired
	private AdRoiService adRoiService;
	
	/**
	 * OPPO代理商广告实时ROI报表
	 * @param param  
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value="/list", method={RequestMethod.POST})
	@ApiOperation(value = "查询", notes = "代理商广告ROI报表查询", httpMethod = "POST")
    @ApiResponses(value = {
    		@ApiResponse(code = 1002, message = "参数校验失败",response = JSONResultModel.class),
            @ApiResponse(code = 1001, message = "查询异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
	public JSONResultModel report(@RequestBody AdRoiParam param ){
		JSONResultModel result = new JSONResultModel();
		try {
			if(!checkDate(param)){
				result.setMsg(msg);
				result.setRet(false);
				result.setErrcode(Constant.Realize.PARAM_CHECK_FAIL);
				return result;
			}
			PageHelper.startPage(param.getPageNum(), param.getPageSize());
			List<AdRoiDTO> list=adRoiService.oppoActualAdRoiReport(param);
			
			List<AdRoiDTO> totalList=adRoiService.oppoActualAdRoiReportTotal(param);
			List<AdRoiVo> exportList=new ArrayList<>();
			Map<String, Map<String, Object>> appMap = adService.getChannelAppInfoMap();
			//数据计算
			if(null!=list&&list.size()>0){
				for (int i = 0; i < list.size(); i++) {
					AdRoiDTO dto=list.get(i);
					AdRoiVo export=reportCount(dto,Constant.INT_ZERO,null==param.getRevenueSource()?3:param.getRevenueSource(),param.getGroup(),appMap);
					exportList.add(export);
				}
			}
			if(null!=totalList&&totalList.size()>0&&null!=totalList.get(0)){
				AdRoiDTO dto=totalList.get(0);
				if(null!=dto){
					AdRoiVo export=reportCount(dto,Constant.INT_ZERO,null==param.getRevenueSource()?3:param.getRevenueSource(),param.getGroup(),appMap);
					exportList.add(export);
				}
			}
			result.setRows(exportList);
			result.setTotal((int)((Page) list).getTotal());
		} catch (Exception e) {
			msg=e.getMessage();
			result.setErrcode(Constant.Realize.REPORT_ERROR_CODE);
			result.setMsg(msg);
			result.setRet(false);
			logger.error("OPPO代理商广告实时ROI报表查询异常:"+msg);
			e.printStackTrace();
		}
		return result;
	}
	/**
	 *  OPPO代理商广告实时ROI报表导出
	 * @return
	 */
	@RequestMapping(value="/export", method={RequestMethod.GET, RequestMethod.POST})
	public JSONResultString exportReport(@RequestBody AdRoiParam param, HttpServletResponse response){
		JSONResultString js=new JSONResultString();
		try {
			String token = param.getToken();
	         if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
	        	 js.setMsg("token验证失败");
	        	 js.setRet(0);
	        	 return js;
	         }else {
	             redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
	         }
	        //自定义列
	        String value = param.getCustomizes();
	        if(StringUtil.is_nullString(value)){
				js.setMsg("请选择自定义列");
				js.setRet(0);
				return js;	
	        }
	        String[] split = value.split(";");
			if(!StringUtils.isEmpty(param.getScope())) {
	            String[] strings = param.getScope().split("&");
	            param.setIndex(strings[0]);
	            param.setSymbol(strings[1]);
	            String number=strings[2];
	            if(StringUtils.isEmpty(number)){
	            	param.setNumber(0d);
	            }else{
	            	param.setNumber(Double.valueOf(number));
	            }
		    }
			List<AdRoiDTO> list=adRoiService.oppoActualAdRoiReport(param);
			//数据计算
			List<AdRoiVo> exportList=new ArrayList<>();
			// 赋值渠道产品名称
		    Map<String, Map<String, Object>> appMap = adService.getChannelAppInfoMap();
			if(null!=list&&list.size()>0){
				for (int i = 0; i < list.size(); i++) {
					AdRoiDTO dto=list.get(i);
					AdRoiVo export=reportCount(dto,Constant.INT_ZERO,null==param.getRevenueSource()?3:param.getRevenueSource(),param.getGroup(),appMap);
					exportList.add(export);
				}
			}
			Map<String,String> head = new LinkedHashMap<>();
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
            ExportExcelUtil.exportXLSX2(response,exportList,head,"广告报表V2_" + System.currentTimeMillis()+ ".xlsx");
		} catch (Exception e) {
			msg=e.getMessage();
			js.setRet(-1);
			js.setMsg(msg);
			logger.error(" OPPO代理商广告实时ROI报表导出异常:"+msg);
			e.printStackTrace();
		}
		return js;
	}
	/**
	 * 计算报表cpa roi 人均广告展示等数据
	 * @param creative  
	 * @return null
	 */
	private AdRoiVo reportCount(AdRoiDTO dto,int zero,int revenueSource,String group,Map<String, Map<String, Object>> appMap){
		//处理媒体及投放子渠道为空的记录 返回自然量给前端
		String campaignId=(String)dto.getCampaignId();
		if(StringUtil.is_nullString(campaignId)&&StringUtil.is_nullString(dto.getMedia())&&StringUtil.is_nullString(dto.getChannel())){
			dto.setMedia(Constant.Realize.NATURAL_FLOW);
			dto.setChannel(Constant.Realize.NATURAL_FLOW);
		}
		AdRoiVo exportDto=new AdRoiVo();
		//生成导出数据
		geneData(exportDto,dto,appMap);
		int addUser=null==dto.getAddUser()?0:dto.getAddUser();
		double spend=null==dto.getSpend()?0:dto.getSpend()  ;
		
		double addUserLtv0=null==dto.getAddUserLtv0()?0:dto.getAddUserLtv0() ;
		double addIpa=null==dto.getAddIpa()?0:dto.getAddIpa()  ;
		double ltv2=null==dto.getLtv2()?0:dto.getLtv2() ;
		//计费+变现
		if(Constant.Realize.REVENUE_SOURCE_IPA==revenueSource){
			//计费收入汇总
			addUserLtv0=CommonUtil.formatDouble(addIpa);
		}else if(Constant.Realize.REVENUE_SOURCE_IPA_REALIZE==revenueSource){
			//计费+变现
			addUserLtv0=CommonUtil.formatDouble(addUserLtv0+addIpa);
		}
		exportDto.setAddUserLtv0(addUserLtv0);
		if(addUser>zero){
			//CPA计算
			exportDto.setCpa(CommonUtil.formatDouble(spend/addUser));
			exportDto.setAddArpu( CommonUtil.formatDouble( addUserLtv0/addUser));
			//首日-人均插屏  avgPlaque
			exportDto.setAvgPlaque(CommonUtil.divideLongDouble(dto.getAplaqueShowTimes(), addUser));
		}else{
			//CPA计算
			exportDto.setCpa(zero);
			//新增用户arpu
			exportDto.setAddArpu(zero);
		}
		if(spend>zero){
			//首日roi
			exportDto.setaRoi(CommonUtil.doubleToPer(addUserLtv0/spend));
			exportDto.setRealizeRoi(CommonUtil.doubleToPer(ltv2/spend));
		}else{
			exportDto.setaRoi("0%");
			exportDto.setRealizeRoi("0%");
		}
		Long installs=null==dto.getInstalls()?0:dto.getInstalls();
		if(null!=installs&&installs.intValue()>0){
			//cpi
			exportDto.setCpi(CommonUtil.formatDouble(spend/installs.intValue()));
		}
		return exportDto;
	}
	/**
	 * 重新生成报表数据
	 * @param exportDto  
	 * @param dto 
	 * @return null
	 */
	private void geneData(AdRoiVo exportDto,AdRoiDTO dto,Map<String, Map<String, Object>> appMap){
		exportDto.setDay(dto.getDay());
		if(null!=appMap){
			Map<String, Object> app = appMap.get(Integer.valueOf(dto.getAppId()));
	        if(null !=app&& null!=app.get("tappname")){
	        	exportDto.setAppId(app.get("tappname").toString());
	        }else{
	        	exportDto.setAppId(dto.getAppId());
	        }
		}else{
			exportDto.setAppId(dto.getAppId());
		}
		exportDto.setMedia(dto.getMedia());
		exportDto.setAccountId(dto.getAccountId());
		exportDto.setGroupName(dto.getGroupName());
		exportDto.setCampaignName(dto.getCampaignName());
		exportDto.setCampaignId(dto.getCampaignId());
		exportDto.setAddUser(null==dto.getAddUser()?0:dto.getAddUser());
		exportDto.setSpend(null==dto.getSpend()?0.00:CommonUtil.formatDouble(dto.getSpend()));
		exportDto.setAdsensePosition(dto.getAdsensePosition());
		if(null!=dto.getStrategy()){
			switch (dto.getStrategy()) {
			case 1:
				exportDto.setStrategy("激活");
				break;
			case 2:
				exportDto.setStrategy("注册");
				break;
			case 3:
				exportDto.setStrategy("付费");
				break;
			case 4:
				exportDto.setStrategy("付费ROI");
				break;
			case 5:
				exportDto.setStrategy("关键行为");
				break;
			case 6:
				exportDto.setStrategy("次留");
				break;
			case 7:
				exportDto.setStrategy("激活+次留");
				break;
			case 8:
				exportDto.setStrategy("激活+付费");
				break;
			case 9:
				exportDto.setStrategy("激活+关键行为");
				break;
			case 10:
				exportDto.setStrategy("每次付费");
				break;
			case 11:
				exportDto.setStrategy("nobid-每次付费");
				break;
			case 12:
				exportDto.setStrategy("nobid-目标付费");
				break;
			case 13:
				exportDto.setStrategy("nobid-付费ROI");
				break;
			case 14:
				exportDto.setStrategy("注册-首日付费ROI");
				break;
			case 15:
				exportDto.setStrategy("广告变现-首日变现ROI");
				break;
			case 16:
				exportDto.setStrategy("首次付费-首日付费ROI");
				break;
			case 17:
				exportDto.setStrategy("激活-注册");
				break;
			case 18:
				exportDto.setStrategy("激活-首日付费ROI");
				break;
			case 19:
				exportDto.setStrategy("目标付费");
				break;
			case 21:
				exportDto.setStrategy("广告变现");
				break;
			case 22:
				exportDto.setStrategy("下载");
				break;
			case 23:
				exportDto.setStrategy("自定义注册");
				break;
			case 24:
				exportDto.setStrategy("自定义付费");
				break;
			case 25:
				exportDto.setStrategy("自定义次留");
				break;
			case 26:
				exportDto.setStrategy("自定义激活");
				break;
			case 27:
				exportDto.setStrategy("广告变现-7日变现ROI");
				break;
			case 999:
				exportDto.setStrategy("其他");
				break;
			default:
				break;
			}
		}
	}
}
