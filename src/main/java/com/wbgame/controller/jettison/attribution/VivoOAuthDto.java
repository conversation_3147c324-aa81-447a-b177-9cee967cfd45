package com.wbgame.controller.jettison.attribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/12
 * @description
 **/
@Data
@ApiModel(value = "vivo 授权参数")
public class VivoOAuthDto {

    @ApiModelProperty(value = "账户",dataType = "String")
    private String account;
    @ApiModelProperty(value = "client_id，用户填写",dataType = "String")
    private String client_id;
    @ApiModelProperty(value = "client_secret，用户填写",dataType = "String")
    private String client_secret;
    @ApiModelProperty(value = "appid",dataType = "String")
    private Integer appid;
    @ApiModelProperty(value = "src_id，数据源id，vivo平台获取",dataType = "String")
    private String src_id;
    @ApiModelProperty(value = "access_token，全程隐藏，不返回，不展示",dataType = "String")
    private String access_token;
    @ApiModelProperty(value = "refresh_token，全程隐藏，不返回，不展示",dataType = "String")
    private String refresh_token;
    @ApiModelProperty(value = "token_date，该字段如果为空则授权不成功，不能查询详细绑定app",dataType = "String")
    private Long token_date;
    @ApiModelProperty(value = "refresh_token_date，全程隐藏，不返回，不展示",dataType = "String")
    private Long refresh_token_date;
    @ApiModelProperty(value = "create_user",dataType = "String")
    private String create_user;
    @ApiModelProperty(value = "create_time",dataType = "String")
    private String create_time;
    @ApiModelProperty(value = "update_user",dataType = "String")
    private String update_user;
    @ApiModelProperty(value = "update_time",dataType = "String")
    private String update_time;

}
