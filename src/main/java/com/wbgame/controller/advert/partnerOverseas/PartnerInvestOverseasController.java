package com.wbgame.controller.advert.partnerOverseas;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.master.SomeMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.DnChannelInfo;
import com.wbgame.service.AdService;
import com.wbgame.service.PartnerService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: 合作方投放明细审核 (海外版)
 * @author: caow
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/partnerInvestInfoNewOverseas")
public class PartnerInvestOverseasController {

	@Autowired
    private RedisTemplate<String, Object> redisTemplate;
	@Autowired
    private PartnerService partnerService;
    @Autowired
    private AdService adService;

    /**
     * 合作方投放明细查询 (海外版)
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(HttpServletRequest request, HttpServletResponse response) throws IOException {
        
    	String[] args = {"sdate","edate","appid","cha_media","ischeck","date_group","appid_group","cha_media_group","country_group","country_code","app_category","order_str"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
        paramMap.put("app_category_filter", "15,16"); // 海外版特定过滤

        JSONObject result = new JSONObject();
        try {

        	String token = request.getParameter("token");
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
            if(cuser.getCompany() != null && !"1".equals(cuser.getCompany())){
                // 外部合作方账号带审核功能
                paramMap.put("check", "ok");
            }

            // 不为管理员用户 则添加应用控制
            if(!"root".equals(cuser.getOrg_id())){
            	result.put("appList", new String[0]);
                
                if(!BlankUtils.checkBlank(cuser.getApp_group())){
                	paramMap.put("apps", cuser.getApp_group());
                	result.put("appList", cuser.getApp_group().split(","));
                }
            }else{
                result.put("appList", new String[0]);
            }
            
            // 分组条件处理
            String group = "tdate,appid,cha_media,country_code";
            List<String> groupList = new ArrayList<>();
            if(!BlankUtils.checkBlank(paramMap.get("date_group"))){
            	groupList.add("tdate");
            }
            if(!BlankUtils.checkBlank(paramMap.get("appid_group"))){
            	groupList.add("appid");
            }
            if(!BlankUtils.checkBlank(paramMap.get("cha_media_group"))){
            	groupList.add("cha_media");
            }
            if(!BlankUtils.checkBlank(paramMap.get("country_group"))){
            	groupList.add("country_code");
            }
            if(!groupList.isEmpty())
            	group = String.join(",", groupList);
            paramMap.put("group", group);

            // 分页查询
            PageHelper.startPage(paramMap);
            List<Map<String, Object>> list = partnerService.selectPartnerInvestInfoNewOverseas(paramMap);
            long size = ((Page) list).getTotal();

			// 解析国家地区
			Map<String, Map<String, Object>> countryMap = partnerService.selectDimCountryMap();
            

            Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
            list.forEach(act -> {
            	if(!BlankUtils.checkBlank(paramMap.get("date_group")))
                    act.put("tdate", act.get("tdate")+"");
            	
                Map<String, Object> app = appMap.get(act.get("appid")+"");
                if(app != null && !BlankUtils.checkBlank(paramMap.get("appid_group")))
                    act.put("appname", app.get("app_name")+"");

				Map<String, Object> country = countryMap.get(act.get("country_code")+"");
				if(country != null){
					act.put("country_name", country.get("country_name")+"");
				}
            });


            result.put("ret", 1);
            result.put("data", list);
            result.put("total", partnerService.selectPartnerInvestInfoNewSumOverseas(paramMap));
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    /**
     * 合作方产品收支导出 (海外版)
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/export", method={RequestMethod.GET, RequestMethod.POST})
    public void export(String token,HttpServletRequest request,HttpServletResponse response) {

    	String[] args = {"sdate","edate","appid","cha_media","ischeck","date_group","appid_group","cha_media_group","country_group","country_code","app_category","order_str"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
        paramMap.put("app_category_filter", "15,16"); // 海外版特定过滤

		CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
        if(cuser.getCompany() != null && !"1".equals(cuser.getCompany())){
            // 外部合作方账号带审核功能
            paramMap.put("check", "ok");
        }

        // 不为管理员用户 则添加应用控制
        if(!"root".equals(cuser.getOrg_id())){
            
            if(!BlankUtils.checkBlank(cuser.getApp_group())){
            	paramMap.put("apps", cuser.getApp_group());
            }
        }
        
        // 分组条件处理
		String group = "tdate,appid,cha_media,country_code";
		List<String> groupList = new ArrayList<>();
		if(!BlankUtils.checkBlank(paramMap.get("date_group"))){
			groupList.add("tdate");
		}
		if(!BlankUtils.checkBlank(paramMap.get("appid_group"))){
			groupList.add("appid");
		}
		if(!BlankUtils.checkBlank(paramMap.get("cha_media_group"))){
			groupList.add("cha_media");
		}
		if(!BlankUtils.checkBlank(paramMap.get("country_group"))){
			groupList.add("country_code");
		}
        if(!groupList.isEmpty())
        	group = String.join(",", groupList);
        paramMap.put("group", group);

        // 查询结果
        List<Map<String, Object>> contentList = partnerService.selectPartnerInvestInfoNewOverseas(paramMap);

		// 解析国家地区
		Map<String, Map<String, Object>> countryMap = partnerService.selectDimCountryMap();

		Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
        contentList.forEach(act -> {
        	if(!BlankUtils.checkBlank(paramMap.get("date_group")))
                act.put("tdate", act.get("tdate")+"");
        	
            Map<String, Object> app = appMap.get(act.get("appid")+"");
            if(app != null && !BlankUtils.checkBlank(paramMap.get("appid_group")))
                act.put("appname", app.get("app_name")+"");
            
            if(act.get("ischeck") != null){
            	String ischeck = act.get("ischeck")+"";
            	act.put("ischeck", "2".equals(ischeck)?"已调整":"1".equals(ischeck)?"已核查":"未审核");
            }

			Map<String, Object> country = countryMap.get(act.get("country_code")+"");
			if(country != null){
				act.put("country_name", country.get("country_name")+"");
			}
		});


        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName =request.getParameter("export_file_name") + DateTime.now().toString("yyyyMMdd")+ ".xlsx";

		ExportExcelUtil.exportXLSX(response, contentList, headerMap, fileName);
    }

    /**
     * 合作方产品审核 (海外版)
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/check", method={RequestMethod.POST})
    public String check(HttpServletRequest request,HttpServletResponse response) throws IOException {

        String tdate = request.getParameter("tdate");
        String token = request.getParameter("token");
        CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);

        if(BlankUtils.sqlValidate(tdate))
            return "{\"ret\":0,\"msg\":\"param is error!\"}";

        /** 检验已审核通过的日期数据，已审核过则不允许覆盖 */
        // 此处的查询也需要考虑 app_category_filter
    	String query = "select ischeck from partner_invest_info_overseas where tdate = '"+tdate+"' and (ischeck='1')";
		List<String> check = adService.queryListString(query);
		if(check != null && check.size() > 0){
			return "{\"ret\":0,\"msg\":\"该日期数据已审核，无需重复操作！\"}";
		}
		
        try {
            // 操作行为记录
            String sql = "insert into partner_channel_check_log(tdate,ischeck,flag,cuser,date) values('"+tdate+"', 1, 'investOverseas', '"+cuser.getLogin_name()+"', now())"; // flag更新为investOverseas
            adService.execSql(sql);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            // 此处的更新也需要考虑 app_category_filter
            String sql = "update partner_invest_info_overseas set ischeck='1' where tdate='"+tdate+"' ";
            adService.execSql(sql);

            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    @RequestMapping(value="/handle", method = {RequestMethod.POST})
    public String handle(String handle, HttpServletRequest request,HttpServletResponse response) throws IOException, Exception {

        Map<String, String> valMap = new HashMap<String, String>();

        Map<String, String[]> parameterMap = request.getParameterMap();
        Iterator<Map.Entry<String, String[]>> iterator = parameterMap.entrySet().iterator();
        while(iterator.hasNext()){
            Map.Entry<String, String[]> next = iterator.next();
            valMap.put(next.getKey(), next.getValue()[0]);
        }

        int result = 0;
        try {
            if("edit".equals(handle)){

                String sql = "update partner_invest_info_overseas set invest_amount=#{obj.investAmount},ischeck='2' "+
                		"where id = #{obj.id} and ischeck != '1' ";
                result = adService.execSqlHandle(sql, valMap);
            }

            if(result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }
    
    /**
     * 同步投放金额 (海外版)
     * @param tdate
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/sync", method={RequestMethod.POST})
    public String sync(HttpServletRequest request, HttpServletResponse response) {

    	String tdate = request.getParameter("tdate");
    	String token = request.getParameter("token");
        CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);

        if(BlankUtils.sqlValidate(tdate) || BlankUtils.checkBlank(tdate))
    		return "{\"ret\":0,\"msg\":\"同步日期不能为空！\"}";
    	
    	/** 检验已审核通过的日期数据，已审核过则不允许覆盖 */
    	String query = "select ischeck from partner_invest_info_overseas where tdate = '"+tdate+"' and (ischeck='1' or ischeck='2')";
		List<String> check = adService.queryListString(query);
		if(check != null && check.size() > 0){
			return "{\"ret\":0,\"msg\":\"该日期有投放数据已调整或已审核，请联系管理员确认是否要覆盖！\"}";
		}

        try {
            // 操作行为记录
            String sql = "insert into partner_channel_check_log(tdate,ischeck,flag,cuser,date) values('"+tdate+"', 1, 'investSyncOverseas', '"+cuser.getLogin_name()+"', now())";
            adService.execSql(sql);
        } catch (Exception e) {
            e.printStackTrace();
        }
        
    	try{
            Map<String, String> contextParamsForSync = new HashMap<>();
            contextParamsForSync.put("app_category_filter", "15,16");

    		boolean resp = partnerService.syncPartnerInvestInfoNewOverseas(tdate, contextParamsForSync);
    		if(resp)
    			return "{\"ret\":1,\"msg\":\"同步成功！\"}";
    		else
    			return "{\"ret\":0,\"msg\":\"同步失败，请稍后重试！\"}";
    	}catch(Exception e){
    		e.printStackTrace();
    		return "{\"ret\":0,\"msg\":\"出现错误，请联系管理员排查！\"}";
    	}
    }
    
}