package com.wbgame.controller.advert.partnerQuery;

import com.alibaba.fastjson.JSONArray;
import com.wbgame.pojo.AdvChaFeeVo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.custom.PageForList;
import com.wbgame.service.AdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: cps渠道收入查询
 * @author: huangmb
 * @date: 2021/06/25
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/cpsProductIncome")
public class CpsProductIncomeController {

    @Autowired
    private AdService adService;

    @Autowired
    private RedisTemplate redisTemplate;

    // 渠道广告收入管理
    @RequestMapping(value="/list", method={RequestMethod.GET,RequestMethod.POST})
    public String list(ModelMap map, HttpServletRequest request, HttpServletResponse response) {

        String pStart = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        String dateStart = BlankUtils.checkNull(request, "dateStart");
        String dateEnd = BlankUtils.checkNull(request, "dateEnd");
        String channel = BlankUtils.checkNull(request, "channel");
        String advType = BlankUtils.checkNull(request, "advType");
        String gameNm = BlankUtils.checkNull(request, "gameNm");


        // pageStart 和 limit设置值
        int pageStart = "".equals(pStart) == true ? 0 : Integer.parseInt(pStart);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        CurrUserVo cuser = (CurrUserVo) request.getAttribute("LoginUser");
        // 当前缓存中该用户的实时权限
        CurrUserVo currUserVo = CommonUtil.userMap.get(cuser.getLogin_name());
        if("2".equals(currUserVo.getCompany())){ // 外部渠道用户
            channel = currUserVo.getNick_name();
        }

        Map<String, String> parmMap = new HashMap<String, String>();
        parmMap.put("beginDt", dateStart);
        parmMap.put("endDt", dateEnd);
        parmMap.put("channel", channel);
        parmMap.put("gameNm", gameNm);
        parmMap.put("advType", advType);

        List<AdvChaFeeVo> mmlist = adService.selectAdvChaFee(parmMap);
        java.text.DecimalFormat my=new java.text.DecimalFormat("#.##");
        Double advFee = 0d;
        Integer showCount = 0;
        Integer clickCount = 0;

        AdvChaFeeVo resVo = new AdvChaFeeVo();

        for (AdvChaFeeVo advFeeVo : mmlist) {
            if(advFeeVo.getAdv_fee()!=null){
                advFee += Double.parseDouble(advFeeVo.getAdv_fee());
            }
            if(advFeeVo.getShow_count()!=null){
                showCount += Integer.parseInt(advFeeVo.getShow_count());
            }
            if(advFeeVo.getClick_count()!=null){
                clickCount += Integer.parseInt(advFeeVo.getClick_count());
            }
            if(advFeeVo.getShow_count()!=null && !"0".equals(advFeeVo.getShow_count()))
            {
                advFeeVo.setClickRate(my.format(Double.parseDouble(advFeeVo.getClick_count())/Double.parseDouble(advFeeVo.getShow_count())*100)+"%");
                advFeeVo.setEcpm(my.format(Double.parseDouble(advFeeVo.getAdv_fee())/Integer.parseInt(advFeeVo.getShow_count())*1000));
            }else{
                advFeeVo.setClickRate("0");
                advFeeVo.setEcpm("0");
            }
            if(advFeeVo.getClick_count()!=null && !"0".equals(advFeeVo.getClick_count()))	{
                advFeeVo.setCpc(my.format(Double.parseDouble(advFeeVo.getAdv_fee())/Integer.parseInt(advFeeVo.getClick_count())));
            }else{
                advFeeVo.setCpc("0");
            }
        }
        resVo.setAdv_dt("总计:");
        resVo.setAdv_fee(my.format(advFee));
        resVo.setShow_count(String.valueOf(showCount));
        resVo.setClick_count(String.valueOf(clickCount));
        if(showCount!=0){
            resVo.setClickRate(my.format(Double.parseDouble(clickCount.toString())/Double.parseDouble(showCount.toString())*100)+"%");
            resVo.setEcpm(my.format(advFee/showCount*1000));
        }
        if(clickCount!=0){
            resVo.setCpc(my.format(advFee/clickCount));
        }

        mmlist.add(resVo);

        redisTemplate.opsForValue().set("totalAdvToExcelList_"+cuser.getLogin_name(), mmlist, 20 * 60, TimeUnit.SECONDS);
        PageForList<AdvChaFeeVo> pager = new PageForList<AdvChaFeeVo>( pageNo, pageSize, mmlist);
        String jsonString = "{'ret':1,'company':'"+currUserVo.getCompany()+"','totalCount':"+pager.getTotalRows()+",'root':"+ JSONArray.toJSONString(pager.getResultList())+"}";
        return jsonString;
    }


    @RequestMapping(value="/handle", method = RequestMethod.POST)
    public String handle(AdvChaFeeVo np,String handle, HttpServletRequest request,
                                                HttpServletResponse response) throws IOException, Exception {

        int result = 0;
        try {
            if("add".equals(handle)){
                result = adService.insertAdvChaFee(np);

            }else if("edit".equals(handle)){

                result = adService.updateAdvChaFee(np);
            }else if("del".equals(handle)){

                result = adService.deleteAdvChaFee(np);
            }
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }



    @RequestMapping(value="/export", method = RequestMethod.POST)
    public void export(ModelMap map, HttpServletRequest request,HttpServletResponse response) {

        String token = request.getParameter("token");
        CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);

        //数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        headerMap.put("adv_dt","日期");

        //数据内容
        List<AdvChaFeeVo> list = (List<AdvChaFeeVo>)redisTemplate.opsForValue().get("totalAdvToExcelList_"+cuser.getLogin_name());

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("dateDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String,Object>>();
        Map<String, Object> contentMap = null;
        DecimalFormat df = new DecimalFormat("#.##");
        if (list!=null){
            for(AdvChaFeeVo temp : list){
                contentMap = new LinkedHashMap<String, Object>();
                contentMap.put("adv_dt", temp.getAdv_dt());

                if(temp.getCha_name()!= null && !"".equals(temp.getCha_name())){
                    headerMap.put("cha_name","渠道名称");
                    contentMap.put("cha_name", temp.getCha_name());
                }
                if(temp.getCha_game()!= null && !"".equals(temp.getCha_game())){
                    headerMap.put("cha_game","游戏名称");
                    contentMap.put("cha_game", temp.getCha_game());
                }
                if(temp.getAd_pos() != null && !"".equals(temp.getAd_pos())){
                    headerMap.put("ad_pos","广告形式");
                    contentMap.put("ad_pos", temp.getAd_pos());
                }
                if(temp.getAdv_fee() != null && !"".equals(temp.getAdv_fee())){
                    headerMap.put("adv_fee","广告收入");
                    contentMap.put("adv_fee", temp.getAdv_fee());
                }
                if(temp.getShow_count() != null && !"".equals(temp.getShow_count())){
                    headerMap.put("show_count","展现量");
                    contentMap.put("show_count", temp.getShow_count());
                }
                if(temp.getClick_count() != null && !"".equals(temp.getClick_count())){
                    headerMap.put("click_count","点击量");
                    contentMap.put("click_count", temp.getClick_count());
                }
                if(temp.getClickRate() != null && !"".equals(temp.getClickRate())){
                    headerMap.put("clickRate","点击率");
                    contentMap.put("clickRate", temp.getClickRate());
                }
                if(temp.getEcpm() != null && !"".equals(temp.getEcpm())){
                    headerMap.put("ecpm","ecpm");
                    contentMap.put("ecpm", temp.getEcpm());
                }
                if(temp.getCpc() != null && !"".equals(temp.getCpc())){
                    headerMap.put("cpc","cpc");
                    contentMap.put("cpc", temp.getCpc());
                }

                contentList.add(contentMap);
            }
        }
        String fileName = "cps渠道收入查询_"+ DateTime.now().toString("yyyyMMddHHmmss")+".xls";
        JxlUtil.doSave(fileName,headerMap,contentList,inMap,null,null,request,response);
    }

}
