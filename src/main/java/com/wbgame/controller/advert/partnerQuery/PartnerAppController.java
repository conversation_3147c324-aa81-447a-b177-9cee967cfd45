package com.wbgame.controller.advert.partnerQuery;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.mapper.master.SomeMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.DnChannelInfo;
import com.wbgame.service.AdService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: 合作产品收支查询
 * @author: huangmb
 * @date: 2021/06/25
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/partnerApp")
public class PartnerAppController {

    @Autowired
    private AdService adService;

    @Autowired
    private SomeMapper someMapper;

    /**
     * 新增合作方产品收支查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String cname = request.getParameter("cname");
        String order_str = request.getParameter("order_str");
        String date_group = request.getParameter("date_group");
        String appid_group = request.getParameter("appid_group");
        CurrUserVo cuser = (CurrUserVo) request.getAttribute("LoginUser");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
                start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
                end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            }
            paramMap.put("start_date", start_date);
            paramMap.put("end_date", end_date);
            paramMap.put("cname", cname);
            paramMap.put("order_str", order_str);

            if(cuser.getCompany() != null && !"1".equals(cuser.getCompany())){
                String group1 = "tdate,appid";
                String group2 = "tdate,appid";
                String group3 = "appid";
                String group_match2 = " ON aa.tdate = bb.tdate AND aa.appid = bb.appid";
                String group_match3 = " ON aa.appid = cc.appid";
                // 有一个不为空才进行处理
                if(!BlankUtils.checkBlank(date_group) || !BlankUtils.checkBlank(appid_group)){
                    if(BlankUtils.checkBlank(date_group)){ // 日期不参与分组
                        group1 = "appid";
                        group2 = "appid";
                        group3 = "appid";
                        group_match2 = " ON aa.appid = bb.appid";
                        group_match3 = " ON aa.appid = cc.appid";
                    }
                    if(BlankUtils.checkBlank(appid_group)){ // 应用不参与分组
                        group1 = "tdate";
                        group2 = "tdate";
                        group3 = "1";
                        group_match2 = "ON aa.tdate = bb.tdate";
                        group_match3 = "ON 1=1";
                    }
                }

                paramMap.put("group1", group1);
                paramMap.put("group2", group2);
                paramMap.put("group3", group3);
                paramMap.put("group_match2", group_match2);
                paramMap.put("group_match3", group_match3);

                // 外部合作方账号带审核功能
                paramMap.put("check", "ok");
            }else{
                String group1 = "tdate,appid,cname";
                String group2 = "tdate,appid,cha_id";
                String group3 = "appid,cha_id";
                String group_match2 = " ON aa.tdate = bb.tdate AND aa.appid = bb.appid AND aa.cname = bb.cha_id";
                String group_match3 = " ON aa.appid = cc.appid AND aa.cname = cc.cha_id";
                if(BlankUtils.checkBlank(date_group)){ // 日期不参与分组
                    group1 = group1.replace("tdate,", "");
                    group2 = group2.replace("tdate,", "");
                    group_match2 = group_match2.replace("aa.tdate = bb.tdate AND", "");
                }
                if(BlankUtils.checkBlank(appid_group)){ // 应用不参与分组
                    group1 = group1.replace("appid,", "");
                    group2 = group2.replace("appid,", "");
                    group3 = group3.replace("appid,", "");
                    group_match2 = group_match2.replace("aa.appid = bb.appid AND", "");
                    group_match3 = group_match3.replace("aa.appid = cc.appid AND", "");
                }

                paramMap.put("group1", group1);
                paramMap.put("group2", group2);
                paramMap.put("group3", group3);
                paramMap.put("group_match2", group_match2);
                paramMap.put("group_match3", group_match3);
            }

            // 不为管理员用户 则添加应用控制
            if(!"root".equals(CommonUtil.userMap.get(cuser.getLogin_name()).getOrg_id())){
                String app_group_c = CommonUtil.userMap.get(cuser.getLogin_name()).getApp_group_c();
                paramMap.put("appid", app_group_c);
                result.put("appList", app_group_c.split(","));
            }else{
                result.put("appList", new String[0]);
            }
            if(!BlankUtils.checkBlank(appid))
                paramMap.put("appid", appid);

            PageHelper.startPage(pageNo, pageSize);
            List<Map<String, Object>> list = adService.selectPartnerApp(paramMap);

            Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
            list.forEach(act -> {
                if(!BlankUtils.checkBlank(date_group))
                    act.put("tdate", act.get("tdate")+"");

                Map<String, Object> app = appMap.get(act.get("appid")+"");
                if(app != null && !BlankUtils.checkBlank(appid_group))
                    act.put("appname", app.get("app_name")+"");
            });

            long size = ((Page) list).getTotal();

            String query = "SELECT CONCAT(tdate,'') tdate,ischeck FROM umeng_channel_check where tdate >= '"+start_date+"' order by tdate";
            List<Map<String, Object>> check = adService.queryListMap(query);

            result.put("ret", 1);
            result.put("data", list);
            result.put("check", check);
            result.put("total", adService.selectPartnerAppSum(paramMap));
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
    }

    /**
     * 合作方产品收支导出
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/export", method={RequestMethod.GET, RequestMethod.POST})
    public void export(HttpServletRequest request,HttpServletResponse response) {

        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");
        String cname = request.getParameter("cname");
        String order_str = request.getParameter("order_str");
        String date_group = request.getParameter("date_group");
        String appid_group = request.getParameter("appid_group");

        CurrUserVo cuser = (CurrUserVo) request.getAttribute("LoginUser");
        Map<String, Object> paramMap = new HashMap<String, Object>();
        if(BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)){
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);
        paramMap.put("cname", cname);
        paramMap.put("order_str", order_str);

        if(cuser.getCompany() != null && !"1".equals(cuser.getCompany())){
            String group1 = "tdate,appid";
            String group2 = "tdate,appid";
            String group3 = "appid";
            String group_match2 = " ON aa.tdate = bb.tdate AND aa.appid = bb.appid";
            String group_match3 = " ON aa.appid = cc.appid";
            if(BlankUtils.checkBlank(date_group)){ // 日期不参与分组
                group1 = "appid";
                group2 = "appid";
                group3 = "appid";
                group_match2 = " ON aa.appid = bb.appid";
                group_match3 = " ON aa.appid = cc.appid";
            }
            if(BlankUtils.checkBlank(appid_group)){ // 应用不参与分组
                group1 = "tdate";
                group2 = "tdate";
                group3 = "1";
                group_match2 = "ON aa.tdate = bb.tdate";
                group_match3 = "ON 1=1";
            }

            paramMap.put("group1", group1);
            paramMap.put("group2", group2);
            paramMap.put("group3", group3);
            paramMap.put("group_match2", group_match2);
            paramMap.put("group_match3", group_match3);

            // 外部合作方账号带审核功能
            paramMap.put("check", "ok");
        }else{
            String group1 = "tdate,appid,cname";
            String group2 = "tdate,appid,cha_id";
            String group3 = "appid,cha_id";
            String group_match2 = " ON aa.tdate = bb.tdate AND aa.appid = bb.appid AND aa.cname = bb.cha_id";
            String group_match3 = " ON aa.appid = cc.appid AND aa.cname = cc.cha_id";
            if(BlankUtils.checkBlank(date_group)){ // 日期不参与分组
                group1 = group1.replace("tdate,", "");
                group2 = group2.replace("tdate,", "");
                group_match2 = group_match2.replace("aa.tdate = bb.tdate AND", "");
            }
            if(BlankUtils.checkBlank(appid_group)){ // 应用不参与分组
                group1 = group1.replace("appid,", "");
                group2 = group2.replace("appid,", "");
                group3 = group3.replace("appid,", "");
                group_match2 = group_match2.replace("aa.appid = bb.appid AND", "");
                group_match3 = group_match3.replace("aa.appid = cc.appid AND", "");
            }

            paramMap.put("group1", group1);
            paramMap.put("group2", group2);
            paramMap.put("group3", group3);
            paramMap.put("group_match2", group_match2);
            paramMap.put("group_match3", group_match3);
        }

        // 不为管理员用户 则添加应用控制
        if(!"root".equals(CommonUtil.userMap.get(cuser.getLogin_name()).getOrg_id())){
            paramMap.put("appid", CommonUtil.userMap.get(cuser.getLogin_name()).getApp_group_c());
        }
        if(!BlankUtils.checkBlank(appid))
            paramMap.put("appid", appid);

        List<Map<String, Object>> contentList = adService.selectPartnerApp(paramMap);
        // 审核状态
        String query = "SELECT CONCAT(tdate,'') tdate,ischeck FROM umeng_channel_check where tdate >= '"+start_date+"' order by tdate";
        List<Map<String, Object>> check = adService.queryListMap(query);

        // 匹配实时应用表中的名称
        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap();
        contentList.forEach(act -> {
            if(!BlankUtils.checkBlank(date_group))
                act.put("tdate", act.get("tdate")+"");

            Map<String, Object> app = appMap.get(act.get("appid")+"");
            if(app != null && !BlankUtils.checkBlank(appid_group))
                act.put("appname", app.get("app_name")+"");

            //此处处理导出审核状态字段缺失问题
            String tdate = act.get("tdate") + "";
            Map<String, Object> ischeckMap = check.stream().filter(t -> tdate.equals(t.get("tdate") + "")).findFirst().orElse(null);
            String ischeck = "未处理";
            if (ischeckMap != null) {
                ischeck = "已核查";
            }
            act.put("status", ischeck);

        });


        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = request.getParameter("value");
        //自定义列数据

        try {
            String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                headerMap.put(s[0],s[1]);
            }
        }catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("自定义列导出异常");
        }
        if(!"1".equals(cuser.getCompany())){
            headerMap.remove("cname");
        }


        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "合作方产品收支查询_" + DateTime.now().toString("yyyyMMdd")+ ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,request, response);
    }

    @RequestMapping(value="/handle", method = RequestMethod.POST)
    public String handle(String handle, HttpServletRequest request,
                                   HttpServletResponse response) throws IOException, Exception {

        Map<String, String> valMap = new HashMap<String, String>();

        Map<String, String[]> parameterMap = request.getParameterMap();
        Iterator<Map.Entry<String, String[]>> iterator = parameterMap.entrySet().iterator();
        while(iterator.hasNext()){
            Map.Entry<String, String[]> next = iterator.next();
            valMap.put(next.getKey(), next.getValue()[0]);
        }

        List<DnChannelInfo> chaList = someMapper.selectDnChannelRat();
        for (int i = 0; i < chaList.size(); i++) {
            if(valMap.get("cname").equals(chaList.get(i).getChaId())){
                BigDecimal rot = new BigDecimal(chaList.get(i).getChaRatio());
                BigDecimal adv = new BigDecimal(valMap.get("adv_income"));
                valMap.put("invest_amount", rot.multiply(adv).toString());
                break;
            }
        }

        int result = 0;
        try {
            if("edit".equals(handle)){

                String sql = "insert into umeng_channel_cost (tdate,appid,cha_id,invest_amount,adv_income,billing_income) "+
                        "values(#{obj.tdate},#{obj.appid},#{obj.cname},#{obj.invest_amount},#{obj.adv_income},#{obj.billing_income}) "+
                        "ON DUPLICATE KEY UPDATE invest_amount=VALUES(invest_amount),adv_income=VALUES(adv_income),billing_income=VALUES(billing_income) ";
                result = adService.execSqlHandle(sql, valMap);

            }

            if(result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }
    /**
     * 合作方产品收支审核
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/check", method={RequestMethod.POST})
    public String check(HttpServletRequest request,HttpServletResponse response) throws IOException {

        String tdate = request.getParameter("tdate");
        CurrUserVo cuser = (CurrUserVo) request.getAttribute("LoginUser");

        if(BlankUtils.sqlValidate(tdate))
            return "{\"ret\":0,\"msg\":\"param is error!\"}";

        try {
            // 操作行为记录
            String sql = " insert into umeng_channel_check_log(tdate,ischeck,flag,cuser,date) values('"+tdate+"', 1, 'haiwai1', '"+cuser.getLogin_name()+"', now())";
            adService.execSql(sql);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            String sql = " replace into umeng_channel_check(tdate,ischeck) values('"+tdate+"', 1)";
            adService.execSql(sql);

            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

    /**
     * 合作方产品收支审核.haiwai2
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="/checkHaiwai2", method={RequestMethod.POST})
    public String checkHaiwai2(HttpServletRequest request,HttpServletResponse response) throws IOException {

        String tdate = request.getParameter("tdate");
        CurrUserVo cuser = (CurrUserVo) request.getAttribute("LoginUser");

        if(BlankUtils.sqlValidate(tdate))
            return "{\"ret\":0,\"msg\":\"param is error!\"}";

        try {
            // 操作行为记录
            String sql = " insert into umeng_channel_check_log(tdate,ischeck,flag,cuser,date) values('"+tdate+"', 1, 'haiwai2', '"+cuser.getLogin_name()+"', now())";
            adService.execSql(sql);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            String sql = " replace into umeng_channel_check_haiwai2(tdate,ischeck) values('"+tdate+"', 1)";
            adService.execSql(sql);

            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

}
