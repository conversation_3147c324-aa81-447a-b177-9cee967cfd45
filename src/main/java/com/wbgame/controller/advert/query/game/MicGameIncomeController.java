package com.wbgame.controller.advert.query.game;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.advert.MicGameIncomeVo;
import com.wbgame.service.AdService;
import com.wbgame.service.advert.MicGameIncomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.ExportExcelUtil;
import jxl.Sheet;
import jxl.Workbook;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname MicGameIncomeController
 * @Description TODO
 * @Date 2022/4/12 19:53
 */

@CrossOrigin
@RestController
@RequestMapping("/advert/micGameIncome")
public class MicGameIncomeController {

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    private AdService adService;

    @Autowired
    MicGameIncomeService micGameIncomeService;

    private static final Logger logger = LoggerFactory.getLogger(MicGameIncomeController.class);

    /**
     * 小游戏收支汇总表-查询
     * @param request
     * @return
     */
    @RequestMapping("getList")
    public Object getList(HttpServletRequest request) {
        JSONObject ret = new JSONObject();
        // token验证
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize);
        Map<String, String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");


        String group = "DATE_FORMAT(tdate,'%Y-%m-%d')";
        String week_group = request.getParameter("week_group");
        String month_group = request.getParameter("month_group");
        String year_group = request.getParameter("year_group");
        String quarter_group = request.getParameter("quarter_group");
        String new_group = request.getParameter("group");

        if (!BlankUtils.checkBlank(month_group)){
            group = "DATE_FORMAT(tdate,'%Y-%m')";
        } else if (!BlankUtils.checkBlank(year_group)) {
            group = "DATE_FORMAT(tdate,'%Y')";
        } else if (!BlankUtils.checkBlank(week_group)){
            group = "DATE_FORMAT(tdate,'%x-%v')";
        } else if (!BlankUtils.checkBlank(quarter_group)){
            group = "CONCAT(YEAR(tdate), '-Q', QUARTER(tdate))";
        }

        if (BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)) {
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);

        String order_str = request.getParameter("order_str");
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);
        paramMap.put("appid", appid);
        paramMap.put("group", group);
        paramMap.put("order_str", order_str);
        paramMap.put("new_group",new_group);

        List<MicGameIncomeVo> list = micGameIncomeService.getMicGameIncomeDataList(paramMap);
        MicGameIncomeVo total = micGameIncomeService.getMicGameIncomeDataListSum(paramMap);

        long size = ((Page) list).getTotal();
        for (MicGameIncomeVo po : list) {
            //处理sum(聚合函数)返回null 条目问题
            if (po == null) {
                list = new ArrayList<>();
                size = 0;
                break;
            }
            String tdate = po.getTdate();
            if (!BlankUtils.checkBlank(week_group) && !BlankUtils.checkBlank(tdate)) {
                // 当为周维度数据，对tdate进行处理操作
                String toWeek = DateUtil.convertDateToWeek(tdate, "至",":","");
                int year = Integer.parseInt(tdate.substring(0, 4));
                po.setTdate(year + "年" + toWeek);
            }
        }
        ret.put("ret", 1);
        ret.put("msg", "ok");
        ret.put("data", list);
        ret.put("total", total);
        ret.put("totalSize", size);
        return ret;
    }

    /**
     * 小游戏收支汇总表-导出
     * @param request
     * @param response
     */
    @RequestMapping("export")
    public void export(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> paramMap = new HashMap<>();
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        String appid = request.getParameter("appid");


        String group = "DATE_FORMAT(tdate,'%Y-%m-%d')";
        String week_group = request.getParameter("week_group");
        String month_group = request.getParameter("month_group");
        String year_group = request.getParameter("year_group");
        String quarter_group = request.getParameter("quarter_group");
        String new_group = request.getParameter("group");

        if (!BlankUtils.checkBlank(month_group)){
            group = "DATE_FORMAT(tdate,'%Y-%m')";
        } else if (!BlankUtils.checkBlank(year_group)) {
            group = "DATE_FORMAT(tdate,'%Y')";
        } else if (!BlankUtils.checkBlank(week_group)){
            group = "DATE_FORMAT(tdate,'%x-%v')";
        } else if (!BlankUtils.checkBlank(quarter_group)){
            group = "CONCAT(YEAR(tdate), '-Q', QUARTER(tdate))";
        }

        if (BlankUtils.checkBlank(start_date) || BlankUtils.checkBlank(end_date)) {
            start_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
            end_date = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        }
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);

        String order_str = request.getParameter("order_str");
        paramMap.put("start_date", start_date);
        paramMap.put("end_date", end_date);
        paramMap.put("appid", appid);
        paramMap.put("group", group);
        paramMap.put("order_str", order_str);
        paramMap.put("new_group",new_group);

        // 数据内容
        String query = "select concat(id,'') as mapkey,app_name from yyhz_0308.app_info ";
        Map<String, Map<String, Object>> appMap = adService.queryListMapOfKey(query);

        List<MicGameIncomeVo> list = micGameIncomeService.getMicGameIncomeDataList(paramMap);
        for (MicGameIncomeVo vo : list) {
            if (vo != null) {
                vo.setAppname(appMap.get(vo.getAppid()) != null ? appMap.get(vo.getAppid()).get("app_name") + "" : "");
            } else {
                list = new ArrayList<>();
                break;
            }
            String tdate = vo.getTdate();
            if (!BlankUtils.checkBlank(week_group) && !BlankUtils.checkBlank(tdate)) {
                // 当为周维度数据，对tdate进行处理操作
                String toWeek = DateUtil.convertDateToWeek(tdate, "至",":","");
                int year = Integer.parseInt(tdate.substring(0, 4));
                vo.setTdate(year + "年" + toWeek);
            }
        }
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)) {
            try {
                String[] split = value.split(";");
                for (int i = 0; i < split.length; i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0], s[1]);
                }
            } catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }
        ExportExcelUtil.exportXLSX2(response, list, headerMap, "小游戏收支汇总_" + DateTime.now().toString("yyyyMMdd") + ".xlsx");
    }

    /**
     * 小游戏收支上传广告金
     *
     * @param request
     * @param file
     * @return
     */
    @RequestMapping("batchImportAdRevenue")
    public Object batchImport(HttpServletRequest request, @RequestParam(value = "fileName") MultipartFile file) {
        JSONObject ret = new JSONObject();
        try {
            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<MicGameIncomeVo> list = new ArrayList<>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);
                int column = sheet.getColumns();
                int row = sheet.getRows();
                DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;
                    String[] vals = new String[column];
                    for (int c = 0; c < column; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    MicGameIncomeVo vo = new MicGameIncomeVo();
                    String tdate = vals[0];
                    boolean flag = true;
                    if (!BlankUtils.checkBlank(tdate)) {
                        try {
                            tdate = DateTime.parse(tdate, format).toString("yyyy-MM-dd");
                        } catch (Exception e) {
                            flag = false;
                        }
                    } else {
                        flag = false;
                    }
                    if (!flag) {
                        return ReturnJson.toErrorJson("第" + r + "行日期格式不正确");
                    }
                    vo.setTdate(tdate);
                    vo.setAppid(vals[1]);
                    String ad_revenue = vals[2];
                    if (!BlankUtils.checkBlank(ad_revenue)){
                        if (!BlankUtils.isNumeric(ad_revenue)){
                            return ReturnJson.toErrorJson("第" + r + "行广告金格式不正确");
                        }
                    }
                    String pay_ad_revenue = vals[3];
                    if (!BlankUtils.checkBlank(pay_ad_revenue)){
                        if (!BlankUtils.isNumeric(pay_ad_revenue)){
                            return ReturnJson.toErrorJson("第" + r + "行内购广告金格式不正确");
                        }
                    }
                    String refund_revenue = vals[4];
                    if (!BlankUtils.checkBlank(refund_revenue)){
                        if (!BlankUtils.isNumeric(refund_revenue)){
                            return ReturnJson.toErrorJson("第" + r + "行退款格式不正确");
                        }
                    }
                    vo.setAd_revenue(ad_revenue);
                    vo.setPay_ad_revenue(pay_ad_revenue);
                    vo.setRefund_revenue(refund_revenue);
                    list.add(vo);
                }
                if (list.size() > 0) {
                    micGameIncomeService.updateMicGameIncomeAdRevenue(list);
                    return ReturnJson.success();
                }
            } else {
                return ReturnJson.toErrorJson("上传文件有误，需要.xls格式文件");
            }

        } catch (Exception e) {
            logger.error("上传文件失败,请联系管理员!错误信息:",e);
            return ReturnJson.toErrorJson("上传文件失败,请联系管理员!错误信息:" + e.getMessage());
        }
        return ret;
    }

    /**
     * 小游戏收支操作接口
     * @param request
     * @return
     */
    @RequestMapping("handle")
    public Object handle(HttpServletRequest request) {
        JSONObject ret = new JSONObject();
        String token = request.getParameter("token");
        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        String handle = request.getParameter("handle");
        String username = "";
        CurrUserVo currUserVo = (CurrUserVo) redisTemplate.opsForValue().get(token);
        if (currUserVo != null) {
            username = currUserVo.getLogin_name();
        }
        MicGameIncomeVo vo = new MicGameIncomeVo();
        String ad_revenue = request.getParameter("ad_revenue");
        String pay_ad_revenue = request.getParameter("pay_ad_revenue");
        String refund_revenue = request.getParameter("refund_revenue");
        String appid = request.getParameter("appid");
        String tdate = request.getParameter("tdate");
        String start_date = request.getParameter("start_date");
        String end_date = request.getParameter("end_date");
        vo.setModify_user(username);
        if ("updateAdRevenue".equals(handle)) {
            List<MicGameIncomeVo> list = new ArrayList<>();
            vo.setAd_revenue(ad_revenue);
            vo.setPay_ad_revenue(pay_ad_revenue);
            vo.setRefund_revenue(refund_revenue);
            vo.setAppid(appid);
            vo.setTdate(tdate);
            vo.setModify_user(username);
            list.add(vo);
            int num = micGameIncomeService.updateMicGameIncomeAdRevenue(list);
            if (num>0){
                return ReturnJson.success();
            }
        } else if ("sync".equals(handle)) {
            StringBuffer stringBuffer = new StringBuffer();
            if (BlankUtils.checkBlank(end_date)||BlankUtils.checkBlank(end_date)){
                return ReturnJson.toErrorJson("必填参数为空!");
            }
            List<String> dateList = DateUtil.getDays(start_date,end_date);
            if (dateList.size()>30){
                return ReturnJson.toErrorJson("一次最多只能拉取30天数据!");
            }
            int num = dateList.size();
            int succNum = 0;
            for (String date:dateList){
                int succ = micGameIncomeService.syncMicGameIncomeData(date);
                if (succ>0){
                    succNum ++ ;
                }else {
                    stringBuffer.append("日期:"+date+"数据同步失败;");
                }
            }
            if (succNum==num){
                return ReturnJson.success();
            }else {
                ReturnJson.toErrorJson(stringBuffer.toString());
            }
        }
        return ReturnJson.toErrorJson("操作失败!");
    }
}
