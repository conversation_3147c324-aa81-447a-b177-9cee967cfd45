package com.wbgame.controller.advert.query.launch;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.custom.AdTotalHourTwoVo;
import com.wbgame.service.AdService;
import com.wbgame.utils.BlankUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @description: 实时新增查询
 * @author: huangmb
 * @date: 2021/06/05
 **/
@CrossOrigin
@RestController
@RequestMapping("/advert/realNewReport")
public class RealNewReportController {

    @Autowired
    private AdService adService;

    @RequestMapping(value="list", method = {RequestMethod.GET, RequestMethod.POST})
    public String list(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String sdate = request.getParameter("sdate");
        String edate = request.getParameter("edate");
        String adtype = request.getParameter("adtype"); // 1为自推广，2为市场，3为两者合计
        String ctype = request.getParameter("ctype"); // new-新增报表，active-活跃报表
        String pid = request.getParameter("pid");

        JSONObject result = new JSONObject();
        try {
            // 筛除sql敏感字符注入
            if(BlankUtils.sqlValidate(sdate)
                    || BlankUtils.sqlValidate(edate)
                    || BlankUtils.sqlValidate(adtype)
                    || BlankUtils.sqlValidate(ctype)){
                return "{\"ret\":0,\"msg\":\"param is error!\"}";
            }

            if(BlankUtils.checkBlank(sdate) || BlankUtils.checkBlank(edate)){
                sdate = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
                edate = DateTime.now().toString("yyyy-MM-dd");
            }

            String table_name = "new_count_hour_two";

            String pro = ",hour00 hour00";
            for (int i = 1; i < 24; i++) {

                if(i < 10){
                    pro += ",(hour0"+i+"- hour0"+(i-1)+") hour0"+i;
                }else if(i==10){
                    pro += ",(hour10 - hour09) hour10";
                }else{
                    pro += ",(hour"+i+"- hour"+(i-1)+") hour"+i;
                }
                //pro += ",hour"+i+" hour"+i;
            }
            String sql = "select tdate,pid"+pro+" from "+table_name+" where tdate in ('"+sdate+"','"+edate+"') ";

            if(!BlankUtils.checkBlank(adtype))
                sql += " and adtype = "+adtype;
            if(!BlankUtils.checkBlank(pid))
                sql += " and pid = "+pid;
            sql += " group by tdate";

            List<AdTotalHourTwoVo> list = adService.queryHourReport(sql);
            for (AdTotalHourTwoVo adTotalHourTwoVo : list) {
                adTotalHourTwoVo.setTotal(adTotalHourTwoVo.getHour00()
                        +adTotalHourTwoVo.getHour01()+adTotalHourTwoVo.getHour02()+adTotalHourTwoVo.getHour03()+adTotalHourTwoVo.getHour04()+adTotalHourTwoVo.getHour05()
                        +adTotalHourTwoVo.getHour06()+adTotalHourTwoVo.getHour07()+adTotalHourTwoVo.getHour08()+adTotalHourTwoVo.getHour09()+adTotalHourTwoVo.getHour10()
                        +adTotalHourTwoVo.getHour11()+adTotalHourTwoVo.getHour12()+adTotalHourTwoVo.getHour13()+adTotalHourTwoVo.getHour14()+adTotalHourTwoVo.getHour15()
                        +adTotalHourTwoVo.getHour16()+adTotalHourTwoVo.getHour17()+adTotalHourTwoVo.getHour18()+adTotalHourTwoVo.getHour19()+adTotalHourTwoVo.getHour20()
                        +adTotalHourTwoVo.getHour21()+adTotalHourTwoVo.getHour22()+adTotalHourTwoVo.getHour23());
            }

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", list.size());
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }
}
