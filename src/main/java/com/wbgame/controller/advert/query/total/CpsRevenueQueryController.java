package com.wbgame.controller.advert.query.total;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Asserts;
import com.wbgame.service.AdService;
import com.wbgame.service.adv2.YyhzService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @description: CPS收入查询
 * @author: caow
 * @date: 2022/07/06
 **/
@CrossOrigin
@RestController
@RequestMapping("/advert/cpsRevenueQuery")
public class CpsRevenueQueryController {

    @Autowired
    private AdService adService;
    @Autowired
    private YyhzService yyhzService;

    /**
     * CPS收入查询.查询
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value="list", method={RequestMethod.GET, RequestMethod.POST})
    public String list(HttpServletRequest request, HttpServletResponse response) throws IOException {

        String[] args = {"sdate","edate","appid","group"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        JSONObject result = new JSONObject();
        try {
        	String sql = "select tdate,appid,SUM(ad_revenue) ad_revenue from cps_revenue_query where tdate BETWEEN #{obj.sdate} AND #{obj.edate} ";
        	if(!BlankUtils.checkBlank(paramMap.get("appid")))
        		sql += " and appid in ("+paramMap.get("appid")+") ";
        	
        	sql += " group by appid";
        	if(!BlankUtils.checkBlank(paramMap.get("group")))
        		sql += ","+paramMap.get("group");
        	
        	
            PageHelper.startPage(paramMap);
            List<Map<String, Object>> list = yyhzService.queryListMapTwo(sql, paramMap);
            long size = ((Page) list).getTotal();
            
            Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
            list.forEach(act -> {
                act.put("tdate", act.get("tdate")+"");
                Map<String, Object> app = appMap.get(act.get("appid")+"");
                if(app != null)
                    act.put("appname", app.get("app_name"));
            });

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
            
            String sum = "select sum(ad_revenue) ad_revenue from cps_revenue_query where tdate BETWEEN #{obj.sdate} AND #{obj.edate}";
            if(!BlankUtils.checkBlank(paramMap.get("appid")))
            	sum += " and appid in ("+paramMap.get("appid")+") ";
            
            List<Map<String, Object>> sumList = yyhzService.queryListMapTwo(sum, paramMap);
            if(sumList != null && !sumList.isEmpty())
            	result.put("total", sumList.get(0));
            
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: "+e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * CPS收入查询.导出
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "export", method={RequestMethod.GET, RequestMethod.POST})
    public void export(HttpServletRequest request,HttpServletResponse response) {

        Map<String, String> headerMap = new LinkedHashMap<String, String>();

        String[] args = {"sdate","edate","appid","group"};
        Map<String, String> paramMap = BlankUtils.getParameter(request, args);

        String sql = "select tdate,appid,SUM(ad_revenue) ad_revenue from cps_revenue_query where tdate BETWEEN #{obj.sdate} AND #{obj.edate} ";
        if(!BlankUtils.checkBlank(paramMap.get("appid")))
    		sql += " and appid in ("+paramMap.get("appid")+") ";
        
        sql += " group by appid";
    	if(!BlankUtils.checkBlank(paramMap.get("group")))
    		sql += ","+paramMap.get("group");
        
        List<Map<String, Object>> contentList = yyhzService.queryListMapTwo(sql, paramMap);
        
        // 赋值应用名称
        Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
        contentList.forEach(act -> {
            act.put("tdate", act.get("tdate")+"");
            Map<String, Object> app = appMap.get(act.get("appid")+"");
            if(app != null)
                act.put("appname", app.get("app_name"));
        });

        String value = request.getParameter("value");
        if (!BlankUtils.checkBlank(value)){
            //自定义列数据
            try {
                String[] split = value.split(";");
                for (int i = 0;i<split.length;i++) {
                    String[] s = split[i].split(",");
                    headerMap.put(s[0],s[1]);
                }
            }catch (Exception e) {
                e.printStackTrace();
                Asserts.fail("自定义列导出异常");
            }
        }

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");

        String fileName = "收入查询_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
//        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null, request, response);

        ExportExcelUtil.exportXLSX(response, contentList, headerMap, fileName);
    }

}
