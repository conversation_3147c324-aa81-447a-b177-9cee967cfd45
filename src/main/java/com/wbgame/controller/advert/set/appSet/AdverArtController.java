package com.wbgame.controller.advert.set.appSet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.master.AdArtGroupConfigMapper;
import com.wbgame.mapper.master.WbSysV3Mapper;
import com.wbgame.mapper.slave.WbSysMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.adv2.AdArtGroupVo;
import com.wbgame.pojo.jettison.report.dto.ArtistDTO;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Classname AdverArtController
 * @Description TODO
 * @Date 2021/5/6 9:59
 */
@CrossOrigin
@RestController
@RequestMapping("/advert/artGroup")
public class AdverArtController {

    @Autowired
    private AdArtGroupConfigMapper adArtGroupConfigMapper;

    @Autowired
    private WbSysV3Mapper wbSysV3Mapper;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 美术组配置查询
     * @param request
     * @return
     */
    @RequestMapping("selectArtGroupConfig")
    public Object selectArtGroupConfig(HttpServletRequest request,AdArtGroupVo vo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        };
        int start = 1;
        int limit = 999;
        if(!BlankUtils.checkBlank(request.getParameter("start")) && !BlankUtils.checkBlank(request.getParameter("limit"))){
            start = Integer.parseInt(request.getParameter("start"));
            limit = Integer.parseInt(request.getParameter("limit"));
        };
        PageHelper.startPage(start,limit);
        Page<AdArtGroupVo> reportPage = adArtGroupConfigMapper.selectAdArtGroupConfig(vo);
        List<AdArtGroupVo> list = reportPage.getResult();
        //特殊处理全部的时候
        if ("all".equals(request.getParameter("artGroupId"))){
            AdArtGroupVo allVo = new AdArtGroupVo();
            allVo.setArtGroupId("all");
            allVo.setArtGroupName("全部");
            List<CurrUserVo> userList = wbSysV3Mapper.selectArtOrgUserInfo();
            StringBuffer sb =new StringBuffer();
            for (int i=0;i<userList.size();i++){
                if (i==userList.size()-1){
                    sb.append(userList.get(i).getNick_name());
                }else{
                    sb.append(userList.get(i).getNick_name()).append(",");
                }
            }
            allVo.setArtGroupConfig(sb.toString());
            list.add(0,allVo);
        }
        JSONObject result = new JSONObject();
        result.put("ret", 1);
        result.put("data", list);
        result.put("msg","success");
        result.put("totalCount", reportPage.getTotal());
        return result;
    }

    /**
     * 操作美术组配置
     * @param request
     * @param adArtGroupVo
     * @return
     */
    @RequestMapping("handle")
    public Object handleArtGroupConfig(HttpServletRequest request,AdArtGroupVo adArtGroupVo){
        // token验证
        String token = request.getParameter("token");
        if(BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        }
        else {
            redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
        }
        int result = 0;
        adArtGroupVo.setModifyUser("admin");
        if ("add".equals(request.getParameter("handle"))) {
            result = adArtGroupConfigMapper.insertAdArtGroupConfig(adArtGroupVo);
        } else if ("edit".equals(request.getParameter("handle"))) {
            result = adArtGroupConfigMapper.updateAdArtGroupConfig(adArtGroupVo);
        } else if ("del".equals(request.getParameter("handle"))) {
            //更新权限表
            adArtGroupConfigMapper.updateMarketFindAdArtGroupId(adArtGroupVo);
            //删除配置
            result = adArtGroupConfigMapper.delAdArtGroupConfig(adArtGroupVo);
        }
        if (result > 0) {
            return "{\"ret\":1,\"msg\":\"操作成功!\"}";
        } else {
            return "{\"ret\":0,\"msg\":\"无效操作!\"}";
        }
    }

    @RequestMapping(value="selectArtOrgUserInfo", method={RequestMethod.GET,RequestMethod.POST})
    public String getUserInfo(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response){

        /* 进行token验证 */
        if (BlankUtils.checkBlank(cu.getToken())) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        };
        if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
            return "{\"ret\":2,\"msg\":\"token is error!\"}";
        } else {
            redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                    TimeUnit.SECONDS);
        };

        JSONObject result = new JSONObject();
        try {
            List<CurrUserVo> list = wbSysV3Mapper.selectArtOrgUserInfo();
            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", list.size());
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误消息："+e.getMessage());
        }
        return result.toJSONString();
    }
}
