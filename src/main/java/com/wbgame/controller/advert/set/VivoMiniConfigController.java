package com.wbgame.controller.advert.set;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.google.common.base.Strings;
import com.wbgame.aop.*;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.mapper.redpack.HbMapper;
import com.wbgame.pojo.AppInfo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.operate.VivoMiniConfigVo;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.jettison.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@RestController
@CrossOrigin
@RequestMapping(value = "/operate/vivoMiniConfig")
@Api(tags = "vivo小游戏配置")
@ApiSupport(author = "huangmb")
@LoginCheck
public class VivoMiniConfigController {

    @Resource
    public HbMapper hbMapper;
    
    @Autowired
    private HttpServletRequest request;

    @Resource
    private YyhzMapper yyhzMapper;

    @RequestMapping(value = "/list")
    @NewPageLimit
    @ApiOperation(value = "查询", notes = "查询vivo小游戏配置", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = VivoMiniConfigVo.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String list(@ApiNeed({"order","start","limit","appid","tappid","pkgName"}) VivoMiniConfigVo param){
        List<VivoMiniConfigVo> list = hbMapper.selectVivoMiniConfigs(param);
        convertApp(list);
        JSONObject jsonObject = new JSONObject();
        long size = ((Page) list).getTotal();
        jsonObject.put("total",size);
        jsonObject.put("list",list);
        return ReturnJson.success(jsonObject);
    }

    @RequestMapping(value = "/export")
    @ApiOperation(value = "导出", notes = "导出vivo小游戏配置", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = VivoMiniConfigVo.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public void export(HttpServletResponse response, @ApiNeed({"order","appid","tappid","pkgName","export_file_name","value"}) VivoMiniConfigVo param){
        List<VivoMiniConfigVo> list = hbMapper.selectVivoMiniConfigs(param);
        convertApp(list);
        Map<String,String> head = new LinkedHashMap<>();
        try {
            String[] split = param.getValue().split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
        }catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = param.getExport_file_name() + "_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX2(response,list,head,fileName);
    }


    @RequestMapping(value = "/add")
    @ApiOperation(value = "新增", notes = "新增vivo小游戏配置", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String add(@ApiIgp({"order","start","limit","appName","export_file_name","value"}) VivoMiniConfigVo param) {
        if (BlankUtils.checkBlank(param.getAppid()) || BlankUtils.checkBlank(param.getTappid())
                || BlankUtils.checkBlank(param.getPkgName()) || BlankUtils.checkBlank(param.getAppKey())
                || BlankUtils.checkBlank(param.getAppSecret()) || BlankUtils.checkBlank(param.getTappName())
                || param.getState() == null) {
            return ReturnJson.error(Constants.ParamError);
        }
        VivoMiniConfigVo newParam = new VivoMiniConfigVo();
        newParam.setAppid(param.getAppid());
        if(hbMapper.selectVivoMiniConfigs(newParam).size() > 0) {
            return ReturnJson.toErrorJson("该产品配置已存在");
        }
        CurrUserVo loginUser = (CurrUserVo)request.getAttribute("LoginUser");
        param.setCreate_owner(loginUser.getLogin_name());
        param.setUpdate_owner(loginUser.getLogin_name());
        List<VivoMiniConfigVo> list = new ArrayList<>();
        list.add(param);
        hbMapper.addVivoMiniConfig(list);
        return ReturnJson.success();
    }

    @RequestMapping(value = "/update")
    @ApiOperation(value = "更新", notes = "更新vivo小游戏配置", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String update(HttpServletRequest request,@ApiIgp({"order","start","limit","appName","export_file_name","value"}) VivoMiniConfigVo param) {
        if (BlankUtils.checkBlank(param.getAppid()) || BlankUtils.checkBlank(param.getTappid())
                || BlankUtils.checkBlank(param.getPkgName()) || BlankUtils.checkBlank(param.getAppKey())
                || BlankUtils.checkBlank(param.getAppSecret()) || BlankUtils.checkBlank(param.getTappName())
                || param.getState() == null) {
            return ReturnJson.error(Constants.ParamError);
        }
        CurrUserVo loginUser = (CurrUserVo)request.getAttribute("LoginUser");
        param.setUpdate_owner(loginUser.getLogin_name());
        hbMapper.updateVivoMiniConfig(param);
        return ReturnJson.success();
    }

    @RequestMapping(value = "/delete")
    @ApiOperation(value = "删除", notes = "删除vivo小游戏配置", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String delete(@ApiNeed({"appid"}) VivoMiniConfigVo param) {
        if (BlankUtils.checkBlank(param.getAppid())) {
            return ReturnJson.error(Constants.ParamError);
        }
        hbMapper.deleteVivoMiniConfig(param.getAppid());
        return ReturnJson.success();
    }

    @RequestMapping(value = "/importExcel")
    @ApiOperation(value = "导入", notes = "导入vivo小游戏配置", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String importExcel(@RequestParam(value = "file")MultipartFile multipartFile) throws Exception {
        if (multipartFile.isEmpty()) {
            ReturnJson.toErrorJson("请选择文件上传");
        }

        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());

        Sheet sheet = xssfWorkbook.getSheetAt(0);
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        List<VivoMiniConfigVo> list = new ArrayList<>();

        for (int i = 1; i < physicalNumberOfRows; i++) {
            Row row = sheet.getRow(i);
            if (null == row) {
                continue;
            }
            int physicalNumberOfCells = row.getPhysicalNumberOfCells();

            VivoMiniConfigVo vivoMiniConfigVo = new VivoMiniConfigVo();
            for (int j = 0; j < physicalNumberOfCells; j++) {
                Cell cell = row.getCell(j);
                switch (j) {
                    case 0:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoMiniConfigVo.setAppid(StringUtil.getString(cell.getStringCellValue()));
                        if (!BlankUtils.isNumeric(vivoMiniConfigVo.getAppid())) {
                            return ReturnJson.toErrorJson("第"+(j+1)+"列|第"+(i+1)+"行:导入appid不能为空并且为数字");
                        }
                        VivoMiniConfigVo newParam = new VivoMiniConfigVo();
                        newParam.setAppid(vivoMiniConfigVo.getAppid());
                        if(hbMapper.selectVivoMiniConfigs(newParam).size() > 0) {
                            return ReturnJson.toErrorJson("第"+(j+1)+"列|第"+(i+1)+"行:该产品id配置已存在");
                        }
                        break;
                    case 1:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoMiniConfigVo.setTappid(StringUtil.getString(cell.getStringCellValue()));
                        if (BlankUtils.checkBlank(vivoMiniConfigVo.getTappid())) {
                            return ReturnJson.toErrorJson("第"+(j+1)+"列|第"+(i+1)+"行:导入vivo小游戏应用id不能为空");
                        }
                        break;
                    case 2:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoMiniConfigVo.setTappName(StringUtil.getString(cell.getStringCellValue()));
                        if (BlankUtils.checkBlank(vivoMiniConfigVo.getTappName())) {
                            return ReturnJson.toErrorJson("第"+(j+1)+"列|第"+(i+1)+"行:导入vivo小游戏名称不能为空");
                        }
                        break;
                    case 3:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoMiniConfigVo.setAppKey(StringUtil.getString(cell.getStringCellValue()));
                        if (BlankUtils.checkBlank(vivoMiniConfigVo.getAppKey())) {
                            return ReturnJson.toErrorJson("第"+(j+1)+"列|第"+(i+1)+"行:导入appKey不能为空");
                        }
                        break;
                    case 4:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoMiniConfigVo.setAppSecret(StringUtil.getString(cell.getStringCellValue()));
                        if (BlankUtils.checkBlank(vivoMiniConfigVo.getAppSecret())) {
                            return ReturnJson.toErrorJson("第"+(j+1)+"列|第"+(i+1)+"行:导入appSecret不能为空");
                        }
                        break;
                    case 5:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        vivoMiniConfigVo.setPkgName(StringUtil.getString(cell.getStringCellValue()));
                        if (BlankUtils.checkBlank(vivoMiniConfigVo.getPkgName())) {
                            return ReturnJson.toErrorJson("第"+(j+1)+"列|第"+(i+1)+"行:导入包名不能为空");
                        }
                        break;
                    case 6:
                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        if (!BlankUtils.isNumeric(cell.getStringCellValue())) {
                            return ReturnJson.toErrorJson("第"+(j+1)+"列|第"+(i+1)+"行:导入启用状态不能为空并且为1或0");
                        }
                        vivoMiniConfigVo.setState(StringUtil.getInt(cell.getStringCellValue()));
                        if (vivoMiniConfigVo.getState() != 1 && vivoMiniConfigVo.getState() != 0) {
                            return ReturnJson.toErrorJson("第"+(j+1)+"列|第"+(i+1)+"行:导入启用状态只可选1或0");
                        }
                        break;
                    default:
                        break;
                }
            }

            CurrUserVo loginUser = (CurrUserVo)request.getAttribute("LoginUser");
            vivoMiniConfigVo.setCreate_owner(loginUser.getLogin_name());
            vivoMiniConfigVo.setUpdate_owner(loginUser.getLogin_name());
            list.add(vivoMiniConfigVo);
        }

        if (CollectionUtils.isEmpty(list)) {
            return ReturnJson.toErrorJson("没有数据");
        }

        hbMapper.addVivoMiniConfig(list);

        return ReturnJson.success("导入成功");
    }

    /**
     * 获取产品名称
     * @param list
     */
    public void convertApp (List<VivoMiniConfigVo> list) {
        if (list == null || list.size() == 0) {
            return;
        }
        Map<Integer, AppInfo> apps = yyhzMapper.selectAllAppInfoList();
        for (VivoMiniConfigVo v : list) {
            AppInfo appInfo = apps.get(Integer.parseInt(v.getAppid()));
            if (appInfo != null) {
                v.setAppName(appInfo.getAppName());
            }
        }
    }
    
    
}
