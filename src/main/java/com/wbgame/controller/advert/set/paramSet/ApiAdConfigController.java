package com.wbgame.controller.advert.set.paramSet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.AdPrjVo;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @description: api广告开通配置
 * @author: huangmb
 * @date: 2021/06/25
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/apiAdConfig")
public class ApiAdConfigController {

    @Autowired
    private SomeService someService;

    /**
     * api广告开通配置
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public String list(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");

        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 当前页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        String ad_sid = BlankUtils.checkNull(request, "ad_sid");
        String ad_bd_sid = BlankUtils.checkNull(request, "ad_bd_sid");
        String app_id = BlankUtils.checkNull(request, "app_id");
        Map<Object, Object> parmMap = new HashMap<>();
        parmMap.put("ad_sid", ad_sid);
        parmMap.put("prjid", ad_bd_sid);
        parmMap.put("status", app_id);
        List<AdPrjVo> list = someService.selectApiAdConfig(parmMap);
        long size = ((Page) list).getTotal();
        JSONObject result = new JSONObject();
        result.put("data", list);
        result.put("ret", 1);
        result.put("totalCount", size);
        return result.toJSONString();
    }

    /**
     * api广告开通配置操作
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/handle", method = {RequestMethod.POST})
    public String handle(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {

        String handle = BlankUtils.checkNull(request, "handle");
        String status = BlankUtils.checkNull(request, "status");
        String ad_id = BlankUtils.checkNull(request, "ad_id");
        String prjmid = BlankUtils.checkNull(request, "prjmid");
//            String permin = BlankUtils.checkNull(request, "permin");
//            String permax = BlankUtils.checkNull(request, "permax");
        String lev = BlankUtils.checkNull(request, "lev");
        String add_pos = BlankUtils.checkNull(request, "add_pos");
        String cityid = BlankUtils.checkNull(request, "cityid");
        String scale_val = BlankUtils.checkNull(request, "scale_val");

        int ret = 0;
        AdPrjVo infovo = new AdPrjVo();
        infovo.setAd_pos(add_pos);
        infovo.setAd_sid(ad_id);
        infovo.setOccur_max("100");
        infovo.setOccur_min("1");
        infovo.setPrjmid(prjmid);
        infovo.setStatu(status);
        infovo.setQz_lelve(lev);
        infovo.setCityids(cityid);
        infovo.setScale_value(scale_val);
        if ("add".equals(handle)) {
            ret = someService.insertApiAdConfig(infovo);
        }
        if ("update".equals(handle)) {
            ret = someService.updateApiAdConfig(infovo);
        }
        JSONObject result = new JSONObject();
        if (ret > 0) {
            result.put("ret", 1);
            result.put("msg", "操作成功");
        } else {
            result.put("ret", 0);
            result.put("msg", "操作失败");
        }
        return result.toJSONString();
    }

    /**
     * 删除api广告配置
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    public String delete(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {

        String prjmid = BlankUtils.checkNull(request, "prjmid");
        String ad_sid = BlankUtils.checkNull(request, "ad_sid");
        Map<Object, Object> map = new HashMap<>();
        map.put("prijmid", prjmid);
        map.put("ad_sid", ad_sid);
        JSONObject result = new JSONObject();
        int res = someService.deleteApiAdConfig(map);
        if (res > 0) {
            result.put("ret", 1);
            result.put("msg", "删除成功");
        } else {
            result.put("ret", 0);
            result.put("msg", "删除失败");
        }
        return result.toJSONString();

    }

    /**
     * 打开关闭api广告配置
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/open", method = {RequestMethod.POST})
    public String open(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {

        String prjmid = BlankUtils.checkNull(request, "prjmid");
        String ad_sid = BlankUtils.checkNull(request, "ad_sid");
        String statu = BlankUtils.checkNull(request, "statu");
        if (statu.equals("0")) {
            statu = "1";
        } else {
            statu = "0";
        }
        String[] ad_sids = ad_sid.split(",");
        String[] prijmids = prjmid.split(",");
        int res = 0;
        for (int i = 0; i < ad_sids.length; i++) {
            Map<Object, Object> map = new HashMap<>();
            map.put("prijmid", prijmids[i]);
            map.put("ad_sid", ad_sids[i]);
            map.put("statu", statu);
            res = someService.openApiAdConfig(map);
        }
        JSONObject result = new JSONObject();
        if (res > 0) {
            result.put("ret", 1);
            result.put("msg", "操作成功");
        } else {
            result.put("ret", 0);
            result.put("msg", "操作失败");
        }
        return result.toJSONString();

    }

}
