package com.wbgame.controller.advert.set.launchSet;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.advert.ReyunAppDebugDTO;
import com.wbgame.service.advert.ReyunAppDebugService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: xugx
 * @createDate: 2023/04/11
 * @class: ReyunAppDebugController
 * @description: 该配置主要实现新产品上线联调热云，通过配置上报一些事件至热云，热云后台显示联调通过即可 
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/reyun/debug")
@Api(tags = "热云调试配置")
public class ReyunAppDebugController {
    
	@Autowired
    private ReyunAppDebugService reyunAppDebugService;

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = "/add")
    public Result<Integer> insertDnBusAppLine(HttpServletRequest request,
                                              @ApiNeed({"appkey", "os", "event_name"})
    						ReyunAppDebugDTO record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        record.setCreate_user(loginUser.getLogin_name());;
        return reyunAppDebugService.addDebugRecord(record);

    }

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping(value = "/list")
    public Result<PageResult<ReyunAppDebugDTO>>
    selectDnBusAppLineByCondition(ReyunAppDebugDTO dto) {

        return reyunAppDebugService.selectDebugRecord(dto);

    }

}
