package com.wbgame.controller.advert.set.appSet;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.SDKConfigInfo;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @description: sdk类型配置
 * @author: huangmb
 * @date: 2021/06/25
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/sdkConfigInfo")
public class SdkConfigInfoController {

    @Autowired
    private SomeService someService;

    /**
     *
     * SDK配置查询
     * @param record
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST })
    public String list(HttpServletRequest request, HttpServletResponse response, SDKConfigInfo record) {
        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        // pageStart 和 limit设置值
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        int pageNo = (pageStart / pageSize) + 1;

        JSONObject result = new JSONObject();
        try {

            PageHelper.startPage(pageNo, pageSize); // 进行分页
            List<SDKConfigInfo> list = someService.selectSDKConfigInfo(record);
            long size = ((Page) list).getTotal();

            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 操作
     * @param record
     * @param request
     * @return
     */
    @RequestMapping(value="/handle", method={RequestMethod.GET,RequestMethod.POST})
    public String handle(SDKConfigInfo record, HttpServletRequest request){
        String token = request.getParameter("token");
        String handle = request.getParameter("handle");

        JSONObject result = new JSONObject();

        int ret = 0;

        try {
            if("add".equals(handle)){
                ret = someService.insertSDKConfigInfo(record);
            }

            if("edit".equals(handle)){
                ret = someService.updateSDKConfigInfo(record);
            }

            if(ret > 0) {
                result.put("ret", 1);
                result.put("msg", "success");
            }else {
                result.put("ret", 0);
                result.put("msg", "无效操作");
            }
            return result.toJSONString();

        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "错误信息 : "+e.getMessage());
            return result.toJSONString();
        }
    }

}
