package com.wbgame.controller.advert.set.operationconfig;

import com.wbgame.aop.ApiNeed;
import com.wbgame.common.Asserts;
import com.wbgame.common.QueryGroup;
import com.wbgame.common.UpdateGroup;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.mobile.DnBusAppLineDTO;
import com.wbgame.pojo.mobile.DnBusAppLineVO;
import com.wbgame.service.mobile.IDnBusAppLineService;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.Default;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: zhangY
 * @createDate: 2022/8/23
 * @class: Line
 * @description:
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/operate/busLineConfig")
@Api(tags = "业务线配置")
public class BusLineController {
    
    
    private IDnBusAppLineService dnBusAppLineService;

    @Autowired
    public void setDnBusAppLineService(IDnBusAppLineService dnBusAppLineService) {
        this.dnBusAppLineService = dnBusAppLineService;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping(value = "/deleteDnBusAppLineById")
    public Result<Integer> deleteDnBusAppLineById(@RequestParam("idList") List<Integer> id) {

        return dnBusAppLineService.deleteDnBusAppLineById(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = "/insertDnBusAppLine")
    public Result<Integer> insertDnBusAppLine(HttpServletRequest request,
                                              @Validated(Default.class)
                                              @ApiNeed({"busLine", "description", "status"})
                                                      DnBusAppLineDTO record) {

        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        record.setCreateUser(loginUser.getLogin_name());
        return dnBusAppLineService.insertDnBusAppLine(record);

    }

    @ApiOperation(value = "查询", notes = "查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start", value = "页码", dataType = "Integer", required = true),
            @ApiImplicitParam(name = "limit", value = "条数", dataType = "Integer", required = true)
    })
    @PostMapping(value = "/selectDnBusAppLineByCondition")
    public Result<PageResult<DnBusAppLineVO>>
    selectDnBusAppLineByCondition(@RequestParam("start") Integer start,
                                  @RequestParam("limit") Integer limit,
                                  @ApiNeed({"busLine", "status"})
                                  @Validated(QueryGroup.class) DnBusAppLineDTO dnBusAppLineDTO) {

        return dnBusAppLineService.selectDnBusAppLineByCondition(start, limit, dnBusAppLineDTO);

    }

    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping(value = "/updateDnBusAppLineById")
    public Result<Integer> updateDnBusAppLineById(HttpServletRequest request,
                                                  @Validated(UpdateGroup.class)
                                                  @ApiNeed({"id", "busLine", "description", "status"})
                                                          DnBusAppLineDTO dnBusAppLineDTO) {

        dnBusAppLineDTO.setUpdateUser(((CurrUserVo) request.getAttribute("LoginUser")).getLogin_name());
        return dnBusAppLineService.updateDnBusAppLineById(dnBusAppLineDTO);
    }

    @ApiOperation(value = "获取业务线配置列表", notes = "获取业务线配置列表")
    @PostMapping(value = "/getModelList")
    public Result<List<DnBusAppLineVO>> getModelList() {

        return dnBusAppLineService.getLineList();
    }

    @ApiOperation(value = "导出", notes = "导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "value", value = "自定义字段", example = "busLine,业务线;status,状态", dataType = "String"),
            @ApiImplicitParam(name = "export_file_name", value = "文件名", example = "业务线.xlsx", dataType = "String")
    })
    @PostMapping(value = "/export")
    public void export(HttpServletResponse response,
                       @RequestParam(name = "value", defaultValue = "busLine,业务线;status,状态") String value,
                       @RequestParam(name = "export_file_name", defaultValue = "业务线.xlsx") String export_file_name,
                       @ApiNeed({"busLine", "status"}) @Validated(QueryGroup.class) DnBusAppLineDTO dnBusAppLineDTO) {

        List<DnBusAppLineVO> list = dnBusAppLineService.export(dnBusAppLineDTO);

        Map<String, String> head = new LinkedHashMap<>();
        try {
            String[] split = value.split(";");
            for (int i = 0; i < split.length; i++) {
                String[] s = split[i].split(",");
                head.put(s[0], s[1]);
            }
        } catch (Exception e) {
            Asserts.fail("自定义列导出异常");
        }
        String fileName = export_file_name + "_" + DateTime.now().toString("yyyyMMdd") + ".xls";
        ExportExcelUtil.export2(response, list, head, fileName);

    }
}
