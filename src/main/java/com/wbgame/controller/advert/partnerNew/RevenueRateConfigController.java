package com.wbgame.controller.advert.partnerNew;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.adv2.DnPartnerRateVo;
import com.wbgame.pojo.mobile.DnwxAreaConfigVo;
import com.wbgame.service.AdService;
import com.wbgame.service.YdService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @description: 合作方收入比例配置
 * @date 2022.11.29
 * @author: caow
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert/revenueRateConfig")
public class RevenueRateConfigController {

	@Autowired
    private RedisTemplate<String, Object> redisTemplate;
	
    @Autowired
    private AdService adService;

    /**
     * 收入比例配置-查询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public String list(HttpServletRequest request, HttpServletResponse response) {

    	String[] args = {"appid","agent","app_category"};
		Map<String, String> paramMap = BlankUtils.getParameter(request, args);
		
        JSONObject result = new JSONObject();
        try {
        	String sql = "select * from partner_revenue_rate where 1=1";
        	if(!BlankUtils.checkBlank(paramMap.get("appid")))
        		sql += " and appid=#{obj.appid} ";
    		if(!BlankUtils.checkBlank(paramMap.get("agent")))
    			sql += " and agent=#{obj.agent} ";
    		if(!BlankUtils.checkBlank(paramMap.get("app_category")))
    			sql += " and app_category=#{obj.app_category} ";
        	
    		sql += " order by id desc";
        		
        	PageHelper.startPage(paramMap); // 进行分页
            List<Map<String, Object>> list = adService.queryListMapTwo(sql, paramMap);
            long size = ((Page) list).getTotal();
            
            list.forEach(act -> act.put("createtime", act.get("createtime")+""));
			list.forEach(act -> act.put("endtime", act.get("endtime")+""));
            
            result.put("ret", 1);
            result.put("data", list);
            result.put("totalCount", size);
        } catch (Exception e) {
            e.printStackTrace();

            result.put("ret", 0);
            result.put("msg", "错误信息: " + e.getMessage());
        }
        return result.toJSONString();
    }

    /**
     * 收入比例配置-操作
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/handle", method = RequestMethod.POST)
    public String handle(String handle, DnPartnerRateVo area, HttpServletRequest request, HttpServletResponse response) {
    	

		// token验证
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		Object object = redisTemplate.opsForValue().get(token);
		JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(object));

		int result = 0;
		try {
			area.setCuser(json.getString("login_name"));
			area.setEuser(json.getString("login_name"));
			
            if ("add".equals(handle)) {
            	String query = "select * from partner_revenue_rate where (app_category=#{obj.app_category} and agent=#{obj.agent})";
            	List<Map<String, Object>> list = adService.queryListMapTwo(query,area);
            	if(list != null && !list.isEmpty()){
            		return "{\"ret\":0,\"msg\":\"该配置信息已存在!\"}";
            	}
            	
                String sql = "insert into partner_revenue_rate(app_category,agent,rate,cuser,createtime,euser,endtime) "+
                		"values(#{obj.app_category},#{obj.agent},#{obj.rate},#{obj.cuser},now(),#{obj.euser},now())";
                result = adService.execSqlHandle(sql, area);
                
            } else if ("edit".equals(handle)) {
            	
            	 String sql = "update partner_revenue_rate set rate=#{obj.rate},euser=#{obj.euser},endtime=now() "+
                 		"where app_category=#{obj.app_category} and agent=#{obj.agent} ";
            	 result = adService.execSqlHandle(sql, area);
                 
            } else if ("del".equals(handle)) {
                
                String sql = "delete from partner_revenue_rate where app_category=#{obj.app_category} and agent=#{obj.agent} ";
                result = adService.execSqlHandle(sql, area);
            }

            if (result > 0)
                return "{\"ret\":1,\"msg\":\"操作成功!\"}";
            else
                return "{\"ret\":0,\"msg\":\"无效操作!\"}";

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"操作失败!\"}";
        }
    }

}
