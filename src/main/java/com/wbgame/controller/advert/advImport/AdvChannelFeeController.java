package com.wbgame.controller.advert.advImport;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.AdvChaFeeVo;
import com.wbgame.pojo.custom.AdvFeeVo;
import com.wbgame.service.AdService;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import jxl.Sheet;
import jxl.Workbook;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 广告收入数据导入
 * @author: huangmb
 * @date: 2021/06/25
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/advert")
public class AdvChannelFeeController {

    @Autowired
    private AdService adService;

    @Autowired
    private SomeService someService;

    /**
     * 新版广告数据导入
     * @param file
     * @param request
     * @param response
     * @throws IOException
     */
    @RequestMapping(value="/importAdvChannelFee", method={RequestMethod.POST})
    public String importAdvChannelFee(@RequestParam(value="fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException{

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setContentType("text/html;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        //记录可能异常的行
        int  error = 0 ;
        try{
            PrintWriter out = response.getWriter();
            JSONObject obj = new JSONObject();

            if(null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")){
                List<AdvFeeVo> list = new ArrayList<>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int column = sheet.getColumns();
                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    error = r+1;
                    if(BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    String[] vals = new String[column];
                    for (int c = 0; c < column; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }

                    AdvFeeVo ki = new AdvFeeVo();
                    ki.setPush_cha(vals[0]);
                    ki.setAppid(vals[1]);
                    ki.setPri_id(vals[2]);
                    if(vals[3] != null && !vals[3].isEmpty()){
                        // 上传过来的日期格式
                        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy/MM/dd");
                        boolean flag = false;
                        try {
                            if(vals[3].split("/").length != 3
                                    || vals[3].split("/")[0].length() != 4) {
                                flag = true;
                            }

                            String adv_dt = DateTime.parse(vals[3], format).toString("yyyy-MM-dd");
                            ki.setAdv_dt(adv_dt);
                        } catch (Exception e) {
                            flag = true;
                        }

                        if(flag){
                            obj.put("ret", 0);
                            obj.put("msg", "日期格式不正确，需要 yyyy/MM/dd 的格式！");
                            out.print(obj.toString());
                            out.close();
                            return null;
                        }
                    }
                    if(vals[4] == null || "".equals(vals[4]) || vals[4].length() == 0){
                        ki.setGive_fee(0d);
                    }else {
                        try {
                            ki.setGive_fee(Double.valueOf(vals[4]));
                        } catch (Exception e) {
                            obj.put("ret", 0);
                            obj.put("msg", "第"+error+"行：'"+vals[4]+"' 可能格式有误");
                            return obj.toJSONString();
                        }

                    }
                    try {
                        ki.setNew_count(Integer.valueOf(vals[5]));
                    } catch (Exception e) {
                        obj.put("ret", 0);
                        obj.put("msg", "第"+error+"行："+vals[5]+"可能格式有误");
                        return obj.toJSONString();
                    }

                    ki.setCpa(vals[6]);

                    try {
                        ki.setDau(Integer.valueOf(vals[7]));
                    } catch (Exception e) {
                        obj.put("ret", 0);
                        obj.put("msg", "第"+error+"行： '"+vals[7]+"' 可能格式有误");
                        return obj.toJSONString();
                    }

                    if(vals[8] == null || "".equals(vals[8]) || vals[8].length() == 0){
                        ki.setGet_fee(0d);
                    }else {
                        try {
                            ki.setGet_fee(Double.valueOf(vals[8]));
                        } catch (Exception e) {
                            obj.put("ret", 0);
                            obj.put("msg", "第"+error+"行： '"+vals[8]+"' 可能格式有误");
                            return obj.toJSONString();
                        }

                    }
                    ki.setArpu(vals[9].trim());

                    if(vals[10] == null || "".equals(vals[10]) || vals[10].length() == 0){
                        ki.setAdv_fee(0d);
                    }else {
                        try {
                            ki.setAdv_fee(Double.valueOf(vals[10].trim()));
                        } catch (Exception e) {
                            obj.put("ret", 0);
                            obj.put("msg", "第"+error+"行：'"+vals[10]+"' 可能格式有误");
                            return obj.toJSONString();
                        }

                    }
                    ki.setAdv_dau(vals[11].trim());
                    ki.setOs_type(vals[12].trim());
                    if(!"1".equals(vals[12].trim()) && !"3".equals(vals[12].trim())){
                        obj.put("ret", 0);
                        obj.put("msg", vals[0]+"-"+vals[1]+" 系统类型不正确，值只能为1或3！");
                        out.print(obj.toString());
                        out.close();
                        return null;
                    }
                    ki.setCha_type(vals[13].trim());
                    if(!"1".equals(vals[13].trim()) && !"2".equals(vals[13].trim()) && !"3".equals(vals[13].trim())){
                        obj.put("ret", 0);
                        obj.put("msg", vals[0]+"-"+vals[1]+" 渠道类型不正确，值只能为1、2、3！");
                        out.print(obj.toString());
                        out.close();
                        return null;
                    }

                    list.add(ki);
                }

                adService.insertAdvChannelFeeTotalNewList(list);
                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
                out.write(obj.toString());
                out.close();
            }else{

                obj.put("ret", 0);
                obj.put("msg", "上传文件有误");
                out.print(obj.toString());
                out.close();
            }

        }catch (Exception e) {
            //上传异常
            e.printStackTrace();
            PrintWriter pw;
            try {
                pw = response.getWriter();
                //返回失败信息
                JSONObject obj = new JSONObject();
                obj.put("ret", 0);
                obj.put("msg", "第"+error+"行有误，请查看");
                pw.print(obj.toString());
                pw.close();
            } catch (IOException e1) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * cps广告收入导入
     *
     * @param file
     * @param request
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/importChannelFeeAdv", method = {RequestMethod.POST})
    public void importChannelFeeAdv(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("text/html;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            PrintWriter out = response.getWriter();
            JSONObject obj = new JSONObject();

            if (null != file && !file.isEmpty() && file.getOriginalFilename().endsWith(".xls")) {
                List<AdvChaFeeVo> list = new ArrayList<>();
                Workbook workbook = Workbook.getWorkbook(file.getInputStream());
                Sheet sheet = workbook.getSheet(0);

                int column = sheet.getColumns();
                int row = sheet.getRows();
                for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
                    if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents()))
                        continue;

                    AdvChaFeeVo ki = new AdvChaFeeVo();
                    String[] vals = new String[column];
                    for (int c = 0; c < column; c++) {
                        vals[c] = sheet.getCell(c, r).getContents();
                    }
                    ki.setCha_name(vals[1]);
                    ki.setCha_game(vals[2]);
                    //兼容数据
                    if (vals[3] == null || vals[3].equals("")) {
                        obj.put("ret", 0);
                        obj.put("msg", "上传文件中广告类型不可为空");
                        out.write(obj.toString());
                        out.close();
                        return;
                    }
                    ki.setAd_pos(vals[3]);
                    ki.setAdv_fee(vals[4]);
                    ki.setShow_count(vals[5]);
                    ki.setClick_count(vals[6]);
                    if (vals[0] != null && !vals[0].equals("")) {
                        // 上传过来的日期格式
                        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy/MM/dd");
                        boolean flag = false;
                        try {
                            if (vals[0].split("/").length != 3
                                    || vals[0].split("/")[0].length() != 4) {
                                flag = true;
                            }

                            String adv_dt = DateTime.parse(vals[0], format).toString("yyyy-MM-dd");
                            ki.setAdv_dt(adv_dt);
                        } catch (Exception e) {
                            flag = true;
                        }

                        if (flag) {
                            obj.put("ret", 0);
                            obj.put("msg", "日期格式不正确，需要 yyyy/MM/dd 的格式！");
                            out.write(obj.toString());
                            out.close();
                            return;
                        }

                    }
                    ki.setBus_nm(vals[7]);
                    list.add(ki);

                }
                someService.insertChannelFeeAdv(list);
                obj.put("ret", 1);
                obj.put("msg", "上传文件成功");
                out.write(obj.toString());
                out.close();
            } else {
                obj.put("ret", 0);
                obj.put("msg", "上传文件有误，需要.xls格式文件！");
                out.write(obj.toString());
                out.close();
            }

        } catch (Exception e) {
            //上传异常
            e.getMessage();
            response.setCharacterEncoding("utf-8");
            PrintWriter pw;
            try {
                pw = response.getWriter();
                JSONObject obj = new JSONObject();
                obj.put("ret", 0);
                obj.put("msg", e.getMessage());
                pw.write(obj.toString());
                pw.close();
            } catch (IOException e1) {
                e1.getMessage();
            }
        }
    }

}
