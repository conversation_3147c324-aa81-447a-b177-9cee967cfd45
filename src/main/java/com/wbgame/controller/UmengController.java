package com.wbgame.controller;

import static com.wbgame.utils.JxlUtil.getNormolCell;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wbgame.common.Asserts;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.*;
import com.wbgame.utils.ExportExcelUtil;
import jxl.Sheet;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.master.SomeMapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.service.AdService;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;

/**
 * 友盟控制类
 * <AUTHOR>  
 */
@Controller
public class UmengController {

    @Autowired
    SomeService someService;
    @Autowired
    AdService adService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private SomeMapper someMapper;
    @Autowired
    private TfxtMapper tfxtMapper;
    

    /**
     * 友盟渠道信息录入
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/selectUmengEnter", method={RequestMethod.POST})
    public @ResponseBody
    String selectUmengEnter(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");

            if(BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
            if(cuser == null){
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }else{
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            String appname = BlankUtils.checkNull(request, "appname");
            String cname = BlankUtils.checkNull(request, "cname");
            String push_cha = BlankUtils.checkNull(request, "push_cha");
            Map<Object, Object> parmMap = new HashMap<>();
            parmMap.put("appname", appname);
            parmMap.put("cname", cname);
            parmMap.put("push_cha", push_cha);
            List<UengIncomeShowVo> list = someService.selectUmengEnter(parmMap);
            long size = ((Page) list).getTotal();
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret",1);
            result.put("totalCount", size);
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 友盟渠道信息录入操作
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/umengIncomeShowHandle", method={RequestMethod.POST})
    public @ResponseBody
    String umengIncomeShowHandle(UengIncomeShowVo np, String handle,CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            if(BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
            if(cuser == null){
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }else{
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            int ret = 0;
            if("add".equals(handle)){
                ret = someService.insertUmengEnter(np);
            }else if("edit".equals(handle)){
                ret = someService.updateUmengEnter(np);
            }else if("del".equals(handle)){
                ret = someService.deleteUmengEnter(np);
            }
            JSONObject result = new JSONObject();
            if(ret > 0){
                result.put("ret",1);
                result.put("msg","操作成功");
            }else {
                result.put("ret",0);
                result.put("msg","操作失败");
            }
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    /**
     * 联动选项
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/selectUmengTreeList", method={RequestMethod.POST})
    public @ResponseBody
    String selectUmengTreeList(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
          if(BlankUtils.checkBlank(cu.getToken()))
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(cu.getToken());
            if(cuser == null){
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }else{
                redisTemplate.opsForValue()
                        .set(cu.getToken(), cuser, 20 * 60, TimeUnit.SECONDS);
            }
            Map<String, UmengTreeListVo> treeMap = new HashMap<String, UmengTreeListVo>();
            //List<UmengTreeListVo> list = new ArrayList<>();
            List<UmengTreeListVo> forList = someService.selectUmengList();
            for (UmengTreeListVo umt : forList) {
                umt.setSub(new ArrayList<>());
                treeMap.put(umt.getMid(), umt);
            }

            List<UmengTreeListVo> subList = someService.selectUmengListTwo();
            for (UmengTreeListVo umt : subList) {
                UmengTreeListVo uz = treeMap.get(umt.getParent_id());
                if(uz != null){
                    uz.getSub().add(umt);
                    treeMap.put(umt.getParent_id(), uz);
                }
            }
            JSONObject result = new JSONObject();
            Collection<UmengTreeListVo> values = treeMap.values();
            List<Object> list = new ArrayList<>(values);
            result.put("data",list);
            result.put("ret",1);
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }
    

    /**
     * 友盟自推广系统查询
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="/selectUmengPromotionChannel", method={RequestMethod.POST})
    public @ResponseBody
    String selectUmengPromotionChannel(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");

            String token = request.getParameter("token");
			if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
				return "{\"ret\":2,\"msg\":\"token is error!\"}";
			else
				redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            String start_date = BlankUtils.checkNull(request, "start_date");
            String end_date = BlankUtils.checkNull(request, "end_date");
            String appname = BlankUtils.checkNull(request, "appname");
            String cname = BlankUtils.checkNull(request, "cname");
            String push_cha = BlankUtils.checkNull(request, "push_cha");
            String channel = BlankUtils.checkNull(request, "channel");
            if(!BlankUtils.checkBlank(channel) && channel.split(",").length > 1){
            	channel = String.join("','", channel.split(","));
			}
            
            Map<Object, Object> parmMap = new HashMap<>();
            if(BlankUtils.checkBlank(request.getParameter("appkey"))){
            	parmMap.put("all", 1);
            }else{
            	String appkey = BlankUtils.checkNull(request, "appkey");
            	if(!BlankUtils.checkBlank(appkey) && appkey.split(",").length > 1){
                	appkey = String.join("','", appkey.split(","));
    			}
            	parmMap.put("appkey", appkey);
            	
            }
            parmMap.put("start_date", start_date);
            parmMap.put("end_date", end_date);
            parmMap.put("appname", appname);
            parmMap.put("cname", cname);
            parmMap.put("push_cha", push_cha);
            parmMap.put("channel", channel);
            parmMap.put("ctype", 2);
            List<UmengChannelTotalVo> list = someService.selectUmengChannelTotal(parmMap);
            
            //导出的excel数据存储
         	redisTemplate.opsForValue().set("selectUmengPromotionChannel-" + token,
         			someService.selectUmengChannelTotal(parmMap), 20 * 60, TimeUnit.SECONDS);
            
            long size = ((Page) list).getTotal();
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret",1);
            result.put("totalCount", size);
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }

    
    @CrossOrigin
    @RequestMapping(value="/selectUmengkeys", method={RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody String selectUmengkeys(HttpServletRequest request){

		// token验证
		String token = request.getParameter("token");
		// token验证
		if(BlankUtils.checkBlank(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		CurrUserVo cuser = (CurrUserVo)redisTemplate.opsForValue().get(token);
		if(cuser == null){
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		}else{
			redisTemplate.opsForValue().set(token, cuser, 20 * 60, TimeUnit.SECONDS);
		}

		JSONObject result = new JSONObject();	
			
		result.put("ret", 1);
		result.put("data", someMapper.selectUmengkeys());
		
		return result.toJSONString();
	}
    /**
     * 友盟自推广 导出接口
     * @param map
     * @param request
     * @param response
     */
    @CrossOrigin
    @RequestMapping(value="/exportUmengPromotionChannel", method={RequestMethod.GET,RequestMethod.POST})
    public String exportUmengPromotionChannel(ModelMap map, HttpServletRequest request, HttpServletResponse response) {
    	// 数据头
		Map<String, String> headerMap = new LinkedHashMap<String, String>();
		String token = request.getParameter("token");
		if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token))
			return "{\"ret\":2,\"msg\":\"token is error!\"}";
		else
			redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
		// 数据内容
		List<UmengChannelTotalVo> list = (List<UmengChannelTotalVo>) redisTemplate.opsForValue().get("selectUmengPromotionChannel-" + token);

		Map<String, String> inMap = new LinkedHashMap<String, String>();
		inMap.put("strDt", "yyyy-MM-dd");
		List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
		Map<String, Object> contentMap = null;
			for(UmengChannelTotalVo temp : list){
	            headerMap.put("tdate","日期");
	            headerMap.put("appname","应用名称");
	            headerMap.put("cname","渠道名称");
	            headerMap.put("push_cha","项目id");
	            headerMap.put("addnum","新增用户");
	            headerMap.put("actnum","活跃用户");
	            
	            contentMap = new LinkedHashMap<String, Object>();
	            contentMap.put("tdate", temp.getTdate());
	            contentMap.put("appname", temp.getAppname());
	            contentMap.put("cname", temp.getCname());
	            contentMap.put("addnum", temp.getAddnum());
	            contentMap.put("actnum", temp.getActnum());
	            contentMap.put("push_cha", temp.getPush_cha());
	            
	            contentList.add(contentMap);
	            
		}
		String fileName = "友盟自推广_" + DateTime.now().toString("yyyyMMdd")
				+ ".xlsx";
//		JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,
//				request, response);

        ExportExcelUtil.exportXLSX(response,contentList,headerMap,fileName);

        return null;
    			
    }

    /**
     * 设置标题样式
     * @return
     */
    public static WritableCellFormat getTitle() {
        WritableFont font = new WritableFont(WritableFont.TIMES, 12);
        WritableCellFormat format = new WritableCellFormat(font);
        try {
            font.setColour(jxl.format.Colour.BLUE);//蓝色字体
            format.setAlignment(jxl.format.Alignment.CENTRE);
            format.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            format.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN, jxl.format.Colour.BLACK);
        }
        catch (WriteException e1) {
            e1.printStackTrace();
        }
        return format;
    }

    public static JSONArray getUmengReport(List<UmengChannelTotalVo> beList,
                                           List<UmengChannelTotalVo> toList){

        List<String> chList = Arrays.asList(new String[]
                {"oppo", "oppoml", "vivo", "huawei", "tencent", "gg", "meizu", "xiaomi",
                "qihoo", "ad4399", "hykb", "taptap", "jinli", "aorayyh"});
        JSONArray array = new JSONArray();
        BigDecimal hundred = new BigDecimal(100);
        for (UmengChannelTotalVo tc : toList) {
            if(chList.contains(tc.getCname())){ // 渠道在列表中，且匹配上周和本周
                for (UmengChannelTotalVo bc : beList) {
                    if(tc.getCname().equals(bc.getCname())){
                        JSONObject channel = new JSONObject();
                        channel.put("cname", tc.getCname());
                        channel.put("cid", tc.getCid());
                        channel.put("appname", tc.getAppname());

                        // 新增、活跃、收入、次留
                        String[] in1 = {tc.getAddnum()+"",tc.getActnum()+"",tc.getAdv_fee()==null?"0":tc.getAdv_fee(),tc.getKeep_num1()};
                        String[] in2 = {bc.getAddnum()+"",bc.getActnum()+"",bc.getAdv_fee()==null?"0":bc.getAdv_fee(),bc.getKeep_num1()};
                        for (int i = 0; i < in1.length; i++) {
                            JSONObject obs = new JSONObject();
                            obs.put("toweek", in1[i]);
                            obs.put("beweek", in2[i]);
                            BigDecimal bd1 = new BigDecimal(0);
                            BigDecimal bd2 = new BigDecimal(0);
                            if(in1[i] != null){
                                bd1 = new BigDecimal(in1[i]);
                            }
                            if(in2[i] != null){
                                bd2 = new BigDecimal(in2[i]);
                            }


                            if(bd1.intValue() != 0 && bd2.intValue() != 0){
                                // (本周值-上周值) * 100 / 上周值，保留两位小数点
                                String rate = bd1.subtract(bd2).multiply(hundred).divide(bd2, 2, RoundingMode.FLOOR).toString();
                                obs.put("weekrate", rate + "%");
                                channel.put("obj"+(i+1), obs);
                            }else{
                                obs.put("weekrate", "0%");
                                channel.put("obj"+(i+1), obs);
                            }
                        }
                        array.add(channel);
                    }
                }
            }
        }

        return array;
    }
    
    /**
     * 应用组 下拉
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="umeng/selectDnGroup", method={RequestMethod.GET,RequestMethod.POST})
    public @ResponseBody
    String selectDnGroup(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try{
            
			JSONObject result = new JSONObject();
			result.put("ret",1);
            Object obj = redisTemplate.opsForValue().get("selectDnGroup");
            if (obj == null) {
                List<DnChannelVo> dnChannelVos = tfxtMapper.selectDnGroup();
                redisTemplate.opsForValue().set("selectDnGroup",dnChannelVos);
                redisTemplate.expire("selectDnGroup",1,TimeUnit.HOURS);
                result.put("data",dnChannelVos);
            }else{
                result.put("data",obj);
            }
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }
    
    /**
     * 应用
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="umeng/selectDnGroupApp", method={RequestMethod.GET,RequestMethod.POST})
    public @ResponseBody
    String selectDnGroupApp(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response,String groupId) {
        try{
            
			JSONObject result = new JSONObject();
			
			result.put("ret",1);

			//String ids = StringUtils.join(groupId, ",");
			
            result.put("data", tfxtMapper.selectDnGroupApp(groupId));
            
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }
    
    
    /**
     * 渠道类型  媒体 子渠道
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @CrossOrigin
    @RequestMapping(value="umeng/selectDnChannelInfo", method={RequestMethod.GET,RequestMethod.POST})
    public @ResponseBody
    String selectDnChannelInfo(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response,String groupId) {
        try{
          
			JSONObject result = new JSONObject();
			
			result.put("ret",1);
            result.put("channelType", someMapper.selectDnChannelType());
            result.put("chaMedia", someMapper.selectDnChaMedia());
            result.put("chaSubLaunch", someMapper.selectDnChaSubLaunch());
            result.put("chaId", someMapper.selectDnChaId());
            
            return result.toJSONString();
        }catch(Exception e){
            e.printStackTrace();
            return "{\"ret\":0,\"msg\":\"server error!\"}";
        }
    }
	
}