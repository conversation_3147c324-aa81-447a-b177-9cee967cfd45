package com.wbgame.controller.action;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.master.mobile.ApiPacketParaConfigMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.mobile.request.HuaWeiPacketAuditRecordParam;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.wbgame.base.BaseErrorEnum;
import com.wbgame.common.response.ApiResponse;
import com.wbgame.pojo.mobile.hw.FileInfo;
import com.wbgame.service.mobile.ApiPacketParaConfigService;
import com.wbgame.utils.jettison.StringUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @since 2024-04-12
 */
@Api(tags = "华为分包")
@RestController
@RequestMapping("/api-packet/client/v1")
public class ApiPacketUploadClientController {
	
    @Autowired
    private ApiPacketParaConfigService apiPacketParaConfigService;
	@Autowired
	private ApiPacketParaConfigMapper apiPacketParaConfigMapper;
    
	/**
     * 华为上传文件,更新平台文件信息,提交审核,获取文件地址
     * @param file
     * @param appid  华为产品ID
     * @param cid  公司主体id
     * @return
     * @throws IOException
     */
	@PostMapping(value="/hw/upload")
	@ApiOperation(value = "上传", notes = "上传", httpMethod = "POST")
    public ApiResponse<FileInfo>  uploadFile(@RequestParam(value = "file",required=true) String file,String fileName,@RequestParam(value = "cid",required=true) Integer cid,
    		@RequestParam(value = "pkg",required=true) String pkg) throws IOException {
		//通过公司主体ID获取华为主体账号的clientId及秘钥
		Map<String, Object> client_info=apiPacketParaConfigService.queryApiClientByCid(cid);
		if(null==client_info){
			return ApiResponse.fail(BaseErrorEnum.CONFIG_MISSING.getCode(),BaseErrorEnum.CONFIG_MISSING.getMsg());
		}
		//通过包号获取华为产品ID
		String hwappid=apiPacketParaConfigService.queryHwAppidByPkg(pkg);
		if(StringUtil.is_nullString(hwappid)){
			return ApiResponse.fail(BaseErrorEnum.PLATFORM_APPID_MISSING.getCode(),BaseErrorEnum.PLATFORM_APPID_MISSING.getMsg());
		}
		return apiPacketParaConfigService.uploadFile(file, client_info, hwappid,fileName);
	}


	/**
	 * 华为上传文件,查询状态
	 * @param id 任务id
	 * @return
	 * @throws IOException
	 */
	@GetMapping(value="/hw/check")
	@ApiOperation(value = "查询", notes = "查询", httpMethod = "GET")
	public ApiResponse<HuaWeiPacketAuditRecordParam> check(String id, HttpServletRequest request, HttpServletResponse response) throws IOException {

		if(BlankUtils.sqlValidate(id))
			return ApiResponse.fail(BaseErrorEnum.VALIDATE_FAILED.getCode(),BaseErrorEnum.VALIDATE_FAILED.getMsg());

		try {
			HuaWeiPacketAuditRecordParam recordParam = apiPacketParaConfigMapper.queryPacketAuditById(id);
			if(null == recordParam){
				return ApiResponse.fail(BaseErrorEnum.RECORD_MISSING.getCode(),BaseErrorEnum.RECORD_MISSING.getMsg());
			}else {
				return ApiResponse.success(recordParam);
			}

		} catch (Exception e) {
			e.printStackTrace();
			return ApiResponse.fail(BaseErrorEnum.FAIL.getCode(),BaseErrorEnum.FAIL.getMsg());
		}
	}

}
