package com.wbgame.controller.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.pojo.AssetsInfo;
import com.wbgame.pojo.AssetsInfoLog;
import com.wbgame.pojo.AssetsInfoTable;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;
import jxl.Sheet;
import jxl.Workbook;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * JS拉起微信分享
 * 参数下发
 * xiang.xin
 *
 */
@Controller
public class WxShareController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    SomeService someService;

    private static String appId = "wx07d91f1b79737c07";                      //appid
    private static String appSecret = "7d6f9987da021a732012c8885b71a3d6";    //密钥
    private static String ACCESS_TOKEN = "Access_token";
    private static String TICKER = "Ticker";


    /**
     * 微信返回参数
     * @return
     */
    @RequestMapping(value = "/wechatParam" ,method= RequestMethod.GET)
    @ResponseBody
    public String getWechatParam(HttpServletRequest request, HttpServletResponse response){
        response.setHeader("Access-Control-Allow-Origin", "*");
        String url = request.getParameter("url");
        if(BlankUtils.checkBlank(url)
                || BlankUtils.checkBlank(url)){
            return "{\"msg\":\"错误信息: 缺少指定参数\"}";
        }
        url = new String(org.apache.commons.codec.binary.Base64.decodeBase64(url));
        System.out.println("接收地址==========="+url);
        ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
        String token = (String) opsForValue.get(ACCESS_TOKEN);
        if(token == null){
            JSONObject jsonObject = getAccessToken();   //获取token
            System.out.println("token返回====="+jsonObject.toString());
            if(jsonObject != null){
                token = (String) jsonObject.get("access_token");
                opsForValue.set(ACCESS_TOKEN,token,7200, TimeUnit.SECONDS);
            }else{
                return "{\"msg\":\"错误信息: 微信调用异常\"}";
            }
        }
        String ticker = (String) opsForValue.get(TICKER);
        if(ticker == null){
            JSONObject jsonObject = getJsApiTicket(token);
            System.out.println("ticker返回====="+jsonObject.toString());
            if(jsonObject != null){
                ticker = (String) jsonObject.get("ticket");
                opsForValue.set(TICKER,ticker,7200,TimeUnit.SECONDS);
            }else{
                return "{\"msg\":\"错误信息: 微信调用异常\"}";
            }
        }
        Map<String, String> map = makeWXTicket(ticker,url);
        return JSON.toJSONString(map);
    }

    /**
     * 获取token
     * @return
     */
    public static JSONObject getAccessToken(){
        String accessTokenUrl= "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="+appId+"&secret="+appSecret+"";
        JSONObject result = WxShareGet(accessTokenUrl);
        return result ;
    }

    /**
     * 获取ticket
     */
    private JSONObject getJsApiTicket(String accessToken){
        String apiTicketUrl= "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token="+accessToken+"&type=jsapi";
        JSONObject result = WxShareGet(apiTicketUrl);
        return result;
    }

    /**
     * 生成微信权限验证的参数
     */
    public Map<String, String> makeWXTicket(String jsApiTicket, String url) {
        Map<String, String> ret = new HashMap<String, String>();
        String nonceStr = createNonceStr();
        String timestamp = createTimestamp();
        String string1;
        String signature = "";

        //注意这里参数名必须全部小写，且必须有序
        string1 = "jsapi_ticket=" + jsApiTicket +
                "&noncestr=" + nonceStr +
                "&timestamp=" + timestamp +
                "&url=" + url;
        try
        {
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(string1.getBytes("UTF-8"));
            signature = byteToHex(crypt.digest());
        }
        catch (NoSuchAlgorithmException e)
        {
            e.printStackTrace();
        }
        catch (UnsupportedEncodingException e)
        {
            e.printStackTrace();
        }

        //ret.put("url", url);
        //ret.put("jsapi_ticket", jsApiTicket);
        ret.put("nonceStr", nonceStr);
        ret.put("timestamp", timestamp);
        ret.put("signature", signature);
        ret.put("appid", appId);
        return ret;
    }

    //字节数组转换为十六进制字符串
    private static String byteToHex(final byte[] hash) {
        Formatter formatter = new Formatter();
        for (byte b : hash)
        {
            formatter.format("%02x", b);
        }
        String result = formatter.toString();
        formatter.close();
        return result;
    }

    //生成随机字符串
    private static String createNonceStr() {
        return UUID.randomUUID().toString();
    }

    //生成时间戳
    private static String createTimestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }

    public static JSONObject WxShareGet(String requestUrl) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String responseContent  = null;
        JSONObject result = null;
        try {
            //创建Get请求，
            HttpGet httpGet = new HttpGet(requestUrl);
            //执行Get请求，
            response = httpClient.execute(httpGet);
            //得到响应体
            HttpEntity entity = response.getEntity();
            //获取响应内容
            responseContent  = EntityUtils.toString(entity,"UTF-8");
            //转换为map
            result = JSON.parseObject(responseContent);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

}