package com.wbgame.aop;

import com.github.pagehelper.PageHelper;
import com.wbgame.utils.BlankUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;

@Aspect
@Component
public class PageLimitAop {

    @Pointcut("@annotation(com.wbgame.aop.PageLimit)")
    public void aspect() {

    }

    @Around("aspect()")
    public String around(ProceedingJoinPoint joinPoint) throws Throwable{
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        //从获取RequestAttributes中获取HttpServletRequest的信息
        HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
        Integer start;
        Integer limit;
        MethodSignature signature =(MethodSignature) joinPoint.getSignature();
        PageLimit pageLimit = joinPoint.getTarget().getClass().getMethod(signature.getName(), signature.getParameterTypes()).getAnnotation(PageLimit.class);
        String startStr = request.getParameter("start");
        String limitStr = request.getParameter("limit");
        if (BlankUtils.checkBlank(startStr)) {
            start = pageLimit.start();
        }else{
            start = Integer.parseInt(startStr);
        }
        if (BlankUtils.checkBlank(limitStr)) {
            limit = pageLimit.limit();
        }else{
            limit = Integer.parseInt(limitStr);
        }
        int pageNo = (start / limit) + 1;
        PageHelper.startPage(pageNo, limit);
        Object target = joinPoint.proceed();
        return target.toString();
    }


}
