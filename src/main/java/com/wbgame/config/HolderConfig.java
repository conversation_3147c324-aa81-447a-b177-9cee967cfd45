package com.wbgame.config;

import com.wbgame.common.SyncDataServiceHolder;
import com.wbgame.common.strategyEnum.Platform;
import com.wbgame.common.strategyEnum.Table;
import com.wbgame.service.budgetWarning.BaseTableService;
import com.wbgame.service.syncdata.CashSyncDataService;
import com.wbgame.task.budgetWarning.BaseBudgetWarningTask;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.wbgame.common.constants.PlatformName.*;

/**
 * <AUTHOR>
 * @date 2023/5/30
 * @description
 **/
@Configuration
public class HolderConfig {

    @Resource
    private CashSyncDataService XYXCashSyncDataServiceImpl;
    @Resource
    private CashSyncDataService GDTCashSyncDataServiceImpl;
    @Resource
    private CashSyncDataService vivoCashSyncDataServiceImpl;
    @Resource
    private CashSyncDataService toutiaoCashSyncDataServiceImpl;
    @Resource
    private CashSyncDataService kuaishouCashSyncDataServiceImpl;
    @Resource
    private CashSyncDataService oppoCashSyncDataServiceImpl;
    @Resource
    private CashSyncDataService xiaomiCashSyncDataServiceImpl;
    @Resource
    private CashSyncDataService huaweiCashSyncDataServiceImpl;
    @Resource
    private CashSyncDataService ohayooCashSyncDataServiceImpl;
    @Resource
    private CashSyncDataService sigmobCashSyncDataServiceImpl;
    @Resource
    private CashSyncDataService honorCashSyncDataServiceImpl;

    @Bean(name = "syncDataServiceHolder")
    public SyncDataServiceHolder syncDataServiceHolder() {
        SyncDataServiceHolder syncDataServiceHolder = new SyncDataServiceHolder();
        Map<String, CashSyncDataService> businessMap = new HashMap<>(32);
        businessMap.put("xyx", XYXCashSyncDataServiceImpl);
        businessMap.put(GDT, GDTCashSyncDataServiceImpl);
        businessMap.put(VIVO, vivoCashSyncDataServiceImpl);
        businessMap.put(HEADLINE, toutiaoCashSyncDataServiceImpl);
        businessMap.put(KUAISHOU, kuaishouCashSyncDataServiceImpl);
        businessMap.put(OPPO, oppoCashSyncDataServiceImpl);
        businessMap.put(XIAOMI, xiaomiCashSyncDataServiceImpl);
        businessMap.put(HUAWEI, huaweiCashSyncDataServiceImpl);
        businessMap.put(OHAYOO, ohayooCashSyncDataServiceImpl);
        businessMap.put(SIGMOB, sigmobCashSyncDataServiceImpl);
        businessMap.put(HONOR, honorCashSyncDataServiceImpl);
        syncDataServiceHolder.setBusinessMap(businessMap);
        return syncDataServiceHolder;
    }


    //    @Autowired
//    private SyncDataService XYXSyncDataServiceImpl;
//    @Autowired
//    private SyncDataService GDTSyncDataServiceImpl;
//    @Autowired
//    private SyncDataService vivoSyncDataServiceImpl;
//    @Autowired
//    private SyncDataService toutiaoSyncDataServiceImpl;
//    @Autowired
//    private SyncDataService kuaishouSyncDataServiceImpl;
//    @Autowired
//    private SyncDataService huaweiSyncDataServiceImpl;
//    @Autowired
//    private SyncDataService ohayooSyncDataServiceImpl;
//    @Autowired
//    private SyncDataService sigmobSyncDataServiceImpl;

    @Resource
    private BaseBudgetWarningTask gdtBudgetWarningTask;
    @Resource
    private BaseBudgetWarningTask vivoBudgetWarningTask;
    @Resource
    private BaseBudgetWarningTask xiaomiBudgetWarningTask;
    @Resource
    private BaseBudgetWarningTask huaweiBudgetWarningTask;
    @Resource
    private BaseBudgetWarningTask oppoBudgetWarningTask;
    @Resource
    private BaseBudgetWarningTask ksBudgetWarningTask;
    @Resource
    private BaseBudgetWarningTask toutiaoBudgetWarningTask;

    @Bean(name = "budgetWarningTaskHolder")
    public Map<String,BaseBudgetWarningTask> budgetWarningTaskHolder() {
        Map<String, BaseBudgetWarningTask> taskMap = new HashMap<>(8);
        taskMap.put(Platform.GDT.media, gdtBudgetWarningTask);
        taskMap.put(Platform.VIVO.media, vivoBudgetWarningTask);
        taskMap.put(Platform.XIAOMI.media, xiaomiBudgetWarningTask);
        taskMap.put(Platform.HUAWEI.media, huaweiBudgetWarningTask);
        taskMap.put(Platform.OPPO.media, oppoBudgetWarningTask);
        taskMap.put(Platform.KUAISHOU.media, ksBudgetWarningTask);
        taskMap.put(Platform.TOUTIAO.media, toutiaoBudgetWarningTask);
        return taskMap;
    }
}
