<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.blankcw.example</groupId>
  <artifactId>wb-ssds</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>jar</packaging>

  <name>wb-ssds</name>
  <url>http://maven.apache.org</url>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>1.5.9.RELEASE</version>
  </parent>

  <properties>
       <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
       <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
       <java.version>1.8</java.version>
  </properties>

  <dependencies>
  	<dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
	</dependency>

	<!-- 测试  -->
    <dependency>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-test</artifactId>
		<scope>test</scope>
	</dependency>

    <!-- redis数据库 -->
	<dependency>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-data-redis</artifactId>
		<version>1.5.13.RELEASE</version>
	</dependency>

	  <!--aop -->
	  <dependency>
		  <groupId>org.springframework.boot</groupId>
		  <artifactId>spring-boot-starter-aop</artifactId>
	  </dependency>

    <!--mybatis-->
	<dependency>
	    <groupId>org.mybatis.spring.boot</groupId>
	    <artifactId>mybatis-spring-boot-starter</artifactId>
	    <version>1.3.1</version>
	</dependency>
	<!-- <dependency>
	    <groupId>com.github.pagehelper</groupId>
	    <artifactId>pagehelper-spring-boot-starter</artifactId>
	    <version>1.2.3</version>
	</dependency> -->
	<dependency>
		<groupId>com.github.pagehelper</groupId>
		<artifactId>pagehelper</artifactId>
		<version>5.1.4</version>
	</dependency>
	<dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
    </dependency>
    <dependency>
	    <groupId>com.alibaba</groupId>
	    <artifactId>druid</artifactId>
	    <version>1.1.2</version>
	</dependency>
	<dependency>
	    <groupId>com.alibaba</groupId>
	    <artifactId>fastjson</artifactId>
	    <version>1.2.58</version>
	</dependency>
	<dependency>
	    <groupId>com.alicp.jetcache</groupId>
	    <artifactId>jetcache-starter-redis</artifactId>
	    <version>2.4.4</version>
	</dependency>
	<dependency>
	    <groupId>joda-time</groupId>
	    <artifactId>joda-time</artifactId>
	    <version>2.7</version>
	</dependency>
	<dependency>
	    <groupId>commons-codec</groupId>
	    <artifactId>commons-codec</artifactId>
	    <version>1.10</version>
	</dependency>
	<dependency>
	    <groupId>org.apache.httpcomponents</groupId>
	    <artifactId>httpclient</artifactId>
	    <version>4.5.3</version>
	</dependency>
	<!--httpmime主要用于构建multi-part/form-data 请求-->
	<dependency>
	    <groupId>org.apache.httpcomponents</groupId>
	    <artifactId>httpmime</artifactId>
	    <version>4.5.3</version>
	</dependency>
	<!-- <dependency>
	     <groupId>org.bouncycastle</groupId>
	     <artifactId>bcprov-jdk15on</artifactId>
	     <version>1.59</version>
	</dependency> -->
	<dependency>
         <groupId>org.bouncycastle</groupId>
         <artifactId>bcprov-jdk16</artifactId>
         <version>1.46</version>
    </dependency>
	<dependency>
	    <groupId>commons-fileupload</groupId>
	    <artifactId>commons-fileupload</artifactId>
	    <version>1.3.1</version>
	</dependency>
	<dependency>
	    <groupId>commons-lang</groupId>
	    <artifactId>commons-lang</artifactId>
	    <version>2.6</version>
	</dependency>

	  <dependency>
		  <groupId>org.jcodec</groupId>
		  <artifactId>jcodec</artifactId>
		  <version>0.2.5</version>
	  </dependency>
	  <dependency>
		  <groupId>org.jcodec</groupId>
		  <artifactId>jcodec-javase</artifactId>
		  <version>0.2.5</version>
	  </dependency>
	  <dependency>
		  <groupId>net.coobird</groupId>
		  <artifactId>thumbnailator</artifactId>
		  <version>0.4.8</version>
	  </dependency>

	<!-- JXL Excel导入导出 -->
	<dependency>
	    <groupId>net.sourceforge.jexcelapi</groupId>
	    <artifactId>jxl</artifactId>
	    <version>2.6.12</version>
	</dependency>
	<dependency>
	    <groupId>dom4j</groupId>
	    <artifactId>dom4j</artifactId>
	    <version>1.6.1</version>
	</dependency>
	<dependency>
		<groupId>com.thoughtworks.xstream</groupId>
	 	<artifactId>xstream</artifactId>
	 	<version>1.3.1</version>
	</dependency>
	<dependency>
	    <groupId>javax.mail</groupId>
	    <artifactId>mail</artifactId>
	    <version>1.4.4</version>
	</dependency>
	<!--<dependency>
	    <groupId>com.aliyun</groupId>
	    <artifactId>aliyun-java-sdk-core</artifactId>
	    <version>4.4.9</version>
	</dependency>
	<dependency>
	    <groupId>com.aliyun.oss</groupId>
	    <artifactId>aliyun-sdk-oss</artifactId>
	    <version>2.4.0</version>
	</dependency>-->
	<dependency>
	    <groupId>com.aliyun</groupId>
	    <artifactId>aliyun-java-sdk-cdn</artifactId>
	    <version>3.0.7</version>
	</dependency>
	<dependency>
	    <groupId>com.jxcell</groupId>
	    <artifactId>jxcell</artifactId>
	   <version>1.1</version>
	</dependency>
	<dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>20.0</version>
      <scope>compile</scope>
    </dependency>
	<!-- <dependency>
	    <groupId>com.oracle</groupId>
	    <artifactId>ojdbc14</artifactId>
	    <version>14</version>
	</dependency> -->
	<dependency>
	    <groupId>org.jsoup</groupId>
	    <artifactId>jsoup</artifactId>
	    <version>1.11.2</version>
	</dependency>
	<!-- Swagger2整合 -->
<!--	<dependency>-->
<!--	    <groupId>io.springfox</groupId>-->
<!--	    <artifactId>springfox-swagger2</artifactId>-->
<!--	    <version>2.7.0</version>-->
<!--	</dependency>-->
<!--	<dependency>-->
<!--	    <groupId>io.springfox</groupId>-->
<!--	    <artifactId>springfox-swagger-ui</artifactId>-->
<!--	    <version>2.7.0</version>-->
<!--	</dependency>-->
	  <!--  knife4j - swagger-->
	  <dependency>
		  <groupId>com.github.xiaoymin</groupId>
		  <artifactId>knife4j-spring-boot-starter</artifactId>
		  <!--在引用时请在maven中央仓库搜索最新版本号-->
		  <version>2.0.5</version>
	  </dependency>
	<!-- 迁移的alipay jar依赖 -->
	  <dependency>
		  <groupId>com.alipay.sdk</groupId>
		  <artifactId>alipay-sdk-java</artifactId>
		  <version>4.39.234.ALL</version>
	  </dependency>
	  <dependency>
		  <groupId>org.jetbrains</groupId>
		  <artifactId>annotations</artifactId>
		  <version>RELEASE</version>
		  <scope>compile</scope>
	  </dependency>
    <!-- 华为云sdk demo -->
	<dependency>
        <groupId>com.hwyun.client</groupId>
        <artifactId>hwyun-sdk</artifactId>
        <version>3.1</version>
    </dependency>
    <dependency>
	  	<groupId>com.alibaba</groupId>
	  	<artifactId>druid-spring-boot-starter</artifactId>
	  	<version>1.1.10</version>
	</dependency>
	<dependency>
	    <groupId>com.aliyun</groupId>
	    <artifactId>aliyun-java-sdk-geoip</artifactId>
	    <version>1.0.3</version>
	</dependency>
	<dependency>
	    <groupId>commons-collections</groupId>
	    <artifactId>commons-collections</artifactId>
	    <version>3.2.1</version>
	</dependency>
	<dependency>
		<groupId>commons-httpclient</groupId>
		<artifactId>commons-httpclient</artifactId>
		<version>3.1</version>
	</dependency>

	  <!-- poi的包 3.15版本后单元格类型获取方式有调整 -->
	  <dependency>
		  <groupId>org.apache.poi</groupId>
		  <artifactId>poi</artifactId>
		  <version>3.14</version>
	  </dependency>
	  <dependency>
		  <groupId>org.apache.poi</groupId>
		  <artifactId>poi-ooxml</artifactId>
		  <version>3.14</version>
	  </dependency>
	  <dependency>
		  <groupId>com.dk</groupId>
		  <artifactId>dk_genrator</artifactId>
		  <version>1.1</version>
	  </dependency>
	 <dependency>
		  <groupId>com.dk</groupId>
		  <artifactId>daemonDDkGenarator</artifactId>
		  <version>1.1</version>
	  </dependency>
	 <dependency>
		  <groupId>com.dk</groupId>
		  <artifactId>daemonDDkGenarator3</artifactId>
		  <version>1.2</version>
	  </dependency>
	  <dependency>
		  <groupId>commons-net</groupId>
		  <artifactId>commons-net</artifactId>
		  <version>3.6</version>
	  </dependency>
	  <dependency>
	    <groupId>it.sauronsoftware</groupId>
	    <artifactId>jave</artifactId>
	    <version>1.0.2</version>
	 </dependency>
	  <!-- 友盟openapi Jar包 -->
	  <dependency>
		  <groupId>com.umeng.client</groupId>
		  <artifactId>umeng-api</artifactId>
		  <version>1.1</version>
	  </dependency>

	  <!--引⼊阿⾥云Core 包-->
	  <dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>aliyun-java-sdk-core</artifactId>
		  <version>[4.3.2,5.0.0)</version>
	  </dependency>
	  <!--引⼊Quick BI阿⾥云SDK-->
	  <dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>quickbi_public20220101</artifactId>
		  <version>1.6.0</version>
	  </dependency>

	  <dependency>
		  <groupId>com.google.code.gson</groupId>
		  <artifactId>gson</artifactId>
		  <version>2.8.6</version>
	  </dependency>
	  <!--引⼊解压缩工具包-->
	  <dependency>
		  <groupId>org.apache.commons</groupId>
		  <artifactId>commons-compress</artifactId>
		  <version>1.18</version>
	  </dependency>

	  <dependency>
		  <groupId>org.brotli</groupId>
		  <artifactId>dec</artifactId>
		  <version>0.1.2</version>
	  </dependency>

	  <dependency>
		  <groupId>com.aliyun.oss</groupId>
		  <artifactId>aliyun-sdk-oss</artifactId>
		  <version>3.10.2</version>
	  </dependency>

	  <!-- 获取视频第一帧依赖 -->
<!--       <dependency>
          <groupId>org.bytedeco</groupId>
          <artifactId>javacpp</artifactId>
          <version>1.4.1</version>
      </dependency>
      <dependency>
          <groupId>org.bytedeco</groupId>
          <artifactId>javacv</artifactId>
          <version>1.4.1</version>
      </dependency>
      <dependency>
          <groupId>org.bytedeco.javacpp-presets</groupId>
          <artifactId>ffmpeg-platform</artifactId>
          <version>3.4.2-1.4.1</version>
      </dependency> -->

	  <dependency>
		  <groupId>org.apache.logging.log4j</groupId>
		  <artifactId>log4j-to-slf4j</artifactId>
		  <version>2.6.2</version>
	  </dependency>

      <dependency>
          <groupId>org.sejda.imageio</groupId>
          <artifactId>webp-imageio</artifactId>
          <version>0.1.6</version>
      </dependency>

		<!-- 亚马逊S3资源依赖 -->
	  <dependency>
           <groupId>com.amazonaws</groupId>
           <artifactId>aws-java-sdk-s3</artifactId>
           <version>1.11.336</version>
       </dependency>
	  <dependency>
           <groupId>com.amazonaws</groupId>
           <artifactId>aws-java-sdk-cloudfront</artifactId>
           <version>1.11.336</version>
       </dependency>

       <!-- dubbo配置nacos注册服务 -->
		<dependency>
	        <groupId>com.alibaba</groupId>
	        <artifactId>dubbo</artifactId>
	        <version>2.6.2</version>
		</dependency>
		<dependency>
	        <groupId>com.alibaba</groupId>
	        <artifactId>dubbo-registry-nacos</artifactId>
	        <version>2.6.7</version>
		</dependency>

		<!-- 公共服务接口Service -->
		<dependency>
			<groupId>com.dnwx.example</groupId>
			<artifactId>wb-service-api</artifactId>
			<version>1.0-RELEASES</version>
		</dependency>

		<!-- 火山引擎对象存储依赖 -->
		<dependency>
		    <groupId>com.volcengine</groupId>
		    <artifactId>ve-tos-java-sdk</artifactId>
		    <version>2.4.0</version>
		</dependency>

      <dependency>
          <groupId>org.redisson</groupId>
          <artifactId>redisson</artifactId>
          <version>3.8.2</version>
      </dependency>
      <dependency>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
		  <version>1.18.8</version>
      </dependency>

	  <dependency>
		  <groupId>com.dnwx</groupId>
		  <artifactId>push-gateway-spring-boot-starter</artifactId>
		  <version>1.0.5-RELEASE</version>
	  </dependency>
	  
	  <!-- doc操作行为 -->
	  <dependency>
		  <groupId>com.deepoove</groupId>
		  <artifactId>poi-tl</artifactId>
		  <version>1.4.2</version>
	  </dependency>


	  <dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>cas20200407</artifactId>
		  <version>3.0.0</version>
	  </dependency>

  </dependencies>
  <build>

	<!-- 打包的文件不带版本号 -->
  	<finalName>wb-ssds</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId><!-- 用来创建可执行的jar -->
        <configuration>
            <jvmArguments>-Dfile.encoding=UTF-8</jvmArguments>
        </configuration>

        <dependencies><!-- 热部署 (需要使用mvn spring-boot:run启动，并开启自动编译) -->
		    <dependency>
		        <groupId>org.springframework</groupId>
		        <artifactId>springloaded</artifactId>
		        <version>1.2.5.RELEASE</version>
		    </dependency>
		</dependencies>
      </plugin>
		<plugin>
			<artifactId>maven-assembly-plugin</artifactId>
			<configuration>
				<!--这部分可有可无,加上的话则直接生成可运行jar包-->
				<!--<archive>-->
				<!--<manifest>-->
				<!--<mainClass>${exec.mainClass}</mainClass>-->
				<!--</manifest>-->
				<!--</archive>-->
				<descriptorRefs>
					<descriptorRef>jar-with-dependencies</descriptorRef>
				</descriptorRefs>
			</configuration>
		</plugin>
    </plugins>
  </build>
</project>
